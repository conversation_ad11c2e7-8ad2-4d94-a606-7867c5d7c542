kind: pipeline
type: kubernetes
name: ci-amd64

platform:
  os: linux
  arch: amd64

trigger:
  ref:
    - refs/pull/**
    - refs/tags/**
  event:
    - push
    - pull_request
    - tag

volumes:
  - name: jfrog-tmp
    temp: {}

steps:
- name: prepare
  image: **************:8081/docker-virtual/alpine:jf
  pull: if-not-exists
  commands:
    - ./scripts/prepare.sh

- name: download-build-depends
  image: **************:8081/docker-virtual/alpine:jf
  pull: if-not-exists
  volumes:
    - name: jfrog-tmp
      path: /jfrog
  environment:
    JF_PLATFORM: linux_deb10
    JFROG_CLI_HOME_DIR: /jfrog
    JFROG_CLI_TEMP_DIR: /jfrog
    ARTIFACTORY_ACCESS_TOKEN:
      from_secret: ARTIFACTORY_ACCESS_TOKEN
  commands:
    - jf config add --url http://**************:8081 --access-token $ARTIFACTORY_ACCESS_TOKEN
    - jf config show
    - ./scripts/download_deps.sh
  depends_on:
    - prepare

- name: build-artifacts
  image: **************:8081/docker-virtual/cmake:jf-debian10
  pull: if-not-exists
  commands:
    - ./scripts/build_artifacts.sh
  environment:
    JF_PLATFORM: linux_deb10
    JFROG_CLI_HOME_DIR: /jfrog
    JFROG_CLI_TEMP_DIR: /jfrog
  depends_on:
    - download-build-depends

- name: build-test-1
  image: **************:8081/docker-virtual/cmake:jf-debian10
  pull: if-not-exists
  environment:
    JF_PLATFORM: linux_deb10
    JFROG_CLI_HOME_DIR: /jfrog
    JFROG_CLI_TEMP_DIR: /jfrog
  commands:
    - ./scripts/build_test.sh test1 UtilUnitTest SipAdapterUnitTest SimpleSipSessionUnitTest SimpleRtpSessionUnitTest MpCallSipGatewayUnitTest MpCallLegacyAppGatewayUnitTest MpCallHigherConfigUnitTest
  depends_on:
    - download-build-depends

- name: build-test-2
  image: **************:8081/docker-virtual/cmake:jf-debian10
  pull: if-not-exists
  environment:
    JF_PLATFORM: linux_deb10
    JFROG_CLI_HOME_DIR: /jfrog
    JFROG_CLI_TEMP_DIR: /jfrog
  commands:
    - ./scripts/build_test.sh test2 SipLineUnitTest
  depends_on:
    - download-build-depends

- name: build-test-3
  image: **************:8081/docker-virtual/cmake:jf-debian10
  pull: if-not-exists
  environment:
    JF_PLATFORM: linux_deb10
    JFROG_CLI_HOME_DIR: /jfrog
    JFROG_CLI_TEMP_DIR: /jfrog
  commands:
    - ./scripts/build_test.sh test3 SipRegLineUnitTest MpCallEbServiceUnitTest
  depends_on:
    - download-build-depends

- name: build-test-report
  image: **************:8081/docker-virtual/cmake:jf-debian10
  pull: if-not-exists
  environment:
    JF_PLATFORM: linux_deb10
    JFROG_CLI_HOME_DIR: /jfrog
    JFROG_CLI_TEMP_DIR: /jfrog
  commands:
    - ./scripts/build_test.sh
  depends_on:
    - build-test-1
    - build-test-2
    - build-test-3

- name: build-doc
  image: **************:8081/docker-virtual/sphinx:jf
  pull: if-not-exists
  commands:
    - ./scripts/build_doc.sh
  depends_on:
    - build-test-report

- name: upload-artifacts
  image: **************:8081/docker-virtual/alpine:jf
  pull: if-not-exists
  volumes:
    - name: jfrog-tmp
      path: /jfrog
  environment:
    JFROG_CLI_HOME_DIR: /jfrog
    JFROG_CLI_TEMP_DIR: /jfrog
    GITEA_ACCESS_TOKEN:
      from_secret: GITEA_ACCESS_TOKEN
  commands:
    - cp tools/jq /usr/local/bin
    - ./scripts/upload_artifacts.sh
  depends_on:
    - build-artifacts
    - build-doc

---

kind: pipeline
type: docker
name: ci-arm64

platform:
  os: linux
  arch: arm64

trigger:
  ref:
    - refs/pull/**
    - refs/tags/**
  event:
    - push
    - pull_request
    - tag

volumes:
  - name: jfrog-tmp
    temp: {}

steps:
- name: prepare
  image: **************:8081/docker-virtual/alpine:jf-arm64
  pull: if-not-exists
  commands:
    - ./scripts/prepare.sh

- name: download-depends
  image: **************:8081/docker-virtual/alpine:jf-arm64
  pull: if-not-exists
  volumes:
    - name: jfrog-tmp
      path: /jfrog
  environment:
    JFROG_CLI_HOME_DIR: /jfrog
    JFROG_CLI_TEMP_DIR: /jfrog
    ARTIFACTORY_ACCESS_TOKEN:
      from_secret: ARTIFACTORY_ACCESS_TOKEN
  commands:
    - jf config add --url http://**************:8081 --access-token $ARTIFACTORY_ACCESS_TOKEN
    - jf config show
    - ./scripts/download_deps.sh
  depends_on:
    - prepare

- name: build-artifacts
  image: **************:8081/docker-virtual/cmake:jf-debian10-arm64
  pull: if-not-exists
  commands:
    - ./scripts/build_artifacts.sh
  environment:
    JFROG_CLI_HOME_DIR: /jfrog
    JFROG_CLI_TEMP_DIR: /jfrog
  depends_on:
    - download-depends

- name: upload-artifacts
  image: **************:8081/docker-virtual/alpine:jf-arm64
  pull: if-not-exists
  volumes:
    - name: jfrog-tmp
      path: /jfrog
  environment:
    JFROG_CLI_HOME_DIR: /jfrog
    JFROG_CLI_TEMP_DIR: /jfrog
    GITEA_ACCESS_TOKEN:
      from_secret: GITEA_ACCESS_TOKEN
  commands:
    - ./scripts/upload_artifacts.sh
  depends_on:
    - build-artifacts
