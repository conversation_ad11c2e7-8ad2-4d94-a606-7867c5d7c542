cmake_minimum_required(VERSION 3.12)

project(jc-sip-gateway
    VERSION 2.4
    LANGUAGES C CXX
)

include(Fetch<PERSON>ontent)

FetchContent_Declare(
    jc-service-base
    GIT_REPOSITORY *********************:jc/jc-service-base.git
    GIT_TAG release/1.3
)
FetchContent_MakeAvailable(jc-service-base)

list(APPEND CMAKE_MODULE_PATH "${CMAKE_CURRENT_SOURCE_DIR}/cmake/")
include(GetGitRevisionDescription)
get_git_head_revision(GIT_REFSPEC GIT_SHA1)
message(GIT_SHA1 ${GIT_SHA1})
if("GIT_SHA1" STREQUAL "")
set(GIT_SHORT_SHA1 "UNKNOWN")
else()
string(SUBSTRING "${GIT_SHA1}" 0 8 GIT_SHORT_SHA1)
endif()

set(CMAKE_SKIP_INSTALL_ALL_DEPENDENCY true)

if(CMAKE_SYSTEM_NAME STREQUAL "Linux")
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -std=c++11")
    set(DEFAULT_OS_NAME linux)

    if(CMAKE_HOST_SYSTEM_PROCESSOR STREQUAL "x86_64")
        set(DEFAULT_COMPILE_DEFINITIONS ZPLATFORM=ZPLATFORM_LINUX OPENSSL_CONF_FILE_LINUX_X86_64 JSM_NO_JSON_KEY -DGIT_HASH=\"${GIT_SHORT_SHA1}\")
        set(DEFAULT_LIB_PATH linux/x86_64)
        set(DEFAULT_PLATFORM_NAME linux-x86_64)

        set(DEFAULT_LINK_OPTIONS
            -static-libgcc
            -static-libstdc++
            -Wl,--wrap=getentropy
            -Wl,--wrap=glob
            -Wl,--wrap=log
            -Wl,--wrap=logf
            -Wl,--wrap=exp
            -Wl,--wrap=expf
            -Wl,--wrap=pow
            -Wl,--wrap=powf
            -Wl,--wrap=fmemopen
        )
        set(WARP_GLIBC_SRC ${PROJECT_SOURCE_DIR}/src/WrapGlibc/__wrap_glibc.c)
    else(CMAKE_HOST_SYSTEM_PROCESSOR STREQUAL "x86_64")
        set(DEFAULT_COMPILE_DEFINITIONS ZPLATFORM=ZPLATFORM_LINUX OPENSSL_CONF_FILE_LINUX_ARM64 JSM_NO_JSON_KEY -DGIT_HASH=\"${GIT_SHORT_SHA1}\")
        set(DEFAULT_LIB_PATH linux/arm64-v8a)
        set(DEFAULT_PLATFORM_NAME linux-arm64)

        set(DEFAULT_LINK_OPTIONS
            -static-libgcc
            -static-libstdc++
        )
        set(WARP_GLIBC_SRC)
    endif(CMAKE_HOST_SYSTEM_PROCESSOR STREQUAL "x86_64")
elseif(CMAKE_SYSTEM_NAME STREQUAL "Darwin")
    set(DEFAULT_OS_NAME mac)

    if(CMAKE_HOST_SYSTEM_PROCESSOR STREQUAL "x86_64")
        set(DEFAULT_COMPILE_DEFINITIONS ZPLATFORM=ZPLATFORM_OSX OPENSSL_CONF_FILE_MACOS_X86_64 JSM_NO_JSON_KEY -DGIT_HASH=\"${GIT_SHORT_SHA1}\")
        set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -std=c++11 -stdlib=libc++ -arch x86_64 -DDARWIN")
        set(DEFAULT_LIB_PATH macos)
        set(DEFAULT_PLATFORM_NAME mac-x86_64)
    else(CMAKE_HOST_SYSTEM_PROCESSOR STREQUAL "x86_64")
        set(DEFAULT_COMPILE_DEFINITIONS ZPLATFORM=ZPLATFORM_OSX OPENSSL_CONF_FILE_MACOS_ARM64 JSM_NO_JSON_KEY -DGIT_HASH=\"${GIT_SHORT_SHA1}\")
        set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -std=c++11 -stdlib=libc++ -arch arm64 -DDARWIN")
        set(DEFAULT_LIB_PATH darwin/arm64-v8a)
        set(DEFAULT_PLATFORM_NAME mac-arm64)
    endif(CMAKE_HOST_SYSTEM_PROCESSOR STREQUAL "x86_64")
elseif(CMAKE_SYSTEM_NAME STREQUAL "Windows")
    set(DEFAULT_COMPILE_DEFINITIONS ZPLATFORM=ZPLATFORM_WIN32 JSM_NO_JSON_KEY)
    set(DEFAULT_OS_NAME win)
    set(DEFAULT_PLATFORM_NAME windows-x86)
else()
    message(STATUS "Configuring on/for ${CMAKE_SYSTEM_NAME}")
endif()

set(CMAKE_EXPORT_COMPILECOMMANDS ON)

if(DEFINED ENV{JssSharedPath})
    set(DEFAULT_INCLUDE_DIRECTORIES
        $ENV{JssSharedPath}/inc
        $ENV{JssSharedPath}/include
        $ENV{JssSharedPath}/include/lemon/voip_zero
        $ENV{JssSharedPath}/3rd/inc
        ${PROJECT_SOURCE_DIR}/inc
        ${PROJECT_SOURCE_DIR}/src
        ${jc-service-base_SOURCE_DIR}/3rd
        ${jc-service-base_SOURCE_DIR}/inc
        ${jc-service-base_SOURCE_DIR}/src
    )
    if(CMAKE_SYSTEM_NAME STREQUAL "Windows")
        set(DEFAULT_JSM_LIBRARIES
            $ENV{JssSharedPath}/lib/vs14/win32/Release/libjsm.lib
            $ENV{JssSharedPath}/lib/vs14/win32/Release/libmme_jrtc.lib
            $ENV{JssSharedPath}/lib/vs14/win32/Release/libjrtc.lib
            $ENV{JssSharedPath}/lib/vs14/win32/Release/libavatar.lib
            $ENV{JssSharedPath}/lib/vs14/win32/Release/libzmf_zero.lib
            $ENV{JssSharedPath}/lib/vs14/win32/Release/libCommonExSeCxx11.lib
        )
        set(DEFAULT_SERVER_LIBRARIES
            $ENV{JssSharedPath}/lib/vs14/win32/Release/libCommonExSeCxx11.lib
        )
    else(CMAKE_SYSTEM_NAME STREQUAL "Windows")
        set(DEFAULT_JSM_LIBRARIES
            $ENV{JssSharedPath}/lib/${DEFAULT_LIB_PATH}/libjsm.a
            $ENV{JssSharedPath}/lib/${DEFAULT_LIB_PATH}/libmme_jrtc.a
            $ENV{JssSharedPath}/lib/${DEFAULT_LIB_PATH}/libjrtc.a
            $ENV{JssSharedPath}/lib/${DEFAULT_LIB_PATH}/libavatar.a
            $ENV{JssSharedPath}/lib/${DEFAULT_LIB_PATH}/libzmf_zero.so
            $ENV{JssSharedPath}/lib/${DEFAULT_LIB_PATH}/libCommonExSeCxx11.a
        )
        set(DEFAULT_SERVER_LIBRARIES
            $ENV{JssSharedPath}/lib/${DEFAULT_LIB_PATH}/libCommonExSeCxx11.a
        )
        set(DEFAULT_CLIENT_LIBRARIES
            $ENV{JssSharedPath}/service/all/linux/lib/x86_64/libmtc_room.a
            $ENV{JssSharedPath}/service/all/linux/lib/x86_64/libmtc_signal.a
            $ENV{JssSharedPath}/service/all/linux/lib/x86_64/libClient.a
            $ENV{JssSharedPath}/service/all/linux/lib/x86_64/libCommonSe.a
            $ENV{JssSharedPath}/service/all/linux/lib/x86_64/libmtc_core.a
            $ENV{JssSharedPath}/service/all/linux/lib/x86_64/libmtc_extra.a
            $ENV{JssSharedPath}/service/all/linux/lib/x86_64/libzmf_zero.a
            $ENV{JssSharedPath}/service/all/linux/lib/x86_64/libmme_jrtc.a
            $ENV{JssSharedPath}/service/all/linux/lib/x86_64/libjrtc.a
            $ENV{JssSharedPath}/service/all/linux/lib/x86_64/libjsm.a
            $ENV{JssSharedPath}/service/all/linux/lib/x86_64/libavatar.a
        )
    endif(CMAKE_SYSTEM_NAME STREQUAL "Windows")
else()
    list(APPEND DEFAULT_DEPS
        gtest=external/gtest/1.7/gtest-1.7.0.5-7c8fe5b
        yaml-cpp=external/yaml-cpp/0.7/yaml-cpp-0.7.0-6937d6b
        protobuf=external/protobuf/3.21/protobuf-3.21.4.1-e9bc725
        common=Platform/cube/3.5/cube-3.5.2228-8a89649
        json=external/jsoncpp/0.10/jsoncpp-0.10.5.2-1c36719
    )
    list(APPEND DEFAULT_DEPS_SNAPSHOT
        grape=sdk/grape/6.5/grape-6.5-0bc8438
        watermelon=sdk/watermelon/6.5/watermelon-6.5-50c59e2
        melon=sdk/melon-4/6.5/melon-4-6.5.7-dbccd11
        jusmeeting=solution/jusmeeting/6.5/jusmeeting-6.5.6.1-276bcb4
    )
    include(FetchContent)
    foreach(pair IN LISTS DEFAULT_DEPS)

        string(FIND "${pair}" "=" pos)
        string(SUBSTRING "${pair}" 0 "${pos}" dep_name)
        math(EXPR pos "${pos} + 1")
        string(SUBSTRING "${pair}" "${pos}" -1 dep_path)

        message("fetch ${dep_name} from ${dep_path}")
        FetchContent_Declare(
            ${dep_name}
            URL http://192.168.18.246:8081/artifactory/cloud-generic-release-local/com/juphoon/${dep_path}-${DEFAULT_PLATFORM_NAME}.tgz
        )
        FetchContent_MakeAvailable(${dep_name})
    endforeach(pair IN LISTS DEFAULT_DEPS)

    foreach(pair IN LISTS DEFAULT_DEPS_SNAPSHOT)

        string(FIND "${pair}" "=" pos)
        string(SUBSTRING "${pair}" 0 "${pos}" dep_name)
        math(EXPR pos "${pos} + 1")
        string(SUBSTRING "${pair}" "${pos}" -1 dep_path)

        message("fetch ${dep_name} from ${dep_path}")
        FetchContent_Declare(
            ${dep_name}
            URL http://192.168.18.246:8081/artifactory/cloud-generic-snapshot-local/com/juphoon/${dep_path}-${DEFAULT_PLATFORM_NAME}.tgz
        )
        FetchContent_MakeAvailable(${dep_name})
    endforeach(pair IN LISTS DEFAULT_DEPS_SNAPSHOT)

    set(DEFAULT_INCLUDE_DIRECTORIES
        ${common_SOURCE_DIR}/inc
        ${yaml-cpp_SOURCE_DIR}/include
        ${json_SOURCE_DIR}/inc
        ${protobuf_SOURCE_DIR}/include
        ${jusmeeting_SOURCE_DIR}/include
        ${lemon_SOURCE_DIR}/service/all/${DEFAULT_OS_NAME}/include
        ${lemon_SOURCE_DIR}/include
        ${PROJECT_SOURCE_DIR}/inc
        ${PROJECT_SOURCE_DIR}/src
    )
    if(CMAKE_SYSTEM_NAME STREQUAL "Windows")
        if(CMAKE_BUILD_TYPE STREQUAL "Debug")
            set(WINDOWS_LIB_PATH Debug)
        else(CMAKE_BUILD_TYPE STREQUAL "Debug")
            set(WINDOWS_LIB_PATH Release)
        endif(CMAKE_BUILD_TYPE STREQUAL "Debug")
        set(DEFAULT_JSM_LIBRARIES
            $ENV{JssSharedPath}/lib/vs14/win32/${WINDOWS_LIB_PATH}/libjsm.lib
            $ENV{JssSharedPath}/lib/vs14/win32/${WINDOWS_LIB_PATH}/libmme_jrtc.lib
            $ENV{JssSharedPath}/lib/vs14/win32/${WINDOWS_LIB_PATH}/libjrtc.lib
            $ENV{JssSharedPath}/lib/vs14/win32/${WINDOWS_LIB_PATH}/libavatar.lib
            $ENV{JssSharedPath}/lib/vs14/win32/${WINDOWS_LIB_PATH}/libzmf_zero.lib
            ${common_SOURCE_DIR}/lib/vs14/win32/${WINDOWS_LIB_PATH}/libCommonExSeCxx11.lib
        )
        set(DEFAULT_SERVER_LIBRARIES
            ${common_SOURCE_DIR}/lib/vs14/win32/${WINDOWS_LIB_PATH}/libCommonExSeCxx11.lib
        )
    else(CMAKE_SYSTEM_NAME STREQUAL "Windows")
        set(DEFAULT_JSM_LIBRARIES
            $ENV{JssSharedPath}/lib/${DEFAULT_LIB_PATH}/libjsm.a
            $ENV{JssSharedPath}/lib/${DEFAULT_LIB_PATH}/libmme_jrtc.a
            $ENV{JssSharedPath}/lib/${DEFAULT_LIB_PATH}/libjrtc.a
            $ENV{JssSharedPath}/lib/${DEFAULT_LIB_PATH}/libavatar.a
            $ENV{JssSharedPath}/lib/${DEFAULT_LIB_PATH}/libzmf_zero.so
            ${common_SOURCE_DIR}/lib/${DEFAULT_LIB_PATH}/libCommonExSeCxx11.a
        )
        set(DEFAULT_SERVER_LIBRARIES
            ${common_SOURCE_DIR}/lib/${DEFAULT_LIB_PATH}/libCommonExSeCxx11.a
        )
    endif(CMAKE_SYSTEM_NAME STREQUAL "Windows")
endif()

set(DEFAULT_SYS_LIBRARIES
    -lpthread -ldl -lz
)

set(CMAKE_INSTALL_RPATH "$ORIGIN")

enable_testing()

option(BUILD_FOR_COVERAGE "option for coverage building")

if((BUILD_FOR_COVERAGE STREQUAL "ON"))
    include(CodeCoverage)
    append_coverage_compiler_flags()
    set(CODE_COVERAGE_VERBOSE TRUE)
    set(GCOVR_ADDITIONAL_ARGS "-f .*src.*")
    setup_target_for_coverage_gcovr_html(
        NAME sip_gateway_stest_coverage
        EXECUTABLE ctest --output-on-failure || exit 0
        DEPENDENCIES tests
        EXCLUDE .*/test/.* .*pb.* .*JSM/.* .*/RecorderService/.* .*/TssarOputStream/.*
    )
endif()

add_subdirectory(src/MpCallHigherConfig)

add_subdirectory(src/SipGatewayEntry)
add_subdirectory(src/Gateway)
add_subdirectory(src/SipAdapter)
add_subdirectory(src/SipLine)
add_subdirectory(src/SipRegLine)
add_subdirectory(src/SimpleSipSession)
add_subdirectory(src/SimpleRtpSession)
add_subdirectory(src/Util)
add_subdirectory(src/MpCallSip)
add_subdirectory(src/MpCallSipGateway)
add_subdirectory(src/MpCallRtpRunner)
add_subdirectory(src/MpCallRtpRunnerDaemon)

add_subdirectory(src/MpCallLegacyAppGateway)

add_subdirectory(src/DbConn)
add_subdirectory(src/Account)
add_subdirectory(src/AcdGateway)
add_subdirectory(src/CcSvr)
add_subdirectory(src/MpCallEb)
add_subdirectory(src/MpCallEbService)

install(
    TARGETS MpCallSipGateway MpCallRtpRunner MpCallRtpRunnerDaemon MpCallLegacyAppGateway MpCallEbService MpCallEbDialer MpCallSipDialer
    EXPORT SipCallService
    RUNTIME DESTINATION bin/update
)

if(DEFINED ENV{JssSharedPath} AND CMAKE_SYSTEM_NAME STREQUAL "Linux")
    install(
        FILES
        $ENV{JssSharedPath}/lib/${DEFAULT_LIB_PATH}/libzmf_zero.so
        DESTINATION bin/update
    )
endif()

if(DEFINED ENV{JssSharedPath} AND CMAKE_SYSTEM_NAME STREQUAL "Linux")
    if(CMAKE_HOST_SYSTEM_PROCESSOR STREQUAL "x86_64")
    install(
        FILES
        $ENV{JssSharedPath}/lib/linux/x86_64/libjssisipcall.so
        $ENV{JssSharedPath}/lib/linux/x86_64/libjssirtp.so

        DESTINATION bin/update
    )
    else(CMAKE_HOST_SYSTEM_PROCESSOR STREQUAL "x86_64")
    install(
        FILES
        $ENV{JssSharedPath}/lib/linux/arm64-v8a/libjssisipcall.so
        $ENV{JssSharedPath}/lib/linux/arm64-v8a/libjssirtp.so

        DESTINATION bin/update
    )
    endif(CMAKE_HOST_SYSTEM_PROCESSOR STREQUAL "x86_64")
endif()

install(
    TARGETS MpCallSipCli SimpleSipSessionCli
    EXPORT SipMpCallTool
    RUNTIME DESTINATION bin
)

add_custom_target(apps
    COMMAND ${CMAKE_COMMAND} -E echo build apps
    DEPENDS MpCallSipGateway MpCallRtpRunner MpCallRtpRunnerDaemon MpCallLegacyAppGateway MpCallEbService
)

add_custom_target(tools
    COMMAND ${CMAKE_COMMAND} -E echo build tools
    DEPENDS MpCallSipCli SimpleSipSessionCli MpCallEbDialer MpCallSipDialer
)

add_custom_target(tests
    COMMAND ${CMAKE_COMMAND} -E echo build tests
    DEPENDS SimpleSipSessionUnitTest SimpleRtpSessionUnitTest SipLineUnitTest UtilUnitTest MpCallSipGatewayUnitTest MpCallEbServiceUnitTest MpCallLegacyAppGatewayUnitTest SipAdapterUnitTest MpCallHigherConfigUnitTest SipRegLineUnitTest
)
