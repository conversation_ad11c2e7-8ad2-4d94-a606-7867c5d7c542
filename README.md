# MpCall 服务

* [版本信息](doc/release_note.md)

## SIP 对接概要说明

MpCall 对接 SIP 线路包括以下网元

* MpCallSipGateway - SIP 信令对接网关服务，负责处理 MpCallServer 与 SIP 线路之间的交互，分配和控制 MpCallRtpRtunner
* MpCallRtpRunner - RTP 对接网关服务，负责处理房间和 SIP 线路之间 RTP 媒体数据的交互
* MpCallRtpRunnerDaemon - MpCallRtpRunner 守护服务，负责一个主机上启动和监控 MpCallRtpRunner 服务

* [部署配置](doc/deploy.md)
* [工具说明](doc/tools.md)

## App 兼容服务

App 兼容服务是使老终端具备加入 MpCall 多方通话的能力，仅支持终端主动加入房间，不支持邀请终端流程。服务包括

* MpCallLegacyAppGateway - 老版本 App 终端接入 MpCallServer 的网关服务

* [部署配置](doc/deploy_app.md)

## 光大定制业务服务

* [光大定制业务服务](doc/eb_service.md)

## 设计文档

* [SipRegLine](doc/design/sipregline.md)
