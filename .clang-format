# 访问说明符(public、private等)的偏移
AccessModifierOffset: -4

# 在大括号前换行: Attach(始终将大括号附加到周围的上下文), Linux(除函数、命名空间和类定义，与Attach类似),
#   Mozilla(除枚举、函数、记录定义，与Attach类似), <PERSON><PERSON><PERSON><PERSON>(除函数定义、catch、else，与Attach类似),
#   <PERSON>man(总是在大括号前换行), GNU(总是在大括号前换行，并对于控制语句的大括号增加额外的缩进), WebKit(在函数前换行), Custom
#   注：这里认为语句块也属于函数
BreakBeforeBraces: Custom
BraceWrapping:
  AfterClass: true
  AfterControlStatement: true
  AfterEnum: true
  AfterFunction: true
  AfterNamespace: true
  AfterStruct: true
  AfterUnion: true
  AfterExternBlock: true
  BeforeCatch: true
  BeforeElse: true
  IndentBraces: false


# 每行字符的限制，0表示没有限制
ColumnLimit: 0

# 缩进宽度
IndentWidth: 4

# 在构造函数的初始化列表的逗号前换行
BreakConstructorInitializersBeforeComma: true

# 命名空间的缩进: None, Inner(缩进嵌套的命名空间中的内容), All
NamespaceIndentation:	Inner
