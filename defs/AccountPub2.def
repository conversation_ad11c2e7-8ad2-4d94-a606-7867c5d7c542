#include "Types.def"

module Account
{
    /* Account/account */
    interface Account
    {
        // bool getUserId(out string userId) onexception(false);
        // bool getSessions(out Common.StrSet sessions) onexception(false);
        // bool getUserIdAndSessions(out string userId,out Common.StrSet sessions) onexception(false);

        // bool postOnlineMessage(string type,Common.StrStrMap params,stream message,string exceptSession = "",out int postCount) async onexception(false);
        // 只要有一个session send成功(recvOnlineMessage成功或者push成功),接口返回true.
        bool sendOnlineMessage(string type,Common.StrStrMap params,stream message) async onexception(false);
        // bool sendOfflineMessage(string type,Common.StrStrMap params,stream message) async onexception(false);

        // bool getProp(string name,out string prop) async onexception(false);
        // bool setProp(string name,string prop,out string oldProp) async onexception(false);
        // bool getProps(Common.StrVec names,out Common.StrStrMap props) async onexception(false);
        // bool getPropsPrefix(string prefix,out Common.StrStrMap props) async onexception(false);
        // bool setProps(Common.StrStrMap props) async onexception(false);
        // bool setPropsCond(Common.StrStrMap conds,Common.StrStrMap props) async onexception(false);
        // bool getSessionsProps(string prefix,out SessionProps status) async onexception(false);

        // bool getStatus(out SessionStatus status) async onexception(false);
        // bool kickOff(string reason) async onexception(false);

        // bool testPush(Common.StrStrMap params) async onexception(false);
    };

    /* Session/account/session */
//    interface Session
//    {
//        bool getUserId(out string userId) onexception(false);
//        bool sendOnlineMessage(string type,Common.StrStrMap params,stream message) async onexception(false);
//
//        bool getProp(string name,out string prop) async onexception(false);
//        bool setProp(string name,string prop,out string oldProp) async onexception(false);
//        bool getProps(Common.StrVec names,out Common.StrStrMap props) async onexception(false);
//        bool getPropsPrefix(string prefix,out Common.StrStrMap props) async onexception(false);
//        bool setProps(Common.StrStrMap props) async onexception(false);
//        bool setPropsCond(Common.StrStrMap conds,Common.StrStrMap props) async onexception(false);
//
//        bool getStatus(out Status status = StatusUnknown) async onexception(false);
//        bool kickOff(string reason) async onexception(false);
//    };
};