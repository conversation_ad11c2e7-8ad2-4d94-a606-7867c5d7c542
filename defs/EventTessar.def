module DataCollection
{
    struct TessarBodyGeneralJsmEvent
    {
        int event_code;
        int time_cost;
        long timestamp;
    };

    vector TessarBodyGeneralJsmEventVec<TessarBodyGeneralJsmEvent>;

    struct TessarBodyGeneral
    {
        int client_type;
        string rroom_id;
        string m_id;
        int join_cost;
        int conf_role;
        int event;
        TessarBodyGeneralJsmEventVec outside_jsm_event;
    };

    struct TessarBodyRtp
    {
        stream audio;
        stream video;
    };

    struct TessarBody
    {
        stream zmf;
        stream jsm;
        TessarBodyRtp rtp;
        TessarBodyGeneral general;
    };

    struct EventTessar
    {
        string tkm_tessar;
        long tkm_collect_time;
        string tkm_domain_id;
        string tkm_app_id;
        int tkm_access_type;
        int tkm_platform;
        string tkm_ip_addr;
        string tkm_sdk_ver;
        string tkm_os_ver;
        string tko_service;
        string tko_account_id;
        string tko_session_id;
        string tko_brand;
        string tko_factory;
        string tko_cpu_ver;
        string tko_net_type;

        TessarBody body;
    };
}
