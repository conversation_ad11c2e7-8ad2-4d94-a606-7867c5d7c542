#include "Types.def"

module JSMD
{
    struct Event
    {
        string pub;
    };

    struct Actor
    {
        int role;
        int state;
        int idx;
        string nick;
    };

    map Actors<string, Actor>;

    struct EventJoin
    {
        string pub;
        Actors actor;
    };

    map ActorState<string, string>;

    struct EventLeave
    {
        string pub;
        ActorState actor;
    };

    struct ActorLeave
    {
        string actorid;
        int actorrole;
        int actorstate;
        int appid;
        int lasttime;
        string sdkver;
        int channeltype;
    };

    struct EventActorLeave
    {
        string pub;
        ActorLeave actorleave;
    };
}
