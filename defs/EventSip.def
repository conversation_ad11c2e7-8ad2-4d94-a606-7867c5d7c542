#include "Types.def"

module SipEvent
{
    /**
     * SIP事件类型枚举
     */
    enum SipEventType
    {
        /** 呼叫事件 */
        SipEventCall           = 0,
        /** 振铃事件 */
        SipEventAlerted        = 1,
        /** 通话事件 */
        SipEventConnected      = 2,
        /** 结束事件 */
        SipEventTerminated     = 3,
        /** 通话质量变化事件 */
        SipEventNetQualityChanged = 5,
        /** Rtp网络质量变化事件 */
        SipEventRtpNetQualityChanged = 6
    }

    /**
     * 统一的SIP事件数据结构
     * 适用于：呼叫事件、振铃事件、通话事件、结束事件
     */
    struct EventSipDetail
    {
        /** 域ID */
        long domainId;
        /** 应用ID */
        long appId;
        /** 开始时间 */
        long beginTimestamp;
        /** 结束时间（用于结束事件，其他事件为0）*/
        long endTimestamp;
        /** 通话ID */
        string callId;
        /** SIP呼叫ID */
        string sipCallId;
        /** 主叫号码 */
        string caller;
        /** 被叫号码 */
        string callee;
        /** 结束类型 */
        int endType;
        /** 通话时长(秒) */
        long duration;
    }

    /**
     * SIP网络质量变化事件
     */
    struct EventSipNetworkChanged
    {
        /** 域ID */
        long domainId;
        /** 应用ID */
        long appId;
        /** 开始时间 */
        long beginTimestamp;
        /** 结束时间（用于结束事件，其他事件为0）*/
        long endTimestamp;
        /** 通话ID */
        string callId;
        /** SIP呼叫ID */
        string sipCallId;
        /** 主叫号码 */
        string caller;
        /** 被叫号码 */
        string callee;
        /** 通话时长(秒) */
        long duration;
        /** 网络状态 */
        int networkStatus;
    }

    /**
     * SIP通话记录数据结构
     * Topic: jrtc.records
     * 类型: 14
     * 编号: 1
     * 名称: 话单
     */
    struct SipCallRecord
    {
        /** 域ID */
        long domainId;
        /** 应用ID */
        long appId;
        /** 开始时间 */
        long beginTimestamp;
        /** 结束时间 */
        long endTimestamp;
        /** 唯一Call-Id */
        string callId;
        /** 业务ID(暂为空) */
        string bizId;
        /** 主叫号码 */
        string caller;
        /** 被叫号码 */
        string callee;
        /** 结束类型 */
        int endType;
        /** 振铃时间 */
        long ringingTimeStamp;
        /** 来源：服务Id */
        string source;
        /** 创建时间 */
        string gmtCreate;
    }
}