module DataCollection
{
    struct EventRtmp
    {
        string callId;
        string bussinessNo;
        int type;
        string rtmpUri;
    };

    struct EventRecordStarted
    {
        string domainId;
        string appId;
        long beginTimestamp;
        long endTimestamp;
        string callId;
        string recorderId;
        string roomId;
        string fileName;
    };

    struct EventRecordStopped
    {
        long beginTimestamp;
        long endTimestamp;
        string callId;
        string recorderId;
        int endType;
        long recordDuration;
        float diffDuration;
        int fileSize;
        string fileName;
        string roomId;
        string moreInfo;
        string domainId;
        string appId;
    };

    struct EventRecordManagerError
    {
        string domainId;
        string appId;
        long timestamp;
        string callId;
        string roomId;
        string err_info;
    };
}
