#include "Types.def"

module NmsFoundation 
{
    vstruct ServiceConfig
    {
        string executablePath;
        Common.StrSet referenceFiles;
        Common.StrStrMap startupParams;
        Common.StrStrMap limitParams;
        bool serviceStop = false;
        bool serviceOos = false; 
        bool serviceDebug = false; 
    };
    map ServiceConfigMap<string,ServiceConfig>;         /* serviceId -> ServiceConfig */
    map NodesConfigMap<string,ServiceConfigMap>;        /* nodeId -> ServiceConfigMap */

    vstruct HostConfig 
    {
        ServiceConfigMap routersConfig; 
        ServiceConfigMap gridsConfig;
        NodesConfigMap nodesConfig;
    };

    struct NetworkInterfaceUsage
    {
        double networkSendKbps = 0;
        double networkRecvKbps = 0;
    };
    map NetworkInterfaceUsageMap<string,NetworkInterfaceUsage>;

    struct FileSystemUsage
    {
        long diskTotalMB = 0;
        double diskOccupiedRate = 0;
    };
    map FileSystemUsageMap<string,FileSystemUsage>; 

    enum ServiceState 
    {
        kServiceStateInit       = 0, 
        kServiceStateUpdating   = 1, 
        kServiceStateStarting   = 2, 
        kServiceStateActivated  = 3, 
        kServiceStateStopping   = 4, 
        kServiceStateCrashed    = 5, 
        kServiceStateNoResponse = 6, 
        kServiceStateStandby    = 7, 
        kServiceStateMask       = 0xf,

        kServiceNoneUpgrade     = 0x10,
        kServicePulling         = 0x20,
        kServicePullFail        = 0x30,
        kServiceWaitingApply    = 0x40,
        kServiceApplying        = 0x50,
        kServiceApplyFail       = 0x60,
        kServiceUpgradeMask      = 0xf0,

        kServiceStateStop       = 0x10000,
        kServiceStateOOS        = 0x20000, 
        kServiceStateDebug      = 0x40000, 
    };

    vstruct ServiceStatus 
    {
        int servicePid = 0;
        ServiceState state = kServiceStateInit;

        long launchTime = 0;
        long currentTime = 0;

        double cpuOccupiedRate = 0;
        long memoryOccupiedKB = 0;
        long virtMemoryOccupiedKB = 0;
        int threadOccupiedNumber = 0;
        double networkSendKbps = 0;
        double networkRecvKbps = 0;

        Common.StrStrMap runtimeProps;
        Common.StrStrMap limitProps;
    };

    map ServiceStatusMap<string,ServiceStatus>; 
    map NodesStatusMap<string,ServiceStatusMap>; 

    vstruct HostStatus
    {        
        double cpuOccupiedRate = -1;
        double cpuWaitIoRate = -1;
        int cpuCoreNumber = -1;

        double memoryOccupiedRate = -1;
        long memoryTotalKbytes = -1;

        NetworkInterfaceUsageMap netUsage;
        FileSystemUsageMap fileUsage;

        ServiceStatus hostStatus; 
        ServiceStatusMap routersStatus;
        ServiceStatusMap gridsStatus;
        NodesStatusMap nodesStatus;
    };

    map ServiceStateMap<string,ServiceState>;
    map NodesStateMap<string,ServiceStateMap>; 

    vstruct HostState 
    {
        string adminOid; 
        ServiceStateMap routersState; 
        ServiceStateMap gridsState; 
        NodesStateMap nodesState; 
    }; 

    struct FileInfo 
    {
        long fileSize = 0; 
        long fileCrc32 = 0; 
    };
    map FileInfoMap<string,FileInfo>;                   /* path -> FileInfo */

    interface NmsConfig  
    {
        bool updateHost(string hostId,HostState hostState,out long configMtime) async onexception(false); 
        bool getHostConfig(string hostId,out HostConfig hostConfig,out long configMtime) async onexception(false); 
        bool applyTemplate(string templateId,Common.StrStrMap params) async onexception(false); 
    }; 

    interface NmsStatus 
    {
        bool reportHostStatus(string hostId,HostStatus hostStatus) async onexception(false); 
    }; 

    interface NmsUpdate 
    {
        bool updateFile(string path,FileInfo info,out stream file,out FileInfo newInfo) onexception(false); 
        bool checkUpdate(FileInfoMap files,out bool isUpdated) onexception(false); 
        bool updateFileEx(string path,FileInfo checkInfo,long offset,out stream file,out FileInfo newInfo) onexception(false); 
    }; 

    interface ServiceManager
    {
        bool updateService(string serviceId,int pid,string adminOid,Common.StrStrMap runtimeProps) onexception(false);
        bool swapInfo(string serviceId,int pid,Common.StrStrMap inParams,out Common.StrStrMap outParams) onexception(false);        
        bool wantUpdateFile(string path) onexception(false);  
    }; 

    interface ServiceAdmin 
    {
        bool shutdown() onexception(false); 
        bool outOfService(bool enable) onexception(false); 

        bool getAllStatistics(string serviceId,out Common.StrStrMap statistics) async onexception(false); 
        bool getServicesStatAndCfg(string serviceId,string statPrefix,string cfgPrefix,out Common.StrStrMap statistics,out Common.StrStrMap cfgs) async onexception(false);
        bool getAllConfigs(string serviceId,out Common.StrStrMap configs) async onexception(false); 
        bool uploadFile(string pathName,stream file,bool executable) async onexception(false);
        bool execCmd(string cmd,out string result) async onexception(false);
    };
};
