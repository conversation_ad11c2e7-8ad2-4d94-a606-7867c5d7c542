#include "Types.def"

module SipGatewayEntry
{
    interface SipGatewayEntry
    {
        bool onInvited(string conferenceId, string callerAccount, string calleeNumber, string domainId, string appId, string deviceIp, Common.StrStrMap params) onexception(false) async;
    }

    struct SipConfMessage
    {
        string Type;
        string ConfUri;
        string From;
        string UserToUserInfo;
        bool Video;
    };
};
