#include "Types.def"

module MpCallSip
{
    /** 媒体方向 */
    enum MediaDirection
    {
        MediaInactive = 0,
        MediaSendOnly,
        MediaRecvOnly,
        MediaSendRecv
    };

    /** RtpRunnerMonitor 接口
     */
    interface RtpRunnerMonitor
    {
        void onError(string reason);
        void onNetworkStatusChanged(Common.StrIntMap networkStatus);
    };

    struct RtpRunnerConfig
    {
        int domainId;
        int appId;

        // name in room
        string name;
        int extraRole;

        // room info, oid&roomId || confNum || Udid
        string oid;
        string roomId;
        string confNum;
        string Udid;

        int audioExcludeRoleMask = 0;
        int audioExcludeRole = 0;
        bool audioMute = false;

        int videoExcludeRoleMask = 0;
        int videoExcludeRole = 0;
        bool videoMute = false;

        bool screenSharingMode = false;

        string bindingUser;
    };

    struct SdpParams
    {
        MediaDirection videoDirection = MediaSendRecv;
        MediaDirection audioDirection = MediaSendRecv;
        bool precondition;
    };

    struct RtpAudioSendStats
    {
        long totalPackets;
        long lostPackets;
        int lostPercent;
        int jitter;
        int bitrate;
    };

    struct RtpAudioRecvStats
    {
        long totalPackets;
        long lostPackets;
        int lostPercent;
        int jitter;
        int bitrate;
        float tmos;
    };

    struct RtpAudioGeneranlStats
    {
        int rtt;
        string networkStatus;
    };

    struct RtpAudioConfigStats
    {
        string localIp;
        int localPort;
        string remoteIp;
        int remotePort;
        string codec;
        int payload;
        int bitrate;
        int packetLen;
    };

    struct RtpAudioStats
    {
        RtpAudioSendStats send;
        RtpAudioRecvStats recv;
        RtpAudioGeneranlStats general;
        RtpAudioConfigStats config;
    };

    struct RtpVideoSendStats
    {
        long totalPackets;
        long lostPackets;
        int lostPercent;
        int jitter;
        int frameRate;
        string resolution;
        int bitrate;
        string codec;
        float spmos;
        float pnsr;
        int encodeTime;
    };

    struct RtpVideoRecvStats
    {
        long totalPackets;
        long lostPackets;
        int lostPercent;
        int jitter;
        int frameRate;
        string resolution;
        int bitrate;
        string codec;
        float pvmos;
        int decodeTime;
    };

    struct RtpVideoGeneralStats
    {
        string captureResolution;
        int captureFrameRate;
        int renderFrameRate;
        int rtt;
        string networkStatus;
    };

    struct RtpVideoConfigStatus
    {
        string localIp;
        int localPort;
        string remoteIp;
        int remotePort;
        string codec;
        int payload;
        int bitrate;
        int frameRate;
        string resolution;
    };

    struct RtpVideoStats
    {
        RtpVideoSendStats send;
        RtpVideoRecvStats recv;
        RtpVideoGeneralStats general;
        RtpVideoConfigStatus config;
    };

    struct RtpStats
    {
        RtpAudioStats audio;
        RtpVideoStats video;
    };

    /** RtpRunner 接口
     */
    interface RtpRunner
    {
        bool callout(string targetOid, string monitorOid, RtpRunnerConfig config, SdpParams sdpParams, out string offerSdp) async onexception(false);
        bool callin(string targetOid, string monitorOid, RtpRunnerConfig config, string peerSdp) async onexception(false);
        bool callinAndOffer(string targetOid, string monitorOid, RtpRunnerConfig config, SdpParams sdpParams, out string localSdp) async onexception(false);
        bool callinAndAnswer(string targetOid, string monitorOid, RtpRunnerConfig config, string peerSdp, SdpParams sdpParams, out string localSdp) async onexception(false);
        bool close() onexception(false);

        bool genOffer(SdpParams sdpParams, out string offerSdp) onexception(false);
        bool setOffer(string offerSdp) onexception(false);
        bool setOfferAndAnswer(string offerSdp, SdpParams sdpParams, out string answerSdp) onexception(false);
        bool genAnswer(SdpParams sdpParams, out string answerSdp) onexception(false);
        bool setAnswer(string answerSdp) onexception(false);
        bool setTalking(bool talking) onexception(false);

        bool getRtpStatus(out RtpStats stats) onexception(false);
    };
};
