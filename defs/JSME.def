#include "Types.def"

module JSM
{

struct RegionConfig
{
    int Id;                 /* region ID, from 10 to 99 */
    string Name;            /* region name in brief */
    string ObjectId;        /* object ID string */
    Common.StrStrMap Params; /* parameters for extension */
};

map RegionConfigMap<int, RegionConfig>;

/**
 * 会议入口
 *
 * 基本概念
 *   JSME 是全球唯一的会议入口服务.
 *     - 动态选择分布式部署的JSMS, 创建会议实例JSMI
 *     - 提供会议号(类似电话号)的查询功能
 *     - 预约会议功能
 *
 *   JSMS 是分布式部署的会议服务
 *     - 保持与JSME的心跳
 *     - 创建会议实例JSMI
 *
 *   JSMI 是动态生成的,进行中的会议实例
 *     - 创建会议引擎jsmd
 *     - 成员间的数据转发
 *
 * jsmiId 是JSMI的objectId,用于客户侧进行RPC调用.
 * roomId 是会议引擎jsmd的ID, 由时间截和序号组成,保证近期唯一
 * confNumber 是近期唯一的会议号,类似电话号.
 *
 * 会议热更新机制
 *   正常运行条件必须满足
 *   JSME.version <= JSMS.version
 *
 *   - JSME 定时从数据库中获取
 *          JSME.version=<ver>
 *          JSMS.HZ08=<ver>
 *          JSMS.BJ16=<ver>, ...
 *   - JSME 收到JSMS.HZ08的心跳时, 比较两者版本号
 *   - 若不存在JSMS.HZ08.version,则更新并添加记录.
 *   - 若 JSMS.HZ08.version < JSME.version, 则更新数据库
 *          JSMS.HZ08.version = JSME.version
 *     并通知 JSMS.HZ08 更新
 *   - JSME.version 必须从"年月日序号"开始并保持递增,例如
 *          JSME.version=2016041201
 *   - 递增 JSME.version 将强制更新所有JSMS
 *   - 复位 JSMS.version = 0 将强制更新该JSMS
 *   - 用于更新的备用JSMS.verion必须为uint32_t 最大值
 *
 * 会议服务特定分配机制
 *   - JSME 定时从数据库读取
 *          JSME.domains="100561 100721 100724 10050"
 *          JSME.domain.100561 = "********** **********"
 *          JSME.domain.100724 = **********
 *          JSME.domain.100721 = **********
 *          JSME.domain.100500 = **********
 *   - JSME 创建会议时,查找对应domain的IP列表,进行分配
 *
 * 依赖的配置文件设置
 * - reserve.duration:  JSME中预约会议的最长MS, 默认10
 * - DeliveryDomain:    JSME/JSMS用于确定推流服务所在domain
 * - Bandwidth:         JSMS中带宽[10,1000]Mpbs
 * - VideoBps:          JSMS中单路视频带宽[800,1600]kpbs
 * - AudioBps:          JSMS中单路视频带宽[10,50]kpbs
 * - DefaultCdnAddr:    JSDE中推流地址,默认rtmp://cdn.live.360.cn/live_juphoon_1/
 * - DefaultMergeMode:  JSDE中视频混屏模式,默认1
 * - DefaultPictureSize:JSDE中视频尺寸,默认0x100
 * - MysqlUri:          JSMDb中数据库地址
 * - global.jmds.pushaddr      JMDS中登入环境,默认"sudp:ae.justalkcloud.com:9851"
 * - global.jmds.capacityCount JMDS中允许最大的ConfDelivery进程个数
 */
interface JSME
{
    /**
     * 获取区域配置信息
     *
     * @param[out] defaultConfig        默认区域配置，当不指定区域时使用的配置
     * @param[out] otherConfigs         其他区域配置
     */
    bool loadRegionConfigs(out RegionConfig defaultConfig, out RegionConfigMap otherConfigs) async onexception(false);

    /**
     * 创建会议
     *
     * @param[in] wantCaps      目前无用 @TODO
     * @param[in] params        额外参数集
     * @param[out] confNumber   返回会场号(类似电话号)
     * @param[out] roomId       返回会场引擎(jsmd)的ID,
     * @param[out] jsmiId       返回会场实例 JSMI的ID
	 * @param[out] outParams    返回直连的ep
     * @return 失败返回 false
     * @retval @TODO 错误原因
     *
     *  params 的自动值,外部不允许设置
     *    - "domainId": 调用者"domain"或"Proxy.DomainId"
     *    - "rooId": 会场引擎(jsmd)的ID, 等价输出参数 roomId
     *    - "confNum": 会场号(类似电话号), 等价输出参数 confNumber
     *    - "agent": JSMI的ID, jsmiId
     *    - "order":  "1"=预约会议, 反之不是
     *    - "duration": 预约会议时长,由JSME.reserve()中设置
     *    - "start": 预约会议开始时间, 由JSME.reserve()中设置
     *    - "createdomainId": 创建者域,由JSMII.qurey_begin()中设置
     *    - "cdndomainId": CDN域,由JSMII.qurey_begin()中设置
     *    - "testdomainId":测试域,由JSMII.qurey_begin()中设置
     *  params 的必有值
     *    - @TODO
     *  params 的可选值
     *    - "plugin.mdc": @TODO 是否提供媒体数据收集服务(Media data collector),默认:无
     *      #- "plugin.mdc.role": MDC 的角色, 默认: 0x10
     *      #- "plugin.mdc.cdnaddr": CDN推流地址,默认:无
     *      #- "plugin.mdc.file": 录制文件名,默认:无
     *      #- "plugin.mdc.viewmode": 视频合并方式, 0=共享模式，1=讲台模式，2=发言模式, 默认: "1"
     *      #- "plugin.mdc.picsize":  视频尺寸, 0x100=低(360P), 0x200=中(480P), 0x300=中(720P), 0x400=中(1080P) 默认: "0x100"
     *
     *    - "plugin.ivr": @TODO 是否提供交互式语音应答服务(Interactive voice response), 默认:无
     *      #- "plugin.ivr.role": IVR 的角色, 默认: 0x10
     *
     *    - "capacity": 最大人数,默认:"4"
     *    - "video": 是否有视频, "1"=视频会议, 默认:"1"
     *    - "title": 会议标题, 默认:"JuphoonMeeting"
     *    - "userdata": 自定义数据(对应 MtcConfDataKey), 默认:无
     *    - "password": 自定义密码, 默认:"123456"
     *    - "viewmode": 会议模式,"1"=自由模式,  "2"=一致模式, 默认:"1"
     *    - "vidquality": 会议质量, "0"=低(360P), "1"=中(720P), "2"=高(@TODO), 默认:"0"
     *    - "vidsquare": 是否方形视频,"1"=方形视频, 默认:"0"
     *
     * @deprecated 推流服务参数
     *    - "accountName" 推流服务账户
     *    - "ivrRole"  IVR角色
     *    - "recRole"  录制器角色
     *    - "cdnaddr"  CDN推流地址
     *    - "cdnviewmode" 视频合并方式
     *    - "cdnpicsize"  视频尺寸
     */
    bool create(Common.StrSet wantCaps,Common.StrStrMap params,out long confNumber,out string roomId, out string jsmiId, out Common.StrStrMap outParams) async onexception(false);

    /**
     * 预约会议
     *
     * @param[in] wantCaps      目前无用 @TODO
     * @param[in] params        额外参数集
     * @param[in] start         会议开始时间
     * @param[in] duration      会议时长
     * @param[out] confNumber   返回会场号(类似电话号)
     * @return 失败返回 false
     * @retval @TODO 错误原因
     *
     * @remarks
     *  params 的有效值 @see JSME.create(...,param)
     */
    bool reserve(Common.StrSet wantCaps, Common.StrStrMap params,long start, long duration, out long confNumber) async onexception(false);

    /**
     * 取消已预约的会议
     *
     * @param[in] confNumber    会场号(类似电话号)
     * @param[in] params        额外参数集
     * @return 失败返回 false
     * @retval @TODO
     *
     * @remarks
     *  params 的必有值
     *    - @TODO
     *  params 的可选值
     *    - @TODO
     */
    bool cancelReserve(long confNumber, Common.StrStrMap params) async onexception(false);

	/**
     * 取消已预约的会议
     *
     * @param[in] privateRoomNumber    会场号(用户指定,由各个与会人员自己事先约定好，不是有服务端生成)
     * @param[in] params        额外参数集
     * @return 失败返回 false
     * @retval @TODO
     *
     * @remarks
     *  params 的必有值
     *    - @TODO
     *  params 的可选值
     *    - @TODO
     */
    bool cancelReserve2(string privateRoomNumber, Common.StrStrMap params) async onexception(false);

    /** 查询会议信息
     *
     * @param[in] confNumber    会场号(类似电话号)
     * @param[out] roomId       返回会场引擎(jsmd)的ID,
     * @param[out] jsmiId       返回会场实例 JSMI的ID
     * @param[out] params       返回的配置信息
     * @param[out] memberList   当前参会的成员列表
     * @return 失败返回 false
     * @retval @TODO 错误原因
     *
     * @remarks
     *  params 的有效值 @see JSME.create(...,param)
     *
     */
    bool query(long confNumber, out string roomId, out string jsmiId, out Common.StrStrMap params, out Common.StrVec memberList) async onexception(false);

    /** 查询会议信息
     *
     * @param[in] privateRoomNumber    会场号(用户指定,由各个与会人员自己事先约定好，不是有服务端生成)
     * @param[in] params        额外参数集
     * @param[out] roomId       返回会场引擎(jsmd)的ID,
     * @param[out] jsmiId       返回会场实例 JSMI的ID
     * @param[out] outParams    返回的配置信息
     * @param[out] memberList   当前参会的成员列表
     * @return 失败返回 false
     * @retval @TODO 错误原因
     *
     * @remarks
     *  params 的有效值 @see JSME.create(...,param)
     *
     */
    bool query2(string privateRoomNumber, Common.StrStrMap params, out string roomId, out string jsmiId, out Common.StrStrMap outParams, out Common.StrVec memberList) async onexception(false);

    /**
     * 结束会议实例
     *
     * @param[in] roomId        会场实例 JSMI的ID
     * @return 失败返回 false
     * @retval @TODO 错误原因
     */
    bool destroy(string roomId) async onexception(false);

    /** 内部用于JSMS更新的函数。 外部不要使用 */
    bool update(string serviceType, string serviceId,int expires,Common.StrStrMap confs, Common.StrStrMap infos,out Common.StrStrMap params) onexception(false);

    /** @TODO */
    bool statusNotify(string  roomId, string confNumber, string userData,  string status, Common.StrStrMap params) async onexception(false);

    /**
     * 根据指定会场号创建并加入会议(音视频能力通道接口)
     *
     * @param[in] privateRoomNumber    会场号(用户指定,由各个与会人员自己事先约定好，不是有服务端生成)
     * @param[in] params        额外参数集
     * @param[out] ep        连接点
     * @param[out] cp        能力集
     * @param[out] outparams        返回的配置信息
     * @return 失败返回 false
     * @retval @TODO
     *
     * @remarks
     *  params 的自动值,外部不允许设置
     *    - "call_domainId": 调用者"domain"或"Proxy.DomainId"
     *    - "accountId": 调用者的账号
     *    - "sessionId": 本次调用会话号
     *    - "host": 调用者本地地址
     *    - "userdefinedId": 调用者指定的会议号
     *    - "domainId": 调用者"domain"或"Proxy.DomainId"
     *    - "rooId": 会场引擎(jsmd)的ID, 等价输出参数 roomId
     *    - "confNum": 会场号(类似电话号), 等价输出参数 confNumber
     *    - "agent": JSMI的ID, jsmiId
     *    - "order":  "1"=预约会议, 反之不是
     *  params 的必有值
     *    - @TODO
     *  params 的可选值
     *    @see JSME.create(...,param)
     */
    bool add(string privateRoomNumber,Common.StrStrMap params,out string ep,out string cp, out Common.StrStrMap outParams) async onexception(false);

	    /**
     * 根据指定会场号创建并加入会议(音视频能力通道接口)
     *
     * @param[in] privateRoomNumber    会场号(用户指定,由各个与会人员自己事先约定好，不是有服务端生成)
     * @param[in] params        额外参数集
     * @param[out] ep        连接点
     * @param[out] outparams        返回的配置信息
     * @return 失败返回 false
     * @retval @TODO
     *
     * @remarks
     *  params 的自动值,外部不允许设置
     *    - "call_domainId": 调用者"domain"或"Proxy.DomainId"
     *    - "accountId": 调用者的账号
     *    - "sessionId": 本次调用会话号
     *    - "host": 调用者本地地址
     *    - "userdefinedId": 调用者指定的会议号
     *    - "domainId": 调用者"domain"或"Proxy.DomainId"
     *    - "rooId": 会场引擎(jsmd)的ID, 等价输出参数 roomId
     *    - "confNum": 会场号(类似电话号), 等价输出参数 confNumber
     *    - "agent": JSMI的ID, jsmiId
     *    - "order":  "1"=预约会议, 反之不是
     *  params 的必有值
     *    - @TODO
     *  params 的可选值
     *    @see JSME.create(...,param)
     */
    bool add2(string privateRoomNumber,Common.StrStrMap params,out string ep,out Common.StrStrMap outParams) async onexception(false);

    /**
     * 创建额外服务实例
     *
     * @param[in] confNumber   会场号(类似电话号)
     * @param[in] wantCaps      目前无用 @TODO
     * @param[in] params        额外参数集
     * @param[out] extraAccount 返回唯一的额外服务账户
     * @return 失败返回 false
     * @retval @TODO 错误原因
     *
     *  params 的自动值,外部不允许设置
     *    - @TODO
     *  params 的必有值
     *    - @TODO
     *  params 的可选值
     *    - @TODO
     */
    bool delivery_create(string confNumber, Common.StrSet wantCaps, Common.StrStrMap params, out string serviceId, out string extraAccount) async onexception(false);

    /** 非互动终端加入会议
     *
     * @param[in] confNumber   会场号(类似电话号)
     * @param[in] params        额外参数集
     * @param[out] ep        连接点
     * @param[out] cp        能力集
     * @param[out] outparams        返回的配置信息
     * @return 失败返回 false
     * @retval @TODO
     * @remarks
     *  params 的有效值 @see JSME.create(...,param)
     *
     */
    bool addRelay(long confNumber, Common.StrStrMap params,
                  out string ep,out string cp,
                  out Common.StrStrMap outParams) async onexception(false);

    /** 非互动终端加入会议
     *
     * @param[in] confId   Num:<confNumber> or Room:<privateRoomNumber>
     * @param[in] params        额外参数集
     * @param[out] ep        连接点
     * @param[out] cp        能力集
     * @param[out] outparams        返回的配置信息
     * @return 失败返回 false
     * @retval @TODO
     * @remarks
     *  params 的有效值 @see JSME.create(...,param)
     *
     */
    bool addRelay2(string confId, Common.StrStrMap params,
                  out string ep,out string cp,
                  out Common.StrStrMap outParams) async onexception(false);

     /** 非互动终端加入会议
     *
     * @param[in] confId   Num:<confNumber> or Room:<privateRoomNumber>
     * @param[in] params        额外参数集
     * @param[out] ep        连接点
     * @param[out] outparams        返回的配置信息
     * @return 失败返回 false
     * @retval @TODO
     * @remarks
     *  params 的有效值 @see JSME.create(...,param)
     *
     */
    bool addNewRelay(string confId, Common.StrStrMap params,
                  out string ep,out Common.StrStrMap outParams) async onexception(false);

    /** 服务转发查询会议信息
     *
     * @param[in] confNumber    会场号(类似电话号)
     * @param[in] selectedJsme  已选择查询过的JSME
     * @param[out] roomId       返回会场引擎(jsmd)的ID,
     * @param[out] jsmiId       返回会场实例 JSMI的ID
     * @param[out] params       返回的配置信息
     * @param[out] memberList   当前参会的成员列表
     * @return 失败返回 false
     * @retval @TODO 错误原因
     *
     * @remarks
     *  params 的有效值 @see JSME.create(...,param)
     *
     */
    bool forwardQuery(long confNumber, Common.StrVec selectedJsme, out string roomId, out string jsmiId, out Common.StrStrMap params, out Common.StrVec memberList) async onexception(false);

    /** 查询会议信息
     *
     * @param[in] privateRoomNumber    会场号(用户指定,由各个与会人员自己事先约定好，不是有服务端生成)
     * @param[in] selectedJsme  已选择查询过的JSME
     * @param[in] params        额外参数集
     * @param[out] roomId       返回会场引擎(jsmd)的ID,
     * @param[out] jsmiId       返回会场实例 JSMI的ID
     * @param[out] outParams    返回的配置信息
     * @param[out] memberList   当前参会的成员列表
     * @return 失败返回 false
     * @retval @TODO 错误原因
     *
     * @remarks
     *  params 的有效值 @see JSME.create(...,param)
     *
     */
    bool forwardQuery2(string privateRoomNumber, Common.StrStrMap params, Common.StrVec selectedJsme, out string roomId, out string jsmiId, out Common.StrStrMap outParams, out Common.StrVec memberList) async onexception(false);

	 /** 拉取会议列表
     *
     * @param[in] domainId  需要拉取列表对应的域id，传-1为查询所有的域
     * @param[in] appId     传-1为查询所有的appId
	   * @param[in] startIndex 待查询的记录开始序号
     * @param[in] count     返回记录的条数，当实际返回小于该值是，表示已经拉取完成
	   * @param[in] params    额外参数集，支持后续扩展使用
	   * @param[out] roomListJson 查询记录，json字符串形式，格式如下:
  		[
  			{
          "domainId": "",
  				"appId":
  				"roomId" :
  				"callId" : "33123141313";
  				"startTime": "";
  			},
  		]
	   * @param[out] outParams 额外参数集，支持后续扩展使用
     * @return 失败返回 false
     * @retval @TODO 错误原因
     *
     *
     */
	bool getRoomList(string domainId, string appId, int page, int size, Common.StrStrMap params, out string roomListJson, out Common.StrStrMap outParams) async onexception(false);

	 /** 查询房间信息
     *
     * @param[in] domainId    待查询的房间所在的domainId
     * @param[in] appId       待查询的房间所在的appId
     * @param[in] roomId      房间号
	 * @param[in] excludeJsmeList 排除的JSME oid，JSME内部转发是需要，外部服务调用时传空即可
     * @param[out] memberList   当前参会的成员列表
     * @param[out] properties   房间属性

     * @return 失败返回 false
     * @retval @TODO 错误原因
     *
     * @remarks
     *  params 的有效值 @see JSME.create(...,param)
     *
     */
  bool queryRoom(string domainId, string appId, string roomId, Common.StrVec excludeJsmeList, out Common.StrVec memberList, out Common.StrStrMap properties) async onexception(false);

	/** 从房间踢人
     *
     * @param[in] domainId    待查询的房间所在的domainId
     * @param[in] appId       待查询的房间所在的appId
     * @param[in] roomId      房间号
     * @param[in] memberList  待剔除的成员账号，支持同时剔除多人
	 * @param[in] excludeJsmeList 排除的JSME oid，JSME内部转发是需要，外部服务调用时传空即可
     * @return 失败返回 false
     * @retval @TODO 错误原因
     *
     * @remarks
     *  params 的有效值 @see JSME.create(...,param)
     *
     */
  bool kickoffUserFromRoom(string domainId, string appId, string roomId, Common.StrVec memberList, Common.StrVec excludeJsmeList) async onexception(false);

	/**
     * 销毁房间
     *
     * @param[in] domainId    待销毁的房间所在的domainId
     * @param[in] appId       待销毁的房间所在的appId
	 * @param[in] roomId      待销毁的房间号
	 * @param[in] params      额外参数集，支持后续扩展使用
	 * @param[in] excludeJsmeList 排除的JSME oid，JSME内部转发是需要，外部服务调用时传空即可
     * @return 失败返回 false
     * @retval @TODO
     *
     * @remarks
     *  params 的必有值
     *    - @TODO
     *  params 的可选值
     *    - @TODO
     */
  bool destroyRoom(string domainId, string appId, string roomId, Common.StrStrMap params, Common.StrVec excludeJsmeList) async onexception(false);

	/**
     * 创建Agent，支持WebRTC Agent，录制Agent， Sip Agent等
     *
     * @param[in] domainId    待销毁的房间所在的domainId
     * @param[in] appId       待销毁的房间所在的appId
	   * @param[in] roomId      待销毁的房间号
	   * @param[in] type        Agentl类型，有以下枚举
	   * 					              JMDS.WebRTC -- WebRTC Agent
	   *                        JMDS.WeChat -- 小程序Agent
	   *                        JMDS.PSTN   -- Sip Agent
	   *                        JMDS        -- 录制Agent
     * @return 失败返回 false
     * @retval @TODO
     *
     * @remarks
     *  params 的必有值
     *    - @TODO
     *  params 的可选值
     *    - @TODO
     */
  bool createAgent(string domainId, string appId, string roomId, string type, Common.StrStrMap params, out Common.StrStrMap outParams) async onexception(false);

  	/**
     * 根据指定会场号创建并加入会议(音视频能力通道接口) 提供终端使用，带有token校验逻辑，服务端集成使用add2

     *
     * @param[in] privateRoomNumber    会场号(用户指定,由各个与会人员自己事先约定好，不是有服务端生成)
     * @param[in] params        额外参数集
     * @param[out] ep        连接点
     * @param[out] outparams        返回的配置信息
     * @return 失败返回 false
     * @retval @TODO
     *
     * @remarks
     *  params 的自动值,外部不允许设置
     *    - "call_domainId": 调用者"domain"或"Proxy.DomainId"
     *    - "accountId": 调用者的账号
     *    - "sessionId": 本次调用会话号
     *    - "host": 调用者本地地址
     *    - "userdefinedId": 调用者指定的会议号
     *    - "domainId": 调用者"domain"或"Proxy.DomainId"
     *    - "rooId": 会场引擎(jsmd)的ID, 等价输出参数 roomId
     *    - "confNum": 会场号(类似电话号), 等价输出参数 confNumber
     *    - "agent": JSMI的ID, jsmiId
     *    - "order":  "1"=预约会议, 反之不是
     *  params 的必有值
     *    - @TODO
     *  params 的可选值
     *    @see JSME.create(...,param)
     */
    bool add3(string privateRoomNumber,Common.StrStrMap params,out string ep,out Common.StrStrMap outParams) async onexception(false);

    /**
     * 创建测试Agent，ConfClient，CcClient等
     *
     * @param[in] type      Agent类型，有以下枚举
     *                      JMDS.RoomClient -- RoomClient Agent
     *                      JMDS.CcClient -- CcClient Agent 暂不支持
     *
     * @param[in] params    配置参数，主要用于TestAgent启动时的媒体参数，key-value例举如下：
     *  key                 数据类型           默认值              是否必须             含义                            备注
     *  role                int                 7                  ×                入会角色，按位修改
     *  capacity            int                 4                  ×                房间最大人数
     *  temporalLayer       int                 2                  ×                房间时间层参数
     *  video               int                 1                  ×                是否视频
     *  appKey              string    "6c06d1b0d9015e47ec144097"   √                终端使用的appkey
     *  ratio               float               1.50               ×                房间全局宽高比
     *  inviteSipUser       int                 0                  ×                是否需要邀请sip用户
     *  sipCoreNet          string       "ChinaMobileVolte"        ×                sip使用的核心网参数               暂不生效
     *  sbcIp               string              ""                 ×                sip Ip地址                      暂不生效
     *  sbcPort             string              ""                 ×                sip 端口                        暂不生效
     *  sipPhoneNum         string             "14715008298"       ×                被叫的sip号码                    暂不生效
     *  password            string             "123456"            ×                房间密码
     *  camFile             string             "jsm.svc"           ×                当作摄像头的svc文件
     *  runTimeOut          int                43200               ×                加入房间停留时间
     *  recFileSplitSize    int                 0                  ×                录制文件分割大小                  0表示不分割, 负值为无效值
     *  token               string              ""                 √                加入房间token
     *  username            string             "bob"               √                成员名字
     *  isRecord            int                 0                  ×                是否开启录制
     *  videoLevel          int                 0                  ×                订阅的层级                       已废弃，现使用订阅的图像宽度和图像高度
     *  resolutionInfo      string  "1 90 50 180 130 360 360 720 900"   ×           svc参数
     *  bweInfo             string          "100 100 0 0"          ×                最大最小收发带宽
     *  audioFile           string              "female.wav"       ×                当作麦克风的音频文件
     *  roomId              string              ""                 √                房间号
     *  fps                 int                 15                 ×                帧率
     *  stateJson           int                 0                  ×                是否打开获取媒体统计，基于天塞2.0
     *  wgwId               string              ""                 √                WebRoomGateWay服务id
     *  wgwInstanceId       string              ""                 √                WebRoomGateWay服务所在实例id
     *  smoothMode          int                 0                  ×                是否使用流畅模式
     *  isDoubleRoom        int                 0                  ×                是否开启双房间模式
     *  isInvRecAgt2SecRoom int                 0                  ×                是否邀请录制代理服务加入第二个房间
     *  secondRoomId        string              ""                 ×                第二个房间的用户自定义房间号
     *  isScreenShare       int                 0                  ×                是否开启屏幕共享
     *  screenShareFile     string        "screenShare.mp4"        ×                作为屏幕共享视频源的文件
     *  remoteRecMergeMode  int                 -1                 ×                远程录制的录制布局模式
     *  remoteRecMergeModeI int                 -1                 ×                远程录制的智能布局普通视频模式
     *  remoteRecScsMode    int                 -1                 ×                远程录制的智能布局屏幕共享模式
     *  temporalmode        int                  2                 ×                房间时间模式
     *  pictureWidth        int                 -1                 ×                订阅的图像宽度
     *  pictureHeight       int                 -1                 ×                订阅的图像高度
     *  recSubPictureWidth  int                 -1                 ×                录制代理终端订阅自己(房间拨测代理终端)的图像宽度
     *  recSubPictureHeight int                 -1                 ×                录制代理终端订阅自己(房间拨测代理终端)的图像高度
	   *  mediaTransProtocol  string               ""                ×                媒体传输协议sarc、sudp默认为空，随机选择
     *  enableStsc          int                 0                  ×                是否开启统计数据上报
     *
     * @param[out] outParams 预留返回的配置信息
     *
     * @return 失败返回 false
     * @retval @TODO
     *
     * @remarks
     *  params 的必有值
     *    - @TODO
     *  params 的可选值
     *    - @TODO
     */
    bool createTestAgent(string type, Common.StrStrMap params, out Common.StrStrMap outParams) async onexception(false) cache(60);

	 /**
	 * add3的扩展接口，用于JSME服务间转发调用，外部不要调用
     * 根据指定会场号创建并加入会议(音视频能力通道接口) 提供终端使用，带有token校验逻辑，服务端集成使用add2

     *
     * @param[in] privateRoomNumber    会场号(用户指定,由各个与会人员自己事先约定好，不是有服务端生成)
     * @param[in] params        额外参数集
	 * @param[in] selectedJsme  已经经过的JSME服务，表明这些服务不用再尝试转发，当尝试到最后一个服务还是不存在房间时，就返回最初收到请求的服务上创建一个
     * @param[out] ep        连接点
     * @param[out] outparams        返回的配置信息
     * @return 失败返回 false
     * @retval @TODO
     *
     * @remarks
     *  params 的自动值,外部不允许设置
     *    - "call_domainId": 调用者"domain"或"Proxy.DomainId"
     *    - "accountId": 调用者的账号
     *    - "sessionId": 本次调用会话号
     *    - "host": 调用者本地地址
     *    - "userdefinedId": 调用者指定的会议号
     *    - "domainId": 调用者"domain"或"Proxy.DomainId"
     *    - "rooId": 会场引擎(jsmd)的ID, 等价输出参数 roomId
     *    - "confNum": 会场号(类似电话号), 等价输出参数 confNumber
     *    - "agent": JSMI的ID, jsmiId
     *    - "order":  "1"=预约会议, 反之不是
     *  params 的必有值
     *    - @TODO
     *  params 的可选值
     *    @see JSME.create(...,param)
     */
    bool forwardadd3(string privateRoomNumber,Common.StrStrMap params,Common.StrVec selectedJsme,out string ep,out Common.StrStrMap outParams) async onexception(false);

};
};
