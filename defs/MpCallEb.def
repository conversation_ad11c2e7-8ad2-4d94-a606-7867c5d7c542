#include "Types.def"

module MpCallEb
{
    struct AgentCallNumber
    {
        string account;
        string number;
    };

    struct SessionInfo
    {
        string sessionId;
        string peerAccount;
    };

    map SessionInfoMap<string, SessionInfo>;

    struct CallRecord
    {
        string callId;
        string serialNumber;
        string passthroughInfo;
        string token;
        SessionInfoMap sessions;
    };

    interface OpenApi
    {
        bool bindSession(string serialNumber, string callId) async onexception(false);
        bool notifyAnswer(string serialNumber, string calleeNumber) async onexception(false);
        bool notifyFilePlay(string serialNumber, string calleeNumber, bool start, string filename) async onexception(false);
    }

    struct OpenApiRequstHead
    {
        string IDNO;
        string ReqName;
        string Terminal;
        string InvokeId;
    };

    struct OpenApiRequstBody
    {
        string FileName;
        string ObjectId;
    };

    struct OpenApiRequest
    {
        OpenApiRequstHead head;
        OpenApiRequstBody body;
    };

    struct OpenApiResponseHead
    {
        string Result;
        string ReqName;
        string Terminal;
        string InvokeId;
    };

    struct OpenApiResponseBody
    {
        string detail;
    };

    struct OpenApiResponse
    {
        OpenApiResponseHead head;
        OpenApiResponseBody body;
    };
}

module AcdGateway
{
	interface AcdGateway
	{
        bool getWaitSize(string httpAddress, Common.StrStrMap inParams, out Common.StrStrMap outParams) async onexception(false);
        bool getQueueLength(int vcid, int skill, Common.StrStrMap inParams, out Common.StrStrMap outParams) async onexception(false);
	}
}
