#include "Types.def"

module JSM
{

/**
 * 会议服务
 *
 * 通常一台服务器上只部署单个JSMS, 但全球部署多台JSMS.
 */
interface JSMS
{
    /**
     * 创建会场实例 JSMI
     * 
     * @param[in] wantCaps      无用
     * @param[in] params        额外参数集
     * @param[out] jsmiId       返回的实例ID   
     * @return 失败返回 false
     * @retval @TODO 错误原因
     *
     * @remarks
     *  params 的有效值 @see JSME.create(...,param)
     */
    bool create(Common.StrSet wantCaps,Common.StrStrMap params,out string jsmiId,out Common.StrStrMap status, out Common.EndpointVec epVec) async onexception(false) cache(60); 	     

    /** 
     * 销毁会议
     * 
     * @param[in] params        额外参数集
     *      jsmsiId和reason
     *      
     * @return 失败返回 false
     * @retval @TODO  错误原因
     *
     * @remarks
     */
    bool destroy(Common.StrStrMap params) async onexception(false) cache(60);
}; 

/**
 * 会议实例
 *
 * 只有会议开始后，才动态创建JSMI实例.
 * 一个JSMS内管理多个JSMI.
 */
interface JSMI
{ 	
    /** @deprecated 使用 destroy(...) */
    bool cancelReserve(string roomId,Common.StrStrMap params) async onexception(false);

    /** @deprecated 使用 command({"req":"kick",...}) */
    // bool kickoff(string roomId,Common.StrStrMap params,string toId) async onexception(false);

    /**
     * 向指定人推送邀请消息
     * @deprecated 使用notify(param["notify"]="invite").
     *
     * @param[in] roomId  无用
     * @param[in] params        额外参数集
     * @param[in] toId          接收者
     * @return 失败返回 false
     *
     * @remarks
     *  消息类型是 "JSMInvite", 携带参数(CallParam)为:
     *    - "Notify.Command" = "JSMInvite"
     *    - "Notify.From" = "uid" of the caller
     *    - "Notify.ConferenceUri" =roomId 
     *
     *  params 的必有值
     *    - "notify": 必须为 "invite".
     */
    bool invite(string roomId, Common.StrStrMap params,string toId) async onexception(false);

    /**
     * 获取ep/cp（用于建立连接)和会议配置信息
     * 
     * @param[in] roomId        目前无用
     * @param[in] params        额外参数集 
     * @param[out] ep           连接点
     * @param[out] cp           能力集
     * @param[out] outParams    返回的配置信息
     * @return 失败返回 false
     * @retval @TODO 错误原因
     *
     * @remarks
     *  params 的有效值 @see JSME.create(...,param)
     */
    bool join(string roomId, Common.StrStrMap params,
              out string ep,out string cp,
              out Common.StrStrMap outParams) async onexception(false);
			  
	 /**
     * 加入会议
     * 
     * @param[in] roomId        目前无用
     * @param[in] params        额外参数集 
     * @param[out] id           会场id
     * @param[out] channel      成员channel id
     * @param[out] outParams    返回的配置信息
     * @return 失败返回 false
     * @retval @TODO 错误原因
     *
     * @remarks
     *  params 的有效值 @see JSME.create(...,param)
     */
    bool join2(string roomId, Common.StrStrMap params, out short id, out short channel, out Common.StrStrMap outParams) async onexception(false);

    /** 
     * 查询会议配置信息
     * 
     * @param[out] roomId       返回会场引擎(jsmd)的ID
     * @param[out] params       返回的配置信息
     * @return 失败返回 false
     * @retval @TODO  错误原因
     *
     * @remarks
     *  params 的有效值 @see JSME.create(...,param)
     */
    bool query(out string roomId, out Common.StrStrMap params, out Common.StrVec memberList) async onexception(false);

    /**
     * 向指定人推送消息
     * 
     * @param[in] roomId        调用者
     * @param[in] params        额外参数集
     * @param[in] toId          接收者
     * @return 失败返回 false
     * @retval @TODO 错误原因
     *
     * @remarks
     *  消息类型是 "JSMNotify", 携带参数(CallParam)为:
     *    - "Notify.Command" = "JSMInvite"
     *    - "Notify.From" = "uid" of the caller
     *    - "Notify.ConferenceUri" = jsmiId
     *
     *  params 的必有值
     *    - "notify": 取值如下
     *      #- "invite"  邀请
     *      #- "decline" 拒绝
     *      #- "cancel"  撤消
     *  params 的可选值
     *    - @TODO
     */
    bool notify(string roomId, Common.StrStrMap params, string toId) async onexception(false);  

    /**
     * 结束会议实例
     * 
     * @param[in] params        额外参数集
     *
     * @remarks
     *  params 的必有值
     *    - @TODO
     *  params 的可选值
     *    - @TODO
     */
    bool destroy(Common.StrStrMap params);

    /**
     * 直接向会场引擎(jsmd)发送请求指令.
     *
     * @param[in] fromId        调用者
     * @param[in] commandJson   请求指令,将等待jsmd返回完成调用过程
     * @param[in] params        额外参数集
     * @return 失败返回 false
     * @retval @TODO 错误原因
     *
     * @remarks
     *  commandJson 有效的请求指令格式:
     *    - 聊天消息: 文字内容通常BASE64编码
     *      {"req":"chat","text":"%s","inverse":"t","actorId":["%s",...]}
     *    - 会议属性: 
     *      {"req":"room","room":{"title":"%s","psswrd":"%s", "screen":"%s", "dat":"%s"...}}
     *    - 成员属性:
     *      {"req":"actor","attr":{"nick":"%s","role":%d,"rolemsk":%d,"state":%d,"statmsk":%d},"inverse":"%s","actorId":["%s",...]}
     *    - 踢人:
     *      {"req":"kick","inverse":"t","actorId":["%s",...]}
     *
     *  params 的必有值
     *    - @TODO
     *  params 的可选值
     *    - @TODO
     */
    bool command(string fromId, string commandJson, Common.StrStrMap params)  async onexception(false);

    /**
     * 注册事件侦听器
     *
     * @param[in] objectId      侦听器,必须实现 @see  ConferenceListener
     * @param[in] params        额外参数集
     * @return 失败返回 false
     *
     * @remarks
     *  params 的必有值
     *    - @TODO
     *  params 的可选值
     *    - @TODO
     */
    bool addListener(string objectId, Common.StrStrMap params);

    /**
     * 取消事件侦听器
     *
     * @param[in] objectId      侦听器,必须实现 @see  ConferenceListener
     * @param[in] params        额外参数集
     * @return 失败返回 false
     *
     * @remarks
     *  params 的必有值
     *    - @TODO
     *  params 的可选值
     *    - @TODO
     */
    bool removeListener(string objectId, Common.StrStrMap params);

    /** 
     * 导入候补成员列表
     * 
     * @param[in] candidatelist       候补成员列表
     * @return 失败返回 false
     * @retval @TODO  错误原因
     *
     * @remarks
     */
    bool importCandidate(Common.StrVec candidatelist) async onexception(false);

    /** 
     * 候补成员拒绝加入
     * 
     * @param[in] userId       用户id
     * @return 失败返回 false
     * @retval @TODO  错误原因
     *
     * @remarks
     */
    bool candidateReject(string userId) async onexception(false);
	
	 /** 
     * 发送信令数据
     * 
     * @param[in] clientId     用户id
	 * @param[in] data        数据
     * @return 失败返回 false
     * @retval @TODO  错误原因
     *
     * @remarks
     */
    bool sendSignal(string clientId, stream data, out Common.StrStrMap params) async onexception(false);
	
    /** 
     * 发送信令数据
     * 
     * @param[in] clientId     用户id
     * @param[in] data        数据
     * @return 失败返回 false
     * @retval @TODO  错误原因
     *
     * @remarks
     */
    bool sendSignal2(string clientId, Common.StreamVec dataList, out Common.StrStrMap params) async onexception(false);
	
	/** 
     * 获取信令数据
     * 
     * @param[in] clientId     用户id
	 * @param[in] data        数据
     * @return 失败返回 false
     * @retval @TODO  错误原因
     *
     * @remarks
     */
    bool recvSignal(string clientId, out Common.StreamVec dataList, out Common.StrStrMap params) async onexception(false);
	
	/** 
     * 获取信令数据
     * 
     * @param[in] clientId     用户id
	 * @param[in] lastRead     上一次读的id
	 * @param[in] param        其他参数
     * @return 失败返回 false
     * @retval @TODO  错误原因
     *
     * @remarks
     */
    bool recvSignal2(string clientId, int lastRead, Common.StrStrMap param, out Common.StreamVec dataList, out Common.StrStrMap params) async onexception(false) cache(60);
	
    /**
    * 重新建立连接
    * 
    * @param[in] params        额外参数集 
	* @param[out] outParams    返回的配置信息
    * @return 失败返回 false
    * @retval @TODO 错误原因
    *
    * @remarks
    */
    bool reconnect(Common.StrStrMap params, out Common.StrStrMap outParams) async onexception(false);

     /**
     * 接管已发送排队机消息 
     * @param[in] roomId            会议ID
     * @param[in] NewAcdSvrOid      要接管发送消息新OID
     * @param[in] params            备用字段
     * @return 失败返回 false
     * @retval @TODO 错误原因
     *
     */
    bool takeOverAcdSvrMessage(string roomId,string NewAcdSvrOid,Common.StrStrMap params) async onexception(false);
};

/**
 * 事件事件侦听器
 * 
 * 通过 JSMI.addEventListener 注册.
 *
 */
interface ConferenceListener
{
    /**
     * 收到会议事件.
     * @param[in] jsmiId        JSMI实例ID
     * @param[in] content       事件内容
     * @param[in] params        额外参数集
     * @retval true 继续侦听事件
     * @retval false 不再处理侦听
     *
     * @remarks
     *  content 的事件格式:
     *    - 根据事件上报每个成员的状态:
     *      #- 新成员加入:
     *         {"pub":"join","actor":{"%s":{"nick":"%s", "role":%d, "state":%d},...}, "time":%d, "roomId":"%s"}
     *      #- 成员属性改变:
     *         {"pub":"actor","actor":{"%s":{"nick":"%s","role":%d,"state":%d},...}, "time":%d, "roomId":"%s"}
     *      #- 有成员离开:
     *         {"pub":"leave","actor":{"%s":"kick","%s":"leave", "%s":"over", ...}, "time":%d, "roomId":"%s"}
     *      #- 成员的统计
     *         {"pub":"link", "link":{"%s":"<link_status>",...}, "time":%d, "roomId":"%s"}
     *    - 根据事件上报会场状态
     *      #- 创建新会场:
     *         {"pub":"new","config":{"capacity":%d,"sender":%d,"media":"%s"...}, "time":%d, "roomId":"%s"}
     *      #- 会议配置改变:
     *         {"pub":"room","room":{"title":"%s","psswrd":"%s","screen":%s...}, "time":%d, "roomId":"%s"}
     *      #- 结束会议:
     *         {"pub":"delete","net":"<net_status>", "time":%d, "roomId":"%s"}
     *      #- 会议的统计
     *         {"pub":"net","net":"<net_status>", "time":%d, "roomId":"%s"}
     *
     *  params 的必有值
     *    - @TODO
     *  params 的可选值
     *    - @TODO
     */
    bool onEvent(string jsmiId, string content, Common.StrStrMap params) async onexception(false);
}

};
