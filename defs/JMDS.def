#include "Types.def"

module JMD
{
 
/**
 * 额外服务
 *
 * 通常一台服务器上只部署单个JMDS, 但全球部署多台JMDS.
 */
interface JMDS
{
    /**
     * 创建会议的额外服务实例
     * 
     * @param[in] params        额外参数集
     * @return 失败返回 false
     * @retval @TODO 错误原因
     *
     * @remarks
     *  params 的有效值 @see JMDE.create(...,param)
     */
    bool create(Common.StrStrMap params, out Common.StrStrMap status) onexception(false);

    /** @TODO */
    bool destroy();  
	
	/**
     * 创建会议的测试实例
     * 
     * @param[in] params        额外参数集
     * @return 失败返回 false
     * @retval @TODO 错误原因
     *
     */
    bool createTestAgent(Common.StrStrMap params, out Common.StrStrMap status) onexception(false);
}; 
};
