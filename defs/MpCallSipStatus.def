#include "MpCallRtp.def"

module MpCallSip
{
    struct StatusSessionBriefStatus
    {
        string sessionId;
        string caller;
        string callee;
        string callId;
        string uui;
        string direction;
        string state;
        long startTimeMs;
        long durationMs;
        string termedReason;
    };

    vector SessionBriefStatusVec<StatusSessionBriefStatus>

    struct ResponseAllCallStatus
    {
        int count;
        SessionBriefStatusVec sessions;
    };

    struct SipEvent
    {
        string type;
        string time;
        string title;
        string detail;
        long timestamp;
    };

    vector SipEventVec<SipEvent>

    struct SipStats
    {
        string local;
        string remote;
        SipEventVec events;
    };

    map SipStatusMap<string, SipStats>

    struct ResponseStatus
    {
        string sessionId;
        string caller;
        string callee;
        string callId;
        string direction;
        string state;
        long startTimeMs;
        long durationMs;
        string termedReason;
        SipStatusMap sipStats;
        RtpStats rtpStats;
    };
}
