module Common
{
/** int 数组 */
vector IntVec<int>;
/** Common.Long 数组 */
vector LongVec<long>;
/** Common.String 数组 */
vector StrVec<string>;
/** Common.Stream 数组 */
vector StreamVec<stream>;

/** short 集合 */
set ShortSet<short>;
/** int 集合 */
set IntSet<int>;
/** Common.Long 集合 */
set LongSet<long>;
/** Common.String 集合 */
set StrSet<string>;
/** Common.Stream 集合 */
set StreamSet<stream>;

/** int -> bool 映射 */
map IntBoolMap<int,bool>;
/** int -> unsigned char 映射 */
map IntByteMap<int,byte>;
/** int -> int 映射 */
map IntIntMap<int,int>;
/** int -> Common.Long 映射 */
map IntLongMap<int,long>;
/** int -> Common.String 映射 */
map IntStrMap<int,string>;
/** int -> Common.Stream 映射 */
map IntStreamMap<int,stream>;

/** Common.Long -> bool 映射 */
map LongBoolMap<long,bool>;
/** Common.Long -> unsigned char 映射 */
map LongByteMap<long,byte>;
/** Common.Long -> int 映射 */
map LongIntMap<long,int>;
/** Common.Long -> Common.Long 映射 */
map LongLongMap<long,long>;
/** Common.Long -> Common.String 映射 */
map LongStrMap<long,string>;
/** Common.Long -> Common.Stream 映射 */
map LongStreamMap<long,stream>;

/** Common.String -> bool 映射 */
map StrBoolMap<string,bool>;
/** Common.String -> unsigned char 映射 */
map StrByteMap<string,byte>;
/** Common.String -> int 映射 */
map StrIntMap<string,int>;
/** Common.String -> Common.Long 映射 */
map StrLongMap<string,long>;
/** Common.String -> Common.String 映射 */
map StrStrMap<string,string>;
/** Common.String -> Common.Stream 映射 */
map StrStreamMap<string,stream>;

/** Common.String -> Common.StrStrMap 映射 */
map StrStrStrMap<string,StrStrMap>;

/** 服务 ID @ingroup objectid */
struct ServerId
{
    /** 服务 ID */
    string _identity;
    /** 服务类别 */
    StrVec _categorys;
};

/** Endpoint 地址 @ingroup endpoint */
struct Endpoint
{
    /** 协议 */
    string _protocol;
    /** 地址 */
    string _host;
    /** 端口 */
    int _port = 0;
    /** 启用压缩的数据大小 */
    int _zipSize = -1;
    /** 额外参数 */
    StrStrMap _params;
};
/** Endpoint 数组 @ingroup endpoint */
vector EndpointVec<Endpoint>;

/** 服务对象 ID @ingroup objectid */
struct ObjectId
{
    /** 服务 ID */
    ServerId _serverId;
    /** Adapter ID */
    string _adapterId;
    /** Cloud ID */
    string _cloudId;
    /** 服务地址信息 */
    EndpointVec _endpoints;
};
};
