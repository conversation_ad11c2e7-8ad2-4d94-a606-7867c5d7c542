#include "Types.def"

module Conf {
    interface JSMConf {

        /**
        *  预约会议
        *  @params
        *  title
        *  mediaType  会议类型 0 音频 1视频
        *  isOpenedChairmanVideo  会议开始是主持人视频是否开启 0 关闭 1开启
        *  isOpenedMemberVideo    会议开始是与会者视频是否开启 0 关闭 1开启
        *  createUserIdentity 用户标识  手机号或邮箱
        *  startTime 会议开始时间 单位分钟
        *  keepDuration 会议时长
        *  memberCount 成员个数
        *  regularType 定期会议 0 无 1 每天 2 每周 3 每两周 4 每月 5 每年
        *  password 密码 无密码则传空
        *  isJoinConferenceBeforeChairman 与会者可否在主持方进入会议前进入会议 0 否 1 可
        *  isSaveMedia 保存视频 0 否
        *  ownerConfId 是否使用个人会议id   0 不使用   使用则上传个人会议id(云企信使用手机号)
        *  memberList : [{
                 "userIdentity": ""   必传
                 "memberName" : ""   必传
                 "isChairman" : 1 主持人 必传
                 "userId" : ""  云企信id  推送使用
            }]

            @return
            outParams
                confId
                confUuid

        */
        bool subscribeConference(Common.StrStrMap params, out Common.StrStrMap outParams) async onexception(false);

        /**
        * 直接开始会议
        * isOpenedVideo 视频是否开启 0 关闭 1开启
        * ownerConfId  是否使用个人会议id   0 不使用   使用则上传个人会议id(云企信使用手机号)
        * params  @see JSME.create(...,param)
                  userId  云企信push使用

          @return
            ep
            cp
            confId
            conference
            memberList

        */
        bool startConference(string userIdentity, int isOpenedVideo, string ownerConfId, Common.StrStrMap params, out Common.StrStrMap outParams) async onexception(false) cache(60);

        /**
        * 查询个人所有会议
       totalOrAdded [
                      {
                        "confId": "1",
                        "createUserIdentity": "1",
                        "title": "1",  必填
                        "mediaType": 0  会议类型  必填
                        "confStatus": 0,   会议状态 0 预约中 1已开始 2 已取消 3 已结束
                        "startTime": "CURRENT_TIMESTAMP",  必填
                        "keepDuration": 30, 必填
                        "memberCount": 10, 必填
                        "regularType": 0, 定期会议 0 无 1 每天 2 每周 3 每两周 4 每月 5 每年  必填
                        "isOpenedChairmanVideo": 1,
                        "isOpenedMemberVideo": 1,
                        "password": "",  选填
                        "isJoinConferenceBeforeChairman": 0,
                        "isSaveMedia": 0, 选填
                        "createTime":  13位时间戳,
                        "modifiedTime": 13位时间戳,
                        "ownerConfId": "0",
                        "confUuid":"",
                        "memberList":[{
                                    "userIdentity": "18868955555",
                                    "memberName": "1",
                                    "isChairman": 1,
                                    "userId": "11111",
                                    "createTime": 13位时间戳
                                   }]
                      }
                      ]

            totalOrAdded：会议json
            changed： 会议json
            removed： confUuid 集合
            baseUpdateTime： -1 获取全部数据
        */
        bool queryConferences(string userIdentity,long baseUpdateTime, out bool diffFromBase,out string totalOrAdded,
                    out string changed,out string removed,out long updateTime) async onexception(false) ;


        /**
         * confStatus -1 个数查询 0 预约中 1已开始 2 已取消 3 已结束
           page 第几页  size 每页个数
         * confStatus -1
         * resultJson : {
         *      reserveConf: 0
         *      startConf: 0
         *      cancelConf: 0
         *      endConf: 0
         * }
         *
         * 其他同上
         *
         */
        bool queryConferences2(string userIdentity, int confStatus, int page, int size, out string resultJson) async onexception(false);



        /**
        *  编辑预约会议
        *  @params
        *  title
        *  mediaType  会议类型  0 音频 1 视频
        *  isOpenedChairmanVideo  会议开始是主持人视频是否开启 0 关闭 1开启
        *  isOpenedMemberVideo    会议开始是与会者视频是否开启 0 关闭 1开启
        *  createUserIdentity 创建者用于id  传手机号
        *  startTime 会议开始时间 单位分钟
        *  keepDuration 会议时长
        *  memberCount 成员个数
        *  regularType 定期会议 0 无 1 每天 2 每周 3 每两周 4 每月 5 每年
        *  password 密码 无密码则传空
        *  isJoinConferenceBeforeChairman 与会者可否在主持方进入会议前进入会议 0 否 1 可
        *  isSaveMedia 保存视频 0 否
        *  ownerConfId 是否使用个人会议id   0 不使用   使用则上传个人会议id(云企信使用手机号)
        *  memberList : [{
                "userIdentity": ""   必传
                "memberName" : ""   必传
                "isChairman" : 1 主持人 必传
                "userId" : ""  云企信用户id

           }]
        */
        bool editConference(string confUuid, Common.StrStrMap params, out Common.StrStrMap outParams) async onexception(false);


        /**
        * 加入会议
        * params  @see JSME.create(...,param)
                       userId 云企信push使用
                       confUuid 区分相同confId 开始预约会议时需要
          @return
            ep
            cp
            confId
            conference
            memberList

        */
        bool startSubOrJoin(string confId, string userIdentity, Common.StrStrMap params, out Common.StrStrMap outParams) async onexception(false) cache(60);

        /**
        * 取消预约会议
           params userId 云企信push使用
        */
        bool cancelSubscribedConference(string confUuid, string userIdentity, Common.StrStrMap params, out Common.StrStrMap outParams) async onexception(false);

        /**
        * 分享会议
            memberJson：[
                            {
                             "userIdentity":"",  必填
                             "memberExpand":"" 扩展字段
                            }
                        ]
        */
        bool shareConference(string confId, string memberJson, string userIdentity, Common.StrStrMap params, out Common.StrStrMap outParams) async onexception(false);

        /**
        * 删除与会者
        */
        bool deleteMember(string confId, string memberUserIdentity, string userIdentity, Common.StrStrMap params, out Common.StrStrMap outParams) async onexception(false);

        /**
        * 用户静音 onOff 0 关  1开
        */
        bool mediaController(int onOff, int mediaType, string confId, string muteUserIdentity, string userIdentity, Common.StrStrMap params, out Common.StrStrMap outParams) async onexception(false);

        /**
        * 延迟会议时长
        */
        bool delayConference(string confId, int minute, string userIdentity, Common.StrStrMap params, out Common.StrStrMap outParams) async onexception(false);

        /**
        * 关闭会议
        */
        bool closeConference(string confId, string userIdentity, Common.StrStrMap params, out Common.StrStrMap outParams) async onexception(false) cache(60);

        /**
        * 离开会议
        */
        bool leaveConference(string roomId, string userIdentity, string reason, Common.StrStrMap params, out Common.StrStrMap outParams) async onexception(false) cache(60);

        /**
        * 会议锁定 lockType 0 解锁  1 锁定
        */
        bool lockConference(int lockType, string confId, string userIdentity, Common.StrStrMap params, out Common.StrStrMap outParams) async onexception(false);

        /**
        *ack响应
        */
        bool ack(string ackId, string userIdentity) async onexception(false);


        bool sendMemberMessage(string confId, string userIdentity, Common.StrStrMap params, stream message) async onexception(false);

        // 主持人切换
        bool changChairman(string confId, string userIdentity, string newChairmanId, Common.StrStrMap params, out Common.StrStrMap outParams) async onexception(false);

        // 单个会议查询 type 0 预约会议  1 进行中会议
        bool queryConferenceInfo(string confUuid, int type, out string resultJson) async onexception(false);

        // 修改与会者昵称
        bool editMemberName(string confId, string memberName, string editUserIdentity, string userIdentity,out Common.StrStrMap outParams) async onexception(false);

        // 会议销毁时JSM通知
        bool destroyConference(string roomId, string reason) async onexception(false) cache(60);

        // 主持人设置会议属性和参与者会议中的属性 confOrUser 0 会议属性 1 成员属性      type record 录制 screenShare 屏幕共享  focus 焦点  handsUp 举手 muteJoin 禁音加入
        bool manageConfUsersProperty(string confId, int confOrUser, string userIdentity, string memberUserIdentity, string type, int onOff, Common.StrStrMap params, out Common.StrStrMap outParams) async onexception(false);

        /**
        * userIdentity 修改人
        * memberUserIdentity 被修改人 支持单个数组json
        * commandType  0 role 1 state
        * msk  为 role state 对应的 rolemsk statmsk
        * mskValue 对应msk的状态
        */
        bool roleStateCommand(string confId, string userIdentity, string memberUserIdentity, int commandType, int msk, int mskValue, out Common.StrStrMap outParams) async onexception(false);

        // jsm会议中成员离开加入 回调
        bool jsmCallback(string roomId, string eventJson, out Common.StrStrMap outParams) async onexception(false);

        // 根据groupId查询会议
        bool queryConferenceById(string id, int type, out string resultJson) async onexception(false);

        bool rejectConferenceInvite(string confId, string userIdentity, Common.StrStrMap params) async onexception(false);

        bool setRelayNodeOid(string confId, Common.StrStrMap params) async onexception(false);

        bool relayNotify(string confId, Common.StrStrMap params) async onexception(false);

        bool jsmsLoadNotify(string roomId, Common.StrStrMap params) async onexception(false);

    }
};