#include "Types.def"

module ServiceRunner
{
    /** 属性类型，使用字符串进行注册和匹配 */
    enum AttrType
    {
        AttrHostId,
        AttrDataCenter,
        AttrPublicAccessible
    };

    /** Runner 注册信息 */
    struct RunnerInfo
    {
        /** Runner 名称 */
        string name;
        /** Runner 服务 ObjectId */
        string objectId;
        /** Runner 管理服务 ObjectId */
        string adminOid;
        /** Runner 属性，在分配资源的时候匹配亲和性参数。参考 AttrType */
        Common.StrStrMap attrs;
    };

    /** 匹配规则集合 */
    map RuleMap<string, Common.StrSet>;

    /** 匹配规则参数
     *
     * Manager 根据参数和 Runner 属性进行匹配，根据匹配程度选择 Runner
     * 1. 仅选择满足所有 includings 条件的 Runner
     * 2. 排除所有满足任意一个 excludings 条件的 Runner
     * 3. affinities 和 antiAffinities 会转换成 score，各占 50% 比例
     * 4. 满足一条 affinities 规则，增加 score = 50 / 规则数量
     * 5. 满足一条 antiAffinities 规则，减少 score = 50 / 规则数量
     *
     * Manager 匹配逻辑
     * 1. 计算所有本地管理 Runner score
     * 2. 如果有 score = 100 的 Runner，则选择其中时间最早的 Runner
     * 3. 否则，向所有 Manager 发起转移 socre 大于当前值的 Runner
     * 4. 如果收到 score = 100 的 Runner，则返回成功
     * 5. 否则，选择 score 最高的 Runner
     * 6. Runner 转移之后，由当前 Manager 管理
     */
    struct RuleParams
    {
        /** 必须满足的条件 */
        RuleMap includings;
        /** 排除在外的条件 */
        RuleMap excludings;
        /** 亲和性条件 */
        RuleMap affinities;
        /** 反亲和性的条件 */
        RuleMap antiAffinities;
    };

    /** Daemon 交互接口
     */
    interface Daemon
    {
        /** 保活接口
         *
         * 1. 由 Runner 发起保活，间隔3秒
         * 2. Runner 和 Daemon 检查超时时间为 12秒
         *
         * param token
         * param state Runner 状态
         */
        bool keepAlive(string token, string state) onexception(false);
    };

    /** Manager 交互接口
     */
    interface Manager
    {
        /** 注册资源接口
         *
         * 1. 仅在 Runner 启动之后调用
         * 2. 一次调用成功之后，则终止后续调用
         * 3. 调用失败，则循环发起调用，间隔 3秒
         *
         * param info Runner 注册信息
         *
         */
        bool registerRunner(RunnerInfo info) onexception(false);

        /** 注销资源接口
         *
         * 1. Runner 注册成功之后，并且即将结束运行之前发起调用
         * 2. 一般此时 Runner 并未被分配处理业务
         * 3. Manager 将 Runner 移除有效集合，停止保活
         */
        bool unregisterRunner(string name) onexception(false);

        /** Manager 请求转移 Runner 资源
         *
         * param rules 资源匹配规则
         * param int 匹配分数
         * param info Runner 注册信息
         *
         * return -1 对应没有符合条件的 Runner
         * return >=0 有符合条件的 Runner
         */
        int transferRunner(int miniScore, RuleParams rules, out RunnerInfo info) onexception(-1);
    };

    /** Runner 交互接口
     */
    interface Runner
    {
        /** 检查 Runner 状态
         *
         * 1. 由 Manager 发起调用，间隔 15秒
         * 2. Runner 和 Mananger 检查保活超时时间 30秒
         * 3. Runner 转移之后，由新 Manager 负责发起调用
         *
         * param manaerOid Manager ObjectID
         * param action 检查动作
         * param status 返回状态
         * param freePercent 返回空闲百分比
         *
         */
        bool check(string managerOid, string action, out string status, out int freePercent) onexception(false);
    }
};
