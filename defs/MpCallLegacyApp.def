#include "Types.def"
#include "MpCall.def"

module MpCall
{
    /**
     * @brief LegacyAppEntry 接口
     *
     * 由 MpCallLegacyAppGateway 调用
     *
     * LegacyAppEntry/<userId>/<privateRoomNumber> 实现服务的接口
     * 例如 LegacyAppEntry/bob/myroom
     */
    interface LegacyAppEntry
    {

        /**
         * 加入
         * CallParams需携带domain,app
         *
         * @param[in]  accountId            用户id
         * @param[in]  privateRoomNumber    房间ID
         * @param[in]  params               房间参数
         * @param[out] ep                   房间Endpoint
         * @param[out] outParams            房间返回参数
         */
        bool join(string accountId, string privateRoomNumber, Common.StrStrMap params, out string ep, out Common.StrStrMap outParams) onexception(false);

        /**
         * 邀请
         *
         * @param[in]   caller              主叫用户id
         * @param[in]   callee              被叫用户id
         * @param[in]   uui                 User-to-user 信息
         * @param[in]   routeId             线路ID
         */
        bool invite(string caller, string callee, string uui, string routeId) onexception(false);

        /**
         * 终止会议
         */
        bool cease() onexception(false);
    };

    /**
     * AppGateway 接口,由 MpCall 服务调用
     * LegacyAppCall/<userId>/<sessId>
     **/
    interface LegacyAppGateway
    {
       /**
         * 邀请通知
         *
         * @param[in]  sessId               唯一会话id
         * @param[in]  caller               主叫号
         * @param[in]  callee               被叫号
         * @param[in]  roomConfig           房间信息(可能为空)
         * @param[in]  inviteConfig         邀请配置
         */
        bool invitation(string sessId, string caller, string callee, RoomConfig roomConfig, InviteConfig inviteConfig) onexception(false);

        /**
         * 取消邀请通知
         *
         * @param[in]  userId               发起取消者id
         * @param[in]  reason               原因
         *
         */
        bool canceled(string userId, string reason) onexception(false);

        /**
         * 振铃通知
         *
         * @param[in]  userId               振铃者id
         *
         */
        bool alerted(string userId) onexception(false);

        /**
         * 接受邀请通知
         *
         * @param[in]  userId               被邀请者id
         * @param[in]  isVideo              是否视频
         * @param[in]  roomConfig           房间信息(可能为空)
         * @param[in]  uui                  随路数据
         *
         */
        bool accepted(string userId, bool isVideo, RoomConfig roomConfig, string uui) onexception(false);

        /**
         * 拒绝邀请通知
         *
         * @param[in]  userId               发起拒绝者id
         * @param[in]  reason               原因
         *
         */
        bool rejected(string userId, string reason) onexception(false);

        /**
         * 成员离开通知
         *
         * @param[in]  userId               离开者id
         *
         */
        bool leaved(string userId) onexception(false);

        /**
         * 结束通知
         *
         * @param[in]  reason               原因
         *
         */
        bool terminated(string reason) onexception(false);
    };
};
