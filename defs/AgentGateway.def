#include "Types.def"

module Gateway
{
struct Msg
	{
		long sequence;
		string data;
	};


	struct CoordinateIn
        {
               string tid; //任务id
               string sdp; //offerSdp
               bool isScreenShare; //是否屏幕共享
               Common.StrStrMap inExt; //扩展入参
        };

        struct CoordinateOut
        {
              string tid; //任务id
              string sdp; //answer sdp
              Common.StrStrMap outExt;//扩展出参
        };

    struct CoordinateIn2
    {
           string tid; //任务id
           string sdp; //offerSdp
           bool isScreenShare; //是否屏幕共享
           int streamId; //流id 推流时传-1则使用一路新的流
           Common.StrStrMap properties; //关于流的一些属性
    };

    struct CoordinateOut2
    {
          string tid; //任务id
          string sdp; //answer sdp
          string streamUrl; //streamUrl
          int streamId; //回传streamId
          Common.StrStrMap properties;//关于流的一些属性
    };

	vector MsgVec<Msg>;
	
	enum TestAgentType
    {
        TestAgentSipDialer,  /* SipDialer */
    }
	
/**
 * 网关服务与终端交互接口
 */
	interface AgentGateway
	{
		/**
		 * 入会成功后建立rpc直连
		 *
		 * @param[in]	instanceId		唯一实例id
		 */
		bool connect(string instanceId, out Common.StrStrMap params) async onexception(false);

		/**
         * 入会成功后建立rpc直连
         *
         * @param[in]	instanceId		唯一实例id
         */
        bool connect2(string instanceId, out Common.StrStrMap params) async onexception(false);

		/**
		 * 心跳
		 *
		 * @param[in]	instanceId		唯一实例id
		 * @param[in]   params			备用
		 */
		bool heartbeat(string instanceId, Common.StrStrMap params) async onexception(false);

		/**
         * 结束客户端、释放资源
         *
         * @param[in]	instanceId		唯一实例id
         */
        bool release(string reason, string instanceId, Common.StrStrMap params) async onexception(false);

		/**
		 * 通过网关向目标终端推送消息
		 *
		 * @param[in]	instanceId		唯一实例id
		 * @param[in]	data			消息
		 */
		bool pushData(string instanceId, Common.StrVec data) async onexception(false);


		/**
		 * 从网关拉取指定终端的消息
         * 从网关获取前端数据，有数据情况会立即返回，没有数据RPC挂起，直到超时
		 * 针对终端发起新的RPC调用，服务端收到返回老接口时，返回值为false，在outParams增加字段"reason"为"expired"，
		 * 终端收到这个消息，不会发起新的RPC调用。
		 *
         *
         * @param[in]	instanceId		唯一实例id
         * @param[in]   consumeId       上一次消费的消息id
         * @param[out]	vecData			消息列表
		 * @param[out]	outParams	    其他可扩展出参
         */
        bool pullData(string instanceId, long consumeId, out MsgVec vecData, out Common.StrStrMap outParams) async onexception(false);


        /**
        * 请求推流,WebRoomGateway专用
        *
        * @param[in]	in @see CoordinateIn
        * @param[out]	out	@see CoordinateOut
        */
        bool publish(string instanceId, CoordinateIn corin, out CoordinateOut corout) async onexception(false);

        /**
        * 请求拉流,WebRoomGateway专用
        *
        * @param[in] srsIn	@see CoordinateIn
        * @param[out]	srsOut	@see CoordinateOut
        */
        bool play(string instanceId, CoordinateIn corin, out CoordinateOut corout) async onexception(false);

        /**
        * 请求推流,WebRoomGateway专用,多流场景
        * 在一个特定用户下使用streamId将不同的流隔离
        * 可以指定一个streamId(corin.streamId)推流，也可以传-1，此时由服务端默认使用一路新的流
        * 可以在服务端保存流的属性(corin.properties)
        *
        * @param[in]	in @see CoordinateIn2
        * @param[in]    userId 成员id,该参数不为空则认为是app的推流
        * @param[out]	out	@see CoordinateOut2
        */
        bool publish2(string instanceId, string userId, CoordinateIn2 corin, out CoordinateOut2 corout) async onexception(false);

        /**
        * 请求拉流,WebRoomGateway专用,多流场景
        * 需要指定streamId，不指定则尝试使用streamId=0
        * 下发保存在服务端的流属性corout.properties
        *
        * @param[in] srsIn	@see CoordinateIn2
        * @param[out]	srsOut	@see CoordinateOut2
        */
        bool play2(string instanceId, CoordinateIn2 corin, out CoordinateOut2 corout) async onexception(false);

        /**
         * 请求邀请一个房间的代理
         * @param[in] roomId 会议号
         * @param[in] userId 用户号
         * @param[in] appKey 业务号
         * @param[in] isPublish 是否推流
         * @param[in] roomParams 房间参数
         */
        bool inviteRoomDelegator(string instanceId, string roomId, string appKey, string userId, bool isPublish, string roomParams) async onexception(false);

        /**
         * 加入房间,由推流agent发起
         * @param[in] roomDelegatorId
         * @param[in] userId 成员id
         * @param[out] instanceId 唯一实例id
         */
        bool join(string roomDelegatorId, string userId, out string instanceId) async onexception(false);


        /**
         * 离开会议, 由推流agent发起
         * @param[in] roomDelegatorId
         * @param[in] userId
         * @param[in] instanceId 唯一实例id
         */
        bool leave(string roomDelegatorId, string userId, string instanceId) async onexception(false);

        /**
         * 测试 agent api地址注册
         * @param[in] type		        测试 agent 类型
         * @param[in] instanceId		唯一实例id
         * @param[in] apiUrl            测试 agent api地址
         * @param[out] objectId         objectId，用于heartbeat保活
         */
        bool registerTestAgent(string type, string instanceId, string apiUrl, out string objectId) async onexception(false);

	};
};
