#include "getCpu.h"

void getCpuIdex(unsigned int CPUInfo[4], unsigned int InfoType, unsigned int ECXValue)//cppcheck-suppress[constParameter]
{
#if defined(__GNUC__)    // GCC
    #if defined(__x86_64__)
        __cpuid_count(InfoType, ECXValue, CPUInfo[0],CPUInfo[1],CPUInfo[2],CPUInfo[3]);
    #elif defined(__arm__)
        // not supported arm
    #endif
#elif defined(_MSC_VER)    // MSVC
    #if defined(_WIN64) || _MSC_VER >= 1600    // Inline assembly is not supported under 64 bit. 1600: VS2010.
        __cpuidex((int*)(void*)CPUInfo, (int)InfoType, (int)ECXValue);
    #else
        if (NULL==CPUInfo)    return;
        _asm{
            // load parameter to register.
            mov edi, CPUInfo;    // seek address of CPUInfo with EDI
            mov eax, InfoType;
            mov ecx, ECXValue;
            // CPUID
            cpuid;
            // save register to CPUInfo
            mov    [edi], eax;
            mov    [edi+4], ebx;
            mov    [edi+8], ecx;
            mov    [edi+12], edx;
        }
    #endif
#endif    // #if defined(__GNUC__)
}

void getCpuId(unsigned int CPUInfo[4], unsigned int InfoType)
{
#if defined(__GNUC__)    // GCC
    #if defined(__x86_64__)
        __cpuid(InfoType, CPUInfo[0],CPUInfo[1],CPUInfo[2],CPUInfo[3]);
    #elif defined(__arm__)
        // not supported arm
    #endif
#elif defined(_MSC_VER)    // MSVC
    #if _MSC_VER >= 1400    // It has supported __cpuid since VC2005.
        __cpuid((int*)(void*)CPUInfo, (int)InfoType);
    #else
        getCpuIdex(CPUInfo, InfoType, 0);
    #endif
#endif    // #if defined(__GNUC__)
}

int getCpuVendor(char* pcVendor)
{
    unsigned int dwBuf[4];
    if (NULL == pcVendor)    return 0;
    // Function 0: Vendor-ID and Largest Standard Function
    getCpuId(dwBuf, 0);
    // save pcVendor
    *(unsigned int *)&pcVendor[0] = dwBuf[1];    // ebx: first 4 characters.
    *(unsigned int *)&pcVendor[4] = dwBuf[3];    // edx: middle 4 characters.
    *(unsigned int *)&pcVendor[8] = dwBuf[2];    // ecx: last 4 characters.
    pcVendor[12] = '\0';
    return 12;
}

int getCpuBrand(char* pcBrand)
{
    unsigned int dwBuf[4];
    if (NULL == pcBrand)    return 0;
    // Function 0x80000000: Largest Extended Function Number
    getCpuId(dwBuf, 0x80000000U);
    if (dwBuf[0] < 0x80000004U)    return 0;
    // Function 80000002h,80000003h,80000004h: Processor Brand String
    getCpuId((unsigned int *)&pcBrand[0], 0x80000002U);    // first 16 characters.
    getCpuId((unsigned int *)&pcBrand[16], 0x80000003U);    // middle 16 characters.
    getCpuId((unsigned int *)&pcBrand[32], 0x80000004U);    // last 16 characters.
    pcBrand[48] = '\0';
    return 48;
}
