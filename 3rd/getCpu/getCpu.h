#ifndef _GET_CPU_H__
#define _GET_CPU_H__

#include <stdio.h>

// intrinsics
#if defined(__GNUC__)    // GCC
    #if defined(__x86_64__)
        #include <cpuid.h>
    #elif defined(__arm__)
        // not supported arm
    #endif
#elif defined(_MSC_VER)    // MSVC
    #if _MSC_VER >= 1400    // VC2005
#include <intrin.h>
    #endif    // #if _MSC_VER >=1400
#else
#error Only supports MSVC or GCC.
#endif    // #if defined(__GNUC__)

#ifdef	__cplusplus
extern "C" {
#endif

/**
 * @brief Get the vendor of cpu.
 *
 * @param [out] pcVendor A string buffer that receives the vendor information of cpu, at least 13 bytes.
 *
 * @retval string length(typically 12) on succeed.
 * @retval 0 on failure.
 */
extern int getCpuVendor(char* pcVendor);

/**
 * @brief Get the brand of cpu.
 *
 * @param [out] pcBrand A string buffer that receives the brand information of cpu, at least 49 bytes.
 *
 * @retval string length(typically 48) on succeed.
 * @retval 0 on failure.
 */
extern int getCpuBrand(char* pcBrand);

#ifdef	__cplusplus
}
#endif

#endif /* _GET_CPU_H__ */