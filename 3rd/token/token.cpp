#include "token.h"
#include "lemon/lemon.h"

#include <stdio.h>
#include <unistd.h>
#include <dirent.h>
#include <errno.h>
#include <string.h>
#include <sys/stat.h>
#include <iostream>
#include <stdlib.h>
#include <fstream>
#include <sstream>

using namespace std;

static const char* GetDefaultNetCardName()
{
    DIR* dir = opendir("/sys/class/net");
    if (!dir)
    {
        return NULL;
    }

    int ret = 0;
    struct stat finfo;
    struct dirent *entry;
    while (ret >= 0 && (entry = readdir(dir)))
    {
        if (entry->d_name[0] == '.') continue;

        std::string fpath("/sys/class/net");
        fpath.append("/").append(entry->d_name);
        ret = stat(fpath.c_str(), &finfo);
        if (ret < 0)
        {
            closedir(dir);
            return NULL;
        }

        if (strcmp(entry->d_name, "lo") != 0)
        {
            closedir(dir);
            return entry->d_name;
        }

    }

    closedir(dir);
    return NULL;
}

std::string g_secret = "juphoonagentsecret20220420";

std::string uint2HexString(unsigned int data)
{
    std::string result = "";
    unsigned char tmp[128] = {0,};
    snprintf((char*)tmp, 128, "%.8x", data);
    result += (char*)tmp;

    return result;
}

std::string short2HexString(unsigned short data)
{
    std::string result = "";
    unsigned char tmp[128] = {0,};
    snprintf((char*)tmp, 128, "%.2x", data);
    result += (char*)tmp;

    return result;
}

std::string generatePrivateToken(std::string &roomId, std::string &userId)
{

    std::string realRoomId = roomId;
    std::string realUserId = userId;

    if (realRoomId.size() > 128)
        realRoomId = realRoomId.substr(0, 128);

    std::size_t start = realUserId.find(":");
    std::size_t end = realUserId.find("@");

    if (start != std::string::npos
        && end != std::string::npos
        && end > start)
    {
        realUserId = realUserId.substr(start + 1, end - start - 1);
    }

    if (realUserId.size() > 128)
        realUserId = realUserId.substr(0, 128);

    std::string strPayload = "";
    strPayload += '0';
    strPayload += '0';
    strPayload += "6766ffa056c1a2ea39334095";
    strPayload += realUserId;
    strPayload += realRoomId;

    struct timeval r;
    gettimeofday (&r, NULL);
    unsigned int tts = (unsigned int) r.tv_sec;
    tts += 3600;

    strPayload += uint2HexString(tts);
    strPayload += uint2HexString(0x7F);
    strPayload += short2HexString(0);

    ZCONST ZCHAR* pcSignature = Mtc_MediahmacSha256AndBase64(g_secret.c_str(), strPayload.c_str());
    if (!pcSignature)
        return "";

    std::string token = "";
    token += '0';
    token += '0';
    token += uint2HexString(0x7F);
    token += uint2HexString(tts);
    token += pcSignature;

    return token;
}

std::string generateMacAddr()
{
    std::string macAddress = "";
    const char* defNetCard = GetDefaultNetCardName();
    if (defNetCard)
    {
        std::string file = "/sys/class/net/";
        file += defNetCard;
        file += "/address";

        std::ifstream ifs(file.c_str());
        if (ifs)
        {
            std::stringstream buffer;
            buffer << ifs.rdbuf();
            std::string mac = buffer.str();

            ifs.close();

            if (mac.size() > 0 && mac[mac.size() - 1] == '\n')
            {
                mac.erase(mac.size() - 1, 1);
            }

            macAddress = mac.c_str();
        }
     }

    return macAddress;
}
