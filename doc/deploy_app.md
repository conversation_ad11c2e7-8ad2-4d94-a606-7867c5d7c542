# App 兼容网关部署配置

## 概要说明

新部署的环境应该在 **非 global DC 下** 增加以下服务

* MpCallLegacyAppGateway

例如 在 DC Main0 的 Signal100 主机上增加服务

* jrtcMpCallLegacyAppGateway.Main0.Signal100

    * exe - jrtcMpCallLegacyAppGateway-2.2-f8aa35d-linux-x86_64.tgz

配置 Account/Signal 服务设置 #JSME 请求代理

* 要求 Account/Signal 为 4.5.9 及以上版本
* 在 Account/Signal 增加配置

        OutgoingCall.Proxy.#JSME=MpCallLegacyAppGateway
        OutgoingCall.Proxy.#SipGatewayEntry=MpCallLegacyAppGateway

## 配置说明

### LegacyAppGateway.SipCoreNetDomains

线路ID和域映射关系，针对App所属域选择线路。例如::

    # App 所属域为 100645 或者 200010 时，使用线路 TestLine 进行呼叫
    LegacyAppGateway.SipCoreNetDomains.TestLine=100645,200010

### LegacyAppGateway.SipCoreNetMap

新旧线路ID映射关系。例如::

    # App 指定 coreNetId 为 LegacyLineName1 或者 LegacyLineName2 时，使用线路 TestLine 进行呼叫
    LegacyAppGateway.SipCoreNetMap.TestLine=LegacyLineName1,LegacyLineName2
