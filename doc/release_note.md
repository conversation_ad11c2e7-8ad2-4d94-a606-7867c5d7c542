# 版本历史

## 版本 2.4

### 版本 2.4.4

* 发布者: Bob Liu
* 日期: 2025/6/13

#### [新增特性]

1. feature: 修改 SipGateway 增加 SipCall.IncomingDomainId 配置 (#180)

    https://project.feishu.cn/juphoonceshi/asdasd/detail/5338025919

    设置呼入的domainId和appId
    SipCall.IncomingDomainId=domainId
    SipCall.IncomingDomainId=domainId/appId

    设置从被叫前缀获取domainId，appId为0
    SipCall.IncomingDomainId=CalleePrefix

    获取domainId和appId之后会增加到 MpCall.call 接口的 CallParams 中。如果为空，
    则不增加 CallParams 参数，与之前逻辑保持一致

2. feature: 修改 SipGateway 增加 SipCall.IncomingCalleeType 配置 (#185)

    https://project.feishu.cn/juphoonceshi/asdasd/detail/5338025919

    呼入号码的被叫类型。可以针对 domain 设置类型，也可以设置缺省类型。被叫类型选择规则如下

    1. 如果配置了 app 对应的配置，则使用 app 对应的类型
    2. 否则，如果设置 domain 对应的配置，则使用 domain 对应的类型
    3. 否则，如果设置缺省类型，则使用缺省类型
    4. 否则，使用 App 类型

    用户类型的配置包括

    - App - 使用 App 方式对接
    - LegacyApp - 使用 LegacyApp 方式对接，针对集成 SIP1.0 接口的终端
    - Sip - 使用 SIP 方式对接
    - Vman - 使用 Vman 方式对接
    - AcdApp - 使用 AcdApp 方式对接
    - H5 - 使用 H5 方式对接
    - Wechat - 使用 Wechat 方式对接
    - Yuv - 使用 Yuv 方式对接
    - Ivvr - 使用 IVVR 方式对接

    设置缺省类型配置

        SipCall.IncomingCalleeType=App

    设置 domain 配置

        SipCall.IncomingCalleeType.Sip=100645,200000

    设置 app 配置

        SipCall.IncomingCalleeType.LegacyApp=100645/4,100000/4

3. feature: AppGateway 增加支持 SIP1.0 邀请的功能 (#181)

    https://project.feishu.cn/juphoonceshi/asdasd/detail/5338025919

    1. AppGateway 增加处理 onInvited 接口，转换成调用 MpCall invite 接口
    2. AppGateway 增加处理 MpCall accept 和 reject 接口

    增加配置 LegacyAppGateway.SipCoreNetDomains 线路ID和域映射关系，针对App所属域选择线路。例如::

        # App 所属域为 100645 或者 200010 时，使用线路 TestLine 进行呼叫
        LegacyAppGateway.SipCoreNetDomains.TestLine=100645,200010

    增加配置 LegacyAppGateway.SipCoreNetMap 新旧线路ID映射关系。例如::

        # App 指定 coreNetId 为 LegacyLineName1 或者 LegacyLineName2 时，使用线路 TestLine 进行呼叫
        LegacyAppGateway.SipCoreNetMap.TestLine=LegacyLineName1,LegacyLineName2

    测试说明：onInvited 测试用例流程
    1. 调用 JSME::add2 发起加入房间请求
    2. 通知 AppSession 成员加入房间
    3. 调用 onInvited 发起邀请 SIP 终端
    4. 调用 alerted 接口通知 AppSession 振铃
    5. 调用 accepted 接口通知 AppSession 通话接通
    6. 调用 terminated 接口通知 AppSession 通话结束

4. feature: AppGateway 增加支持邀请对接 SIP1.0 终端 App 的功能 (#182)

    https://project.feishu.cn/juphoonceshi/asdasd/detail/5338025919

    1. AppGateway 增加处理 MpCall invitation 接口，向终端发送 onlineMessage 的
    邀请入会的消息。消息发送之后 invitation 接口返回
    2. 发送 onlineMessage 成功则调用 accept 接口，否则调用 reject 接口。MpCall 会
    处理 App 入会事件

    测试说明：增加视频和音频接听测试用例
    1. 调用 invitation 接口发起邀请
    2. 通知 AppSession 成员加入房间
    3. 调用 cancelReserve2 接口结束通话

5. feature: 增加 SipDialer 工具 (#183)

    https://project.feishu.cn/juphoonceshi/asdasd/detail/5338025919

    该服务是SIP拨测服务。支持 SIP 呼入和呼出

    - SIP 呼入的时候，服务会自动接听
    - 可以通过 HTTP 接口发起 SIP 呼出，超过指定超时时间会自动挂断 SIP 通话

    |           配置项目            |                         说明                         | 必选 | 缺省值  |                    样例                    |
    | :---------------------------- | ---------------------------------------------------- | ---- | ------- | ------------------------------------------ |
    | SipCall.RemoteAddress         | SIP 服务远端地址，需要设置 SipGateway 监听地址       | 是   |         | SipCall.RemoteAddress=**************:13000 |
    | SipCall.LocalAddress          | SIP 服务监听地址，需要设置 SipGateway 的远端地址配置 | 是   |         | SipCall.LocalAddress=0.0.0.0:17000         |
    | SipDialer.Http.ListenHost     | HTTP 服务监听地址                                    |      | 0.0.0.0 | SipDialer.Http.ListenHost=0.0.0.0          |
    | SipDialer.Http.ListenPort     | HTTP 服务监听端口                                    |      | 8080    | SipDialer.Http.ListenPort=8080             |
    | SipDialer.RtpPorts.UpperBound | RTP 端口范围上限                                     |      | 22000   | SipDialer.RtpPorts.UpperBound=22000        |
    | SipDialer.RtpPorts.LowerBound | RTP 端口范围下限                                     |      | 22999   | SipDialer.RtpPorts.LowerBound=22999        |

    可以通过 http://*************:8080/ 访问拨测服务。同时提供接口包括

    - /api/v1/call - 呼出接口
    - /api/v1/call/status - 会话列表接口
    - /api/v1/call/{sessionId}/status - 会话详情接口

    相关功能增加
    - RtpFileReader - 从 RTP Dump 文件读取 RTP 数据，该文件可以通过 MmeTester 生成
    - RtpFileSender - 将 RTP Dump 读取数据按照生成文件时的频率发送
    - SimpleRtpSession - RTP 会话模块，整合端口管理、SDP协商、RTP Dump发送模块和网络收发功能，提供基本的统计信息

6. feature: 修改 SipGateway 增加状态页面 (#201)

    https://project.feishu.cn/juphoonceshi/asdasd/detail/5338025919

    当同时配置了 ``SipGateway.Http.ListenHost`` 和 ``SipGateway.Http.ListenPort`` 的时候，会启动一个 HTTP 服务，提供 SipGateway 的统计页面。包含以下内容

    - 当前会话列表
    - 会话SIP消息流程图
    - 会话RTP统计信息

7. feature: 修改 RtpRunner 增加检测并占用端口功能 (#203)

    https://project.feishu.cn/productplatform/story/detail/5979780994

    在 RtpRunner 启动的时候占用分配的端口，在开始工作之前释放端口给jssi使用。
    如果占用不成功则启动失败，由Daemon重新分配

8. feature: 修改 ServiceLocator 避免使用 Resource2 接口 (#207)

    https://project.feishu.cn/juphoonceshi/asdasd/detail/5312924045

    适配宁波银行老版本 Grid 不是 Resource2 接口的情况

9. feature: 增加SIP事件通知 (#202)

    reason: https://project.feishu.cn/juphoonceshi/asdasd/detail/5338025919

    changes:
    - 增加SIP事件类型定义，呼叫、振铃、通话、结束、网络状态变化及话单事件
    - 增加事件发送功能的模块，增加RtpRunner定时回调网络状态变化的接口，并实现
    事件上报功能
    - 增加endType结束类型的定义
    - 增加单元测试，增加SiplineMock的实现，增加MpCallRtpServerMock.h文件

10. feature: 修改 RtpRunner 增加 SDP 带宽配置，用于对接线路控制媒体码率 (#209)

    https://project.feishu.cn/juphoonceshi/asdasd/detail/5978886810

    RtpRunnerDaemon 分别增加 RtpRunner.AudioBandwidth 和 RtpRunner.VideoBandwidth
    控制音频和视频带宽协商参数。缺省为不增加带宽协商参数

11. feature: 修改 SipLine 增加支持针对多条线路设置分配比例的功能 (#211)

    https://project.feishu.cn/juphoonceshi/asdasd/detail/5976875072

    SipCall.RemoteAddress 格式修改为 `<address>:<port>,<weight>`
    其中 weight 有效值范围 1-9999，缺省值为 1

    多个地址格式为 `<address1>:<port1>,<weight1>;<address2>:<port2>,<weight2>;`

12. feature: 修改 SipLine 支持在呼叫没有响应的时候，选择新的线路重试呼叫 (#214)

    https://project.feishu.cn/juphoonceshi/asdasd/detail/5976875072

    1. 增加 SipLineCalloutSession 处理呼叫无响应重试的流程
    1.1 SipLineCalloutSession 记录已经使用的链接 connId（由远端地址和端口组成）
    1.2 SipConnServer::selectConn 接口支持过滤已经使用的链接
    2. 增加 SipLine::schd 驱动 SipLineSession 处理重试的流程
    3. 增加 SipLineSessionManager 替换 SessInfoManager 功能

13. feature: 修改 IsbcServer 支持线路地址熔断功能 (#215)

    https://project.feishu.cn/juphoonceshi/asdasd/detail/5976875072

    1. 增加 SipConnServer::countResult 接口记录链接成功失败的信息
    2. 修改 SipIsbcConnServer 增加处理链接熔断的功能
    3. 修改 SipLineCalloutSession 增加调用 connResult 接口记录成功失败状态
    4. 增加 SipCall.LineBreak.DetectTimeSeconds 配置线路熔断的检测时间长度，缺省60秒
    5. 增加 SipCall.LineBreak.FailRatio 配置线路熔断的失败比例，缺省50%
    6. 增加 SipCall.LineBreak.ContinuousFailCount 配置线路熔断的连续失败次数，缺省3
    7. 增加 SipCall.LineBreak.RecoverySeconds 配置线路熔断恢复时间，缺省60秒

    SipGateway 会根据 ``SipCall.LineBreak.DetectTimeSeconds`` 配置的时间长度，处理链接熔断。

    * ``SipCall.LineBreak.FailRatio`` 配置指定时间范围之内的呼叫失败比例，单位为百分比。配置 0 表示熔断不考虑失败比例
    * ``SipCall.LineBreak.ContinuousFailCount`` 配置指定时间范围之内，连续呼叫失败次数。配置 0 表示熔断不考虑连续失败次数

    熔断处理包括

    1. 熔断状态下，不会选择对应地址进行呼叫
    2. 根据配置 ``SipCall.LineBreak.RecoverySeconds`` 等待指定时长之后，恢复线路连接
    3. 熔断恢复之后，会重新检测线路是否可用。如果配置了 OPTIONS 保活检测，需要等待保活检测成功之后，才会恢复成可用状态。否则，会立即恢复成可用状态

14. feature: 增加会场网络状态变化通知 (#213)

    reason: https://project.feishu.cn/juphoonceshi/asdasd/detail/5338025919

    changes:
    - rtpRunner服务增加onNetworkStatusChanged接口，并增加会场断线事件处理逻辑
    - SipPhoneSession的onNetworkStatusChanged接口参数增加会场网络状态
    - 增加网络事件上报接口事件类型参数
    - 增加单元测试

15. feature: 修改 SipDailer 支持集成压测平台 (#221)

    https://project.feishu.cn/juphoonceshi/asdasd/detail/5338025919

    1. 调用 registerTestAgent 接口注册 SipDialer 接口地址
    2. 调用 heartbeat 接口进行保活
    3. 修改 SipDialer call 接口返回 Call-ID 信息
    4. 修改 SipDialer.Http.ListenPort 缺省值为随机端口，可以通过统计信息 SipDialer.WebPortal 查看

16. feature: 修改 SipDialer 增加回声模式 (#217)

    https://juphoons.feishu.cn/wiki/NGGmw7zDui8xx6kAVX9c03Nvnjg

    SipDialer 在回声模式下将 RTP 流回放到对端，用于测试线路延迟等

    1. 增加 SipDialer.EchoMode 配置设置是否开启回声模式
    2. 增加 SipDialer.EchoTimeoutSeconds 配置设置回声模式下超时时长

17. feature: 优化重传过程的Sip流程显示 (#218)

    reason: https://project.feishu.cn/juphoonceshi/asdasd/detail/5338025919

    changes:
    - 优化重传的流程图显示，增加获取同一sess的callId集合的接口
    - 优化OPTIONS和REGISTER事件请求不进入事件统计
    - 优化部分成员变量未初始化的问题

18. feature: 修改 SipPhoneSession 解决 accept 之后立刻 requestVideo 失败的问题 (#225)

    https://project.feishu.cn/juphoonceshi/asdasd/detail/6160843610

    1. 增加 waitState 功能，可以支持等待指定状态
    2. 修改 SipPhoneSession 不使用可重入的锁

19. feature: 修改 SipGateway 默认开启统计页面，缺省监听任意端口 (#226)

    https://project.feishu.cn/juphoonceshi/asdasd/detail/6160843610

    缺省不用设置 SipGateway.Http.ListenHost 和 SipGateway.Http.ListenPort 配置，使用随机端口。
    如果设置对应的配置，则使用配置的内容
    增加 SipGateway.WebPortal 统计信息显示统计页面的地址

#### [依赖版本信息]

    jc/jc-service-base      =1.3.8
    Platform/cube           =3.6.56-34faa51
    sdk/avatar              =8.0.57-fa01641
    sdk/grape               =7.1.53-8a3a5a5
    sdk/watermelon          =7.1.53-d14e3b5
    sdk/melon-4             =7.1.53-40bf426
    solution/jusmeeting     =********-febe210
    VoIMS/SDK/Lemon         =2.1.10-f747613

---

### 版本 2.4.3

* 发布者: Bob Liu
* 日期: 2025/4/29

#### [新增特性]

1. feature: 修改 RtpRunner 增加屏幕共享的编码模式 (#195)

    https://project.feishu.cn/juphoonceshi/asdasd/detail/5312924045

    1. SipGateway 增加 SipCall.ScreenSharingMode 配置，针对线路级别设置
    2. RtpRunnerDaemon 增加 RtpRunner.ScreenSharingMode 配置，针对 RtpRunner 级别设置

#### [依赖版本信息]

    jc/jc-service-base  =1.3.6
    Platform/cube       =3.6.52-da566d7
    sdk/avatar          =8.0.55-1e0fe56
    sdk/grape           =7.1.46-a193dd3
    sdk/watermelon      =7.1.46-6e3a168
    sdk/melon-4         =7.1.46-6d42ae7
    solution/jusmeeting =7.1.46-a5c8d7b
    VoIMS/SDK/Lemon     =2.1.9-f930321

---

### 版本 2.4.2

* 发布者: Bob Liu
* 日期: 2025/4/25

#### [新增特性]

1. feature: 增加 SipCall.PAIUriFormat 指定 PAI 头域类型，缺省为不增加 PAI 头域 (#193)

    https://project.feishu.cn/juphoonceshi/asdasd/detail/5312924045

    C09 默认设置 PAI 头域为 TelUri 类型，解决本地号码呼叫的问题

#### [依赖版本信息]

    jc/jc-service-base  =1.3.5
    Platform/cube       =3.6.52-da566d7
    sdk/avatar          =8.0.55-1e0fe56
    sdk/grape           =7.1.46-a193dd3
    sdk/watermelon      =7.1.46-6e3a168
    sdk/melon-4         =7.1.46-6d42ae7
    solution/jusmeeting =7.1.46-a5c8d7b
    VoIMS/SDK/Lemon     =2.1.8-16046a0

---

### 版本 2.4.1

* 发布者: Bob Liu
* 日期: 2025/4/23

#### [新增特性]

1. feature: 修改 RtpRunner 支持视频切换成屏幕共享的画面 (#191)

    https://project.feishu.cn/juphoonceshi/asdasd/detail/5312924045

    如果 RtpRunner 在视频桥接模式下，如果存在视频共享则将画面切换成视频共享画面

#### [修复]

1. fix: 修改通话前5秒出现噪音的问题 (#184) (2.3.12)

    https://project.feishu.cn/juphoonceshi/quexian/detail/5857942622

    1. 在加入会议时设置音频设备，之前在SIP通话接通之后设置，可能两个设备同时使用的问题
    2. 在加入会议时，如果是静音状态则停止发送音频到会场

#### [依赖版本信息]

    jc/jc-service-base =1.3.4
    Platform/cube       =3.6.52-da566d7
    sdk/avatar          =8.0.55-1e0fe56
    sdk/grape           =7.1.46-a193dd3
    sdk/watermelon      =7.1.46-6e3a168
    sdk/melon-4         =7.1.46-6d42ae7
    solution/jusmeeting =7.1.46-a5c8d7b
    VoIMS/SDK/Lemon     =2.1.8-16046a0

---

### 版本 2.4.0

* 发布者: Bob Liu
* 日期: 2025/4/14

#### [新增特性]

1. 增加 C09 类型的特性标签 (#177)

    https://project.feishu.cn/juphoonceshi/asdasd/detail/5312924045

    1. 将控制流程统一到 SipSessionConfigurator 中实现
    2. 将光大控制流程实现移动到 SipSessionController 中
    3. 增加C09特性标签对应宁波银行 SIP 呼入处理，被叫类型统一为 IVVR

#### [依赖版本信息]

    jc/jc-service-base =1.3.4
    Platform/cube      =3.6.52-da566d7
    sdk/avatar         =8.0.55-1e0fe56
    sdk/grape          =7.1.46-a193dd3
    sdk/watermelon     =7.1.46-6e3a168
    sdk/melon-4        =7.1.46-6d42ae7
    solution/jusmeeting=7.1.46-a5c8d7b
    VoIMS/SDK/Lemon    =2.1.7-3d3c839

---

## 版本 2.3

### 版本 2.3.12

* 发布者: Bob Liu
* 日期: 2025/4/15

#### [优化]

1. merge: 合并版本 b0203.1.5

#### [修复]

1. fix: 修改通话前5秒出现噪音的问题 (#184)

    https://project.feishu.cn/juphoonceshi/quexian/detail/5857942622

    1. 在加入会议时设置音频设备，之前在SIP通话接通之后设置，可能两个设备同时使用的问题
    2. 在加入会议时，如果是静音状态则停止发送音频到会场

2. 更新 Jssi 库修改 SIP 协议没有重发的问题

#### [依赖版本信息]

    jc/jc-service-base =1.2.16
    Platform/cube      =3.6.52-da566d7
    sdk/avatar         =8.0.55-1e0fe56
    sdk/grape          =7.1.46-a193dd3
    sdk/watermelon     =7.1.46-6e3a168
    sdk/melon-4        =7.1.46-6d42ae7
    solution/jusmeeting=7.1.46-a5c8d7b
    VoIMS/SDK/Lemon    =2.1.8-16046a0

---

### 版本 2.3.11

* 发布者: Bob Liu
* 日期: 2025/3/21

#### [新增特性]

1. feature: 对接线路要求，主叫号码增加格式配置，支持user=phone (#164)

    更新 Jssi/2.1.7 呼叫中去掉 pai 头域
    增加 RtpRunner.LocalIp 配置指定本地监听地址
    更新 Jssi/2.1.7 版本解决 SDP 中设置 publicIp 不生效的问题
    更新 Common/3.6.52 版本解决测试中发现的 RtpRunner 异常退出的问题

2. feature: 修改调用 MpCall cancel 接口传递被叫ID

#### [依赖版本信息]

    jc/jc-service-base =1.2.12
    Platform/cube      =3.6.52-da566d7
    sdk/avatar         =8.0.55-1e0fe56
    sdk/grape          =7.1.46-a193dd3
    sdk/watermelon     =7.1.46-6e3a168
    sdk/melon-4        =7.1.46-6d42ae7
    solution/jusmeeting=7.1.46-a5c8d7b
    VoIMS/SDK/Lemon    =2.1.7-3d3c839

---

### 版本 2.3.10

* 发布者: Bob Liu
* 日期: 2025/3/5

#### [修复]

1. fix: 修改 Manager 选择 Runner 考虑 CPU 负载的功能没有生效的问题 (#163)

    https://project.feishu.cn/juphoonceshi/quexian/detail/5402073181

    更新 jc-service-base 提交 4270e0a51afdb754a21a7f35a34aff94c0a897aa
    在呼叫的日志中增加状态的信息以协助定位问题

#### [依赖版本信息]

    jc/jc-service-base =1.2.12
    Platform/cube      =3.6.48-3329618
    sdk/avatar         =8.0.55-1e0fe56
    sdk/grape          =7.1.46-a193dd3
    sdk/watermelon     =7.1.46-6e3a168
    sdk/melon-4        =7.1.46-6d42ae7
    solution/jusmeeting=7.1.46-a5c8d7b
    VoIMS/SDK/Lemon    =2.1.6-121582e

---

### 版本 2.3.9

* 发布者: Bob Liu
* 日期: 2025/1/23

#### [优化]

1. fix: 修改单元测试代码覆盖率统计不正确的问题 (#158)

    1. 修改执行脚本在测试用例执行异常的时候退出
    2. 更新排除文件的定义
    3. 解决Mac平台测试用例编译问题

#### [修复]

1. fix: 修改 SipGateway 统计信息中有重复的链接信息 (#159)

    Ref-Id: a4e3cd856b63d6500bb6a218df36a242ac318cfb

2. 修改线路连接超时恢复之后，SipGateway 服务没有正常监听的问题 (jc-service-base/1.2.11)

#### [依赖版本信息]

    jc/jc-service-base =1.2.11
    Platform/cube      =3.6.48-3329618
    sdk/avatar         =8.0.55-1e0fe56
    sdk/grape          =7.1.46-a193dd3
    sdk/watermelon     =7.1.46-6e3a168
    sdk/melon-4        =7.1.46-6d42ae7
    solution/jusmeeting=7.1.46-a5c8d7b
    VoIMS/SDK/Lemon    =2.1.6-121582e

---

### 版本 2.3.8

* 发布者: Bob Liu
* 日期: 2024/12/23

#### [新增特性]

1. 修改增加 SipCall.Precondition 配置参数 (#126)
2. feature: 修改 RtpRunner 增加视频桥接的功能 (#153)

    1. 通过 RtpRunner.VideoPipeMode 设置是否是桥接的功能，缺省是桥接，可以设置合流
    2. 在桥接模式下，RtpRunner 只会转发一个成员的视频
    3. 可以通过 RtpRunner.VideoPipePrioritys 配置转发成员的优先级

#### [依赖版本信息]

    jc/jc-service-base =1.2.10
    Platform/cube      =3.6.47-e57b74d
    sdk/avatar         =8.0.55-1e0fe56
    sdk/grape          =7.1.46-a193dd3
    sdk/watermelon     =7.1.46-6e3a168
    sdk/melon-4        =7.1.46-6d42ae7
    solution/jusmeeting=7.1.46-a5c8d7b
    VoIMS/SDK/Lemon    =2.1.6-121582e

---

### 版本 2.3.7

* 发布者: Bob Liu
* 日期: 2024/12/3

#### [优化]

1. ServiceRunnerManager 增加平衡 runner 的功能 (jc-service-base/1.2.9)

    feishu:#5022238479
    https://project.feishu.cn/juphoonceshi/quexian/detail/5022238479

    Manager 在 Runner 之后启动时，如果没有业务操作，此时 Runner 不会主动注册到 Manager
    导致暂时无法使用。

    增加逻辑检查如果当前可用 Runner 数量为 0，则主动向其它 Manager 转移 Runner

    测试场景
    1. 有多个 MpCallSipGateway 服务，在重启其中一个 MpCallSipGateway 之后，会主动发起
        转移 MpCallRtpRunner
    2. 在当前 MpCallSipGateway 无可用 MpCallRtpRunner 的时候，会主动发起转移

#### [依赖版本信息]

    jc/jc-service-base  =1.2.9
    Platform/cube       =3.6.38-100f1c2
    sdk/avatar          =8.0.55-1e0fe56
    sdk/grape           =7.1.39-aa4cbc8
    sdk/watermelon      =********-e51004f
    sdk/melon-4         =********-87663c4
    solution/jusmeeting =********-847a62a
    VoIMS/SDK/Lemon     =2.1.5-d88a9aa

---

### 版本 2.3.6

* 发布者: Bob Liu
* 日期: 2024/10/30

#### [新增特性]

1. feature: 支持 rtp runner 和 sip gateway 注册到同一个核心网 (#139)

#### [修复]

1. 修改 SDP Session Version 超出范围的问题 (#143) (#148)
2. 修改通话建立后收到 update 未发送响应问题 (Jssi/2.1.5)

#### [依赖版本信息]

    jc/jc-service-base  =1.2.7
    Platform/cube       =3.6.38-100f1c2
    sdk/avatar          =8.0.55-1e0fe56
    sdk/grape           =7.1.39-aa4cbc8
    sdk/watermelon      =********-e51004f
    sdk/melon-4         =********-87663c4
    solution/jusmeeting =********-847a62a
    VoIMS/SDK/Lemon     =2.1.5-d88a9aa

---

### 版本 2.3.5

* 发布者: Bob Liu
* 日期: 2024/9/19

#### [新增特性]

1. 修改 RtpRunner 在有屏幕共享的时候转发屏幕共享视频 (#136)
2. 修改 RtpRunner 角色匹配规则，如果 excludeRole=-1 则匹配任意 mask 指定的角色位 (#137)

#### [修复]

1. 修改 isbc 接收 INVITE 没有正确处理连接的问题

#### [依赖版本信息]

    jc/jc-service-base  =1.2.4
    Platform/cube       =3.6.38-100f1c2
    sdk/avatar          =8.0.55-1e0fe56
    sdk/grape           =7.1.39-aa4cbc8
    sdk/watermelon      =********-e51004f
    sdk/melon-4         =********-87663c4
    solution/jusmeeting =********-847a62a
    VoIMS/SDK/Lemon     =2.1.4-62f108c

---

### 版本 2.3.4

* 发布者: Bob Liu
* 日期: 2024/8/5

#### [修复]

1. 修改同一个连接第二次收到 INVITE 请求处理不正确的问题 (#132)

    问题引入的提交 a4e3cd856b63d6500bb6a218df36a242ac318cfb

#### [依赖版本信息]

    jc/jc-service-base  =1.2.4
    Platform/cube       =3.6.38-100f1c2
    sdk/avatar          =8.0.55-1e0fe56
    sdk/grape           =7.1.39-aa4cbc8
    sdk/watermelon      =********-e51004f
    sdk/melon-4         =********-87663c4
    solution/jusmeeting =********-847a62a
    VoIMS/SDK/Lemon     =2.1.4-62f108c

---

### 版本 2.3.3

* 发布者: Bob Liu
* 日期: 2024/7/25

#### [新增特性]

1. 增加支持控制音频和视频静音的功能 (#125)

#### [依赖版本信息]

    jc/jc-service-base  =1.2.4
    Platform/cube       =3.6.38-100f1c2
    sdk/avatar          =8.0.55-1e0fe56
    sdk/grape           =7.1.39-aa4cbc8
    sdk/watermelon      =********-e51004f
    sdk/melon-4         =********-87663c4
    solution/jusmeeting =********-847a62a
    VoIMS/SDK/Lemon     =2.1.4-62f108c

---

### 版本 2.3.2

* 发布者: Bob Liu
* 日期: 2024/7/12

#### [修复]

1. 修改在加入成功之前获取 connection 导致媒体不通的问题，修改join之前不发起接收信令

#### [依赖版本信息]

    jc/jc-service-base  =1.2.3
    Platform/cube       =3.6.36-fbbac52
    sdk/avatar          =8.0.55-1e0fe56
    sdk/grape           =7.1.39-aa4cbc8
    sdk/watermelon      =********-e51004f
    sdk/melon-4         =********-87663c4
    solution/jusmeeting =********-847a62a
    VoIMS/SDK/Lemon     =2.1.3-30a5148

---

### 版本 2.3.1

* 发布者: Bob Liu
* 日期: 2024/7/11

#### [新增特性]

1. 修改支持设置 Contact URI 为公网地址 (#121)
2. 修改 A-SBC 支持设置本地监听端口范围 SipCall.Registration.LocalPorts (#118)

#### [优化]

1. 增加设置备份目录的名称 (#122)

#### [修复]

1. 修改SIP呼入没有正确设置sessId的问题 (#120)
2. 修改在接入会议之后没有过滤会场已有成员的音频 (#119)

#### [依赖版本信息]

    jc/jc-service-base  =1.2.2
    Platform/cube       =3.6.36-fbbac52
    sdk/avatar          =8.0.55-1e0fe56
    sdk/grape           =7.1.39-aa4cbc8
    sdk/watermelon      =********-e51004f
    sdk/melon-4         =********-87663c4
    solution/jusmeeting =********-847a62a
    VoIMS/SDK/Lemon     =2.1.3-30a5148

---

### 版本 2.3.0

* 发布者: Bob Liu
* 日期: 2024/6/18

#### [新增特性]

1 增加光大定制业务服务 MpCallEbService (#70)

    1. 修改 SipGateway RtpRunner 支持 Invite re-Invite 不携带 SDP 的流程

        收到 Invite 不携带 SDP，则在接听的时候生成 Offer，在收到 Ack 的时候设置 Answer
        收到 re-Invite 不携带 SDP，则生成并通过 200 OK 发送 Offer，收到 Ack 的时候设置 Answer
        增加处理 hold 和 unhold 状态

    2. 修改 SIP 获取 UUI 信息的处理，修改 Hold 通知所有会话成员，增加组装 AccountId 接口，增加查询排队信息接口
    3. 增加 MpCallEbDialer 拨测服务
    4. 修改拨测问题，切换使用 debian10 编译，否则 regex 有问题

2. 修改 SipGateway 可以使用假 RtpRunner 方式 (#72)
3. EbService 增加 redis 存储 (#73)

    1. 支持使用 Redis 作为存储，支持多服务部署
    2. 增加 token 检查以及 pti 的信息
        callout uui 获取 token 和 pti 信息
        callin uui 设置 csn 和 pti 信息
    3. 修改 EbService 初始化失败进程仍然启动，显示错误统计信息
    4. 优化 EbDialer 从数据库获取有效号码

4. 修改 SipGateway 支持多个地址 (#30)

    1. 集成 jssi/2.1 分支，初始化不需要设置本地和远端地址，对应信息从 caller 和 callee URI 获取，
        支持多个连接的 SIP 通话
    2. 增加 SimpleSipSession 将 OPTIONS 保活移到到 SipLine，支持多个连接的保活
    3. 修改 SipLine 支持建立多个 Outgoing 连接，通过解析 SIP 消息的 call-id 区分使用不同的连接。
        初始 INVITE 请求首先随机分配活跃连接，其次再随机分配有效配置的连接
    4. 修改 SipCall.RemoteAddress 支持设置多个地址，使用 ; 分隔

5. SipGateway 增加早期媒体支持的配置 SipCall.EarlyMedia (#84)
6. 修改 SipGateway 支持对接 A-SBC (#115)
7. 增加支持 PRACK 方法 (#116)

#### [优化]

1. 修改 SipGateway 中 sessId 保持完整内容 (#110)
2. 收发 SIP 消息的日志中增加 sessId 信息 (#111)

#### [修复]

1. 修改 LegacyAppGateway 日志信息 (#112)

#### [依赖版本信息]

    Platform/cube       =3.6.36-fbbac52
    sdk/avatar          =8.0.53-2af0e58
    sdk/grape           =7.1.38-f398b32
    sdk/watermelon      =7.1.38-554f713
    sdk/melon-4         =7.1.38-64247d7
    solution/jusmeeting =7.1.38-f0d0fa1
    VoIMS/SDK/Lemon     =2.1.2-183f5f5

---

## 版本 2.2

### 版本 2.2.18

* 发布者: Bob Liu
* 日期: 2024/5/16

#### [新增特性]

1. 增加对 Sip 线路是否正常的检测 (#102)
2. 支持加入国密房间 (#107)

#### [优化]

1. 增加启动失败情况下延迟启动的处理，修改失败重试间隔为60秒 (#103)
2. 修改 SipSession 日志增加 sessId caller callee 信息 (#105)
3. 修改依赖 jc-service-base 仓库 (#106)

#### [修复]

1. 修改子进程中关闭fd (#104)

#### [依赖版本信息]

    jc/jc-service-base  =1.1.3
    Platform/cube       =3.6.33-ca9cda4
    sdk/avatar          =8.0.53-2af0e58
    sdk/grape           =********-78d68fb
    sdk/watermelon      =********-a62cf00
    sdk/melon-4         =********-80d4fa6
    solution/jusmeeting =7.1.36-e319c3b
    VoIMS/SDK/Lemon     =2.0.10-397634c

---

### 版本 2.2.17

* 发布者: Bob Liu
* 日期: 2024/4/15

#### [新增特性]

1. 修改支持发起单向视频的协商 (#99)

    1. 单向视频协商由 MpCall 服务接口参数决定，缺省为双向协商
    2. SipGateway 增加音频过滤配置 SipCall.AudioExcludeRole
    3. 修改 SessionLocator 避免过多的日志
    4. 增加AppSession解码的测试

2. 修改 RtpRunner 支持自适应单人视频还是合流的场景 (#100)

#### [依赖版本信息]

    Platform/cube           = 3.6.32-9f200af
    sdk/avatar              = 8.0.53-2af0e58
    sdk/grape               = 7.1.35-e585631
    sdk/watermelon          = 7.1.35-881cf95
    sdk/melon-4             = 7.1.35-bb3267d
    solution/jusmeeting     = 7.1.35-61a4319
    VoIMS/SDK/Lemon         = 2.0.10-397634c

---

### 版本 2.2.16

* 发布者: Bob Liu
* 日期: 2024/3/15

#### [新增特性]

1. RtpRunner 增加处理带外 dtmf (#94)
    1. 接收 RTP DTMF 发送到 会场
    2. 接收会场 DTMF 发送到 RTP
    3. 同步 jc-service-base 处理
2. 修改 RtpRunner 根据服务区分 Locators 配置 (#97)
    1. AppGateway 增加事件处队列数量缺省配置
    2. SipGateway 增加配置 SipCall.LeaveSync 与 App 同步结束 SIP 会话

#### [依赖版本信息]

    Platform/cube       = 3.6.32-9f200af
    sdk/avatar          = 8.0.45-6d4958e
    sdk/grape           = 7.1.34-e95a1ba
    sdk/watermelon      = 7.1.34-ad5cf13
    sdk/melon-4         = 7.1.34-2699b41
    solution/jusmeeting = 7.1.34-7c62c3c
    VoIMS/SDK/Lemon     = 2.0.10-397634c

---

### 版本 2.2.15

* 发布者: Bob Liu
* 日期: 2024/3/8

#### [新增特性]

1. 修改 AcdSipGateway 的 Category 为 AcdSipCall (#95)

    如果 MpCall 和 Acd 需要使用同一个 SIP 线路，则需要同时部署 AcdSipGateway
    和 MpCallSipGateway

#### [修复]

1. 更新 jssi 解决 SSRC 变化导致没有声音的问题 (#92)

#### [依赖版本信息]

    Platform/cube       = 3.6.30-eca6885
    sdk/avatar          = 8.0.45-6d4958e
    sdk/grape           = ********-b2799c3
    sdk/watermelon      = ********-a953f26
    sdk/melon-4         = ********-88c770e
    solution/jusmeeting = ********-40e6125
    VoIMS/SDK/Lemon     = 2.0.9-969b7cd

---

### 版本 2.2.14

* 发布者: Bob Liu
* 日期: 2024/2/22

#### [修复]

1. 修改系统测试问题 (#91)

    1. 调整日志避免过多的日志输出
        1. jssi 日志调整为 debug
        2. SipGateway 日志增加完整 SIP 报文输出
    2. 修改端口配置，保持偶数开始奇数结束
    3. 修改 AppSession 结束的时候主动将当前账号从会议中提出

#### [依赖版本信息]

    Platform/cube       = 3.6.30-eca6885
    sdk/avatar          = 8.0.45-6d4958e
    sdk/grape           = ********-b2799c3
    sdk/watermelon      = ********-a953f26
    sdk/melon-4         = ********-88c770e
    solution/jusmeeting = ********-40e6125
    VoIMS/SDK/Lemon     = 2.0.7-d4ddc19

---

### 版本 2.2.13

* 发布者: Bob Liu
* 日期: 2024/1/31

#### [新增特性]

1. 选择 Runner 算法增加考虑空闲比例，当前使用 CPU 空闲比例 (#85)
    1. 空闲比例接近则分配 Runner 时避免上一次分配的 Host
    2. 增加对分配 Runner 之后的异常处理
        1. 选择之后设置 Runner 为 Pending
        2. 使用者调用 getRunner 之后 Runner 设置为 Working
        3. 对于析构之前没有调用 getRunner 则重置 Runner 状态为 Ready
    3. 修改 Daemon 启动 Runner 失败之后间隔 15 秒再试

#### [优化]

1. 修改 SipGateway 设置公网地址时候不能接收传入连接的问题 (#88)

    LocalAddress 不是本地地址的时候会导致打开两个 socket 监听的问题，从而导致收到新
    地址的数据时候，没有预期进入到 recvConnection 处理

    修改之后，LocalAddress 不是本地地址的时候，则 connect 使用 0.0.0.0 避免创建两个
    socket

#### [依赖版本信息]

    Platform/cube       = 3.6.30-eca6885
    sdk/avatar          = 8.0.45-6d4958e
    sdk/grape           = ********-b2799c3
    sdk/watermelon      = ********-a953f26
    sdk/melon-4         = ********-88c770e
    solution/jusmeeting = ********-40e6125
    VoIMS/SDK/Lemon     = 2.0.7-d4ddc19

---

### 版本 2.2.12

* 发布者: Bob Liu
* 日期: 2024/1/30

#### [优化]

1. 修改避免将 ServiceRunner 没有定义属性纳入计算，区分 NoResource 和 NoBetterResource (#86)

#### [修复]

1. 修改收到 RE-INVITE 200OK 之后没有发送 ACK 的问题 (#81)
2. 增加 RtpAgent 超时时间避免长时间没有返回导致 SipGateway 异常退出 (#87)
    1. 修改默认 EventManager.MaxProcessors 为 10 避免异常
    2. 增加 RtpDaemon 中异常状态处理
    3. 增加 SipGateway 中异常状态的判断

#### [依赖版本信息]

    Platform/cube       = 3.6.30-eca6885
    sdk/avatar          = 8.0.45-6d4958e
    sdk/grape           = ********-b2799c3
    sdk/watermelon      = ********-a953f26
    sdk/melon-4         = ********-88c770e
    solution/jusmeeting = ********-40e6125
    VoIMS/SDK/Lemon     = 2.0.7-d4ddc19

---

### 版本 2.2.11

* 发布者: Bob Liu
* 日期: 2024/1/18

#### [优化]

1. 更新 cfg 文件增加正则表达式 (#82)

#### [依赖版本信息]

    Platform/cube       = 3.6.30-eca6885
    sdk/avatar          = 8.0.45-6d4958e
    sdk/grape           = ********-b2799c3
    sdk/watermelon      = ********-a953f26
    sdk/melon-4         = ********-88c770e
    solution/jusmeeting = ********-40e6125
    VoIMS/SDK/Lemon     = 2.0.6-467c0ee

---

### 版本 2.2.10

* 发布者: Bob Liu
* 日期: 2023/12/27

#### [新增特性]

1. 分配 RtpRunner 增加延时等待的机制 (#76)
2. 添加媒体质量事件收集功能 (#77)
3. 适配 Lemon jssi/2.0.6 版本接口修改 (#78)
4. 呼出收到 200 OK 之后通知接通的事件
5. 支持设置不打开本地端口监听的模式

#### [优化]

1. 修改 Runner 退出时超时被 Daemon abort 的问题 (#71)

    1. 修改 Runner 退出的时候主动上报状态
    2. Daemon 在 Stopping 检查超时修改为 30s
    3. Daemon 在 Stopping 超时的时候也执行 abort

#### [修复]

1. 修改没有本地监听导致不能处理传入的连接 (#75)

#### [依赖版本信息]

    Platform/cube       = 3.6.27-d1b53d2
    sdk/avatar          = 8.0.45-6d4958e
    sdk/grape           = ********-b2799c3
    sdk/watermelon      = ********-a953f26
    sdk/melon-4         = ********-88c770e
    solution/jusmeeting = ********-40e6125
    VoIMS/SDK/Lemon     = 2.0.6-467c0ee

---

### 版本 2.2.9

* 发布者: Bob Liu
* 日期: 2023/11/23

#### [修复]

1. 改进获取 RtpRunner 信息的接口 (#67)

#### [依赖版本信息]

    Platform/cube       = 3.6.27-d1b53d2
    sdk/avatar          = 8.0.45-6d4958e
    sdk/grape           = ********-b2799c3
    sdk/watermelon      = ********-a953f26
    sdk/melon-4         = ********-88c770e
    solution/jusmeeting = ********-40e6125
    VoIMS/SDK/Lemon     = 2.0.5-dca3a1c

---

### 版本 2.2.8

* 发布者: Bob Liu
* 日期: 2023/11/17

#### [优化]

1. 优化 SipGateway 中 SIP 协议相关日志

#### [修复]

1. 修改 SipGateway RtpRunner 适配视频单向协商的接口 (#65)

#### [依赖版本信息]

    Platform/cube       = 3.6.27-d1b53d2
    sdk/avatar          = 8.0.45-6d4958e
    sdk/grape           = ********-b2799c3
    sdk/watermelon      = ********-a953f26
    sdk/melon-4         = ********-88c770e
    solution/jusmeeting = ********-40e6125
    VoIMS/SDK/Lemon     = 2.0.5-dca3a1c

---

### 版本 2.2.7

* 发布者: Bob Liu
* 日期: 2023/11/15

#### [新增特性]

1. 增加通知 DTMF 事件的功能 (#62)

#### [修复]

1. 修改视频端口默认值的问题，更新 Runner Id 生成策略 (#63)

#### [依赖版本信息]

    Platform/cube       = 3.6.27-d1b53d2
    sdk/avatar          = 8.0.45-6d4958e
    sdk/grape           = ********-b2799c3
    sdk/watermelon      = ********-a953f26
    sdk/melon-4         = ********-88c770e
    solution/jusmeeting = ********-40e6125
    VoIMS/SDK/Lemon     = 2.0.5-dca3a1c

---

### 版本 2.2.6

* 发布者: Bob Liu
* 日期: 2023/11/10

#### [新增特性]

1. RtpRunnerDaemon 增加端口管理模块分配 RtpRunner 使用端口 (#44)
2. AppSession SipSession 增加保活事件处理 (#61)
    SipGateway 增加 Jssi 版本统计信息，将 Event 通知修改成异步调用

#### [修复]

1. 修改 RtpRunner 修改端口范围配置没有生效的问题
2. SipGateway 修改在处理 SIP 事件的时候出现 CPU 高的问题 (Common/3.6.27)

#### [依赖版本信息]

    Platform/cube       = 3.6.27
    sdk/avatar          = 8.0.45-6d4958e
    sdk/grape           = ********-b2799c3
    sdk/watermelon      = ********-a953f26
    sdk/melon-4         = ********-88c770e
    solution/jusmeeting = ********-40e6125
    VoIMS/SDK/Lemon     = 2.0.5-dca3a1c

---

### 版本 2.2.5

* 发布者: Bob Liu
* 日期: 2023/11/8

#### [新增特性]

1. SipGateway 允许 SIP 修改升降级视频 (#40)
2. RtpRunner 设置自适应长宽比 (#42)

#### [依赖版本信息]

    Platform/cube       = 3.6.19-2d0a07d
    sdk/avatar          = 8.0.45-6d4958e
    sdk/grape           = ********-b2799c3
    sdk/watermelon      = ********-a953f26
    sdk/melon-4         = ********-88c770e
    solution/jusmeeting = ********-40e6125
    VoIMS/SDK/Lemon     = 2.0.2-f15c69d

---

### 版本 2.2.4

* 发布者: Bob Liu
* 日期: 2023/11/1

#### [新增特性]

1. LegacyAppGateway 添加 createAgent 接口 (#38)
2. RtpRunner 在会议中的视频状态由 SIP 协商结果决定 (#35)
    1. 加入会议之后根据第一次协商结果设置是是否开启视频
    2. 后续协商变化之后，相应设置会场中视频状态
    3. 增加 Jsm 请求队列化处理设置状态

#### [修复]

1. LegacyAppGateway 修改调用异步 command 接口避免死锁问题
2. 解决 SipPhoneSession 在 RtpRunner 异常的之后没有结束的问题 (#37)

#### [依赖版本信息]

    Platform/cube       = 3.6.19-2d0a07d
    sdk/avatar          = 8.0.45-6d4958e
    sdk/grape           = ********-b2799c3
    sdk/watermelon      = ********-a953f26
    sdk/melon-4         = ********-88c770e
    solution/jusmeeting = ********-40e6125
    VoIMS/SDK/Lemon     = 2.0.2-f15c69d

---

### 版本 2.2.3

* 发布者: Bob Liu
* 日期: 2023/10/19

#### [修复]

1. 修改互斥锁加锁代码错误

#### [依赖版本信息]

    Platform/cube       = 3.6.19-2d0a07d
    sdk/avatar          = 8.0.45-6d4958e
    sdk/grape           = ********-b2799c3
    sdk/watermelon      = ********-a953f26
    sdk/melon-4         = ********-88c770e
    solution/jusmeeting = ********-40e6125
    VoIMS/SDK/Lemon     = 2.0.2-f15c69d

---

### 版本 2.2.2

* 发布者: Bob Liu
* 日期: 2023/10/18

#### [新增特性]

1. 增加缺省 Locators 相关的配置，减少运维部署配置，优化日志信息 (#29)
2. SipGateway 增加支持指定 ExtraRole 参数 (#31)
3. 修改 SipMpCall 增加支持 AcdServer 配合使用的 (#32)
4. 增加请求视频功能实现，修改默认接受对方SIP视频请求 (#33)

#### [配置修改]

1. 服务设置缺省配置 Locators.MpCall Locators.TpAppAgent Locators.TpSipAgent，部署可以不用配置

#### [依赖版本信息]

    Platform/cube       = 3.6.19-2d0a07d
    sdk/avatar          = 8.0.45-6d4958e
    sdk/grape           = ********-b2799c3
    sdk/watermelon      = ********-a953f26
    sdk/melon-4         = ********-88c770e
    solution/jusmeeting = ********-40e6125
    VoIMS/SDK/Lemon     = 2.0.2-f15c69d

---

### 版本 2.2.1

* 发布者: Bob Liu
* 日期: 2023/7/28

#### [修复]

1. LegacyAppGateway 增加处理终端请求结束会议的处理 (#27)

#### [依赖版本信息]

    Platform/cube       = 3.6.19-2d0a07d
    sdk/avatar          = 8.0.45-6d4958e
    sdk/grape           = ********-b2799c3
    sdk/watermelon      = ********-a953f26
    sdk/melon-4         = ********-88c770e
    solution/jusmeeting = ********-40e6125
    VoIMS/SDK/Lemon     = 2.0.2-f15c69d

---

### 版本 2.2.0

* 发布者: Bob Liu
* 日期: 2023/7/20

#### [新增特性]

1. 增加 MpCall SIP 对接服务
2. 增加 MpCall App 兼容服务

#### [依赖版本信息]

* Platform/cube: 3.6.19-2d0a07d
* sdk/avatar: 8.0.45-6d4958e
* sdk/grape: ********-b2799c3
* sdk/watermelon: ********-a953f26
* sdk/melon-4: ********-88c770e
* solution/jusmeeting: ********-40e6125
* VoIMS/SDK/Lemon: 2.0.2-f15c69d

---
