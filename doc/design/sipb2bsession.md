# SipB2BSession 架构设计文档

## 目录
- [概述](#概述)
- [核心模块架构](#核心模块架构)
- [主要模块说明](#主要模块说明)
- [Sip会话流程](#sip会话流程)
- [核心设计特点](#核心设计特点)
- [关键技术实现](#关键技术实现)
- [配置管理](#配置管理)
- [监控与诊断](#监控与诊断)
- [总结](#总结)

## 概述

Sipb2bSession 是 SIP B2BUA（背靠背用户代理）架构中的核心会话管理组件，专为处理双向呼叫桥接而设计。

## 核心模块架构

```mermaid
graph LR
    subgraph "Application Layer"
        App[SipDialer]
    end

    subgraph "SipB2BSession Core"
        B2BSessionI[SipB2BSessionI<br/>会话管理]
        OutgoingSessionListener[OutgoingSessionListener<br/>呼出事件监听器]
        IncomingSessionListener[IncomingSessionListener<br/>呼入事件监听器]
    end

    subgraph "SipRegLine Core"
        SipRegLine[SipRegLine<br/>注册线路控制]
        SipRegLineListener
    end

    subgraph "SipLine Core"
        SipLine[SipLine<br/>SIP线路控制]
        SipLineListener
    end

    subgraph "SipRegLineSession Layer"
        IncomingSession[IncomingSession<br/>呼入会话]
        OutgoingSession[OutgoingSession<br/>呼出会话]
    end

    subgraph "SIP Protocol Layer"
        SipCallIf[SipCallInterface<br/>呼叫接口]
        SipCallListener[SipCallListener<br/>监听接口]
    end

    App -- Create --> B2BSessionI
    App -- Create --> SipRegLine
    App -- Create --> SipLine
    App <--> SipRegLineListener
    App <--> SipLineListener
    B2BSessionI --> SipRegLine
    SipRegLine --> SipRegLineListener
    SipRegLine <--> SipCallListener
    SipLine <--> SipCallListener
    SipLine --> SipLineListener

    IncomingSession --> SipCallIf
    OutgoingSession --> SipCallIf
    SipCallIf --> SipCallListener

    IncomingSessionListener --> B2BSessionI
    OutgoingSessionListener --> B2BSessionI
    OutgoingSessionListener <--> OutgoingSession
    B2BSessionI <--> IncomingSession

    linkStyle 0,4,8,9 stroke:#ff0000,stroke-width:3px
```

## 主要模块说明

### 1. SipDialer
- **职责**: 核心拨号服务模块
- **功能**:
    - 创建B2B会话或标准SIP会话
    - 创建SipLine线路或SipRegLine线路

### 2. B2BSession
- **职责**: 核心会话管理组件
- **功能**:
    - 同时管理两个独立的SIP会话：呼入和呼出
    - 处理事件响应
    - 维护双会话状态机，确保会话状态一致性

### 3. SipRegLineI
- **职责**: 线路管理组件
- **功能**:
    - 呼入和呼出会话线路的创建和管理
    - 处理连接事件

### 4. SipRegLineSession
- **核心组件**：
  - `IncomingSession`：呼入会话线路管理
  - `OutgoingSession`：呼出会话线路管理
- **职责**：监听和使用呼入/呼出sip协议接口
- **功能**:
    - 呼入/呼出会话监听
    - 调用协议接口生成对应Sip信令


### Sip会话流程
#### 呼叫流程
```mermaid
sequenceDiagram
    participant A as 主叫方
    participant I as IncomingSession
    participant R as SipRegLineI/SipLine
    participant B as B2BSessionI
    participant O as OutgoingSession
    participant S as SipCallInterface
    participant C as 被叫方

    A->>S: INVITE
    Note over S: 触发会话创建
    S->>R: 调用onCreateSession接口
    R->>R: 判断被叫是否在注册列表
    R->>I: 创建呼入会话
    R->>B: 创建B2BSession
    I->>B: 事件转发
    B->>O: 创建呼出会话
    O->>+S: 生成INVITE消息
    S->>-O: onSend
    O->>C: INVITE

    C->>O: 180 Ringing
    O->>B: 事件转发
    B->>I: 触发SipAlert
    I->>+S: 生成180响应消息
    S->>-I: onSend
    I->>A: 180 Ringing

    C->>O: 200 OK
    O->>B: 事件转发
    B->>I: 触发SipAnswer
    I->>+S: 生成200响应消息
    S->>-I: onSend
    I->>A: 200 OK
```
#### 正常挂断流程（主叫方发起）
```mermaid
sequenceDiagram
    participant A as 主叫方
    participant I as IncomingSession
    participant B as B2BSessionI
    participant O as OutgoingSession
    participant C as 被叫方

    %% 主叫方发起挂断
    A->>I: BYE
    I->>B: 挂断请求
    B->>O: 终止会话
    O->>C: BYE
    C->>O: 200 OK
    O->>B: 确认终止
    B->>I: 清理资源
    I->>A: 200 OK
    B->>B: 释放会话资源
```

#### 正常挂断流程（被叫方发起）
```mermaid
sequenceDiagram
    participant C as 被叫方
    participant O as OutgoingSession
    participant B as B2BSessionI
    participant I as IncomingSession
    participant A as 主叫方

    %% 被叫方发起挂断
    C->>O: BYE
    O->>B: 挂断请求
    B->>I: 终止会话
    I->>A: BYE
    A->>I: 200 OK
    I->>B: 确认终止
    B->>O: 清理资源
    O->>C: 200 OK
    B->>B: 释放会话资源
```

#### 异常处理流程
```mermaid
sequenceDiagram
    participant A as 主叫方
    participant I as IncomingSession
    participant B as B2BSessionI
    participant O as OutgoingSession
    participant C as 被叫方

    %% 呼出失败场景
    A->>I: INVITE
    I->>B: 创建B2B会话
    B->>O: 创建呼出会话
    O->>C: INVITE
    C->>O: 486 Busy Here
    O->>B: 呼出失败
    B->>I: 转发错误响应
    I->>A: 486 Busy Here
    B->>B: 清理资源

    %% 超时场景
    Note over B: 检测到超时
    B->>O: 终止呼出会话
    B->>I: 终止呼入会话
    O->>C: CANCEL
    I->>A: 408 Request Timeout
    B->>B: 释放所有资源
```

**呼叫流程数据流说明:**
1. **消息接收**: SIP客户端发送INVITE消息 → Conn接收 → 上报至SipRegLineI
2. **映射建立**: SipRegLineI解析INVITE中的CallId，建立CallId与Conn的关联关系
3. **协议处理**: 将INVITE消息转发给SipCallInterface进行协议处理
4. **会话创建**: SipCallInterface触发onCreateSession回调，判断被叫账号是否是已注册账号，创建SipRegLineSessionI和B2bSession
5. **监听器创建**: 通过B2bSession创建SipRegLineSessionListener，建立会话事件处理机制
6. **来电通知**: 通过onCallIncoming回调通知应用层有新的来电请求
7. **发起外呼**: 通过被叫用户id匹配已注册的用户连接发起外呼并创建OutgoingSession
8. **消息接收和转发**: 在呼入/呼出SIP会话中收到消息通知，在对应的呼出/呼入SIP会话中产生新的SIP消息并转发

## 核心设计特点

### 1. 分层抽象架构
- **网络层**: 基于 SimpleSipSession 框架的 SIP 传输层，支持 UDP/TCP/TLS 协议
  - `SipTransport`: 抽象传输接口，提供协议无关的消息传输能力
  - `SipConn`: 连接管理，处理网络连接的建立、维护和释放
  - `SipHostPort`: 网络地址抽象，统一处理主机和端口信息
- **协议层**: SIP 协议栈实现，负责 SIP 消息的解析、生成和状态管理
  - `SipCallInterface`: SIP 呼叫协议接口，封装底层 SIP SDK 调用
  - `SipMessage`: SIP 消息抽象，支持请求和响应消息的统一处理
  - `SipTransaction`: SIP 事务管理，维护请求-响应的关联关系
- **会话层**: SIP 会话管理，提供呼叫会话的生命周期管理
  - `SipLineSession`: 基础会话抽象，定义会话的通用接口
  - `SipRegLineSession`: 注册线路会话，支持用户注册和呼叫路由
  - `IncomingSession/OutgoingSession`: 呼入/呼出会话的具体实现
- **应用层**: B2B 业务逻辑实现，提供呼叫桥接和路由功能
  - `DialerB2bSession`: B2B 会话核心，协调呼入和呼出会话
  - `SipDialer`: 拨号服务，提供统一的呼叫发起接口

### 2. 事件驱动架构
- **异步事件处理**: 采用监听器模式，实现松耦合的事件通知机制
  - `SipLineSessionListener`: 会话事件监听器，处理呼叫状态变化
  - `OutgoingSessionListener`: 呼出会话专用监听器，处理外呼事件
  - `IncomingSessionListener`: 呼入会话专用监听器，处理来电事件
- **事件传播链**: 事件从底层协议栈向上传播至应用层
  - SIP 协议事件 → 会话事件 → B2B 会话事件 → 应用层事件
- **状态同步机制**: 通过事件驱动实现双会话状态的实时同步

### 3. 状态机管理
- **会话状态机**: 严格的状态转换控制，确保会话状态的一致性

```mermaid
stateDiagram-v2
    [*] --> StateIdle: 创建会话
    StateIdle --> StateCalling: 收到INVITE/发起呼叫
    StateCalling --> StateConnected: 呼叫接通
    StateCalling --> StateTermed: 呼叫失败/取消
    StateConnected --> StateTermed: 正常挂断
    StateTermed --> [*]: 资源清理

    note right of StateCalling
        双会话协调状态
        - 呼入会话：Incoming
        - 呼出会话：Calling
    end note

    note right of StateConnected
        媒体通道建立
        - 双向RTP流
        - 信令转发
    end note
```

- **双会话协调**: B2B 会话同时管理呼入和呼出两个独立的状态机
- **异常状态处理**: 完善的错误状态处理和恢复机制

### 4. 资源管理
- **生命周期管理**: 基于智能指针的自动资源管理
  - `Common::Handle<T>`: 引用计数智能指针，防止内存泄漏
  - RAII 原则：资源获取即初始化，确保资源的正确释放
- **连接池管理**: 高效的网络连接复用机制
- **会话池管理**: 会话对象的创建、复用和销毁管理

### 5. 线程安全设计
- **递归互斥锁**: 使用 `Common::RecMutex` 保证线程安全
- **锁粒度控制**: 细粒度锁设计，减少锁竞争
- **无锁数据结构**: 在可能的情况下使用无锁数据结构提高性能

### 6. 错误处理与恢复
- **分层错误处理**: 不同层次的错误分类和处理策略
  - 网络层错误：连接断开、超时等
  - 协议层错误：SIP 响应码、格式错误等
  - 会话层错误：状态不一致、资源不足等
  - 应用层错误：业务逻辑错误、配置错误等
- **超时管理**: 多级超时控制机制
  - 连接超时：网络连接建立超时
  - 事务超时：SIP 事务处理超时
  - 会话超时：呼叫建立和维持超时
- **故障恢复**: 自动重试和故障转移机制

### 7. 可扩展性设计
- **插件化架构**: 支持功能模块的动态加载和扩展
- **配置驱动**: 通过配置文件控制系统行为，无需重新编译
- **协议扩展**: 支持 SIP 扩展协议和自定义头域

### 8. 性能优化
- **内存池**: 预分配内存池，减少频繁的内存分配和释放
- **对象池**: 会话对象复用，减少对象创建开销
- **异步 I/O**: 非阻塞 I/O 模型，提高并发处理能力
- **批量处理**: 事件批量处理机制，提高处理效率

## 配置管理

### 1. 系统配置项
- **网络配置**: 监听地址、端口、协议类型
- **SIP配置**: 域名、用户代理、超时参数
- **媒体配置**: 编解码器、RTP参数
- **日志配置**: 日志级别、输出格式、轮转策略

### 2. 动态配置更新
- 支持运行时配置更新，无需重启服务
- 配置变更通知机制
- 配置验证和回滚机制

## 监控与诊断

### 1. 性能指标
- **会话指标**: 并发会话数、会话成功率、平均会话时长
- **网络指标**: 连接数、吞吐量、延迟、丢包率
- **系统指标**: CPU使用率、内存使用率、线程数

### 2. 日志系统
- **结构化日志**: 使用统一的日志格式和上下文信息
- **日志级别**: DEBUG、INFO、WARN、ERROR、FATAL
- **日志轮转**: 按大小和时间自动轮转日志文件

### 3. 事件追踪
- **会话追踪**: 完整的会话生命周期事件记录
- **消息追踪**: SIP消息的发送和接收记录
- **错误追踪**: 详细的错误信息和调用栈

## 总结

SipB2BSession 架构设计体现了以下核心设计理念：

### 设计优势
1. **高内聚低耦合**: 模块化设计，各层职责清晰，接口简洁
2. **可扩展性强**: 支持插件化扩展和协议扩展
3. **高可靠性**: 完善的错误处理和恢复机制
4. **高性能**: 异步I/O、对象池、内存池等优化技术
5. **易维护**: 结构化日志、监控指标、诊断工具

### 技术特色
1. **双会话同步**: 创新的B2B会话管理机制
2. **事件驱动**: 松耦合的异步事件处理架构
3. **状态机**: 严格的状态管理和转换控制
4. **资源管理**: 智能指针和RAII原则的应用
5. **线程安全**: 细粒度锁和无锁数据结构

### 应用场景
- **企业通信**: 企业内部电话系统的呼叫路由
- **运营商网关**: 不同网络间的SIP呼叫桥接
- **云通信**: 云端SIP服务的呼叫转接
- **视频会议**: 多方通话的媒体桥接
- **呼叫中心**: 客服系统的呼叫分发

本架构设计为构建高性能、高可靠的SIP B2BUA系统提供了完整的技术方案，能够满足各种复杂的企业通信需求。