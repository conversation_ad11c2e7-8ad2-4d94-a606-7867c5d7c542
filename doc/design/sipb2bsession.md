# SipB2BSession 架构设计文档

## 概述

Sipb2bSession 是 SIP B2BUA（背靠背用户代理）架构中的核心会话管理组件，专为处理双向呼叫桥接而设计。

## 核心模块架构

```mermaid
graph LR
    subgraph "Application Layer"
        App[SipDialer]
    end

    subgraph "SipB2BSession Core"
        B2BSessionI[SipB2BSessionI<br/>会话管理]
        OutgoingSessionListener[OutgoingSessionListener<br/>呼出事件监听器]
        IncomingSessionListener[IncomingSessionListener<br/>呼入事件监听器]
    end

    subgraph "SipRegLine Core"
        SipRegLine[SipRegLine<br/>注册线路控制]
        SipRegLineListener
    end

    subgraph "SipLine Core"
        SipLine[SipLine<br/>SIP线路控制]
        SipLineListener
    end

    subgraph "SipRegLineSession Layer"
        IncomingSession[IncomingSession<br/>呼入会话]
        OutgoingSession[OutgoingSession<br/>呼出会话]
    end

    subgraph "SIP Protocol Layer"
        SipCallIf[SipCallInterface<br/>呼叫接口]
        SipCallListener[SipCallListener<br/>监听接口]
    end

    App -- Create --> B2BSessionI
    App -- Create --> SipRegLine
    App -- Create --> SipLine
    App <--> SipRegLineListener
    App <--> SipLineListener
    B2BSessionI --> SipRegLine
    SipRegLine --> SipRegLineListener
    SipRegLine <--> SipCallListener
    SipLine <--> SipCallListener
    SipLine --> SipLineListener

    IncomingSession --> SipCallIf
    OutgoingSession --> SipCallIf
    SipCallIf --> SipCallListener

    IncomingSessionListener --> B2BSessionI
    OutgoingSessionListener --> B2BSessionI
    OutgoingSessionListener <--> OutgoingSession
    B2BSessionI <--> IncomingSession

    linkStyle 0,4,8,9 stroke:#ff0000,stroke-width:3px
```

## 主要模块说明

### 1. SipDialer
- **职责**: 核心拨号服务模块
- **功能**:
    - 创建B2B会话或标准SIP会话
    - 创建SipLine线路或SipRegLine线路

### 2. B2BSession
- **职责**: 核心会话管理组件
- **功能**:
    - 同时管理两个独立的SIP会话：呼入和呼出
    - 处理事件响应
    - 维护双会话状态机，确保会话状态一致性

### 3. SipRegLineI
- **职责**: 线路管理组件
- **功能**:
    - 呼入和呼出会话线路的创建和管理
    - 处理连接事件

### 4. SipRegLineSession
- **核心组件**：
  - `IncomingSession`：呼入会话线路管理
  - `OutgoingSession`：呼出会话线路管理
- **职责**：监听和使用呼入/呼出sip协议接口
- **功能**:
    - 呼入/呼出会话监听
    - 调用协议接口生成对应Sip信令


### Sip会话流程
#### 呼叫流程
```mermaid
sequenceDiagram
    participant A as 主叫方
    participant I as IncomingSession
    participant R as SipRegLineI/SipLine
    participant B as B2BSessionI
    participant O as OutgoingSession
    participant S as SipCallInterface
    participant C as 被叫方

    A->>S: INVITE
    Note over S: 触发会话创建
    S->>R: 调用onCreateSession接口
    R->>R: 判断被叫是否在注册列表
    R->>I: 创建呼入会话
    R->>B: 创建B2BSession
    I->>B: 事件转发
    B->>O: 创建呼出会话
    O->>+S: 生成INVITE消息
    S->>-O: onSend
    O->>C: INVITE

    C->>O: 180 Ringing
    O->>B: 事件转发
    B->>I: 触发SipAlert
    I->>+S: 生成180响应消息
    S->>-I: onSend
    I->>A: 180 Ringing

    C->>O: 200 OK
    O->>B: 事件转发
    B->>I: 触发SipAnswer
    I->>+S: 生成200响应消息
    S->>-I: onSend
    I->>A: 200 OK
```
#### 正常挂断流程（主叫方发起）
```mermaid
sequenceDiagram
    participant A as 主叫方
    participant I as IncomingSession
    participant B as B2BSessionI
    participant O as OutgoingSession
    participant C as 被叫方

    %% 主叫方发起挂断
    A->>I: BYE
    I->>B: 挂断请求
    B->>O: 终止会话
    O->>C: BYE
    C->>O: 200 OK
    O->>B: 确认终止
    B->>I: 清理资源
    I->>A: 200 OK
    B->>B: 释放会话资源
```

#### 正常挂断流程（被叫方发起）
```mermaid
sequenceDiagram
    participant C as 被叫方
    participant O as OutgoingSession
    participant B as B2BSessionI
    participant I as IncomingSession
    participant A as 主叫方

    %% 被叫方发起挂断
    C->>O: BYE
    O->>B: 挂断请求
    B->>I: 终止会话
    I->>A: BYE
    A->>I: 200 OK
    I->>B: 确认终止
    B->>O: 清理资源
    O->>C: 200 OK
    B->>B: 释放会话资源
```

**呼叫流程数据流说明:**
1. **消息接收**: SIP客户端发送INVITE消息 → Conn接收 → 上报至SipRegLineI
2. **映射建立**: SipRegLineI解析INVITE中的CallId，建立CallId与Conn的关联关系
3. **协议处理**: 将INVITE消息转发给SipCallInterface进行协议处理
4. **会话创建**: SipCallInterface触发onCreateSession回调，判断被叫账号是否是已注册账号，创建SipRegLineSessionI和B2bSession
5. **监听器创建**: 通过B2bSession创建SipRegLineSessionListener，建立会话事件处理机制
6. **来电通知**: 通过onCallIncoming回调通知应用层有新的来电请求
7. **发起外呼**: 通过被叫用户id匹配已注册的用户连接发起外呼并创建OutgoingSession
8. **消息接收和转发**: 在呼入/呼出SIP会话中收到消息通知，在对应的呼出/呼入SIP会话中产生新的SIP消息并转发

## 核心设计特点

### 1. 分层抽象架构
- **网络层**: 