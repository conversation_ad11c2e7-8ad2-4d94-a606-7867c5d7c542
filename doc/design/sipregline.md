# SipRegLine 架构设计文档

## 概述

SipRegLine 是一个基于 SIP 协议的注册线路管理模块，负责处理 SIP 用户注册和会话管理。模块采用分层架构设计，支持多客户端连接管理和会话生命周期管理。

## 核心模块架构

```mermaid
graph TB
    subgraph "Application Layer"
        App[Application]
        Listener[SipRegLineListener]
    end

    subgraph "SipRegLine Core"
        SipRegLineI[SipRegLineI<br/>主控制器]
        SessionMgr[SipRegLineSessionManager<br/>会话管理器]
        ConnServer[SipRegLineConnServer<br/>连接服务器]
    end

    subgraph "Connection Layer"
        ConnMgr[SipRegLineConnManager<br/>连接管理器]
        Conn[SipRegLineConn<br/>网络连接]
        Cache[TimeExpirationCache<br/>CallId缓存]
    end

    subgraph "SIP Protocol Layer"
        ServerReg[ServerRegistration<br/>注册服务器]
        SipDriver[SipDriver<br/>SIP驱动]
        SipCallIf[SipCallInterface<br/>呼叫接口]
    end

    subgraph "Session Layer"
        Session[SipRegLineSessionI<br/>会话实例]
        SessionListener[SipRegLineSessionListener<br/>会话监听器]
    end

    App --> SipRegLineI
    SipRegLineI --> SessionMgr
    SipRegLineI --> ConnServer
    SipRegLineI --> ServerReg
    SipRegLineI --> SipCallIf

    ConnServer --> ConnMgr
    ConnMgr --> Conn
    ConnMgr --> Cache

    SessionMgr --> Session
    Session --> SessionListener

    ServerReg --> SipDriver
    Conn --> ServerReg
    Session --> SipCallIf
```

## 主要模块说明

### 1. SipRegLineI (主控制器)
- **职责**: 系统核心控制器，协调各模块工作
- **功能**:
  - 管理 SIP 注册服务器
  - 处理连接服务器事件
  - 管理会话生命周期
  - 与 SipCallInterface 交互

### 2. SipRegLineConnServer/ConnManager (连接管理)
- **职责**: 管理网络连接和消息路由
- **功能**:
  - UDP 监听和连接管理
  - CallId 与连接映射缓存
  - 消息接收和转发

### 3. SipRegLineSessionManager (会话管理)
- **职责**: 管理 SIP 会话实例
- **功能**:
  - 会话创建和销毁
  - SessionId 和 CallId 索引管理
  - 会话状态维护

### 4. TimeExpirationCache (缓存管理)
- **职责**: 提供时间过期的缓存机制
- **功能**:
  - CallId 与连接关系缓存
  - 自动过期清理
  - 高效查找和更新

## 数据流向设计

### SIP 注册流程

```mermaid
sequenceDiagram
    participant Client as SIP客户端
    participant Conn as SipRegLineConn
    participant Server as ConnServer
    participant RegI as SipRegLineI
    participant ServerReg as ServerRegistration

    Client->>+Conn: REGISTER消息
    Conn->>+Server: onRecv()
    Server->>+RegI: onRecv()
    RegI->>+ServerReg: recv(message, conn)

    Note over ServerReg: 验证用户凭据<br/>绑定用户与连接

    ServerReg->>+RegI: 注册请求回调
    RegI->>-ServerReg: 返回200 OK
    ServerReg->>Conn: onSendMessage()
    Conn->>-Client: 200 OK响应
```

**注册数据流说明:**
1. **接收阶段**: SIP客户端发送REGISTER消息 → Conn接收 → 上报至SipRegLineI
2. **处理阶段**: SipRegLineI转发给ServerRegistration处理用户认证和注册
3. **绑定阶段**: ServerRegistration成功后在内部建立用户账号与Conn的映射关系
4. **响应阶段**: ServerRegistration通过Conn的发送接口返回注册响应

**主叫会话数据流说明:**
1. **会话创建**: 应用层调用SipCall接口，创建SipRegLineSessionI会话实例
2. **连接查找**: 通过ServerRegistration查找被叫用户对应的Conn对象
3. **呼叫发起**: 向SipCallInterface发起呼叫并指定Call-ID
4. **映射建立**: 建立CallId与Conn的关联关系，缓存到TimeExpirationCache
5. **消息发送**: SipCallInterface生成INVITE后，通过CallId查找Conn发送消息
6. **响应处理**: 接收SIP响应并通过会话事件通知应用层

**被叫会话数据流说明:**
1. **消息接收**: SIP客户端发送INVITE消息 → Conn接收 → 上报至SipRegLineI
2. **映射建立**: SipRegLineI解析INVITE中的CallId，建立CallId与Conn的关联关系
3. **协议处理**: 将INVITE消息转发给SipCallInterface进行协议处理
4. **会话创建**: SipCallInterface触发onCreateSession回调，创建SipRegLineSessionI
5. **监听器创建**: 通过应用层创建SessionListener，建立会话事件处理机制
6. **来电通知**: 通过onCallIncoming回调通知应用层有新的来电请求

### SIP 会话流程

#### 主叫方向 (Outgoing Call)

```mermaid
sequenceDiagram
    participant App as 应用层
    participant RegI as SipRegLineI
    participant SessionMgr as SessionManager
    participant Session as SipRegLineSessionI
    participant ServerReg as ServerRegistration
    participant SipCall as SipCallInterface
    participant Conn as SipRegLineConn
    participant Client as SIP客户端

    App->>+RegI: SipCall(caller, callee)
    RegI->>+SessionMgr: 创建Session
    SessionMgr->>+Session: new SipRegLineSessionI

    Note over RegI: 关键动作1: 查找目标用户连接
    RegI->>+ServerReg: 查找callee对应的Conn
    ServerReg->>-RegI: 返回Conn对象

    Note over RegI: 关键动作2: 指定Call-ID发起呼叫
    RegI->>+SipCall: 发起呼叫(指定CallId)

    Note over RegI: 关键动作3: 建立CallId与Conn映射
    RegI->>RegI: 保存CallId->Conn映射关系

    Note over SipCall: 生成INVITE消息

    SipCall->>+RegI: onSend(message)
    RegI->>RegI: 根据CallId查找Conn
    RegI->>+Conn: sendMessage()
    Conn->>-Client: INVITE消息

    Client->>Conn: SIP响应
    Conn->>RegI: onRecv()
    RegI->>SipCall: RecvSipMsg()
    SipCall->>Session: 会话事件回调
    Session->>-App: 通知会话状态
```

#### 被叫方向 (Incoming Call)

```mermaid
sequenceDiagram
    participant Client as SIP客户端
    participant Conn as SipRegLineConn
    participant RegI as SipRegLineI
    participant SipCall as SipCallInterface
    participant Session as SipRegLineSessionI
    participant App as 应用层

    Client->>+Conn: INVITE消息
    Conn->>+RegI: onRecv(INVITE)

    Note over RegI: 关键动作: 绑定CallId与Conn
    RegI->>RegI: 解析CallId，建立CallId->Conn映射

    RegI->>+SipCall: RecvSipMsg()

    Note over SipCall: 解析INVITE<br/>触发会话创建

    SipCall->>+RegI: onCreateSession()
    RegI->>RegI: 查找目标用户Conn
    RegI->>+Session: new SipRegLineSessionI
    Session->>+App: 创建SessionListener
    App->>-Session: SessionListener
    Session->>-RegI: Session对象
    RegI->>-SipCall: SessionListener

    SipCall->>+Session: onCallIncoming()
    Session->>-App: 来电通知

    Note over App: 应用决策<br/>(接听/拒绝)

    App->>Session: SipAnswer()/SipTerm()
```

## 关键数据映射关系

### 连接管理映射

```mermaid
graph LR
    subgraph "ConnManager缓存"
        A[用户账号] --> B[SipRegLineConn]
        C[CallId] --> B
        D[连接信息] --> B
    end
```

### 会话管理映射

```mermaid
graph LR
    subgraph "SessionManager索引"
        A[SessionId] --> C[SipRegLineSessionI]
        B[CallId] --> C
    end

    subgraph "Session关联"
        C --> D[SipCallSession]
        C --> E[SipRegLineConn]
        C --> F[SessionListener]
    end
```

## 核心设计特点

### 1. 分层解耦设计
- **网络层**: 负责 UDP 连接管理和消息收发
- **协议层**: 处理 SIP 协议解析和状态机
- **会话层**: 管理呼叫会话生命周期
- **应用层**: 提供业务逻辑接口

### 2. 缓存机制
- **连接缓存**: 使用 TimeExpirationCache 缓存 CallId → Conn 映射，TTL=3600秒
- **会话索引**: 支持 SessionId 和 CallId 双重索引快速查找
- **自动清理**: 过期连接和会话自动清理，避免内存泄漏

### 3. 事件驱动架构
- **监听器模式**: 各层通过 Listener 接口解耦
- **回调机制**: 异步事件通过回调函数传递
- **状态同步**: 会话状态变化及时通知应用层

### 4. 线程安全设计
- **互斥锁保护**: 关键数据结构使用 std::mutex 保护
- **会话隔离**: 每个会话独立管理，避免相互影响
- **原子操作**: 连接状态检查使用原子操作

## 使用场景

1. **SIP 用户注册**: 客户端向服务器注册，建立身份认证和连接映射
2. **点对点呼叫**: 通过注册用户之间建立直接的语音/视频通话
3. **会议系统**: 支持多方会话管理和媒体协商
4. **网关服务**: 作为 SIP 网关连接不同网络域的用户

该架构设计确保了 SIP 协议的完整实现，同时提供了灵活的扩展能力和良好的性能表现。
