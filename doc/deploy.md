# SIP 对接网关部署配置

## 部署说明

### 基础配置

新部署的环境应该在 **非 global DC 下** 增加以下服务

* MpCallSipGateway
* MpCallRtpRunnerDaemon

例如 在 DC Main0 的 Signal100 主机上增加服务

* jrtcMpCallSipGateway.Main0.Signal100

    * exe - jrtcMpCallSipGateway-2.2-f8aa35d-linux-x86_64.tgz

* jrtcMpCallRtpRunnerDaemon.Main0.Signal100

    * exe - jrtcMpCallRtpRunnerDaemon-2.2-f8aa35d-linux-x86_64.tgz

针对 jrtcMpCallSipGateway 服务增加线路配置

* SipCall.CoreNetId - 指定线路 ID
* SipCall.LocalAddress - 指定 SIP 地址和端口
* SipCall.RemoteAddress - 指定线路 SIP 接入地址和端口
* SipCall.ExclusiveRtpRunner - 指定是否独占 rtp runner

例如：

* SipCall.CoreNetId=Test
* SipCall.LocalAddress=0.0.0.0:17000
* SipCall.RemoteAddress=**************:13000
* SipCall.ExclusiveRtpRunner=1

基本部署和配置完成。SIP 对接服务支持在多个主机上增加多个服务。需要注意

* 一个 MpCallSipGateway 仅支持指定一个线路
* 如果需要对接多个线路，需要针对每个线路，增加至少一个对应 MpCallSipGateway 服务
* 不同线路的 MpCalLSipGatweay 服务指定不同的 SipCall.CoreNetId 配置
* MpCallSipGateway 独占一个 rtp runner。同一个 dc 下的 rtp runner 与 MpCallSipGateway 共用一个线路。

### 单个线路，单个对接地址，多服务负载

场景说明

1. 对接方仅有一个线路地址
2. 服务需要多个分摊负载

单个 DC 方案，配置举例

1. jrtcMpCallSipGateway.Main0.Host10
    * SipCall.CoreNetId=Sample
    * SipCall.RemoteAddress=*************:13000
2. jrtcMpCallSipGateway.Main0.Host11
    * 配置同上
3. jrtcMpCallRtpRunnerDaemon.Main0.Host10
4. jrtcMpCallRtpRunnerDaemon.Main0.Host11

RtpRunnerDaemon 配置的数量与 SipGateway 的数量没有对应关系，应该根据并发负载情况部署。

多个 DC 方案

1. DC1
    1. jrtcMpCallSipGateway.DC1.Host10
        * SipCall.CoreNetId=Sample
        * SipCall.RemoteAddress=*************:13000
    2. jrtcMpCallRtpRunnerDaemon.DC1.Host10
2. DC2
    1. jrtcMpCallSipGateway.DC2.Host20
        * 配置同上
    2. jrtcMpCallRtpRunnerDaemon.DC2.Host20

每个 DC 下 应该都要 **同时部署** SipGateway 和 RtpRunnerDaemon 服务

### 单个线路，多个线路地址

场景说明

1. 对接方为一个对接系统/核心网
2. 对接方提供多个线路地址

单个 DC 方案，配置举例

1. jrtcMpCallSipGateway.Main0.Host10
    * SipCall.CoreNetId=Sample
    * SipCall.RemoteAddress=*************:13000,3;***************:13000,1**
    * SipCall.KeepAlive=OPTIONS:60
2. jrtcMpCallRtpRunnerDaemon.Main0.Host10

SipGateway 会在多个线路地址进行选择

* 如果存在活跃的地址（即有近期有收到 SIP 消息的地址），则在这些地址中按照指定权重**随机选择**，其中 3 和 1 表示权重
* 否则，在当前有效配置的所有地址中**随机选择**

一般需要配置使用 SIP OPTIONS 消息进行保活。由于活跃的检测时间是 **保活时长+30秒**。
所以，OPTION 保活时长不要配置太长，以免线路异常的时候切换时间过长。

### 多个线路

场景说明

1. 对接方为多个线路，业务可能会选择不同的线路进行呼叫

单个 DC 方案，配置举例

1. jrtcMpCallSipGateway.Main0.Host10
    * SipCall.CoreNetId=Line1
    * SipCall.RemoteAddress=*************:13000
2. jrtcMpCallSipGateway.Main0.Host11
    * SipCall.CoreNetId=**Line2**
    * SipCall.RemoteAddress=***************:13000**
3. jrtcMpCallRtpRunnerDaemon.Main0.Host10
4. jrtcMpCallRtpRunnerDaemon.Main0.Host11

一个 SipGateway 服务只能配置属于一个线路，需要为每个不同线路部署至少一个 SipGateway 服务。

## MpCallSipGateway

|               配置项目                |                  说明                  | 必选 |   缺省值   |                    样例                    |
| :------------------------------------ | -------------------------------------- | ---- | ---------- | ------------------------------------------ |
| SipCall.CoreNetId                     | SIP 线路 ID                            | 是   |            | SipCall.CoreNetId=Test                     |
| SipCall.ASBC                          | 设置 SIP 线路为 A-SBC 模式             |      | 0          | SipCall.ASBC=1                             |
| SipCall.ExclusiveRtpRunner            | 独占 rtp runner                        |      |            | SipCall.ExclusiveRtpRunner=1               |
| SipCall.RemoteAddress                 | SIP 线路地址和端口                     | 是   |            | SipCall.RemoteAddress=**************:13000 |
| SipCall.LocalAddress                  | SIP 本地地址和端口                     |      |            | SipCall.LocalAddress=0.0.0.0:17000         |
| SipCall.ContactEx                     | SIP Contact 头域额外参数               |      |            | SipCall.ContactEx=xxx                      |
| SipCall.CalleePrefix                  | SIP 被叫号码额外前缀                   |      |            | SipCall.CalleePrefix=091                   |
| SipCall.KeepAlive                     | SIP 线路保活配置                       |      | OFF        | SipCall.KeepAlive=OFF                      |
| SipCall.NoListening                   | SIP 是否打开本地端口监听               |      | 0          | SipCall.NoListening=1                      |
| SipCall.LeaveSync                     | 终端离开之后同步结束 SIP 会话          |      | 0          | SipCall.LeaveSync=1                        |
| SipCall.EarlyMedia                    | SIP 是否打开 EarlyMedia                |      | 0          | SipCall.EarlyMedia=1                       |
| SipCall.Precondition                  | SIP 是否打开 Precondition              |      | 0          | SipCall.Precondition=1                     |
| SipCall.ScreenSharingMode             | 是否使用屏幕共享的编码模式             |      | 0          | SipCall.ScreenSharingMode=1                |
| SipCall.AudioExcludeRole              | 过滤成员音频的角色配置                 |      |            | SipCall.AudioExcludeRole=0x40/0x80040      |
| SipCall.Registration.Accounts         | A-SBC账号信息                          |      |            |                                            |
| SipCall.Registration.Realm            | A-SBC账号realm信息                     |      |            | SipCall.Registration.Realm=juphoon.com     |
| SipCall.Registration.LocalPorts       | A-SBC账号本地监听端口范围              |      |            |                                            |
| SipCall.FeatureTag                    | SipGateway流程控制特性标签             |      |            | SipCall.FeatureTag=C09                     |
| SipCall.IncomingDomainId              | 呼入号码的域ID                         |      |            | SipCall.IncomingDomainId=100645/4          |
| SipCall.IncomingCalleeType            | 呼入号码的被叫类型                     |      |            | SipCall.IncomingCalleeType=LegacyApp       |
| SipCall.ResponseTimeoutSeconds        | 线路响应超时时间，单位秒               |      | 4          | SipCall.ResponseTimeoutSeconds=4           |
| SipCall.LineBreak.DetectTimeSeconds   | 线路熔断的检测时间长度，单位秒         |      | 60         | SipCall.LineBreak.DetectTimeSeconds=60     |
| SipCall.LineBreak.FailRatio           | 线路熔断的失败比例，单位百分比         |      | 50         | SipCall.LineBreak.FailRatio=50             |
| SipCall.LineBreak.ContinuousFailCount | 线路熔断的连续失败次数                 |      | 3          | SipCall.LineBreak.ContinuousFailCount=3    |
| SipCall.LineBreak.RecoverySeconds     | 线路熔断恢复时间，单位秒               |      | 60         | SipCall.LineBreak.RecoverySeconds=60       |
| RunnerManager.AllocTimeout            | 分配 Runner 失败重试超时时长，单位秒。 |      | 0          | RunnerManager.AllocTimeout=10              |
| SipGateway.Http.ListenHost            | 统计页面监听地址                       |      | 0.0.0.0    | SipGateway.Http.ListenHost=0.0.0.0         |
| SipGateway.Http.ListenPort            | 统计页面监听端口                       |      | (随机端口) | SipGateway.Http.ListenPort=8080            |

### SipCall.RemoteAddress

管理配置指定线路的地址和端口，即远端的地址和端口

1. 如果未配置 ``SipCall.LocalAddress``，则本地使用系统随机端口
2. 否则，本地地址和端口使用 ``SipCall.LocalAddress`` 配置指定的内容
3. 如果未配置 ``SipCall.RemoteAddress`` 则仅支持被动连接的方式
4. 如果设置多个远端地址，则使用 ; 分隔每个地址，每个地址的格式为 ``<address>:<port>,<weight>``，例如::

        SipCall.RemoteAddress=*************:13000,3;*************:13000,1

    其中，*************:13000 的权重为 3，*************:13000 的权重为 1。如果未指定权重，则默认权重为 1。权重有效值范围 1-9999

#### 重试和熔断

SipGateway 如果配置 ``SipCall.ResponseTimeoutSeconds`` 大于 0，则会启动重试功能。重试处理包括

1. 在指定时间之内，SIP 通话没有收到任何响应，则会使用新的链接重新发起呼叫
2. 如果已经尝试所有链接，则等待当前通话超时结束

SipGateway 会根据 ``SipCall.LineBreak.DetectTimeSeconds`` 配置的时间长度，处理链接熔断。

* ``SipCall.LineBreak.FailRatio`` 配置指定时间范围之内的呼叫失败比例，单位为百分比。配置 0 表示熔断不考虑失败比例
* ``SipCall.LineBreak.ContinuousFailCount`` 配置指定时间范围之内，连续呼叫失败次数。配置 0 表示熔断不考虑连续失败次数

熔断处理包括

1. 熔断状态下，不会选择对应地址进行呼叫
2. 根据配置 ``SipCall.LineBreak.RecoverySeconds`` 等待指定时长之后，恢复线路连接
3. 熔断恢复之后，会重新检测线路是否可用。如果配置了 OPTIONS 保活检测，需要等待保活检测成功之后，才会恢复成可用状态。否则，会立即恢复成可用状态

### SipCall.LocalAddress

指定对接线路时候使用的本地地址和端口

1. 如果配置 ``SipCall.NoListening=1``，则不打开本地端口监听，仅支持主动连接的方式
2. 否则，打开本地端口监听，允许使用被动连接的方式

### 地址配置方案

地址配置可以是以下模式

|                 配置方式                 |      主动连接      | 被动连接 |
| :--------------------------------------- | ------------------ | -------- |
| 同时配置本地和远端地址                   | 支持，指定本地端口 | 支持     |
| 仅配置本地地址                           |                    | 支持     |
| 仅配置远端地址                           | 支持，随机本地端口 |          |
| 同时配置本地和远端地址，设置 NoListening | 支持，指定本地端口 |          |

### SipCall.KeepAlive

线路保活支持以下模式

* OFF - 关闭
* CRLF - 通过发送 SIP PING 报文保活
* OPTIONS - 通过发送 SIP OPTIONS 请求保活
* CRLF OPTIONS - 同时使用 SIP PING 和 OPTIONS 保活

CRLF 和 OPTIONS 保活可以设置间隔时间，例如

* CRLF:30 - 表示 SIP PING 报文发送间隔为 30 秒
* OPTIONS:3600 - 表示 SIP OPTIONS 报文发送间隔为 3600 秒
* CRLF:30 OPTIONS:3600 - 表示间隔 30 秒发送一次 SIP PING，间隔 3600 秒发送一次 SIP OPTIONS

### SipCall.AudioExcludeRole

过滤成员音频的角色配置。配置内容为 <role>/<role_mask>，例如::

    SipCall.AudioExcludeRole=0x40/0x80040

或者可以只配置 <role> 例如以下两个配置为等价配置::

    SipCall.AudioExcludeRole=0x40
    SipCall.AudioExcludeRole=0x40/0x7fffffff

### RunnerManager.AllocTimeout

分配 Runner 失败重试超时时长，单位秒

* 0 -  缺省值，Manager 尝试分配 Runner 失败之后直接返回失败，不进行重试
* \>0 - Manager 分配 Runner 失败之后，间隔 1 秒再次发起重试，直至超过指定时长

### A-SBC 配置方案

启用 A-SBC 模式，增加配置::

    SipCall.ASBC=1

A-SBC 需要配置账号信息，例如::

    SipCall.Registration.Accounts=500|juphoon
    SipCall.Registration.Realm=pbx.juphoon.com
    SipCall.RemoteAddress=***********:5070

如果 AuthName 和 UserName 不同，则::

    SipCall.Registration.Accounts=+*************|+<EMAIL>|89aHZrreLd1R
    SipCall.Registration.Realm=ngv.ims.chinaunicom.cn
    SipCall.RemoteAddress=************:5070

本地端口范围可以不用设置，在不设置的情况下使用随机端口。也可以指定端口范围，例如::

    SipCall.Registration.LocalPorts=5070

或者

    SipCall.Registration.LocalPorts=10000-11000

### SipCall.FeatureTag

SipGateway流程控制特性标签。可选值

- C09 宁波银行流程，SIP呼入的被叫类型为 IVVR

### SipCall.IncomingDomainId

呼入号码的域ID。可选值为 domainId/appId 或者 CalleePrefix

- domainId/appId - 从当前线路呼入的呼叫，使用指定域ID
- CalleePrefix - 从当前线路呼入的呼叫，使用被叫号码6个字符前缀作为域ID

例如::

    # 使用指定域ID，AppId 为 0
    SipCall.IncomingDomainId=100645

    # 使用指定域ID，并指定 AppID
    SipCall.IncomingDomainId=100645/4

    # 使用被叫号码6个字符前缀作为域ID
    SipCall.IncomingDomainId=CalleePrefix

### SipCall.IncomingCalleeType

呼入号码的被叫类型。可以针对 domain 设置类型，也可以设置缺省类型。被叫类型选择规则如下

1. 如果配置了 app 对应的配置，则使用 app 对应的类型
2. 否则，如果设置 domain 对应的配置，则使用 domain 对应的类型
3. 否则，如果设置缺省类型，则使用缺省类型
4. 否则，使用 App 类型

用户类型的配置包括

- App - 使用 App 方式对接
- LegacyApp - 使用 LegacyApp 方式对接，针对集成 SIP1.0 接口的终端
- Sip - 使用 SIP 方式对接
- Vman - 使用 Vman 方式对接
- AcdApp - 使用 AcdApp 方式对接
- H5 - 使用 H5 方式对接
- Wechat - 使用 Wechat 方式对接
- Yuv - 使用 Yuv 方式对接
- Ivvr - 使用 IVVR 方式对接

设置缺省类型配置

    SipCall.IncomingCalleeType=App

设置 domain 配置

    SipCall.IncomingCalleeType.Sip=100645,200000

设置 app 配置

    SipCall.IncomingCalleeType.LegacyApp=100645/4,100000/4

### 统计页面

SipGateway 服务会启动一个 HTTP 服务，提供统计页面。包含以下内容

- 当前会话列表
- 会话SIP消息流程图
- 会话RTP统计信息

![SipGateway 统计页面](./sipgw_stats.png)

## MpCallRtpRunnerDaemon

针对 Daemon 服务的配置

|                   配置项目                   |                说明                 | 必选 |       缺省值       |                       样例                       |
| :------------------------------------------- | ----------------------------------- | ---- | ------------------ | ------------------------------------------------ |
| Daemon.Endpoints                             | Daemon 服务监听地址                 |      | sudp -h 127.0.0.1; | Daemon.Endpoints=sudp -h 127.0.0.1;              |
| MpCallRtpRunner.Count.Max                    | 最大 Runner 数量                    |      | 50                 | MpCallRtpRunner.Count.Max=10                     |
| MpCallRtpRunner.Count.PreStart               | 预启动 Runner 数量                  |      | 1                  | MpCallRtpRunner.Count.PreStart=2                 |
| MpCallRtpRunner.WorkPath                     | Runner 工作目录                     |      | runners            | MpCallRtpRunner.WorkPath=/home/<USER>/log/runners  |
| MpCallRtpRunner.WorkPath.ExpiredDays         | Runner 工作目录超时清理时间，单位天 |      | 30                 | MpCallRtpRunner.WorkPath.ExpiredDays=15          |
| MpCallRtpRunner.WorkPath.ScanIntervalSeconds | Runner 工作目录扫描间隔，单位秒     |      | 3600               | MpCallRtpRunner.WorkPath.ScanIntervalSeconds=600 |
| SipCall.CoreNetId                            | SIP 线路 ID                         |      |                    | SipCall.CoreNetId=Test                           |

针对 Runner 的配置

|                配置项目                 |                 说明                 | 必选 |             缺省值             |                      样例                      |
| :-------------------------------------- | ------------------------------------ | ---- | ------------------------------ | ---------------------------------------------- |
| RtpRunner.RtpWorkPath                   | RTP 模块工作目录                     |      | Runner 工作目录的 jssi 子目录  |                                                |
| RtpRunner.AudioPorts.UpperBound         | 音频端口范围上限                     |      | 17099                          |                                                |
| RtpRunner.AudioPorts.LowerBound         | 音频端口范围下限                     |      | 17000                          |                                                |
| RtpRunner.VideoPorts.UpperBound         | 视频端口范围上限                     |      | 17299                          |                                                |
| RtpRunner.VideoPorts.LowerBound         | 视频端口范围下限                     |      | 17200                          |                                                |
| RtpRunner.VideoConfig.Width             | 协商视频宽度                         |      | 352                            |                                                |
| RtpRunner.VideoConfig.Height            | 协商视频高度                         |      | 288                            |                                                |
| RtpRunner.VideoConfig.FrameRate         | 协商视频帧速率                       |      | 15                             |                                                |
| RtpRunner.VideoConfig.BitRate           | 协商视频码率                         |      | 768                            |                                                |
| RtpRunner.OverrideVideoConfig.Width     | 视频宽度                             |      | 352                            |                                                |
| RtpRunner.OverrideVideoConfig.Height    | 视频高度                             |      | 288                            |                                                |
| RtpRunner.OverrideVideoConfig.FrameRate | 视频帧速率                           |      | 15                             |                                                |
| RtpRunner.OverrideVideoConfig.BitRate   | 视频码率                             |      | 768                            |                                                |
| RtpRunner.KeyFrameInterval              | 视频关键帧间隔，单位秒               |      | 2                              |                                                |
| RtpRunner.DtmfPayloadType               | DTMF Payload Type                    |      | 101                            |                                                |
| RtpRunner.VideoPipeMode                 | 视频管道模式，设置 Merged 为合流模式 |      | 桥接模式，只发送一个成员视频流 | RtpRunner.VideoPipeMode=Merged                 |
| RtpRunner.VideoPipePrioritys            | 视频桥接模式下优先级配置             |      |                                | RtpRunner.VideoPipePrioritys=0x40,0x80/0x80080 |
| RtpRunner.ScreenSharingMode             | 是否使用屏幕共享的编码模式           |      |                                | RtpRunner.ScreenSharingMode=1                  |
| RtpRunner.AudioBandwidth                | 音频SDP带宽配置                      |      | 0,0,0                          | RtpRunner.AudioBandwidth=64,800,2400           |
| RtpRunner.VideoBandwidth                | 视频SDP带宽配置                      |      | 0,0,0                          | RtpRunner.VideoBandwidth=256,800,2400          |

## 配合 AcdServer 使用

缺省 SIP 对接服务是配合 MpCall 服务使用。如果需要配合 AcdServer 使用，需要将服务名称对应修改为

* jrtcMpCallSipGateway -> jrtcAcdSipGateway
* jrtcMpCallRtpRunnerDaemon -> jrtcAcdRtpRunnerDaemon
