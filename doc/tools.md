# SIP 对接网关工具说明

## MpCallSipCli

MpCallSipGateway 测试命令行工具。使用该工具的先决条件

1. 工具需要部署有 Router 服务的主机上
2. 工具需要在 cube 目录下（或者 cube 子目录下）运行，cube 目录中包含 server.cfg 配置文件
3. server.cfg 文件中需要包含 global.Locators 配置
4. 需要给 MpCallSipGatweay 服务增加 MpCallSipGateway.TargetOid=MpCallSipCli 配置（该配置仅用于测试，实际运行应该删除该配置！！！）

启动测试工具

```
./MpCallSipCli dc=DC0 domain=100645 global.Log.Level=3 global.Log.File=cli.log
```

* dc=DC0 指定 DC 信息
* domain=100645 指定 DomainId
* global.Log.Level=3 指定日志级别
* global.Log.File=cli.log 指定输出日志文件

测试呼出

* invite 命令，例如 ``invite 123 18612340000``
* call 命令，例如 ``call 123 18612340000`` 。call 命令和 invite 的区别在于会预先创建房间

测试呼入

* 该程序会自动接听呼入的 SIP 通话

其它命令

* term 命令，结束通话

## MpCallSipDialer

该服务是SIP拨测服务。支持 SIP 呼入和呼出

- SIP 呼入的时候，服务会自动接听
- 可以通过 HTTP 接口发起 SIP 呼出，超过指定超时时间会自动挂断 SIP 通话

|           配置项目            |                         说明                         | 必选 |   缺省值   |                    样例                    |
| :---------------------------- | ---------------------------------------------------- | ---- | ---------- | ------------------------------------------ |
| SipCall.RemoteAddress         | SIP 服务远端地址，需要设置 SipGateway 监听地址       | 是   |            | SipCall.RemoteAddress=**************:13000 |
| SipCall.LocalAddress          | SIP 服务监听地址，需要设置 SipGateway 的远端地址配置 | 是   |            | SipCall.LocalAddress=0.0.0.0:17000         |
| SipDialer.Http.ListenHost     | HTTP 服务监听地址                                    |      | 0.0.0.0    | SipDialer.Http.ListenHost=0.0.0.0          |
| SipDialer.Http.ListenPort     | HTTP 服务监听端口                                    |      | (随机端口) | SipDialer.Http.ListenPort=8080             |
| SipDialer.RtpPorts.UpperBound | RTP 端口范围上限                                     |      | 22000      | SipDialer.RtpPorts.UpperBound=22000        |
| SipDialer.RtpPorts.LowerBound | RTP 端口范围下限                                     |      | 22999      | SipDialer.RtpPorts.LowerBound=22999        |
| SipDialer.EchoMode            | 是否启用回声模式                                     |      | 0          | SipDialer.EchoMode=1                       |
| SipDialer.EchoTimeoutSeconds  | 设置回声模式下通话超时时长，单位秒                   |      | 30         | SipDialer.EchoTimeoutSeconds=30            |

可以通过 http://*************:8080/ 访问拨测服务。

![SipDialer 统计页面](./sipdialer_stats.png)

同时提供接口包括

- /api/v1/call - 呼出接口
- /api/v1/call/status - 会话列表接口
- /api/v1/call/{sessionId}/status - 会话详情接口

### 呼出接口

呼出接口支持 POST 请求地址 `/api/v1/call` 请求体为 JSON 格式，例如::

    {
        "caller": "10086",
        "callee": "10086",
        "video": false,
        "is3pcc": false,
        "timeout": 30,
        "callback": "http://*************:8080/callback"
    }

请求体说明

- caller - 主叫号码
- callee - 被叫号码
- timeout - 超时时间，单位秒
- video - 是否开启视频, 可选，缺省为 false
- is3pcc - 是否模拟第三方呼叫控制流程（3pcc），第一个INVITE中不携带SDP，缺省为 false
- callback - 回调地址，可选，当 SIP 通话结束的时候，会回调该地址

返回值说明

- 200 - 呼出成功，返回 JSON 格式，包含
    - sessionId - 会话ID
    - callId - SIP Call-ID
- 400 - 呼出失败，请求体错误
- 500 - 呼出失败，服务端错误

当通话结束的时候会执行回调请求，请求体为 JSON 格式，例如::

    {
        "sessionId": 1234567890,
        "error": true,
        "reason": "timeout"
    }

请求体说明

- sessionId - 呼叫会话ID
- error - 是否错误
- reason - 结束原因

### 会话列表接口

会话列表接口支持 GET 请求地址 `/api/v1/call/status` 响应返回为 JSON 格式，例如::

    {
        "count": 1,
        "sessions": [
            {
                "sessionId": 1234567890,
                "caller": "10086",
                "callee": "10086",
                "state": "Calling",
                "direction": "outgoing",
                "duration": 100,
                "startTime": "2021-01-01 00:00:00.000",
                "error": false,
                "reason": ""
            }
        ]
    }

响应体说明

- count - 会话数量
- sessions - 会话列表
    - sessionId - 会话ID
    - caller - 主叫号码
    - callee - 被叫号码
    - direction - 会话方向
    - state - 会话状态
    - duration - 会话时长,单位毫秒
    - startTime - 会话开始时间
    - error - 是否错误
    - reason - 结束原因

### 会话详情接口

会话详情接口支持 GET 请求地址 `/api/v1/call/{sessionId}/status` 响应返回为 JSON 格式，例如::

    {
        "sessionId": 1234567890,
        "state": "Calling",
        "rtpStats": {
            "audio": {
                "payloadType": 105,
                "lastPayloadType": 255,
                "overallNetwork": {
                    "bytes": 100,
                    "packets": 100,
                    "bps": 100,
                    "pps": 100
                },
                "currentNetwork": {
                    "bytes": 100,
                    "packets": 100,
                    "bps": 100,
                    "pps": 100
                },
                "overallRtp": {
                    "bytes": 100,
                    "packets": 100,
                    "bps": 100,
                    "pps": 100,
                    "lost": 100,
                    "lostRate": 100
                },
                "currentRtp": {
                    "bytes": 100,
                    "packets": 100,
                    "bps": 100,
                    "pps": 100,
                    "lost": 100,
                    "lostRate": 100
                }
            },
            "video": {
                "payloadType": 255,
                "lastPayloadType": 255,
                "overallNetwork": {
                    "bytes": 100,
                    "packets": 100,
                    "bps": 100,
                    "pps": 100
                },
                "currentNetwork": {
                    "bytes": 100,
                    "packets": 100,
                    "bps": 100,
                    "pps": 100
                },
                "overallRtp": {
                    "bytes": 100,
                    "packets": 100,
                    "bps": 100,
                    "pps": 100,
                    "lost": 100,
                    "lostRate": 100
                },
                "currentRtp": {
                    "bytes": 100,
                    "packets": 100,
                    "bps": 100,
                    "pps": 100,
                    "lost": 100,
                    "lostRate": 100
                }
            }
        }
    }

响应体说明

- sessionId - 会话ID
- state - 会话状态
- rtpStats - RTP 统计信息
    - audio - 音频统计信息
        - payloadType - 负载类型
        - lastPayloadType - 上一次负载类型
        - overallNetwork - 总体网络统计信息
            - bytes - 字节数
            - packets - 包数
            - bps - 比特率
            - pps - 包速率
        - currentNetwork - 当前网络统计信息，格式与 overallNetwork 相同
        - overallRtp - 总体RTP统计信息
            - bytes - 字节数
            - packets - 包数
            - bps - 比特率
            - pps - 包速率
            - lost - 丢包数
            - lostRate - 丢包率
        - currentRtp - 当前RTP统计信息，格式与 overallRtp 相同
    - video - 视频统计信息，格式与 audio 相同
