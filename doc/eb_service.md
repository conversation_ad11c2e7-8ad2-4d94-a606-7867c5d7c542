# 光大定制业务服务

## 概述

光大定制业务服务，主要基于 MpCall 完成针对光大使用中兴排队机完成视频客服的场景。

![总体架构示意图](eb_arch.png)

其中 业务定制服务 EbService 主要功能包括

1. 坐席绑定账号
    1. 坐席调用 EbService 接口获取 SIP 号码
    2. EbService 保存 坐席账号和 SIP 号码之间绑定关系
    3. 坐席保活绑定关系

2. 访客呼入排队邀请坐席
    1. 访客发起呼叫之后，SipGateway 向 EbService 查询对应 SIP 呼叫号码，以及组装 UUI 信息
    2. 排队机将 中兴流水号 设置到 EbService
    3. SipGateway 处理坐席邀请 INVITE 时，向 EbService 查询 SIP 号码对应的 坐席账号，以及对应 MpCall 会话信息。
       SipGateway 通过 MpCall 会话发起对应账号的呼叫
    4. 处理访客的排队信息查询
    5. 坐席接听之后，通过排队机调用 EbService 接口将接听的通知发送给坐席 SDK

3. 保持和取回业务
    1. 坐席保持之后，通过排队机调用 EbService 接口将保持的通知发送给坐席 SDK
    2. 坐席取回之后，通过排队机调用 EbService 接口将取回的通知发送给坐席 SDK


设计目标

1. 定制服务 包含所有定制接口的实现
2. 定制服务 仅处理数据保存，避免保存会话信息以及处理业务流程
3. 定制服务 需要满足双活
4. 降低 MpCall 服务定制处理
5. 尽量保持 HTTP 接口和终端接口不变化

## 服务设计


主要接口

1. 面向 排队机+IVR （HTTP协议）
    1. 设置中兴流水号，将该信息保存，在收到呼入 INVITE 的时候查询
    2. 调用查询排队信息的接口
    3. 提供接口通知接口，向坐席终端发送接听通知
    4. 提供保持接口
    5. 提供取回接口

2. 面向 SipGateway
    1. 查询呼叫号码
        1. 返回中兴配置的区分渠道的号码，SipGateway 使用该号码作为 SIP 被叫号码
        2. 构造 UUI SIP 信息，将 业务定制服务的 HTTP 接口地址增加到 UUI 信息中
    2. 查询账号
        1. 通过中兴呼叫 INVITE 被叫号码查询坐席登录的账号，SipGateway 使用该账号发起 MpCall 呼叫
        2. 通过 UUI 信息查询对应 MpCall 会话信息，SipGateway 通过该会话发起邀请

3. 面向 访客
    1. 提供查询排队信息的接口

4. 面向 坐席
    1. 提供分配账号接口 - 坐席将登录账号与 SIP 呼叫号码进行关联
    2. 提供账号保活接口 - 坐席需要持续保活 账号和 SIP 号码
    3. 通知坐席接听
    4. 提供查询队列信息的接口 ???
    5. 通知坐席保持
    6. 通知坐席取回

## 服务配置

### MpCallEbService

EbService 服务是光大业务定制服务，例如部署

    MpCallEbService.DC0.2-0
    MpCallEbService.DC0.2-1

或者

    MpCallEbService.DC0.Signal100
    MpCallEbService.DC0.Signal101

服务配置

|         配置项目         |                   说明                    | 必选 | 缺省值 |                样例                 |
| :----------------------- | ----------------------------------------- | ---- | ------ | ----------------------------------- |
| Eb.LineCallNumber        | 中兴排队机渠道呼入号码                    | 是   |        | Eb.LineCallNumber=666000            |
| Eb.Agent.CallNumberRange | 坐席呼叫号码的范围,不同服务配置不同的范围 | 是   |        | Eb.Agent.CallNumberRange=1000-1999  |
| Eb.Http.ListenHost       | HTTP 服务监听地址                         | 是   |        | Eb.Http.ListenHost=*************    |
| Eb.Http.ListenPort       | HTTP 服务监听端口                         | 是   |        | Eb.Http.ListenPort=18080            |
| RedisUri                 | Redis 服务地址                            | 是   |        | RedisUri=redis://*************:6379 |

不同服务必须配置相同的号码范围。应该保证号码范围要大于可能签入坐席的总数

### MpCallEbSipGateway

光大增加的定制流程需要设置 MpCallEbSipGateway 服务将业务控制服务设置到 EbService 服务上

|          配置项目           |               说明               | 必选 |  缺省值   |                    样例                    |
| :-------------------------- | -------------------------------- | ---- | --------- | ------------------------------------------ |
| SipCall.CoreNetId           | SIP 线路 ID                      | 是   |           | SipCall.CoreNetId=Test                     |
| SipCall.RemoteAddress       | SIP 线路地址和端口               | 是   |           | SipCall.RemoteAddress=**************:13000 |
| SipCall.LocalAddress        | SIP 本地地址和端口               | 是   |           | SipCall.LocalAddress=0.0.0.0:17000         |
| SipGateway.DummyRtpRunner   | 设置 SipGateway 使用假 RtpRunner |      | 1         | SipGateway.DummyRtpRunner=1                |

当前一个 MpCallEbSipGateway 服务只能设置一个远端地址。如果一台机器上需要同时连接多个远端地址，需要在同一台机器上部署多个 MpCallEbSipGateway 服务。
每个 MpCallEbSipGateway 分别设置一个远端地址。

### MpCallEbDialer

该服务是模拟中兴排队机的拨测服务，当前只能部署一个

|          配置项目          |                         说明                         | 必选 | 缺省值 |                    样例                     |
| :------------------------- | ---------------------------------------------------- | ---- | ------ | ------------------------------------------- |
| EbDialer.CalleeNumberRange | 拨测呼叫坐席的号码范围                               | 是   |        | EbDialer.CalleeNumberRange=1000-1999        |
| EbDialer.HttpUri           | EbService 服务监听 HTTP 地址                         | 是   |        | EbDailer.HttpUri=http://*************:18080 |
| SipCall.RemoteAddress      | SIP 服务远端地址，需要设置 SipGateway 监听地址       | 是   |        | SipCall.RemoteAddress=**************:13000  |
| SipCall.LocalAddress       | SIP 服务监听地址，需要设置 SipGateway 的远端地址配置 | 是   |        | SipCall.LocalAddress=0.0.0.0:17000          |
| RedisUri                   | Redis 服务地址                                       | 是   |        | RedisUri=redis://*************:6379         |

