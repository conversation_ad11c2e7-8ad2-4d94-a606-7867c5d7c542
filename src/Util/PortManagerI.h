//
// *****************************************************************************
// Copyright (c) 2023 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//
// Created on 2023/11/9 by <PERSON>
//

#include "Common/Util.h"
#include "PortManager.h"

namespace SipMpCall
{

class PortsI;
class PortManagerI;
typedef Common::Handle<PortsI> PortsIPtr;
typedef Common::Handle<PortManagerI> PortManagerIPtr;

class PortsI : public Ports
{
public:
    PortsI(const PortManagerIPtr &manager, int port, int count)
        : _manager(manager)
        , _ports{port, port + 1, port + 2, port + 3}
        , _count(count)
    {
    }

    ~PortsI();

    virtual int operator[](int offset) override;

public:
    int _ports[4];
    int _count;
    PortManagerIPtr _manager;
};

class PortManagerI : public PortManager
{
public:
    explicit PortManagerI(int span);

    virtual PortsPtr allocatePort() override;

    bool init(int lowerBound, int upperBound);
    void releasePort(int port);

private:
    bool checkPort(int port);

private:
    Common::Lock _lock;
    const int _span;
    int _basePort;
    int _upperLimit;
    int _spanCount;

    int _nextPort;
    set<int> _allocatedIds;
};


} // namespace SipMpCall