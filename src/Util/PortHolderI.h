//
// *****************************************************************************
// Copyright (c) 2025 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//

#pragma once

#include "PortHolder.h"
#include "Common/Net.h"
#include <vector>

namespace SipMpCall
{

class PortHolderI : public PortHolder
{
public:
    class Listener : virtual public Common::NetReceiver
    {
    public:
        void close();

    public:
        Common::NetSenderPtr _sender;
    };

    typedef Common::Handle<Listener> ListenerPtr;

    PortHolderI(unsigned short startPort, unsigned char portCount);
    virtual ~PortHolderI();

    bool init(const Common::NetDriverPtr &driver);

private:
    ListenerPtr open(const Common::NetDriverPtr &driver, unsigned short port);

private:
    unsigned short _startPort;
    unsigned char _portCount;
    std::vector<ListenerPtr> _listeners;
};

} // namespace SipMpCall
