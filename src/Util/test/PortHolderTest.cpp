//
// *****************************************************************************
// Copyright (c) 2025 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//

#include "gtest/gtest.h"
#include "Util/PortHolder.h"
#include "Common/Util.h"

class PortHolderTest : public ::testing::Test
{
protected:
    void SetUp() override
    {
        Common::setLogLevel(3);
        Common::setLogPrint(true);
        driver = Common::NetDriver::create(1024);
    }

    void TearDown() override
    {
        driver->shutdown();
        driver = nullptr;
    }

protected:
    Common::NetDriverPtr driver;
};

TEST_F(PortHolderTest, create)
{
    SipMpCall::PortHolderPtr portHolder = SipMpCall::PortHolder::create(driver, 10000, 4);
    ASSERT_TRUE(portHolder);

    SipMpCall::PortHolderPtr testPortHolder;
    for (int i = 0; i < 4; i++)
    {
        testPortHolder = SipMpCall::PortHolder::create(driver, 10000 + i, 1);
        ASSERT_FALSE(testPortHolder);
    }

    portHolder = nullptr;

    for (int i = 0; i < 4; i++)
    {
        testPortHolder = SipMpCall::PortHolder::create(driver, 10000 + i, 1);
        ASSERT_TRUE(testPortHolder);
    }
}
