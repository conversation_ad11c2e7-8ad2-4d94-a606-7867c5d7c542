//
// *****************************************************************************
// Copyright (c) 2025 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//

#include "gtest/gtest.h"
#include "Util/TimeExpirationCache.h"

#include <string>
#include <vector>
#include <algorithm>
#include <thread>
#include <chrono>

namespace SipMpCall
{

class TimeExpirationCacheExpiredTest : public ::testing::Test
{
protected:
    void SetUp() override
    {
        // Create cache with 2 seconds TTL for testing
        cache_.reset(new TimeExpirationCache<std::string, std::string>(2));
    }

    void TearDown() override
    {
        cache_.reset();
    }

    std::unique_ptr<TimeExpirationCache<std::string, std::string>> cache_;
};

// Test basic checkExpired functionality
TEST_F(TimeExpirationCacheExpiredTest, BasicCheckExpired)
{
    std::string value;

    // Add some items
    cache_->put("key1", "value1");
    cache_->put("key2", "value2");
    cache_->put("key3", "value3");

    // Verify all items are present
    EXPECT_TRUE(cache_->get("key1", value));
    EXPECT_TRUE(cache_->get("key2", value));
    EXPECT_TRUE(cache_->get("key3", value));
    EXPECT_EQ(3, cache_->size());

    // No items should be expired yet
    auto expired = cache_->checkExpired();
    EXPECT_TRUE(expired.empty());
    EXPECT_EQ(3, cache_->size());

    // Wait for expiration (TTL is 2 seconds)
    std::this_thread::sleep_for(std::chrono::seconds(3));

    // Check expired should return all keys
    expired = cache_->checkExpired();
    EXPECT_EQ(3, expired.size());
    EXPECT_EQ(0, cache_->size());

    // Verify the expired keys
    std::sort(expired.begin(), expired.end());
    std::vector<std::string> expected = {"key1", "key2", "key3"};
    std::sort(expected.begin(), expected.end());
    EXPECT_EQ(expected, expired);
}

// Test partial expiration
TEST_F(TimeExpirationCacheExpiredTest, PartialExpiration)
{
    std::string value;

    // Add first batch of items
    cache_->put("early1", "value1");
    cache_->put("early2", "value2");

    // Wait 1 second
    std::this_thread::sleep_for(std::chrono::seconds(1));

    // Add second batch
    cache_->put("late1", "value3");
    cache_->put("late2", "value4");

    // Wait another 1.5 seconds (total 2.5 seconds)
    std::this_thread::sleep_for(std::chrono::milliseconds(1500));

    // Only early items should be expired
    auto expired = cache_->checkExpired();
    EXPECT_EQ(2, expired.size());
    EXPECT_EQ(2, cache_->size());

    // Verify expired keys are the early ones
    std::sort(expired.begin(), expired.end());
    std::vector<std::string> expected = {"early1", "early2"};
    std::sort(expected.begin(), expected.end());
    EXPECT_EQ(expected, expired);

    // Verify late items are still present
    EXPECT_TRUE(cache_->get("late1", value));
    EXPECT_TRUE(cache_->get("late2", value));

    // Early items should be gone
    EXPECT_FALSE(cache_->get("early1", value));
    EXPECT_FALSE(cache_->get("early2", value));
}

// Test access prevents expiration
TEST_F(TimeExpirationCacheExpiredTest, AccessPreventsExpiration)
{
    std::string value;

    // Add items
    cache_->put("accessed", "value1");
    cache_->put("not_accessed", "value2");

    // Wait 1.5 seconds and access one item
    std::this_thread::sleep_for(std::chrono::milliseconds(1500));
    EXPECT_TRUE(cache_->get("accessed", value)); // This should reset TTL

    // Wait another 1 second (total 2.5 seconds)
    std::this_thread::sleep_for(std::chrono::seconds(1));

    // Only the non-accessed item should be expired
    auto expired = cache_->checkExpired();
    EXPECT_EQ(1, expired.size());
    EXPECT_EQ("not_accessed", expired[0]);
    EXPECT_EQ(1, cache_->size());

    // Accessed item should still be present
    EXPECT_TRUE(cache_->get("accessed", value));
    EXPECT_EQ("value1", value);
}

// Test multiple checkExpired calls
TEST_F(TimeExpirationCacheExpiredTest, MultipleCheckExpiredCalls)
{
    // Add items at different times
    cache_->put("key1", "value1");

    std::this_thread::sleep_for(std::chrono::milliseconds(500));
    cache_->put("key2", "value2");

    std::this_thread::sleep_for(std::chrono::milliseconds(500));
    cache_->put("key3", "value3");

    // Wait for first item to expire
    std::this_thread::sleep_for(std::chrono::milliseconds(1200)); // Total 2.2 seconds for key1

    auto expired = cache_->checkExpired();
    EXPECT_EQ(1, expired.size());
    EXPECT_EQ("key1", expired[0]);
    EXPECT_EQ(2, cache_->size());

    // Second call should find no new expired items
    expired = cache_->checkExpired();
    EXPECT_TRUE(expired.empty());
    EXPECT_EQ(2, cache_->size());

    // Wait for second item to expire (but not third)
    std::this_thread::sleep_for(std::chrono::milliseconds(400)); // Total: key2=2100ms, key3=1600ms

    expired = cache_->checkExpired();
    EXPECT_EQ(1, expired.size());
    EXPECT_EQ("key2", expired[0]);
    EXPECT_EQ(1, cache_->size());
}

// Test checkExpired with empty cache
TEST_F(TimeExpirationCacheExpiredTest, CheckExpiredEmptyCache)
{
    EXPECT_TRUE(cache_->empty());

    auto expired = cache_->checkExpired();
    EXPECT_TRUE(expired.empty());
    EXPECT_TRUE(cache_->empty());
}

// Test checkExpired efficiency with many items
TEST_F(TimeExpirationCacheExpiredTest, EfficiencyWithManyItems)
{
    const int num_items = 1000;

    // Add many items
    for (int i = 0; i < num_items; ++i)
    {
        cache_->put("key" + std::to_string(i), "value" + std::to_string(i));
    }

    EXPECT_EQ(num_items, cache_->size());

    // Wait for expiration
    std::this_thread::sleep_for(std::chrono::seconds(3));

    // Check expired should efficiently remove all items
    auto start = std::chrono::high_resolution_clock::now();
    auto expired = cache_->checkExpired();
    auto end = std::chrono::high_resolution_clock::now();

    EXPECT_EQ(num_items, expired.size());
    EXPECT_EQ(0, cache_->size());

    // Should complete reasonably fast (less than 100ms for 1000 items)
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);
    EXPECT_LT(duration.count(), 100);
}

// Test SIP scenario: periodic cleanup of registration cache
TEST_F(TimeExpirationCacheExpiredTest, SipRegistrationCleanup)
{
    struct SipRegistration
    {
        std::string user;
        std::string contact;
        int expires;
    };

    TimeExpirationCache<std::string, SipRegistration> reg_cache(2); // 2 second TTL

    // Add some registrations
    SipRegistration reg1 = {"<EMAIL>", "sip:alice@*************:5060", 3600};
    SipRegistration reg2 = {"<EMAIL>", "sip:bob@*************:5060", 3600};
    SipRegistration reg3 = {"<EMAIL>", "sip:charlie@*************:5060", 3600};

    reg_cache.put("<EMAIL>", reg1);
    reg_cache.put("<EMAIL>", reg2);
    reg_cache.put("<EMAIL>", reg3);

    EXPECT_EQ(3, reg_cache.size());

    // Simulate periodic cleanup after expiration
    std::this_thread::sleep_for(std::chrono::seconds(3));

    auto expired_users = reg_cache.checkExpired();
    EXPECT_EQ(3, expired_users.size());
    EXPECT_EQ(0, reg_cache.size());

    // Verify all users were expired
    std::sort(expired_users.begin(), expired_users.end());
    std::vector<std::string> expected = {"<EMAIL>", "<EMAIL>", "<EMAIL>"};
    std::sort(expected.begin(), expected.end());
    EXPECT_EQ(expected, expired_users);
}

} // namespace SipMpCall
