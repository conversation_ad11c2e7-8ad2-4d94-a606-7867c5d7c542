//
// *****************************************************************************
// Copyright (c) 2025 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//

#include "gtest/gtest.h"
#include "Util/TimeExpirationCache.h"

#include <string>
#include <memory>
#include <thread>
#include <chrono>

namespace SipMpCall
{

class TimeExpirationCacheTest : public ::testing::Test
{
protected:
    void SetUp() override
    {
        // Create cache with 3 seconds TTL for testing
        cache_.reset(new TimeExpirationCache<std::string, std::string>(3));
    }

    void TearDown() override
    {
        cache_.reset();
    }

    std::unique_ptr<TimeExpirationCache<std::string, std::string>> cache_;
};

// Test basic put and get operations
TEST_F(TimeExpirationCacheTest, BasicPutAndGet)
{
    std::string value;

    // Initially empty
    EXPECT_FALSE(cache_->get("key1", value));

    // Put and get
    cache_->put("key1", "value1");
    EXPECT_TRUE(cache_->get("key1", value));
    EXPECT_EQ("value1", value);

    // Update existing key
    cache_->put("key1", "value1_updated");
    EXPECT_TRUE(cache_->get("key1", value));
    EXPECT_EQ("value1_updated", value);
}

// Test multiple key-value pairs
TEST_F(TimeExpirationCacheTest, MultipleKeys)
{
    std::string value;

    // Put multiple values
    cache_->put("key1", "value1");
    cache_->put("key2", "value2");
    cache_->put("key3", "value3");

    // Get all values
    EXPECT_TRUE(cache_->get("key1", value));
    EXPECT_EQ("value1", value);

    EXPECT_TRUE(cache_->get("key2", value));
    EXPECT_EQ("value2", value);

    EXPECT_TRUE(cache_->get("key3", value));
    EXPECT_EQ("value3", value);

    // Non-existent key
    EXPECT_FALSE(cache_->get("key4", value));
}

// Test TTL expiration
TEST_F(TimeExpirationCacheTest, TTLExpiration)
{
    std::string value;

    // Put value
    cache_->put("key1", "value1");
    EXPECT_TRUE(cache_->get("key1", value));
    EXPECT_EQ("value1", value);

    // Wait for expiration (TTL is 3 seconds)
    std::this_thread::sleep_for(std::chrono::seconds(4));

    // Should be expired now
    EXPECT_FALSE(cache_->get("key1", value));
}

// Test access refreshes TTL
TEST_F(TimeExpirationCacheTest, AccessRefreshesTTL)
{
    std::string value;

    // Put value
    cache_->put("key1", "value1");

    // Access every 2 seconds for 6 seconds total
    for (int i = 0; i < 3; ++i)
    {
        std::this_thread::sleep_for(std::chrono::seconds(2));
        EXPECT_TRUE(cache_->get("key1", value));
        EXPECT_EQ("value1", value);
    }

    // Should still be valid because we've been accessing it
    EXPECT_TRUE(cache_->get("key1", value));
    EXPECT_EQ("value1", value);

    // Now wait without access
    std::this_thread::sleep_for(std::chrono::seconds(4));
    EXPECT_FALSE(cache_->get("key1", value));
}

// Test partial expiration
TEST_F(TimeExpirationCacheTest, PartialExpiration)
{
    std::string value;

    // Put first value
    cache_->put("key1", "value1");

    // Wait 2 seconds, then put second value
    std::this_thread::sleep_for(std::chrono::seconds(2));
    cache_->put("key2", "value2");

    // Wait 2 more seconds (total 4 seconds)
    std::this_thread::sleep_for(std::chrono::seconds(2));

    // key1 should be expired (4 seconds old), key2 should be valid (2 seconds old)
    EXPECT_FALSE(cache_->get("key1", value));
    EXPECT_TRUE(cache_->get("key2", value));
    EXPECT_EQ("value2", value);
}

// Test different value types
TEST_F(TimeExpirationCacheTest, IntegerValues)
{
    TimeExpirationCache<std::string, int> int_cache(3);
    int value;

    int_cache.put("int_key", 42);
    EXPECT_TRUE(int_cache.get("int_key", value));
    EXPECT_EQ(42, value);

    int_cache.put("int_key", 100);
    EXPECT_TRUE(int_cache.get("int_key", value));
    EXPECT_EQ(100, value);
}

// Test with custom struct
struct TestData
{
    std::string name;
    int age;

    bool operator==(const TestData& other) const
    {
        return name == other.name && age == other.age;
    }
};

TEST_F(TimeExpirationCacheTest, CustomStructValues)
{
    TimeExpirationCache<std::string, TestData> struct_cache(3);
    TestData value;

    TestData data1{"Alice", 25};
    TestData data2{"Bob", 30};

    struct_cache.put("user1", data1);
    struct_cache.put("user2", data2);

    EXPECT_TRUE(struct_cache.get("user1", value));
    EXPECT_EQ(data1, value);

    EXPECT_TRUE(struct_cache.get("user2", value));
    EXPECT_EQ(data2, value);
}

// Test zero TTL (immediate expiration)
TEST_F(TimeExpirationCacheTest, ZeroTTL)
{
    TimeExpirationCache<std::string, std::string> zero_cache(0);
    std::string value;

    zero_cache.put("key1", "value1");

    // With 0 TTL, items should expire immediately
    EXPECT_FALSE(zero_cache.get("key1", value));
}

// Test very short TTL
TEST_F(TimeExpirationCacheTest, ShortTTL)
{
    TimeExpirationCache<std::string, std::string> short_cache(1); // 1 second TTL
    std::string value;

    short_cache.put("key1", "value1");

    // Should be available immediately
    EXPECT_TRUE(short_cache.get("key1", value));
    EXPECT_EQ("value1", value);

    // Wait a bit more than 1 second
    std::this_thread::sleep_for(std::chrono::milliseconds(1100));

    // Should be expired
    EXPECT_FALSE(short_cache.get("key1", value));
}

// Test concurrent access patterns (basic thread safety considerations)
TEST_F(TimeExpirationCacheTest, ConcurrentAccess)
{
    std::string value;

    // Put initial value
    cache_->put("shared_key", "initial_value");

    // Create two threads: one reading, one writing
    std::thread reader([this]() {
        std::string read_value;
        for (int i = 0; i < 10; ++i)
        {
            cache_->get("shared_key", read_value);
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }
    });

    std::thread writer([this]() {
        for (int i = 0; i < 5; ++i)
        {
            cache_->put("shared_key", "updated_value_" + std::to_string(i));
            std::this_thread::sleep_for(std::chrono::milliseconds(200));
        }
    });

    reader.join();
    writer.join();

    // Final check
    EXPECT_TRUE(cache_->get("shared_key", value));
    EXPECT_TRUE(value.find("updated_value_") == 0); // Should be one of the updated values
}

// Test SIP-specific use case: registration cache
TEST_F(TimeExpirationCacheTest, SipRegistrationUseCase)
{
    struct SipRegistration
    {
        std::string user;
        std::string contact;
        int expires;

        bool operator==(const SipRegistration& other) const
        {
            return user == other.user && contact == other.contact && expires == other.expires;
        }
    };

    TimeExpirationCache<std::string, SipRegistration> reg_cache(5); // 5 seconds TTL
    SipRegistration reg_value;

    // Simulate SIP registration
    SipRegistration reg1{"<EMAIL>", "sip:alice@*************:5060", 3600};
    reg_cache.put("<EMAIL>", reg1);

    // Check registration status
    EXPECT_TRUE(reg_cache.get("<EMAIL>", reg_value));
    EXPECT_EQ(reg1, reg_value);

    // Update contact (re-registration)
    SipRegistration reg1_updated{"<EMAIL>", "sip:alice@*************:5060", 3600};
    reg_cache.put("<EMAIL>", reg1_updated);

    EXPECT_TRUE(reg_cache.get("<EMAIL>", reg_value));
    EXPECT_EQ(reg1_updated, reg_value);
}

// Test SIP-specific use case: DNS cache
TEST_F(TimeExpirationCacheTest, DnsCacheUseCase)
{
    TimeExpirationCache<std::string, std::string> dns_cache(2); // 2 seconds TTL
    std::string ip;

    // DNS resolution
    dns_cache.put("sip.example.com", "************");
    dns_cache.put("proxy.example.com", "************");

    // Use cached DNS results
    EXPECT_TRUE(dns_cache.get("sip.example.com", ip));
    EXPECT_EQ("************", ip);

    EXPECT_TRUE(dns_cache.get("proxy.example.com", ip));
    EXPECT_EQ("************", ip);

    // Wait for expiration
    std::this_thread::sleep_for(std::chrono::seconds(3));

    // Should need re-resolution
    EXPECT_FALSE(dns_cache.get("sip.example.com", ip));
    EXPECT_FALSE(dns_cache.get("proxy.example.com", ip));
}

} // namespace SipMpCall
