//
// *****************************************************************************
// Copyright (c) 2025 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//

#include "Util/PortManager.h"
#include "Common/Util.h"
#include "gtest/gtest.h"

class PortManagerTest : public ::testing::Test
{
protected:
    void SetUp(int span)
    {
        Common::setLogLevel(3);
        Common::setLogPrint(true);
        portManager = SipMpCall::PortManager::create(10000, 10000 + 99, span);
    }

    void TearDown() override
    {
        portManager = nullptr;
    }

protected:
    SipMpCall::PortManagerPtr portManager;
};

TEST_F(PortManagerTest, create)
{
    SetUp(2);
    ASSERT_TRUE(portManager);

    for (int j = 0; j < 2; j++)
    {
        for (int i = 0; i < 50; i++)
        {
            SipMpCall::PortsPtr ports = portManager->allocatePort();
            ASSERT_TRUE(ports);
            ASSERT_EQ((*ports)[0], 10000 + i * 2);
            ASSERT_EQ((*ports)[1], 10000 + i * 2 + 1);
        }
    }

    for (int j = 0; j < 2; j++)
    {
        std::vector<SipMpCall::PortsPtr> ports;
        for (int i = 0; i < 50; i++)
        {
            SipMpCall::PortsPtr port = portManager->allocatePort();
            ASSERT_TRUE(port);
            ports.push_back(port);
        }

        SipMpCall::PortsPtr port = portManager->allocatePort();
        ASSERT_FALSE(port);
        ports.clear();
    }
}

TEST_F(PortManagerTest, create_span_4)
{
    SetUp(4);
    ASSERT_TRUE(portManager);

    for (int j = 0; j < 2; j++)
    {
        for (int i = 0; i < 25; i++)
        {
            SipMpCall::PortsPtr ports = portManager->allocatePort();
            ASSERT_TRUE(ports);
            ASSERT_EQ((*ports)[0], 10000 + i * 4);
            ASSERT_EQ((*ports)[1], 10000 + i * 4 + 1);
            ASSERT_EQ((*ports)[2], 10000 + i * 4 + 2);
            ASSERT_EQ((*ports)[3], 10000 + i * 4 + 3);
        }
    }

    for (int j = 0; j < 2; j++)
    {
        std::vector<SipMpCall::PortsPtr> ports;
        for (int i = 0; i < 25; i++)
        {
            SipMpCall::PortsPtr port = portManager->allocatePort();
            ASSERT_TRUE(port);
            ports.push_back(port);
        }

        SipMpCall::PortsPtr port = portManager->allocatePort();
        ASSERT_FALSE(port);
        ports.clear();
    }
}
