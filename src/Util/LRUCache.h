//
// *****************************************************************************
// Copyright (c) 2025 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//

#pragma once

#include <list>
#include <unordered_map>

namespace SipMpCall
{

template <typename Key, typename Value>
class LRUCache
{
public:
    explicit LRUCache(size_t capacity)
        : capacity_(capacity)
    {
    }

    bool get(const Key &key, Value &value)
    {
        auto it = cache_map_.find(key);
        if (it == cache_map_.end())
            return false;

        // Move the accessed element to the front of the list
        cache_list_.splice(cache_list_.begin(), cache_list_, it->second);
        value = it->second->second;
        return true;
    }

    void put(const Key &key, const Value &value)
    {
        auto it = cache_map_.find(key);
        if (it != cache_map_.end())
        {
            // Update the value and move the element to the front of the list
            it->second->second = value;
            cache_list_.splice(cache_list_.begin(), cache_list_, it->second);
        }
        else
        {
            if (cache_list_.size() == capacity_)
            {
                // Remove the least recently used element
                auto last = cache_list_.end();
                --last;
                cache_map_.erase(last->first);
                cache_list_.pop_back();
            }
            // Insert the new element at the front of the list
            cache_list_.emplace_front(key, value);
            cache_map_[key] = cache_list_.begin();
        }
    }

private:
    size_t capacity_;
    std::list<std::pair<Key, Value>> cache_list_;
    std::unordered_map<Key, typename std::list<std::pair<Key, Value>>::iterator> cache_map_;
};

} // namespace SipMpCall
