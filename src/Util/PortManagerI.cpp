//
// *****************************************************************************
// Copyright (c) 2023 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//
// Created on 2023/11/9 by <PERSON>
//

#include "PortManagerI.h"
#include "Common/Util.h"
#include <exception>

namespace SipMpCall
{

PortsI::~PortsI()
{
    _manager->releasePort(_ports[0]);
}

int PortsI::operator[](int offset)
{
    if (offset < 0 || offset >= _count)
        throw Common::Exception("InvalidOffset");

    return _ports[offset];
}

PortManagerI::PortManagerI(int span)
    : _span(span)
    , _basePort(0)
    , _spanCount(0)
    , _nextPort(0)
    , _upperLimit(0)
{
    if (span <= 0 || span > 4)
        throw Common::Exception("InvalidSpan");
}

bool PortManagerI::init(int lowerBound, int upperBound)
{
    int spanCount = (upperBound - lowerBound + 1) / _span;
    if (spanCount == 0)
    {
        UTIL_LOG_ERR("PortManager", "content:init invalid ports:" + Common::String(lowerBound) + "/" + Common::String(upperBound));
        return false;
    }

    _basePort = lowerBound;
    _spanCount = spanCount;
    _nextPort = _basePort;
    _upperLimit = _basePort + _span * _spanCount;
    UTIL_LOG_IFO("PortManager", "content:init ports:" + Common::String(lowerBound) + "/" + Common::String(upperBound) + " base:" + Common::String(_basePort) + " limit:" + Common::String(_upperLimit) + " count:" + Common::String(_spanCount));
    return true;
}

PortsPtr PortManagerI::allocatePort()
{
    PortsIPtr ports;

    _lock.lock();

    if (_allocatedIds.size() >= _spanCount)
    {
        UTIL_LOG_WRN("PortManager", "content:alloc failed, allocated:" + Common::String((int)_allocatedIds.size()));
        _lock.unlock();
        return 0;
    }

    for (int i = 0; i < _spanCount; i++)
    {
        if (_allocatedIds.find(_nextPort) == _allocatedIds.end())
        {
            ports = new PortsI(this, _nextPort, _span);
            _allocatedIds.insert(_nextPort);
            UTIL_LOG_IFO("PortManager", "content:alloc port:" + Common::String(_nextPort) + " count:" + Common::String(_span));
        }

        _nextPort += _span;
        if (_nextPort >= _upperLimit)
            _nextPort = _basePort;

        if (ports)
            break;
    }

    _lock.unlock();
    return ports;
}

void PortManagerI::releasePort(int port)
{
    _lock.lock();
    UTIL_LOG_IFO("PortManager", "content:release port:" + Common::String(port));
    _allocatedIds.erase(port);
    _lock.unlock();
}

PortManagerPtr PortManager::create(int lowerBound, int upperBound, int span)
{
    try
    {
        PortManagerIPtr manager = new PortManagerI(span);
        if (!manager->init(lowerBound, upperBound))
            throw Common::Exception("InitFailed");
        UTIL_LOG_IFO("PortManager", "content:create ports:" + Common::String(lowerBound) + "-" + Common::String(upperBound) + " span:" + Common::String(span));
        return manager;
    }
    catch (std::exception &e)
    {
        UTIL_LOG_ERR("PortManager", "content:create failed ports:" + Common::String(lowerBound) + "-" + Common::String(upperBound) + " span:" + Common::String(span) + " reason:" + e.what());
    }

    return 0;
}

} // namespace SipMpCall