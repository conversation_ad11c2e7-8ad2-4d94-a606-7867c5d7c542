//
// *****************************************************************************
// Copyright (c) 2025 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//

#pragma once

#include "Common/Util.h"

#include <mutex>
#include <map>

namespace SipMpCall
{

template<int MaxType, typename T>
class IndexManager
{
public:
    struct ObjectId
    {
        ObjectId()
            : type(0)
        {
        }

        ObjectId(int type, const std::string &id)
            : type(type)
            , id(id)
        {
        }

        int type;
        std::string id;
    };

public:
    void insert(const ObjectId &objectId, const Common::ObjectPtr &object)
    {
        std::lock_guard<std::mutex> lock(_mutex);
        _indexMap[objectId.type][objectId.id] = object;
    }

    template <typename... Args>
    void remove(Args... sessionIds)
    {
        std::lock_guard<std::mutex> lock(_mutex);
        (void)std::initializer_list<int>{(_indexMap[sessionIds.type].erase(sessionIds.id), 0)...};
    }

    void remove(const ObjectId &objectId, const std::vector<ObjectId> &sessionIds)
    {
        std::lock_guard<std::mutex> lock(_mutex);
        _indexMap[objectId.type].erase(objectId.id);
        for (const auto &sessId : sessionIds)
        {
            _indexMap[sessId.type].erase(sessId.id);
        }
    }

    Common::ObjectPtr getObject(const ObjectId &objectId) const
    {
        std::lock_guard<std::mutex> lock(_mutex);
        auto it = _indexMap[objectId.type].find(objectId.id);
        if (it != _indexMap[objectId.type].end())
            return it->second;
        return nullptr;
    }

protected:
    mutable std::mutex _mutex;
    std::map<std::string, Common::ObjectPtr> _indexMap[MaxType];
};

};
