//
// *****************************************************************************
// Copyright (c) 2025 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//

#pragma once

#include <memory>
#include "Common/Net.h"

namespace SipMpCall
{

class PortHolder;
typedef std::shared_ptr<PortHolder> PortHolderPtr;

class PortHolder : public std::enable_shared_from_this<PortHolder>
{
public:
    static PortHolderPtr create(const Common::NetDriverPtr &driver, unsigned short startPort, unsigned char portCount);
};

} // namespace SipMpCall
