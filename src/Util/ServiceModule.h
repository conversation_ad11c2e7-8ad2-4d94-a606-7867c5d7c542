//
// *****************************************************************************
// Copyright (c) 2023 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//
// Created on 2023/12/13 by <PERSON>
//

#pragma once

#include "Common/Util.h"

namespace Service
{

class ServiceModule : virtual public Common::Shared
{
public:
    virtual bool onActivate(Common::String &failReason) = 0;
    virtual void onDeactivate() {}
    virtual void onUpdateConfigs() {}
};

} // namespace Service