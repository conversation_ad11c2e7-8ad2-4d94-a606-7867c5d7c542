//
// *****************************************************************************
// Copyright (c) 2025 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//

#include "PortHolderI.h"

namespace SipMpCall
{

void PortHolderI::Listener::close()
{
    if (_sender)
    {
        _sender->close();
        _sender = 0;
    }
}

PortHolderI::PortHolderI(unsigned short startPort, unsigned char portCount)
    : _startPort(startPort)
    , _portCount(portCount)
{
}

PortHolderI::~PortHolderI()
{
    for (ListenerPtr listener : _listeners)
    {
        listener->close();
    }
    _listeners.clear();
}

bool PortHolderI::init(const Common::NetDriverPtr &driver)
{
    for (unsigned short port = _startPort; port < _startPort + _portCount; ++port)
    {
        ListenerPtr listener = open(driver, port);
        if (!listener)
        {
            return false;
        }

        _listeners.push_back(listener);
    }

    return true;
}

PortHolderI::ListenerPtr PortHolderI::open(const Common::NetDriverPtr &driver, unsigned short port)
{
    ListenerPtr listener = new Listener();
    listener->_sender = driver->listen("udp", "0.0.0.0", port, listener);
    if (!listener->_sender)
    {
        UTIL_LOGFMT_ERR("PortHolder", "content:listen failed, port: %d", port);
        return 0;
    }

    UTIL_LOGFMT_DBG("PortHolder", "content:listen success, port: %d", port);
    return listener;
}

PortHolderPtr PortHolder::create(const Common::NetDriverPtr &driver, unsigned short startPort, unsigned char portCount)
{
    std::shared_ptr<PortHolderI> portHolder = std::make_shared<PortHolderI>(startPort, portCount);
    if (!portHolder->init(driver))
    {
        return nullptr;
    }

    return portHolder->shared_from_this();
}

} // namespace SipMpCall
