//
// *****************************************************************************
// Copyright (c) 2023 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//
// Created on 2023/11/9 by <PERSON>
//

#include "Common/Util.h"

namespace SipMpCall
{

class Ports : virtual public Common::Shared
{
public:
    virtual int operator[](int offset) = 0;
};

typedef Common::Handle<Ports> PortsPtr;

class PortManager;
typedef Common::Handle<PortManager> PortManagerPtr;

class PortManager : virtual public Common::Shared
{
public:
    static PortManagerPtr create(int lowerBound, int upperBound, int span = 2);
    virtual PortsPtr allocatePort() = 0;
};

} // namespace SipMpCall