aux_source_directory(. UTIL_SRC)
add_library(Util ${UTIL_SRC})
target_include_directories(Util PRIVATE
    ${DEFAULT_INCLUDE_DIRECTORIES}
)
target_compile_definitions(Util PRIVATE
    ${DEFAULT_COMPILE_DEFINITIONS}
)

# UnitTest
aux_source_directory(test UTIL_TEST_SRC)
add_executable(UtilUnitTest ${UTIL_TEST_SRC})
target_include_directories(UtilUnitTest PRIVATE
    ${DEFAULT_INCLUDE_DIRECTORIES}
    ${PROJECT_SOURCE_DIR}/3rd
)
target_compile_definitions(UtilUnitTest PRIVATE
    ${DEFAULT_COMPILE_DEFINITIONS}
)

target_link_libraries(UtilUnitTest Util JsmLog
    ${DEFAULT_SERVER_LIBRARIES}
    $ENV{JssSharedPath}/lib/${DEFAULT_LIB_PATH}/libgtest.a
    $ENV{JssSharedPath}/lib/${DEFAULT_LIB_PATH}/libgmock.a
    $ENV{JssSharedPath}/lib/${DEFAULT_LIB_PATH}/libgtest_main.a
    ${DEFAULT_SYS_LIBRARIES}
)
include(GoogleTest)
gtest_discover_tests(UtilUnitTest)