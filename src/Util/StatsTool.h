//
// *****************************************************************************
// Copyright (c) 2025 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//

#pragma once

#include <map>
#include <string>

namespace SipMpCall
{

class StatsTool
{
public:
    void reset()
    {
        for (auto &kv : _stats)
            kv.second.clear();
    }

    void add(const std::string &key, int id)
    {
        if (_stats[key].empty())
        {
            _stats[key][id] = id;
            return;
        }

        auto last = _stats[key].rbegin();
        if (last->second + 1 == id)
        {
            last->second = id;
            return;
        }

        _stats[key][id] = id;
    }

    std::map<std::string, std::string> getStats()
    {
        std::map<std::string, std::string> stats;

        for (auto &kv : _stats)
        {
            std::string content;
            int count = 0;

            for (auto &kv2 : kv.second)
            {
                if (kv2.first == kv2.second)
                {
                    content += std::to_string(kv2.first) + " ";
                    count++;
                }
                else
                {
                    content += std::to_string(kv2.first) + "-" + std::to_string(kv2.second) + " ";
                    count += kv2.second - kv2.first + 1;
                }
            }

            stats[kv.first] = "count:" + std::to_string(count) + " ids:" + content;
        }

        return stats;
    }

private:
    std::map<std::string, std::map<int, int>> _stats;
};

} // namespace SipMpCall
