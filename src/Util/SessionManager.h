//
// *****************************************************************************
// Copyright (c) 2025 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//

#pragma once

#include "IndexManager.h"

namespace SipMpCall
{

template<int MaxType, typename T>
class SessionIndexManager : public IndexManager<MaxType, T>
{
public:
    class Session : virtual public Common::Shared
    {
    public:
        virtual void close() = 0;
        virtual bool schd(T *t) = 0;

    protected:
        void insert(IndexManager<MaxType, T> *manager, typename IndexManager<MaxType, T>::ObjectId id)
        {
            manager->insert(id, this);
            _sessionIds.push_back(id);
        }

        void remove(IndexManager<MaxType, T> *manager)
        {
            for (const auto &id : _sessionIds)
                manager->remove(id);
            _sessionIds.clear();
        }

    protected:
        std::vector<typename IndexManager<MaxType, T>::ObjectId> _sessionIds;
    };

    typedef Common::Handle<Session> SessionPtr;

public:
    SessionIndexManager()
        : _keepTermedSession(false)
    {
    }

    void reset()
    {
        std::set<SessionPtr> sessions;

        do
        {
            std::lock_guard<std::mutex> lock(this->_mutex);
            for (int i = 0; i < MaxType; ++i)
            {
                for (auto kv : this->_indexMap[i])
                    sessions.insert(SessionPtr::dynamicCast(kv.second));
            }
        } while (0);

        for (auto session : sessions)
            session->close();
    }

    void schd(T *t)
    {
        std::set<SessionPtr> sessions;

        do
        {
            std::lock_guard<std::mutex> lock(this->_mutex);
            for (int i = 0; i < MaxType; ++i)
            {
                for (auto kv : this->_indexMap[i])
                    sessions.insert(SessionPtr::dynamicCast(kv.second));
            }
        } while (0);

        for (auto it = sessions.begin(); it != sessions.end();)
        {
            if (!(*it)->schd(t))
                it = sessions.erase(it);
            else
                ++it;
        }

        if (!_keepTermedSession)
        {
            for (auto &session : sessions)
                session->close();
        }
    }

    void setKeepTermedStatus(bool keepTermedSession)
    {
        std::lock_guard<std::mutex> lock(this->_mutex);
        _keepTermedSession = keepTermedSession;
    }

protected:
    bool _keepTermedSession;
};

};