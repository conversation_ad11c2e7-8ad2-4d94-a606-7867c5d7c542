//
// *****************************************************************************
// Copyright (c) 2025 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//

#include "gmock/gmock.h"
#include "Util/SessionManager.h"

namespace SipMpCall
{

template <int MaxType, typename T>
class SessionIndexManagerSessionMock : public SessionIndexManager<MaxType, T>::Session
{
public:
    MOCK_METHOD(void, close, (), (override));
    MOCK_METHOD(bool, schd, (T * t), (override));
};

} // namespace SipMpCall
