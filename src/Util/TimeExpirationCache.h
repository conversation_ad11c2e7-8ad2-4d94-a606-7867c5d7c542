//
// *****************************************************************************
// Copyright (c) 2025 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//

#pragma once

#include <unordered_map>
#include <chrono>
#include <vector>
#include <set>

namespace SipMpCall
{

/**
 * @brief Time-based expiration cache
 *
 * This cache automatically expires items based on time-to-live (TTL).
 * Suitable for scenarios where data freshness is critical,
 * such as SIP registration status, DNS cache, or authentication tokens.
 */
template <typename Key, typename Value>
class TimeExpirationCache
{
public:
    explicit TimeExpirationCache(uint32_t ttl_seconds)
        : ttl_seconds_(ttl_seconds)
    {
    }

    bool get(const Key &key, Value &value)
    {
        auto it = cache_map_.find(key);
        if (it == cache_map_.end())
            return false;

        // Check if the item has expired
        if (isExpired(it->second.timestamp))
        {
            // Remove from both cache and expiration set
            ExpirationEntry expired_entry = {it->second.timestamp, key};
            expiration_set_.erase(expired_entry);
            cache_map_.erase(it);
            return false;
        }

        // Update timestamp on access to extend TTL
        auto old_timestamp = it->second.timestamp;
        auto new_timestamp = getCurrentTime();

        // Remove old entry from expiration set
        ExpirationEntry old_entry = {old_timestamp, key};
        expiration_set_.erase(old_entry);

        // Update timestamp in cache
        it->second.timestamp = new_timestamp;
        value = it->second.value;

        // Add new entry to expiration set
        ExpirationEntry new_entry = {new_timestamp, key};
        expiration_set_.insert(new_entry);

        return true;
    }

    void put(const Key &key, const Value &value)
    {
        auto now = getCurrentTime();

        // Remove from expiration set if key already exists
        auto existing_it = cache_map_.find(key);
        if (existing_it != cache_map_.end())
        {
            ExpirationEntry old_entry = {existing_it->second.timestamp, key};
            expiration_set_.erase(old_entry);
        }

        TimestampedValue timestamped_value;
        timestamped_value.value = value;
        timestamped_value.timestamp = now;

        cache_map_[key] = timestamped_value;

        // Add to expiration set
        ExpirationEntry new_entry = {now, key};
        expiration_set_.insert(new_entry);
    }

    /**
     * @brief Check and remove expired items
     * @return Vector of expired keys that were removed
     */
    std::vector<Key> checkExpired()
    {
        std::vector<Key> expired_keys;
        auto now = getCurrentTime();

        // Find expired items from the beginning of the sorted set
        auto it = expiration_set_.begin();
        while (it != expiration_set_.end())
        {
            if (!isExpired(it->timestamp))
            {
                // Since the set is sorted by timestamp, no more expired items
                break;
            }

            // Remove from cache and add to expired list
            expired_keys.push_back(it->key);
            cache_map_.erase(it->key);

            // Remove from expiration set and advance iterator
            it = expiration_set_.erase(it);
        }

        return expired_keys;
    }

    /**
     * @brief Get the number of items in cache
     */
    size_t size() const
    {
        return cache_map_.size();
    }

    /**
     * @brief Check if cache is empty
     */
    bool empty() const
    {
        return cache_map_.empty();
    }

    /**
     * @brief Clear all items from cache
     */
    void clear()
    {
        cache_map_.clear();
        expiration_set_.clear();
    }

    /**
     * @brief Check if a key exists and is not expired
     */
    bool contains(const Key &key)
    {
        auto it = cache_map_.find(key);
        if (it == cache_map_.end())
            return false;

        if (isExpired(it->second.timestamp))
        {
            // Remove expired item
            ExpirationEntry entry = {it->second.timestamp, key};
            expiration_set_.erase(entry);
            cache_map_.erase(it);
            return false;
        }

        return true;
    }

private:
    struct TimestampedValue
    {
        Value value;
        std::chrono::steady_clock::time_point timestamp;
    };

    // Entry for expiration tracking, sorted by timestamp
    struct ExpirationEntry
    {
        std::chrono::steady_clock::time_point timestamp;
        Key key;

        // Comparison operator for std::set ordering
        bool operator<(const ExpirationEntry& other) const
        {
            if (timestamp != other.timestamp)
                return timestamp < other.timestamp;
            return key < other.key;  // Handle equal timestamps
        }
    };

    std::chrono::steady_clock::time_point getCurrentTime() const
    {
        return std::chrono::steady_clock::now();
    }

    bool isExpired(const std::chrono::steady_clock::time_point &timestamp) const
    {
        auto duration = std::chrono::duration_cast<std::chrono::seconds>(getCurrentTime() - timestamp);
        return duration.count() >= static_cast<long>(ttl_seconds_);
    }

private:
    uint32_t ttl_seconds_;
    std::unordered_map<Key, TimestampedValue> cache_map_;
    std::set<ExpirationEntry> expiration_set_;  // Sorted by timestamp for efficient expiration checking
};

} // namespace SipMpCall