//
// *****************************************************************************
// Copyright (c) 2025 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//

#include "gtest/gtest.h"
#include "SipAdapter/StatsDecorder.h"

TEST(StatsDecorder, Audio)
{
    std::string stats = R"(
Sent Statistics:
  Packets:        595
  Lost:           N/A
  Lost Ratio:     N/A
  Jitter:         N/A
  Bitrate:        68.3
      FEC:        0.0
   RedNum:        0
Received Statistics:
  Packets:        0
  Lost:           N/A
  Lost Ratio:     N/A
  Jitter:         N/A
  Bitrate:        0.0
  EpdRate/lr/dc:  98/0/0
  TMos:           0.0
General statistics:
  RTT:            N/A
  Netw Status:    Unknown
Configuration:
  Mic:
    Channel Mute: off
    Global Mute:  off
    System Mute:  off
  Spk:
    Channel Mute: off
    Global Mute:  off
    System Mute:  off
  Local IP:       0.0.0.0
  Local Port:     10540
  Remote IP:      **************
  Remote Port:    22062
  RTCP Mux:       off
  SRTP:           off
  Codec:          PCMU
  Payload:        0
  Bitrate:        64000
  Pkt Len:        20
  Nack:           off
  RTX:            off
  FEC/RED:        off
  Ext APM:        off
  AEC:            on
    Mode:         Adaptive-Sde
    HowlSupp:     Auto
    Sts:          Auto
  AGC:            on
    Mode:         Fixed
    Target:       3
    Gain:         9
  Rx AGC:         off
    Mode:         Fixed
    Target:       3
    Gain:         9
  VAD:            off
    Mode:         Normal
  ANR:            on
    Mode:         High
    Noise:        very faint:4 dB
    SNR:          moderate:30 dB
  Rx ANR:         off
    Mode:         Low
  ARS:            N/A
    BR Min:       N/A
    BR Max:       N/A
)";
    SipClient::StatsDecorder decorder;

    EXPECT_TRUE(decorder.decode(stats));
    EXPECT_STREQ(decorder.get("Sent Statistics#Packets").c_str(), "595");
    EXPECT_STREQ(decorder.get("Sent Statistics#Lost").c_str(), "N/A");
    EXPECT_STREQ(decorder.get("Sent Statistics#Lost Ratio").c_str(), "N/A");
    EXPECT_STREQ(decorder.get("Sent Statistics#Jitter").c_str(), "N/A");
    EXPECT_STREQ(decorder.get("Sent Statistics#Bitrate").c_str(), "68.3");
    EXPECT_STREQ(decorder.get("Sent Statistics#Bitrate#FEC").c_str(), "0.0");
    EXPECT_STREQ(decorder.get("Sent Statistics#Bitrate#RedNum").c_str(), "0");
    EXPECT_STREQ(decorder.get("Received Statistics#Packets").c_str(), "0");
    EXPECT_STREQ(decorder.get("Received Statistics#Lost").c_str(), "N/A");
    EXPECT_STREQ(decorder.get("Received Statistics#Lost Ratio").c_str(), "N/A");
    EXPECT_STREQ(decorder.get("Received Statistics#Jitter").c_str(), "N/A");
    EXPECT_STREQ(decorder.get("Received Statistics#Bitrate").c_str(), "0.0");
    EXPECT_STREQ(decorder.get("Received Statistics#EpdRate/lr/dc").c_str(), "98/0/0");
    EXPECT_STREQ(decorder.get("Received Statistics#TMos").c_str(), "0.0");
    EXPECT_STREQ(decorder.get("General statistics#RTT").c_str(), "N/A");
    EXPECT_STREQ(decorder.get("General statistics#Netw Status").c_str(), "Unknown");
    EXPECT_STREQ(decorder.get("Configuration#Mic#Channel Mute").c_str(), "off");
    EXPECT_STREQ(decorder.get("Configuration#Mic#Global Mute").c_str(), "off");
    EXPECT_STREQ(decorder.get("Configuration#Mic#System Mute").c_str(), "off");
    EXPECT_STREQ(decorder.get("Configuration#Spk#Channel Mute").c_str(), "off");
    EXPECT_STREQ(decorder.get("Configuration#Spk#Global Mute").c_str(), "off");
    EXPECT_STREQ(decorder.get("Configuration#Spk#System Mute").c_str(), "off");
    EXPECT_STREQ(decorder.get("Configuration#Local IP").c_str(), "0.0.0.0");
    EXPECT_STREQ(decorder.get("Configuration#Local Port").c_str(), "10540");
    EXPECT_STREQ(decorder.get("Configuration#Remote IP").c_str(), "**************");
    EXPECT_STREQ(decorder.get("Configuration#Remote Port").c_str(), "22062");
    EXPECT_STREQ(decorder.get("Configuration#RTCP Mux").c_str(), "off");
    EXPECT_STREQ(decorder.get("Configuration#SRTP").c_str(), "off");
    EXPECT_STREQ(decorder.get("Configuration#Codec").c_str(), "PCMU");
    EXPECT_STREQ(decorder.get("Configuration#Payload").c_str(), "0");
    EXPECT_STREQ(decorder.get("Configuration#Bitrate").c_str(), "64000");
    EXPECT_STREQ(decorder.get("Configuration#Pkt Len").c_str(), "20");
    EXPECT_STREQ(decorder.get("Configuration#Nack").c_str(), "off");
    EXPECT_STREQ(decorder.get("Configuration#RTX").c_str(), "off");
    EXPECT_STREQ(decorder.get("Configuration#FEC/RED").c_str(), "off");
    EXPECT_STREQ(decorder.get("Configuration#Ext APM").c_str(), "off");
    EXPECT_STREQ(decorder.get("Configuration#AEC").c_str(), "on");
    EXPECT_STREQ(decorder.get("Configuration#AEC#Mode").c_str(), "Adaptive-Sde");
    EXPECT_STREQ(decorder.get("Configuration#AEC#HowlSupp").c_str(), "Auto");
    EXPECT_STREQ(decorder.get("Configuration#AEC#Sts").c_str(), "Auto");
    EXPECT_STREQ(decorder.get("Configuration#AGC").c_str(), "on");
    EXPECT_STREQ(decorder.get("Configuration#AGC#Mode").c_str(), "Fixed");
    EXPECT_STREQ(decorder.get("Configuration#AGC#Target").c_str(), "3");
    EXPECT_STREQ(decorder.get("Configuration#AGC#Gain").c_str(), "9");
    EXPECT_STREQ(decorder.get("Configuration#Rx AGC").c_str(), "off");
    EXPECT_STREQ(decorder.get("Configuration#Rx AGC#Mode").c_str(), "Fixed");
    EXPECT_STREQ(decorder.get("Configuration#Rx AGC#Target").c_str(), "3");
    EXPECT_STREQ(decorder.get("Configuration#Rx AGC#Gain").c_str(), "9");
    EXPECT_STREQ(decorder.get("Configuration#VAD").c_str(), "off");
    EXPECT_STREQ(decorder.get("Configuration#VAD#Mode").c_str(), "Normal");
    EXPECT_STREQ(decorder.get("Configuration#ANR").c_str(), "on");
    EXPECT_STREQ(decorder.get("Configuration#ANR#Mode").c_str(), "High");
    EXPECT_STREQ(decorder.get("Configuration#ANR#Noise").c_str(), "very faint:4 dB");
    EXPECT_STREQ(decorder.get("Configuration#ANR#SNR").c_str(), "moderate:30 dB");
    EXPECT_STREQ(decorder.get("Configuration#Rx ANR").c_str(), "off");
    EXPECT_STREQ(decorder.get("Configuration#Rx ANR#Mode").c_str(), "Low");
    EXPECT_STREQ(decorder.get("Configuration#ARS").c_str(), "N/A");
    EXPECT_STREQ(decorder.get("Configuration#ARS#BR Min").c_str(), "N/A");
    EXPECT_STREQ(decorder.get("Configuration#ARS#BR Max").c_str(), "N/A");
}

TEST(StatsDecorder, Video)
{
    std::string stats = R"(
Sent Statistics:
  Packets:        167
  Lost:           N/A
  Lost Ratio:     N/A
  Jitter:         N/A
  FPS/Key-F/SLI-F:14/0/0
  Resolution:     240x432
  Bitrate(Total): N/A
    Cdc/Set:      16/768
    FEC:          N/A
    NACK:         N/A
    redNum:       0
    QP:           19
  Codec:          H264:OpenH264
  SpMos:          5.0
  PSNR:           42.74
  EncodeTime:     0
Received Statistics:
  Packets:        947
  Lost:           0
  Lost Ratio:     0
  Jitter:         6
  Framerate:      30
  Resolution:     352x640
  Bitrate:        494
  Codec:          H264
  PvMos:          5.0
  DecodeTime:     1
General Statistics:
  Capture Res:    720x1280
  Capture Fr:     21
  Render Fr:      30
  RTT:            N/A
  Netw Status:    Good
  CPU Load:       N/A
Configuration:
  Capture:        Camera
  Local IP:       0.0.0.0
  Local Port:     10644
  Remote IP:      **************
  Remote Port:    22064
  RTCP Mux:       off
  SRTP:           off
  Codec:          H264
  Payload:        121
  Bitrate:        768
  Framerate:      15
  Resolution:     352x288
  Auto Fallback:  N/A
  FEC:            off|124|123
  FIR:            off
  Key Interval:   2
    Repeat:       0
  NACK:           off
  RTX:            off
  TMMBR:          off
  RPSI:           off
  Small NALU:     on
  ARS:            off
    BR Min:       10
    BR Max:       2000
    FR Min:       1
    FR Max:       30
  Res. Ctrl:      off
  Res. Mode:      1
  Fr Ctrl:        off
  CPU Load Ctrl:  off
    Target:       80
  Bw Efficient:   on
  Error Conceal:  off
  Enhance color:  off
  Boost bright:   off
  Boost contrast: off
  RTP Ext:        N/A
  Render Name:    jssiCallStream
)";
    SipClient::StatsDecorder decorder;

    EXPECT_TRUE(decorder.decode(stats));
    EXPECT_STREQ(decorder.get("Sent Statistics#Packets").c_str(), "167");
    EXPECT_STREQ(decorder.get("Sent Statistics#Lost").c_str(), "N/A");
    EXPECT_STREQ(decorder.get("Sent Statistics#Lost Ratio").c_str(), "N/A");
    EXPECT_STREQ(decorder.get("Sent Statistics#Jitter").c_str(), "N/A");
    EXPECT_STREQ(decorder.get("Sent Statistics#FPS/Key-F/SLI-F").c_str(), "14/0/0");
    EXPECT_STREQ(decorder.get("Sent Statistics#Resolution").c_str(), "240x432");
    EXPECT_STREQ(decorder.get("Sent Statistics#Bitrate(Total)").c_str(), "N/A");
    EXPECT_STREQ(decorder.get("Sent Statistics#Bitrate(Total)#Cdc/Set").c_str(), "16/768");
    EXPECT_STREQ(decorder.get("Sent Statistics#Bitrate(Total)#FEC").c_str(), "N/A");
    EXPECT_STREQ(decorder.get("Sent Statistics#Bitrate(Total)#NACK").c_str(), "N/A");
    EXPECT_STREQ(decorder.get("Sent Statistics#Bitrate(Total)#redNum").c_str(), "0");
    EXPECT_STREQ(decorder.get("Sent Statistics#Bitrate(Total)#QP").c_str(), "19");
    EXPECT_STREQ(decorder.get("Sent Statistics#Codec").c_str(), "H264:OpenH264");
    EXPECT_STREQ(decorder.get("Sent Statistics#SpMos").c_str(), "5.0");
    EXPECT_STREQ(decorder.get("Sent Statistics#PSNR").c_str(), "42.74");
    EXPECT_STREQ(decorder.get("Sent Statistics#EncodeTime").c_str(), "0");
    EXPECT_STREQ(decorder.get("Received Statistics#Packets").c_str(), "947");
    EXPECT_STREQ(decorder.get("Received Statistics#Lost").c_str(), "0");
    EXPECT_STREQ(decorder.get("Received Statistics#Lost Ratio").c_str(), "0");
    EXPECT_STREQ(decorder.get("Received Statistics#Jitter").c_str(), "6");
    EXPECT_STREQ(decorder.get("Received Statistics#Framerate").c_str(), "30");
    EXPECT_STREQ(decorder.get("Received Statistics#Resolution").c_str(), "352x640");
    EXPECT_STREQ(decorder.get("Received Statistics#Bitrate").c_str(), "494");
    EXPECT_STREQ(decorder.get("Received Statistics#Codec").c_str(), "H264");
    EXPECT_STREQ(decorder.get("Received Statistics#PvMos").c_str(), "5.0");
    EXPECT_STREQ(decorder.get("Received Statistics#DecodeTime").c_str(), "1");
    EXPECT_STREQ(decorder.get("General Statistics#Capture Res").c_str(), "720x1280");
    EXPECT_STREQ(decorder.get("General Statistics#Capture Fr").c_str(), "21");
    EXPECT_STREQ(decorder.get("General Statistics#Render Fr").c_str(), "30");
    EXPECT_STREQ(decorder.get("General Statistics#RTT").c_str(), "N/A");
    EXPECT_STREQ(decorder.get("General Statistics#Netw Status").c_str(), "Good");
    EXPECT_STREQ(decorder.get("General Statistics#CPU Load").c_str(), "N/A");
    EXPECT_STREQ(decorder.get("Configuration#Capture").c_str(), "Camera");
    EXPECT_STREQ(decorder.get("Configuration#Local IP").c_str(), "0.0.0.0");
    EXPECT_STREQ(decorder.get("Configuration#Local Port").c_str(), "10644");
    EXPECT_STREQ(decorder.get("Configuration#Remote IP").c_str(), "**************");
    EXPECT_STREQ(decorder.get("Configuration#Remote Port").c_str(), "22064");
    EXPECT_STREQ(decorder.get("Configuration#RTCP Mux").c_str(), "off");
    EXPECT_STREQ(decorder.get("Configuration#SRTP").c_str(), "off");
    EXPECT_STREQ(decorder.get("Configuration#Codec").c_str(), "H264");
    EXPECT_STREQ(decorder.get("Configuration#Payload").c_str(), "121");
    EXPECT_STREQ(decorder.get("Configuration#Bitrate").c_str(), "768");
    EXPECT_STREQ(decorder.get("Configuration#Framerate").c_str(), "15");
    EXPECT_STREQ(decorder.get("Configuration#Resolution").c_str(), "352x288");
    EXPECT_STREQ(decorder.get("Configuration#Auto Fallback").c_str(), "N/A");
    EXPECT_STREQ(decorder.get("Configuration#FEC").c_str(), "off|124|123");
    EXPECT_STREQ(decorder.get("Configuration#FIR").c_str(), "off");
    EXPECT_STREQ(decorder.get("Configuration#Key Interval").c_str(), "2");
    EXPECT_STREQ(decorder.get("Configuration#Key Interval#Repeat").c_str(), "0");
    EXPECT_STREQ(decorder.get("Configuration#NACK").c_str(), "off");
    EXPECT_STREQ(decorder.get("Configuration#RTX").c_str(), "off");
    EXPECT_STREQ(decorder.get("Configuration#TMMBR").c_str(), "off");
    EXPECT_STREQ(decorder.get("Configuration#RPSI").c_str(), "off");
    EXPECT_STREQ(decorder.get("Configuration#Small NALU").c_str(), "on");
    EXPECT_STREQ(decorder.get("Configuration#ARS").c_str(), "off");
    EXPECT_STREQ(decorder.get("Configuration#ARS#BR Min").c_str(), "10");
    EXPECT_STREQ(decorder.get("Configuration#ARS#BR Max").c_str(), "2000");
    EXPECT_STREQ(decorder.get("Configuration#ARS#FR Min").c_str(), "1");
    EXPECT_STREQ(decorder.get("Configuration#ARS#FR Max").c_str(), "30");
    EXPECT_STREQ(decorder.get("Configuration#Res. Ctrl").c_str(), "off");
    EXPECT_STREQ(decorder.get("Configuration#Res. Mode").c_str(), "1");
    EXPECT_STREQ(decorder.get("Configuration#Fr Ctrl").c_str(), "off");
    EXPECT_STREQ(decorder.get("Configuration#CPU Load Ctrl").c_str(), "off");
    EXPECT_STREQ(decorder.get("Configuration#CPU Load Ctrl#Target").c_str(), "80");
    EXPECT_STREQ(decorder.get("Configuration#Bw Efficient").c_str(), "on");
    EXPECT_STREQ(decorder.get("Configuration#Error Conceal").c_str(), "off");
    EXPECT_STREQ(decorder.get("Configuration#Enhance color").c_str(), "off");
    EXPECT_STREQ(decorder.get("Configuration#Boost bright").c_str(), "off");
    EXPECT_STREQ(decorder.get("Configuration#Boost contrast").c_str(), "off");
    EXPECT_STREQ(decorder.get("Configuration#RTP Ext").c_str(), "N/A");
    EXPECT_STREQ(decorder.get("Configuration#Render Name").c_str(), "jssiCallStream");
}