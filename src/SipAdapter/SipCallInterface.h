//
// *****************************************************************************
// Copyright (c) 2023 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//
// Created on 2023/5/24 by <PERSON>
//

#pragma once

#include "Common/Common.h"

namespace SipClient
{

class SipCallSession;
typedef Common::Handle<SipCallSession> SipCallSessionPtr;

enum SipCallSessionEventType
{
    SipCallSessionEventIncoming,
    SipCallSessionEventOutgoing,
    SipCallSessionEventAltered,
    SipCallSessionEventAnswered,
    Sip<PERSON>allSessionEventUpdate,
    SipCallSessionEventRequestUpdate,
    SipCallSessionEventResponseUpdate,
    SipCallSessionEventConnected,
    SipCallSessionEventRequestModify,
    SipCallSessionEventResponseModify,
    SipCallSessionEventTermed,
    SipCallSessionEventOther
};

class SipCallSessionListener : virtual public Common::Shared
{
public:
    virtual void onCallIncoming(const SipCallSessionPtr &session, const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) {}
    virtual void onCallOutgoing(const SipCallSessionPtr &session, const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) {}
    virtual void onCallAlerted(const SipCallSessionPtr &session, const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) {}
    virtual void onCallAnswered(const SipCallSessionPtr &session, const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) {}
    virtual void onCallUpdate(const SipCallSessionPtr &session, const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) {}
    virtual void onCallRequestUpdate(const SipCallSessionPtr &session, const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) {}
    virtual void onCallResponseUdate(const SipCallSessionPtr &session, const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) {}
    virtual void onCallConnected(const SipCallSessionPtr &session, const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) {}
    virtual void onCallRequestModify(const SipCallSessionPtr &session, const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) {}
    virtual void onCallResponseModify(const SipCallSessionPtr &session, const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) {}
    virtual void onCallTerminated(const SipCallSessionPtr &session, const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) {}
};

typedef Common::Handle<SipCallSessionListener> SipCallSessionListenerPtr;

typedef std::map<std::string, std::string> SipCallExtHdrs;

class SipCallSession : virtual public Common::Shared
{
public:
    virtual unsigned int sessId() = 0;

    virtual bool SipSetContactUri(const std::string &pcContactUri) = 0;
    virtual bool SipAlert(const std::string &pcAnswerSdp) = 0;
    virtual bool SipAnswer(const std::string &pcAnswerSdp, const SipCallExtHdrs &pcExtHdrs) = 0;
    virtual bool SipTerm() = 0;
    virtual bool SipUpdate(const std::string &pcOfferSdp) = 0;
    virtual bool SipUpdateRsp(const std::string &pcAnswerSdp) = 0;
    virtual bool SipAck(const std::string &pcAnswerSdp) = 0;

    virtual bool GetCalledUri(std::string &ppcDispName, std::string &ppcUri) = 0;
    virtual bool GetPeerUri(std::string &ppcDispName, std::string &ppcUri) = 0;
    virtual bool GetCallTermedReason(std::string &ppcSipPhase, std::string &ppcReason) = 0;
};

class SipCallListener : virtual public Common::Shared
{
public:
    virtual bool onSend(const std::string &message) = 0;

    virtual SipCallSessionListenerPtr onCreateSession(const SipCallSessionPtr &session) = 0;
};

typedef Common::Handle<SipCallListener> SipCallListenerPtr;

struct SipCallConfig
{
    SipCallConfig()
        : earlyMedia(false)
        , precondition(false)
    {
    }

    std::string workPath;
    std::string callNumber;
    bool earlyMedia;
    bool precondition;
};

class SipCallInterface;
typedef Common::Handle<SipCallInterface> SipCallInterfacePtr;

class SipCallInterface : virtual public Common::Shared
{
public:
    // Get instance with initialization (creates and initializes if not exists)
    static SipCallInterfacePtr getInstance(const Common::ApplicationPtr &application, const SipCallConfig &config, const SipCallListenerPtr &listener, bool &created);
    static SipCallInterfacePtr getInstance();
    static void destroyInstance();
    static bool injectInstance(const SipCallInterfacePtr &sipCall);

    virtual bool addListener(const SipCallListenerPtr &listener) = 0;
    virtual bool GetSipSdkVer(std::string &ppcLemonVer, std::string &ppcAvatarVer) = 0;
    virtual bool RecvSipMsg(const std::string &message) = 0;

    virtual SipCallSessionPtr SipCall(const SipCallSessionListenerPtr &listener, const std::string &pcCallerUri, const std::string &pcCalleeUri, const std::string &pcCallId, const std::string &pcContactUri, const std::string &pcOfferSdp, const std::string &pcUriParms, const SipCallExtHdrs &pcExtHdrs) = 0;
};

} // namespace SipClient
