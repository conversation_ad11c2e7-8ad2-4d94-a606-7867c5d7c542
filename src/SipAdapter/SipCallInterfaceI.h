//
// *****************************************************************************
// Copyright (c) 2023 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//
// Created on 2023/5/25 by <PERSON>
//

#include "Common/Util.h"
#include "SipCallInterface.h"
#include "jssi/jssi_sip_call.h"

namespace SipClient
{

class SipCallSessionEvent : public Common::Shared
{
public:
    SipCallSessionEvent(const SipClient::SipCallSessionListenerPtr &_listener, const SipClient::SipCallSessionPtr &_session, int _eventType, const std::string &_sdp, unsigned int _statusCode, bool _suptVideo, const std::string &_msg)
        : listener(_listener)
        , session(_session)
        , eventType(_eventType)
        , sdp(_sdp)
        , statusCode(_statusCode)
        , suptVideo(_suptVideo)
        , msg(_msg)
    {
    }

    SipClient::SipCallSessionListenerPtr listener;
    SipClient::SipCallSessionPtr session;
    int eventType;
    std::string sdp;
    unsigned int statusCode;
    bool suptVideo;
    std::string msg;
};

typedef Common::Handle<SipCallSessionEvent> SipCallSessionEventPtr;

class SipCallSessionEventProcessor : public Common::AppEventProcessor
{
public:
    void onProcEvent(const Common::ObjectPtr &event) override;
};

class SipCallSessionI : public SipCallSession
{
public:
    SipCallSessionI(Common::RecMutex &mutex, const Common::ApplicationPtr &application)
        : _sessId(0xffffffff)
        , _mutex(mutex)
        , _termed(false)
        , _eventArray(application->createEventArray(new SipCallSessionEventProcessor()))
    {
    }

    virtual unsigned int sessId() override;

    virtual bool SipSetContactUri(const std::string &pcContactUri) override;
    virtual bool SipAlert(const std::string &pcAnswerSdp) override;
    virtual bool SipAnswer(const std::string &pcAnswerSdp, const std::map<std::string, std::string> &pcExtHdrs) override;
    virtual bool SipTerm() override;
    virtual bool SipUpdate(const std::string &pcOfferSdp) override;
    virtual bool SipUpdateRsp(const std::string &pcAnswerSdp) override;
    virtual bool SipAck(const std::string &pcAnswerSdp) override;

    virtual bool GetCalledUri(std::string &ppcDispName, std::string &ppcUri) override;
    virtual bool GetPeerUri(std::string &ppcDispName, std::string &ppcUri) override;
    virtual bool GetCallTermedReason(std::string &ppcSipPhase, std::string &ppcReason) override;

    void onCallEvent(unsigned int iEventType, const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg);

private:
    enum SipCallSessionEventType eventSip2Session(unsigned int iEventType);

private:
    Common::RecMutex &_mutex;
    bool _termed;
    unsigned int _sessId;
    SipCallSessionListenerPtr _listener;
    Common::AppEventArrayPtr _eventArray;

    friend class SipCallInterfaceI;
};

typedef Common::Handle<SipCallSessionI> SipCallSessionIPtr;

class SipCallInterfaceI : public SipCallInterface, public Common::Thread
{
public:
    SipCallInterfaceI(const Common::ApplicationPtr &application, const SipCallListenerPtr &listener)
        : _application(application)
        , _listener(listener)
        , _lastCheckTicks(Common::getCurTicks())
    {
    }

    bool SipInit(const SipCallConfig &pstSipConfig);
    void SipRelease();

    virtual bool addListener(const SipCallListenerPtr &listener) override;
    virtual bool GetSipSdkVer(std::string &ppcLemonVer, std::string &ppcAvatarVer) override;
    virtual bool RecvSipMsg(const std::string &message) override;
    virtual SipCallSessionPtr SipCall(const SipCallSessionListenerPtr &listener, const std::string &pcCallerUri, const std::string &pcCalleeUri, const std::string &pcCallId, const std::string &pcContactUri, const std::string &pcOfferSdp, const std::string &pcUriParms, const std::map<std::string, std::string> &pcExtHdrs) override;

    virtual void onRun() override;

    void onEvent(void *zEvent);
    void onSipCallEvent(unsigned int iSessId, unsigned int iEventType, const char *pcSdp, unsigned int iStatCode, bool bSuptVideo, const char *pcSipMsg);
    void onSend(unsigned char *pucBuf, unsigned iLen);

    static bool readExtHdrs(const char **ppcExtHdrs, int size, const std::map<std::string, std::string> &pcExtHdrs);

private:
    void __checkTermed();
    void addSession(const SipCallSessionIPtr &session);
    void removeSession(const SipCallSessionIPtr &session);

private:
    Common::RecMutex _mutex;
    std::vector<void *> _events;
    Common::ApplicationPtr _application;
    SipCallListenerPtr _listener;
    std::set<SipCallListenerPtr> _listeners;
    unsigned int _lastCheckTicks;
    std::map<unsigned int, SipCallSessionIPtr> _sessions;
};

typedef Common::Handle<SipCallInterfaceI> SipCallInterfaceIPtr;

} // namespace SipClient