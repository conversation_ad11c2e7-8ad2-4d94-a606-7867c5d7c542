//
// *****************************************************************************
// Copyright (c) 2023 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//
// Created on 2023/5/25 by <PERSON>
//

#include "Common/Util.h"
#include "SipCallInterface.h"
#include "SipCallInterfaceI.h"

#include <mutex>

namespace SipClient
{

static int JssiOnEvent(void *zEvnt)
{
    SipCallInterfaceIPtr sipClient = SipCallInterfaceIPtr::dynamicCast(SipCallInterface::getInstance());
    if (sipClient)
        sipClient->onEvent(zEvnt);
    return 0;
}

static void JssiOnSipCallEvent(unsigned iSessId, unsigned iEvntType, const char *pcSdp, unsigned iStatCode, bool bSuptVideo, const char *pcSipMsg)
{
    SipCallInterfaceIPtr sipClient = SipCallInterfaceIPtr::dynamicCast(SipCallInterface::getInstance());
    if (sipClient)
        sipClient->onSipCallEvent(iSessId, iEvntType, pcSdp, iStatCode, bSuptVideo, pcSipMsg);
}

static void JssiOnLog(void *zCookie, const char *content)
{
    UTIL_LOG_DBG("Jssi", content);
}

static int JssiOnSend(unsigned char *pucBuf, unsigned iLen)
{
    SipCallInterfaceIPtr sipClient = SipCallInterfaceIPtr::dynamicCast(SipCallInterface::getInstance());
    if (sipClient)
        sipClient->onSend(pucBuf, iLen);
    return 0;
}

void SipCallSessionEventProcessor::onProcEvent(const Common::ObjectPtr &event)
{
    SipCallSessionEventPtr sessEvent = SipCallSessionEventPtr::dynamicCast(event);

    switch (sessEvent->eventType)
    {
    case SipClient::SipCallSessionEventIncoming:
        sessEvent->listener->onCallIncoming(sessEvent->session, sessEvent->sdp, sessEvent->statusCode, sessEvent->suptVideo, sessEvent->msg);
        break;
    case SipClient::SipCallSessionEventOutgoing:
        sessEvent->listener->onCallOutgoing(sessEvent->session, sessEvent->sdp, sessEvent->statusCode, sessEvent->suptVideo, sessEvent->msg);
        break;
    case SipClient::SipCallSessionEventAltered:
        sessEvent->listener->onCallAlerted(sessEvent->session, sessEvent->sdp, sessEvent->statusCode, sessEvent->suptVideo, sessEvent->msg);
        break;
    case SipClient::SipCallSessionEventAnswered:
        sessEvent->listener->onCallAnswered(sessEvent->session, sessEvent->sdp, sessEvent->statusCode, sessEvent->suptVideo, sessEvent->msg);
        break;
    case SipClient::SipCallSessionEventUpdate:
        sessEvent->listener->onCallUpdate(sessEvent->session, sessEvent->sdp, sessEvent->statusCode, sessEvent->suptVideo, sessEvent->msg);
        break;
    case SipClient::SipCallSessionEventRequestUpdate:
        sessEvent->listener->onCallRequestUpdate(sessEvent->session, sessEvent->sdp, sessEvent->statusCode, sessEvent->suptVideo, sessEvent->msg);
        break;
    case SipClient::SipCallSessionEventResponseUpdate:
        sessEvent->listener->onCallResponseUdate(sessEvent->session, sessEvent->sdp, sessEvent->statusCode, sessEvent->suptVideo, sessEvent->msg);
        break;
    case SipClient::SipCallSessionEventConnected:
        sessEvent->listener->onCallConnected(sessEvent->session, sessEvent->sdp, sessEvent->statusCode, sessEvent->suptVideo, sessEvent->msg);
        break;
    case SipClient::SipCallSessionEventRequestModify:
        sessEvent->listener->onCallRequestModify(sessEvent->session, sessEvent->sdp, sessEvent->statusCode, sessEvent->suptVideo, sessEvent->msg);
        break;
    case SipClient::SipCallSessionEventResponseModify:
        sessEvent->listener->onCallResponseModify(sessEvent->session, sessEvent->sdp, sessEvent->statusCode, sessEvent->suptVideo, sessEvent->msg);
        break;
    case SipClient::SipCallSessionEventTermed:
        sessEvent->listener->onCallTerminated(sessEvent->session, sessEvent->sdp, sessEvent->statusCode, sessEvent->suptVideo, sessEvent->msg);
        break;
    }
}

unsigned int SipCallSessionI::sessId()
{
    return _sessId;
}

bool SipCallSessionI::SipSetContactUri(const std::string &pcContactUri)
{
    Common::RecLock lock(_mutex);
    return JSSI_SipClientSipSetContactUri(_sessId, pcContactUri.empty() ? nullptr : pcContactUri.c_str()) == 0;
}

bool SipCallSessionI::SipAlert(const std::string &pcAnswerSdp)
{
    Common::RecLock lock(_mutex);
    return JSSI_SipClientSipAlert(_sessId, pcAnswerSdp.c_str()) == 0;
}

bool SipCallSessionI::SipAnswer(const std::string &pcAnswerSdp, const std::map<std::string, std::string> &pcExtHdrs)
{
    Common::RecLock lock(_mutex);

    const char *apcExtHdrs[17];
    if (!SipCallInterfaceI::readExtHdrs(apcExtHdrs, 17, pcExtHdrs))
    {
        UTIL_LOG_WRN("SipCallInterface", "content:sip answer convert externsion headers failed");
        return false;
    }
    return JSSI_SipClientSipAnswer(_sessId, pcAnswerSdp.c_str(), apcExtHdrs) == 0;
}

bool SipCallSessionI::SipTerm()
{
    Common::RecLock lock(_mutex);
    bool ret = JSSI_SipClientSipTerm(_sessId) == 0;
    if (!ret)
        _termed = true;
    return ret;
}

bool SipCallSessionI::SipUpdate(const std::string &pcOfferSdp)
{
    Common::RecLock lock(_mutex);
    return JSSI_SipClientSipUpdate(_sessId, pcOfferSdp.c_str()) == 0;
}

bool SipCallSessionI::SipUpdateRsp(const std::string &pcAnswerSdp)
{
    Common::RecLock lock(_mutex);
    return JSSI_SipClientSipUpdateRsp(_sessId, pcAnswerSdp.c_str()) == 0;
}

bool SipCallSessionI::SipAck(const std::string &pcAnswerSdp)
{
    Common::RecLock lock(_mutex);
    return JSSI_SipClientSipAck(_sessId, pcAnswerSdp.c_str()) == 0;
}

bool SipCallSessionI::GetCalledUri(std::string &ppcDispName, std::string &ppcUri)
{
    Common::RecLock lock(_mutex);
    const char *name, *uri;
    if (JSSI_SipClientGetCalledUri(_sessId, &name, &uri) == 0)
    {
        ppcDispName = name;
        ppcUri = uri;
        return true;
    }

    return false;
}

bool SipCallSessionI::GetPeerUri(std::string &ppcDispName, std::string &ppcUri)
{
    Common::RecLock lock(_mutex);
    const char *name, *uri;
    if (JSSI_SipClientGetPeerUri(_sessId, &name, &uri) == 0)
    {
        ppcDispName = name;
        ppcUri = uri;
        return true;
    }

    return false;
}

bool SipCallSessionI::GetCallTermedReason(std::string &ppcSipPhase, std::string &ppcReason)
{
    Common::RecLock lock(_mutex);
    const char *phase, *reason;
    if (JSSI_SipClientGetCallTermedReason(_sessId, &phase, &reason) == 0)
    {
        ppcSipPhase = phase;
        ppcReason = reason;
        return true;
    }

    return false;
}

void SipCallSessionI::onCallEvent(unsigned int iEvntType, const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg)
{
    _eventArray->pushEvent(new SipCallSessionEvent(_listener, this, iEvntType, pcSdp, iStatCode, bSuptVideo, pcSipMsg), false);
}

enum SipCallSessionEventType SipCallSessionI::eventSip2Session(unsigned int iEventType)
{
    if (iEventType > EN_JSSI_RTC_CALL_OTHER)
        return SipCallSessionEventOther;
    return (enum SipCallSessionEventType)iEventType;
}

bool SipCallInterfaceI::SipInit(const SipCallConfig &pstSipConfig)
{
    ST_JSSI_SIP_CONFIG config = {0};
    config.strSipDir = pstSipConfig.workPath.c_str();
    config.bEarlyMedia = pstSipConfig.earlyMedia;
    config.bPrecondition = pstSipConfig.precondition;
    config.zCookie = (size_t)this;
    config.pfnEvntDrive = (PFN_JSSISIPEVNTDRIVE)JssiOnEvent;
    config.pfnJssiSipCallEvnt = (PFN_JSSISIPCAllCB)JssiOnSipCallEvent;
    config.pfnJssiSipSendMsg = (PFN_JSSISIPSENDSIPMSG)JssiOnSend;
    config.pfnJssiLogCallback = (PFN_JSSILOGCALLBACK)JssiOnLog;

    return JSSI_SipClientSipInit(&config) == 0;
}

void SipCallInterfaceI::SipRelease()
{
    UTIL_LOG_IFO("SipCallInterface", "content:release sip call.");
    Common::RecLock lock(_mutex);
    JSSI_SipClientSipRelease();
    stopRun(false);
    _application = 0;
    _listener = 0;
    _listeners.clear();
    _events.clear();
    _sessions.clear();
}

bool SipCallInterfaceI::addListener(const SipCallListenerPtr &listener)
{
    Common::RecLock lock(_mutex);
    if (!_listener)
    {
        UTIL_LOG_WRN("SipCallInterface", "content:add listener failed.");
        return false;
    }

    UTIL_LOG_IFO("SipCallInterface", "content:add listener.");
    _listeners.insert(listener);
    return true;
}

bool SipCallInterfaceI::GetSipSdkVer(std::string &ppcLemonVer, std::string &ppcAvatarVer)
{
    const char *ver1, *ver2;
    if (JSSI_SipClientGetSipSdkVer(&ver1, &ver2) == 0)
    {
        ppcLemonVer = ver1;
        ppcAvatarVer = ver2;
        return true;
    }

    return false;
}

bool SipCallInterfaceI::RecvSipMsg(const std::string &message)
{
    if (!isRunning())
    {
        UTIL_LOG_IFO("SipCallInterface", "content:receive message after stopped.");
        return false;
    }

    return JSSI_SipClientRecvSipMsg((unsigned char*)message.c_str(), (unsigned int)message.size()) == 0;
}

SipCallSessionPtr SipCallInterfaceI::SipCall(const SipCallSessionListenerPtr &listener, const std::string &pcCallerUri, const std::string &pcCalleeUri, const std::string &pcCallId, const std::string &pcContactUri, const std::string &pcOfferSdp, const std::string &pcUriParms, const std::map<std::string, std::string> &pcExtHdrs)
{
    Common::RecLock lock(_mutex);

    SipCallSessionIPtr session;
    try
    {
        session = new SipCallSessionI(_mutex, _application);
    }
    catch (const std::exception &e)
    {
        UTIL_LOG_WRN("SipCallInterface", "content:sip call create session failed, caller:" + Common::String(pcCallerUri.c_str()) + " callee:" + pcCalleeUri.c_str());
        return nullptr;
    }

    const char *apcExtHdrs[17];
    if (!readExtHdrs(apcExtHdrs, 17, pcExtHdrs))
    {
        UTIL_LOG_WRN("SipCallInterface", "content:sip call convert externsion headers failed, caller:" + Common::String(pcCallerUri.c_str()) + " callee:" + pcCalleeUri.c_str());
        return nullptr;
    }

    unsigned int sessId = JSSI_SipClientSipCall(pcCallerUri.c_str(), pcCalleeUri.c_str(), pcCallId.empty() ? nullptr : pcCallId.c_str(), pcContactUri.empty() ? nullptr : pcContactUri.c_str(), pcOfferSdp.c_str(), pcUriParms.c_str(), apcExtHdrs);
    if (sessId == 0xffffffff)
    {
        UTIL_LOG_WRN("SipCallInterface", "content:sip call failed, caller:" + Common::String(pcCallerUri.c_str()) + " callee:" + pcCalleeUri.c_str());
        return nullptr;
    }

    session->_sessId = sessId;
    session->_listener = listener;
    addSession(session);

    UTIL_LOG_IFO("SipCallInterface", "content:sip call succeed, caller:" + Common::String(pcCallerUri.c_str()) + " callee:" + pcCalleeUri.c_str());
    return session.get();
}

void SipCallInterfaceI::onRun()
{
    while (isRunning())
    {
        Common::sleep(10);

        Common::RecLock lock(_mutex);

        for (auto &event : _events)
            JSSI_SipClientSipEvntDrive(event);
        _events.clear();

        __checkTermed();
    }
}

void SipCallInterfaceI::onEvent(void *zEvent)
{
    Common::RecLock lock(_mutex);
    _events.push_back(zEvent);
}

void SipCallInterfaceI::onSipCallEvent(unsigned int iSessId, unsigned int iEventType, const char *pcSdp, unsigned int iStatCode, bool bSuptVideo, const char *pcSipMsg)
{
    Common::RecLock lock(_mutex);

    if (iEventType == EN_JSSI_RTC_CALL_INCOMING)
    {
        SipCallSessionIPtr session;
        try
        {
            session = new SipCallSessionI(_mutex, _application);
            session->_sessId = iSessId;
        }
        catch (const std::exception &e)
        {
            UTIL_LOG_WRN("SipCallInterface", "content:sip call incoming create session failed, sessId:" + Common::String((int)iSessId));
            JSSI_SipClientSipTerm(iSessId);
            return;
        }

        SipCallSessionListenerPtr sessionListener;
        for (auto &listener : _listeners)
        {
            sessionListener = listener->onCreateSession(session.get());
            if (sessionListener)
                break;
        }

        if (!sessionListener)
            sessionListener = _listener->onCreateSession(session.get());

        if (!sessionListener)
        {
            UTIL_LOG_WRN("SipCallInterface", "content:sip call session incoming no listener returned, sessId:" + Common::String((int)iSessId));
            JSSI_SipClientSipTerm(iSessId);
            return;
        }

        session->_listener = sessionListener;
        addSession(session);
        session->onCallEvent(iEventType, pcSdp ? pcSdp : "", iStatCode, bSuptVideo, pcSipMsg ? pcSipMsg : "");
        return;
    }

    auto it = _sessions.find(iSessId);
    if (it == _sessions.end())
    {
        UTIL_LOG_WRN("SipCallInterface", "content:session not found, id:" + Common::String((Common::Long)iSessId));
        return;
    }

    it->second->onCallEvent(iEventType, pcSdp ? pcSdp : "", iStatCode, bSuptVideo, pcSipMsg ? pcSipMsg : "");

    if (iEventType == EN_JSSI_RTC_CALL_TERMED)
        removeSession(it->second);
}

void SipCallInterfaceI::onSend(unsigned char *pucBuf, unsigned iLen)
{
    Common::RecLock lock(_mutex);
    std::string message(reinterpret_cast<const char *>(pucBuf), iLen);
    for (auto &listener : _listeners)
    {
        if (listener->onSend(message))
            return;
    }

    if (_listener)
        _listener->onSend(message);
}

void SipCallInterfaceI::__checkTermed()
{
    if (Common::getCurTicks() - _lastCheckTicks < 1000)
        return;
    _lastCheckTicks = Common::getCurTicks();

    for (auto it = _sessions.begin(); it != _sessions.end();)
    {
        if (it->second->_termed)
            it = _sessions.erase(it);
        else
            ++it;
    }
}

void SipCallInterfaceI::addSession(const SipCallSessionIPtr &session)
{
    Common::RecLock lock(_mutex);
    UTIL_LOG_IFO("SipCallInterface", "content:add session:" + Common::String((int)session->sessId()));
    _sessions[session->sessId()] = session;
}

void SipCallInterfaceI::removeSession(const SipCallSessionIPtr &session)
{
    Common::RecLock lock(_mutex);
    UTIL_LOG_IFO("SipCallInterface", "content:remove session:" + Common::String((int)session->sessId()));
    _sessions.erase(session->sessId());
}

bool SipCallInterfaceI::readExtHdrs(const char **ppcExtHdrs, int size, const std::map<std::string, std::string> &pcExtHdrs)
{
    if (pcExtHdrs.size() * 2 >= size)
    {
        UTIL_LOG_WRN("SipCallInterface", "content:too many externsion headers, count:" + Common::String((int)pcExtHdrs.size()));
        return false;
    }

    int i = 0;
    for (auto &kv : pcExtHdrs)
    {
        ppcExtHdrs[i++] = kv.first.c_str();
        ppcExtHdrs[i++] = kv.second.c_str();
    }

    ppcExtHdrs[i] = nullptr;
    return true;
}

static SipCallInterfacePtr __sipClient;
static std::mutex __sipClientMutex;

SipCallInterfacePtr SipCallInterface::getInstance(const Common::ApplicationPtr &application, const SipCallConfig &config, const SipCallListenerPtr &listener, bool &created)
{
    std::lock_guard<std::mutex> lock(__sipClientMutex);
    if (!__sipClient)
    {
        try
        {
            SipCallInterfaceIPtr sipCall = new SipCallInterfaceI(application, listener);
            if (!sipCall->SipInit(config))
            {
                UTIL_LOG_ERR("SipCallInterface", "content:init failed.");
                return nullptr;
            }

            if (!sipCall->startRun(0, "SipCall"))
            {
                UTIL_LOG_ERR("SipCallInterface", "content:start sip call failed.");
                return nullptr;
            }

            UTIL_LOG_IFO("SipCallInterface", "content:create sip call.");
            created = true;
            __sipClient = sipCall;
        }
        catch (const std::exception &e)
        {
            UTIL_LOG_ERR("SipCallInterface", "content:create failed.");
            return nullptr;
        }
    }
    else
    {
        created = false;
    }

    return __sipClient;
}

SipCallInterfacePtr SipCallInterface::getInstance()
{
    std::lock_guard<std::mutex> lock(__sipClientMutex);
    if (__sipClient)
        return __sipClient;

    return nullptr;
}

void SipCallInterface::destroyInstance()
{
    std::lock_guard<std::mutex> lock(__sipClientMutex);
    if (__sipClient)
    {
        UTIL_LOG_IFO("SipCallInterface", "content:destroy sip call instance.");
        SipCallInterfaceIPtr sipClient = SipCallInterfaceIPtr::dynamicCast(__sipClient);
        if (sipClient)
            sipClient->SipRelease();
        __sipClient = nullptr;
    }
}

bool SipCallInterface::injectInstance(const SipCallInterfacePtr &sipCall)
{
    std::lock_guard<std::mutex> lock(__sipClientMutex);
    if (__sipClient)
    {
        UTIL_LOG_WRN("SipCallInterface", "content:instance already exists.");
        return false;
    }

    UTIL_LOG_IFO("SipCallInterface", "content:inject instance.");
    __sipClient = sipCall;
    return true;
}

} // namespace SipClient
