//
// *****************************************************************************
// Copyright (c) 2023 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//
// Created on 2023/5/24 by <PERSON>
//

#pragma once

#include "Common/Common.h"
#include "SipRtpInterface.h"

namespace SipClient
{

class SipRtpInterfaceI : public SipRtpInterface
{
public:
    SipRtpInterfaceI(const SipRtpListenerPtr &listener)
        : _listener(listener)
    {
    }

    // SipRtpInterface
    bool GetRtpSdkVer(std::string &ppcLemon<PERSON>er, std::string &ppcAvatarVer, std::string &ppcMelonVer, std::string &ppcWatermelonVer) override;
    void SdkRelease() override;
    bool setScreenSharingMode(bool screenSharingMode) override;
    std::string GenOfferSdp(int videoDirection, bool bPrecondition) override;
    std::string GenAnswerSdp(const std::string &pcPeerSDP, int videoDirection, bool bPrecondition) override;
    bool NegoAndApplySdp(const std::string &pcAnswerSdp, int videoDirection) override;
    void SetAudioDelay(int delayMs) override;
    bool SendDtmf(int iDtmfType) override;
    bool muteAudioFromRtp(bool mute) override;
    bool muteAudioToRtp(bool mute) override;
    int TessarReg(int iExtUsrId, bool bVideo) override;
    int TessarUnreg(int iExtUsrId, bool bVideo) override;
    std::string GetTessarData(int iUserId, bool bVideo, int iLevel, int iType) override;
    std::string getAudioStats() override;
    std::string getVideoStats() override;

    void onEvent(void *event);
    void onDtmfEvent(unsigned int event);

private:
    Common::RecMutex _mutex;
    SipRtpListenerPtr _listener;
};

typedef Common::Handle<SipRtpInterfaceI> SipRtpInterfaceIPtr;

} // namespace SipClient