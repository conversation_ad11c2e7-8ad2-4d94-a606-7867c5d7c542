aux_source_directory(. SIP_ADAPTER_SRC)
add_library(SipAdapter ${SIP_ADAPTER_SRC})
target_include_directories(SipAdapter PRIVATE
    ${DEFAULT_INCLUDE_DIRECTORIES}
)
target_compile_definitions(SipAdapter PRIVATE
    ${DEFAULT_COMPILE_DEFINITIONS}
)

# UnitTest
aux_source_directory(test SIP_ADAPTER_TEST_SRC)
add_executable(SipAdapterUnitTest ${SIP_ADAPTER_TEST_SRC})
target_include_directories(SipAdapterUnitTest PRIVATE
    ${DEFAULT_INCLUDE_DIRECTORIES}
    ${PROJECT_SOURCE_DIR}/3rd
)
target_compile_definitions(SipAdapterUnitTest PRIVATE
    ${DEFAULT_COMPILE_DEFINITIONS}
)

target_link_libraries(SipAdapterUnitTest
    ${DEFAULT_SERVER_LIBRARIES}
    $ENV{JssSharedPath}/lib/${DEFAULT_LIB_PATH}/libgtest.a
    $ENV{JssSharedPath}/lib/${DEFAULT_LIB_PATH}/libgmock.a
    $ENV{JssSharedPath}/lib/${DEFAULT_LIB_PATH}/libgtest_main.a
    ${DEFAULT_SYS_LIBRARIES}
)
include(GoogleTest)
gtest_discover_tests(SipAdapterUnitTest)
