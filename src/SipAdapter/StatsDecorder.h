//
// *****************************************************************************
// Copyright (c) 2025 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//

#pragma once

#include "StatsDecorderI.h"

#include <string>

namespace SipClient
{

class StatsDecorder : public StatsDecorderI
{
public:
    bool decode(const std::string &stats)
    {
        return __decode(stats);
    }

    std::string get(const std::string &path)
    {
        return __get(path);
    }
};

} // namespace SipClient
