//
// *****************************************************************************
// Copyright (c) 2023 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//
// Created on 2023/5/24 by <PERSON>
//

#include "Common/Common.h"
#include "Common/Util.h"
#include "SipRtpInterface.h"
#include "SipRtpInterfaceI.h"
#include "jssi/jssi_rtp.h"
#include <exception>

namespace SipClient
{

const char *SipRtpInterface::AudioPumpDevice = JSSI_AUDIO_PUMP;
const char *SipRtpInterface::VideoCaptureDevice = JSSI_CAPTURE_DEV;
const char *SipRtpInterface::VideoPumpDevice = JSSI_VIDEO_PUMP;

static SipRtpInterfaceIPtr __rtpClient;

static int JssiOnEvent(void *zEvnt)
{
    SipRtpInterfaceIPtr rtpClient = __rtpClient;
    if (rtpClient)
        rtpClient->onEvent(zEvnt);
    return 0;
}

static void JssiOnDtmfEvent(unsigned iSessId, unsigned iDtmfType)
{
    UTIL_LOG_IFO("SipRtpInterface", "content:JssiOnDtmfEvent sess:" + Common::String((int)iSessId) + " dtmf:" + Common::String((int)iDtmfType));
    SipRtpInterfaceIPtr rtpClient = __rtpClient;
    if (rtpClient)
        rtpClient->onDtmfEvent(iDtmfType);
}

static void JssiOnLog(void *zCookie, const char *content)
{
    UTIL_LOG_IFO("Jssi", content);
}

Common::Handle<SipRtpInterface> SipRtpInterface::create(const SipRtpConfig &config, const SipRtpListenerPtr &listener)
{
    SipRtpInterfaceIPtr rtpClient;
    try
    {
        rtpClient = new SipRtpInterfaceI(listener);
    }
    catch (const std::exception &e)
    {
        UTIL_LOG_ERR("SipRtpInterface", "content:create interface failed, reason:" + Common::String(e.what()));
        return nullptr;
    }

    ST_JSSI_SDP_CONFIG jssiConfig = {0};
    jssiConfig.strSipDir = config.workPath.c_str();
    if (config.localIp.empty())
    {
        jssiConfig.strLclIp = "0.0.0.0";
    }
    else
    {
        jssiConfig.strLclIp = config.localIp.c_str();
        jssiConfig.strSdpPublicIp = config.localIp.c_str();
    }
    jssiConfig.iAMinPort = config.audioPorts.lowerBound;
    jssiConfig.iAMaxPort = config.audioPorts.upperBound;
    jssiConfig.iVMinPort = config.videoPorts.lowerBound;
    jssiConfig.iVMaxPort = config.videoPorts.upperBound;
    jssiConfig.iWidth = config.videoConfig.width;
    jssiConfig.iHeight = config.videoConfig.height;
    jssiConfig.iFrameRate = config.videoConfig.frameRate;
    jssiConfig.iBitRate = config.videoConfig.bitRate;
    jssiConfig.iOverrideWidth = config.overrideVideoConfig.width;
    jssiConfig.iOverrideHeight = config.overrideVideoConfig.height;
    jssiConfig.iOverrideFrameRate = config.overrideVideoConfig.frameRate;
    jssiConfig.iOverrideBitRate = config.overrideVideoConfig.bitRate;
    jssiConfig.iKeyPeriod = config.keyframeInterval;
    jssiConfig.pfnEvntDrive = (PFN_JSSIRTPEVNTDRIVE)JssiOnEvent;
    jssiConfig.pfnJssiDtmfEvnt = (PFN_JSSIDTMFEVNT)JssiOnDtmfEvent;
    jssiConfig.pfnJssiLogCallback = (PFN_JSSILOGCALLBACK)JssiOnLog;
    jssiConfig.bScreenSharingMode = config.screenSharingMode;
    jssiConfig.iAAsBw = config.audioBandwidth.AS;
    jssiConfig.iARsBw = config.audioBandwidth.RS;
    jssiConfig.iARrBw = config.audioBandwidth.RR;
    jssiConfig.iVAsBw = config.videoBandwidth.AS;
    jssiConfig.iVRsBw = config.videoBandwidth.RS;
    jssiConfig.iVRrBw = config.videoBandwidth.RR;

    if (JSSI_SipClientSdpInit(&jssiConfig) != 0)
    {
        UTIL_LOG_ERR("SipRtpInterface", "content:init sip rtp interface failed.");
        return nullptr;
    }

    __rtpClient = rtpClient;
    UTIL_LOG_IFO("SipRtpInterface", "content:init sip rtp interface ok.");
    return rtpClient.get();
}

bool SipRtpInterfaceI::GetRtpSdkVer(std::string &ppcLemonVer, std::string &ppcAvatarVer, std::string &ppcMelonVer, std::string &ppcWatermelonVer)
{
    const char *pcLemonVer, *pcAvatarVer, *pcMelonVer, *pcWatermelonVer;
    if (JSSI_SipClientGetRtpSdkVer(&pcLemonVer, &pcAvatarVer, &pcMelonVer, &pcWatermelonVer) != 0)
        return false;

    ppcLemonVer = pcLemonVer;
    ppcAvatarVer = pcAvatarVer;
    ppcMelonVer = pcMelonVer;
    ppcWatermelonVer = pcWatermelonVer;

    return true;
}

void SipRtpInterfaceI::SdkRelease()
{
    Common::RecLock lock(_mutex);
    JSSI_SipClientRelease();
    __rtpClient = 0;
}

bool SipRtpInterfaceI::setScreenSharingMode(bool screenSharingMode)
{
    Common::RecLock lock(_mutex);
    UTIL_LOG_IFO("SipRtpInterface", "content:set screen sharing mode:" + Common::String(screenSharingMode));
    return JSSI_SipClientSdpSetConfig("ScreenSharingMode", screenSharingMode ? "1" : "0") == 0;
}

std::string SipRtpInterfaceI::GenOfferSdp(int videoDirection, bool bPrecondition)
{
    Common::RecLock lock(_mutex);
    const char *sdp = JSSI_SipClientGenOfferSdp(videoDirection, bPrecondition);
    if (!sdp)
    {
        UTIL_LOG_WRN("SipRtpInterface", "content:generate offer sdp failed.");
        return "";
    }

    UTIL_LOG_IFO("SipRtpInterface", "content:generate offer sdp.");
    return std::string(sdp);
}

std::string SipRtpInterfaceI::GenAnswerSdp(const std::string &pcPeerSDP, int videoDirection, bool bPrecondition)
{
    Common::RecLock lock(_mutex);
    const char *sdp = JSSI_SipClientGenAnswerSdp(pcPeerSDP.c_str(), videoDirection, bPrecondition);
    if (!sdp)
    {
        UTIL_LOG_WRN("SipRtpInterface", "content:generate answer sdp failed, direction:" + Common::String(videoDirection) + ", precondition:" + Common::String(bPrecondition) + ", peer sdp:\n" + pcPeerSDP.c_str());
        return "";
    }

    UTIL_LOG_IFO("SipRtpInterface", "content:generate answer sdp.");
    return std::string(sdp);
}

bool SipRtpInterfaceI::NegoAndApplySdp(const std::string &pcAnswerSdp, int videoDirection)
{
    Common::RecLock lock(_mutex);
    if (JSSI_SipClientNegoAndApplySdp(pcAnswerSdp.c_str(), videoDirection) != 0)
    {
        UTIL_LOG_WRN("SipRtpInterface", "content:negotiate answer sdp failed.");
        return false;
    }

    UTIL_LOG_IFO("SipRtpInterface", "content:negotiate answer sdp.");
    return true;
}

void SipRtpInterfaceI::SetAudioDelay(int delayMs)
{
    Common::RecLock lock(_mutex);
    UTIL_LOG_IFO("SipRtpInterface", "content:set audio delay.");
    JSSI_SipClientSetAudioDelay(delayMs);
}

bool SipRtpInterfaceI::SendDtmf(int iDtmfType)
{
    Common::RecLock lock(_mutex);
    UTIL_LOG_IFO("SipRtpInterface", "content:send dtmf:" + Common::String(iDtmfType));
    return JSSI_SipClientSendDtmf(iDtmfType) == 0;
}

bool SipRtpInterfaceI::muteAudioFromRtp(bool mute)
{
    Common::RecLock lock(_mutex);
    UTIL_LOG_IFO("SipRtpInterface", "content:mute audio from rtp.");
    return JSSI_SipClientMuteAudioFromRtp(mute) == 0;
}

bool SipRtpInterfaceI::muteAudioToRtp(bool mute)
{
    Common::RecLock lock(_mutex);
    UTIL_LOG_IFO("SipRtpInterface", "content:mute audio to rtp.");
    return JSSI_SipClientMuteAudioToRtp(mute) == 0;
}

int SipRtpInterfaceI::TessarReg(int iExtUsrId, bool bVideo)
{
    Common::RecLock lock(_mutex);
    UTIL_LOG_IFO("SipRtpInterface", "content:tessar reg.");
    return JSSI_SipClientTessarReg(iExtUsrId, bVideo);
}

int SipRtpInterfaceI::TessarUnreg(int iExtUsrId, bool bVideo)
{
    Common::RecLock lock(_mutex);
    UTIL_LOG_IFO("SipRtpInterface", "content:tessar unreg.");
    return JSSI_SipClientTessarUnreg(iExtUsrId, bVideo);
}

std::string SipRtpInterfaceI::GetTessarData(int iUserId, bool bVideo, int iLevel, int iType)
{
    Common::RecLock lock(_mutex);
    const char *pcData = JSSI_SipClientGetTessarData(iUserId, bVideo, iLevel, iType);
    if (!pcData)
    {
        UTIL_LOG_WRN("SipRtpInterface", "content:get tessar data failed.");
        return "";
    }

    UTIL_LOG_IFO("SipRtpInterface", "content:get tessar data.");
    return std::string(pcData);
}

std::string SipRtpInterfaceI::getAudioStats()
{
    Common::RecLock lock(_mutex);
    const char *pcData = JSSI_SipClientSdpGetConfig("RtpAudioStatus");
    return std::string(pcData);
}

std::string SipRtpInterfaceI::getVideoStats()
{
    Common::RecLock lock(_mutex);
    const char *pcData = JSSI_SipClientSdpGetConfig("RtpVideoStatus");
    return std::string(pcData);
}

void SipRtpInterfaceI::onEvent(void *event)
{
    Common::RecLock lock(_mutex);
    JSSI_SipClientEvntDrive(event);
}

void SipRtpInterfaceI::onDtmfEvent(unsigned int event)
{
    DtmfEvent dtmfEvent;
    if (event <= EN_JSSI_SESS_DTMF_D)
        dtmfEvent = (DtmfEvent)event;
    else
        return;

    SipRtpListenerPtr listener = _listener;
    if (listener)
        listener->onDtmfEvent(dtmfEvent);
}

} // namespace SipClient
