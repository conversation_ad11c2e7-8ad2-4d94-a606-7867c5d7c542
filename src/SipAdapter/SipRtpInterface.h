//
// *****************************************************************************
// Copyright (c) 2023 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//
// Created on 2023/5/24 by <PERSON>
//

#pragma once

#include "Common/Common.h"

namespace SipClient
{

struct SipRtpConfig
{
    struct VideoConfig
    {
        VideoConfig()
            : width(0)
            , height(0)
            , frameRate(0)
            , bitRate(0)
        {
        }

        unsigned int width;
        unsigned int height;
        unsigned int frameRate;
        unsigned int bitRate;
    };

    struct PortRange
    {
        PortRange()
            : upperBound(0)
            , lowerBound(0)
        {
        }

        unsigned int upperBound;
        unsigned int lowerBound;
    };

    struct BandwidthConfig
    {
        BandwidthConfig()
            : AS(0)
            , RS(0)
            , RR(0)
        {
        }

        unsigned int AS;
        unsigned int RS;
        unsigned int RR;
    };

    SipRtpConfig()
        : keyframeInterval(0)
        , dtmfPayloadType(0)
        , screenSharingMode(false)
    {
    }

    std::string localIp;
    std::string workPath;
    PortRange audioPorts;
    PortRange videoPorts;
    VideoConfig videoConfig;
    VideoConfig overrideVideoConfig;
    unsigned int keyframeInterval;
    unsigned int dtmfPayloadType;
    bool screenSharingMode;
    BandwidthConfig audioBandwidth;
    BandwidthConfig videoBandwidth;
};

enum DtmfEvent
{
    DtmfDigit0,
    DtmfDigit1,
    DtmfDigit2,
    DtmfDigit3,
    DtmfDigit4,
    DtmfDigit5,
    DtmfDigit6,
    DtmfDigit7,
    DtmfDigit8,
    DtmfDigit9,
    DtmfStar,
    DtmfPound,
    DtmfLetterA,
    DtmfLetterB,
    DtmfLetterC,
    DtmfLetterD
};

class SipRtpListener : virtual public Common::Shared
{
public:
    virtual void onDtmfEvent(enum DtmfEvent event) = 0;
};

typedef Common::Handle<SipRtpListener> SipRtpListenerPtr;

class SipRtpInterface : virtual public Common::Shared
{
public:
    static const char * AudioPumpDevice;
    static const char * VideoCaptureDevice;
    static const char * VideoPumpDevice;
    static Common::Handle<SipRtpInterface> create(const SipRtpConfig &config, const SipRtpListenerPtr &listener);

    virtual bool GetRtpSdkVer(std::string &ppcLemonVer, std::string &ppcAvatarVer, std::string &ppcMelonVer, std::string &ppcWatermelonVer) = 0;
    virtual void SdkRelease() = 0;
    virtual bool setScreenSharingMode(bool screenSharingMode) = 0;
    virtual std::string GenOfferSdp(int videoDirection, bool bPrecondition) = 0;
    virtual std::string GenAnswerSdp(const std::string &pcPeerSDP, int videoDirection, bool bPrecondition) = 0;
    virtual bool NegoAndApplySdp(const std::string &pcAnswerSdp, int videoDirection) = 0;
    virtual void SetAudioDelay(int delayMs) = 0;
    virtual bool SendDtmf(int iDtmfType) = 0;
    virtual bool muteAudioFromRtp(bool mute) = 0;
    virtual bool muteAudioToRtp(bool mute) = 0;

    virtual int TessarReg(int iExtUsrId, bool bVideo) = 0;
    virtual int TessarUnreg(int iExtUsrId, bool bVideo) = 0;
    virtual std::string GetTessarData(int iUserId, bool bVideo, int iLevel, int iType) = 0;

    virtual std::string getAudioStats() = 0;
    virtual std::string getVideoStats() = 0;
};

typedef Common::Handle<SipRtpInterface> SipRtpInterfacePtr;

} // namespace SipClient
