//
// *****************************************************************************
// Copyright (c) 2023 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//
// Created on 2023/5/24 by <PERSON>
//

#pragma once

#include "gmock/gmock.h"

#include "SipCallInterface.h"

using ::testing::_;

namespace SipClient
{

class SipCallSessionListenerMock : public SipCallSessionListener
{
public:
    MOCK_METHOD(void, onCallIncoming, (const SipClient::SipCallSessionPtr &session, const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg), (override));
    MOCK_METHOD(void, onCallOutgoing, (const SipClient::SipCallSessionPtr &session, const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg), (override));
    MOCK_METHOD(void, onCallAlerted, (const SipClient::SipCallSessionPtr &session, const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg), (override));
    MOCK_METHOD(void, onCallAnswered, (const SipClient::SipCallSessionPtr &session, const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg), (override));
    MOCK_METHOD(void, onCallUpdate, (const SipClient::SipCallSessionPtr &session, const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg), (override));
    MOCK_METHOD(void, onCallRequestUpdate, (const SipClient::SipCallSessionPtr &session, const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg), (override));
    MOCK_METHOD(void, onCallResponseUdate, (const SipClient::SipCallSessionPtr &session, const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg), (override));
    MOCK_METHOD(void, onCallConnected, (const SipClient::SipCallSessionPtr &session, const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg), (override));
    MOCK_METHOD(void, onCallRequestModify, (const SipClient::SipCallSessionPtr &session, const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg), (override));
    MOCK_METHOD(void, onCallResponseModify, (const SipClient::SipCallSessionPtr &session, const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg), (override));
    MOCK_METHOD(void, onCallTerminated, (const SipClient::SipCallSessionPtr &session, const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg), (override));
};

typedef Common::Handle<SipCallSessionListenerMock> SipCallSessionListenerMockPtr;

class SipCallSessionMock : public SipCallSession
{
public:
    MOCK_METHOD(unsigned int, sessId, (), (override));
    MOCK_METHOD(bool, SipSetContactUri, (const std::string &pcContactUri), (override));
    MOCK_METHOD(bool, SipAlert, (const std::string &pcAnswerSdp), (override));
    MOCK_METHOD(bool, SipAnswer, (const std::string &pcAnswerSdp, const SipCallExtHdrs &pcExtHdrs), (override));
    MOCK_METHOD(bool, SipTerm, (), (override));
    MOCK_METHOD(bool, SipUpdate, (const std::string &pcOfferSdp), (override));
    MOCK_METHOD(bool, SipUpdateRsp, (const std::string &pcAnswerSdp), (override));
    MOCK_METHOD(bool, SipAck, (const std::string &pcAnswerSdp), (override));
    MOCK_METHOD(bool, GetCalledUri, (std::string &ppcDispName, std::string &ppcUri), (override));
    MOCK_METHOD(bool, GetPeerUri, (std::string &ppcDispName, std::string &ppcUri), (override));
    MOCK_METHOD(bool, GetCallTermedReason, (std::string &ppcSipPhase, std::string &ppcReason), (override));
};

typedef Common::Handle<SipCallSessionMock> SipCallSessionMockPtr;

class SipCallListenerMock : public SipCallListener
{
public:
    MOCK_METHOD(bool, onSend, (const std::string &message), (override));
    MOCK_METHOD(SipCallSessionListenerPtr, onCreateSession, (const SipCallSessionPtr &session), (override));
};

typedef Common::Handle<SipCallListenerMock> SipCallListenerMockPtr;

class SipCallInterfaceMock : public SipCallInterface
{
public:
    MOCK_METHOD(bool, addListener, (const SipCallListenerPtr &listener), (override));
    MOCK_METHOD(bool, GetSipSdkVer, (std::string &ppcLemonVer, std::string &ppcAvatarVer), (override));
    MOCK_METHOD(bool, RecvSipMsg, (const std::string &message), (override));
    MOCK_METHOD(SipCallSessionPtr, SipCall, (const SipCallSessionListenerPtr &listener, const std::string &pcCallerUri, const std::string &pcCalleeUri, const std::string &pcCallId, const std::string &pcContactUri, const std::string &pcOfferSdp, const std::string &pcUriParms, const SipCallExtHdrs &pcExtHdrs), (override));
};

typedef Common::Handle<SipCallInterfaceMock> SipCallInterfaceMockPtr;

class SipCallInterfaceInjection
{
public:
    SipCallInterfaceInjection()
    {
        _sipCall = new SipClient::SipCallInterfaceMock();
        EXPECT_CALL(*_sipCall, GetSipSdkVer(_, _)).WillRepeatedly([](std::string &ver1, std::string &ver2) {
            ver1 = "1.0.0";
            ver2 = "1.0.0";
            return true;
        });

        SipClient::SipCallInterface::injectInstance(_sipCall);
    }

    ~SipCallInterfaceInjection()
    {
        SipClient::SipCallInterface::destroyInstance();
    }

    SipClient::SipCallInterfaceMockPtr _sipCall;
};

} // namespace SipClient
