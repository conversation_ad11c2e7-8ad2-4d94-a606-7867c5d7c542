//
// *****************************************************************************
// Copyright (c) 2025 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//

#pragma once

#include <string>
#include <map>
#include <vector>
#include <sstream>
#include <algorithm>
#include <iostream>
static std::string ltrim(std::string s)
{
    s.erase(s.begin(), std::find_if(s.begin(), s.end(), [](unsigned char ch) {
                return !std::isspace(ch);
            }));
    return s;
}

static std::string rtrim(std::string s)
{
    s.erase(std::find_if(s.rbegin(), s.rend(), [](unsigned char ch) {
                return !std::isspace(ch);
            }).base(),
            s.end());
    return s;
}

static std::string trim(std::string s)
{
    return ltrim(rtrim(s));
}

namespace SipClient
{

class StatsDecorderI
{
private:
    struct StatsNode
    {
        std::string name;
        std::string value;

        std::map<std::string, StatsNode> children;
    };

protected:
    bool __decode(const std::string &stats)
    {
        _root.children.clear();

        // break stats into lines
        std::stringstream ss(stats);
        std::vector<std::string> lines;
        std::string line;
        while (std::getline(ss, line))
        {
            lines.push_back(line);
        }

        return __decode(lines, _root, -1) > 0;
    }

    std::string __get(const std::string &path)
    {
        return __get(path, _root);
    }

private:
    int __decode(const std::vector<std::string> &lines, StatsNode &parent, int parentIndent)
    {
        int currentIndent = -1;
        int count = 0;
        StatsNode *lastNode = nullptr;
        int lastIndent = -1;
        for (auto it = lines.begin(); it != lines.end(); it++)
        {
            if (trim(*it).empty())
                continue;

            int indent = __getIndent(*it);
            if (currentIndent < 0)
                currentIndent = indent;

            if (indent <= parentIndent)
            {
                break;
            }

            if (indent > currentIndent)
            {
                int childrenCount = __decode(std::vector<std::string>(it, lines.end()), *lastNode, lastIndent);
                it += childrenCount - 1;
                count += childrenCount;
            }
            else
            {
                count++;
                StatsNode node;
                size_t pos = it->find(':');
                if (pos == std::string::npos)
                {
                    node.name = trim(*it);
                }
                else
                {
                    node.name = trim(it->substr(0, pos));
                    node.value = trim(it->substr(pos + 1));
                }

                parent.children[node.name] = node;
                lastNode = &parent.children[node.name];
                lastIndent = indent;
            }
        }

        return count;
    }

    int __getIndent(const std::string &line)
    {
        int indent = 0;
        for (char c : line)
        {
            if (c == ' ')
            {
                indent++;
            }
            else
            {
                break;
            }
        }

        return indent;
    }

    std::string __get(const std::string &path, StatsNode &node)
    {
        if (path.empty())
        {
            return node.value;
        }

        size_t pos = path.find('#');
        if (pos == 0)
        {
            return __get(path.substr(1), node);
        }

        std::string key = path.substr(0, pos);
        auto it = node.children.find(key);
        if (it == node.children.end())
        {
            return "";
        }

        if (pos == std::string::npos)
        {
            return it->second.value;
        }

        return __get(path.substr(pos + 1), it->second);
    }

private:
    StatsNode _root;
};

} // namespace SipClient
