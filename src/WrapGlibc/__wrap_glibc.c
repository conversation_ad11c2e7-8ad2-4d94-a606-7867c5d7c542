#include <unistd.h>
#include <fcntl.h>
#include <limits.h>
#include <stdarg.h>
#include <stdint.h>
#include <string.h>
#include <math.h>
#include <stdio.h>
#include <stdlib.h>
#include <glob.h>
#ifdef __GNUC__
#define GCC_VERSION (__GNUC__ * 10000 + __GNUC_MINOR__ * 100  + __GNUC_PATCHLEVEL__)
#else
#define GCC_VERSION  0
#endif

/* 目前最低支持 glibc 2.2.5,可以兼容至 CentOS6
 * 可通如下命令定位函数
 *    objdump -T libjsep.so | grep GLIBC_2.28
 * 可通过如下命令,打印当前 glibc 版本
 *    ldd --version
 *
 * 各平台 glibc 的默认版本
 * CentOS6      2.12
 * CentOS7      2.17
 * Ubuntu16     2.23
 * Ubuntu18     2.27
 * debein10     2.28
 */
#define _JOIN(x,y,z) x ## y ## z
#define _OLD_NAME(f) _JOIN(__, f, _old)
#define _WRAP_NAME(f) _JOIN(__wrap, _, f)
#define _ASM_STR(f,v) __asm__(".symver __" #f "_old, " #f "@GLIBC_" #v)

_ASM_STR(glob, 2.2.5);
int _OLD_NAME(glob)(const char *__restrict __pattern, int __flags,
                      int (*__errfunc)(const char *, int),
                      glob_t *__restrict __pglob);
int _WRAP_NAME(glob)(const char *__restrict __pattern, int __flags,
                       int (*__errfunc)(const char *, int),
                       glob_t *__restrict __pglob)
{
    return _OLD_NAME(glob)(__pattern, __flags, __errfunc, __pglob);
}

int __getentropy_old(void *buffer, size_t length)
{
    return 0;
}
int _WRAP_NAME(getentropy)(void *buffer, size_t length)
{
    return _OLD_NAME(getentropy)(buffer, length);
}

// _ASM_STR(memcpy, 2.17);
// void *_OLD_NAME(memcpy)(void *, const void *, size_t);
// void *_WRAP_NAME(memcpy)(void *dest, const void *src, size_t n) { return _OLD_NAME(memcpy)(dest, src, n); }

/* float (*)(float) 2.17 的函数*/
#define WRAP_GLIB_FF(f)            \
    _ASM_STR(f, 2.2.5);            \
    float _OLD_NAME(f)(float arg); \
    float _WRAP_NAME(f)(float arg) \
    {                              \
        return _OLD_NAME(f)(arg);  \
    }

WRAP_GLIB_FF(logf)
WRAP_GLIB_FF(log2f)
WRAP_GLIB_FF(expf)
WRAP_GLIB_FF(exp2f)

#define WRAP_GLIB_DD(f)              \
    _ASM_STR(f, 2.2.5);              \
    double _OLD_NAME(f)(double arg); \
    double _WRAP_NAME(f)(double arg) \
    {                                \
        return _OLD_NAME(f)(arg);    \
    }

WRAP_GLIB_DD(log)
WRAP_GLIB_DD(exp)

#define WRAP_GLIB_FFF(f)                        \
    _ASM_STR(f, 2.2.5);                         \
    float _OLD_NAME(f)(float arg1, float arg2); \
    float _WRAP_NAME(f)(float arg1, float arg2) \
    {                                           \
        return _OLD_NAME(f)(arg1, arg2);        \
    }

WRAP_GLIB_FFF(powf)

#define WRAP_GLIB_DDD(f)                           \
    _ASM_STR(f, 2.2.5);                            \
    double _OLD_NAME(f)(double arg1, double arg2); \
    double _WRAP_NAME(f)(double arg1, double arg2) \
    {                                              \
        return _OLD_NAME(f)(arg1, arg2);           \
    }

WRAP_GLIB_DDD(pow)

#define WRAP_GLIB_FMEMOPEN(f)                                     \
    _ASM_STR(f, 2.2.5);                                           \
    FILE *_OLD_NAME(f)(void *buf, size_t size, const char *mode); \
    FILE *_WRAP_NAME(f)(void *buf, size_t size, const char *mode) \
    {                                                             \
        return _OLD_NAME(f)(buf, size, mode);                     \
    }

WRAP_GLIB_FMEMOPEN(fmemopen)

// _ASM_STR(fcntl, 2.17);
// int _OLD_NAME(fcntl)(int fd, int cmd, ...);
// int _WRAP_NAME(fcntl64)(int fd, int cmd, ...)
// {
//   int result;
//   va_list va;
//   va_start(va, cmd);

//   switch (cmd) {
//     //
//     // File descriptor flags
//     //
//   case F_GETFD: goto takes_void;
//   case F_SETFD: goto takes_int;

//                 // File status flags
//                 //
//   case F_GETFL: goto takes_void;
//   case F_SETFL: goto takes_int;

//                 // File byte range locking, not held across fork() or clone()
//                 //
//   case F_SETLK: goto takes_flock_ptr_INCOMPATIBLE;
//   case F_SETLKW: goto takes_flock_ptr_INCOMPATIBLE;
//   case F_GETLK: goto takes_flock_ptr_INCOMPATIBLE;

//                 // File byte range locking, held across fork()/clone() -- Not POSIX
//                 //
//   case F_OFD_SETLK: goto takes_flock_ptr_INCOMPATIBLE;
//   case F_OFD_SETLKW: goto takes_flock_ptr_INCOMPATIBLE;
//   case F_OFD_GETLK: goto takes_flock_ptr_INCOMPATIBLE;

//                     // Managing I/O availability signals
//                     //
//   case F_GETOWN: goto takes_void;
//   case F_SETOWN: goto takes_int;
//   case F_GETOWN_EX: goto takes_f_owner_ex_ptr;
//   case F_SETOWN_EX: goto takes_f_owner_ex_ptr;
//   case F_GETSIG: goto takes_void;
//   case F_SETSIG: goto takes_int;

//                  // Notified when process tries to open or truncate file (Linux 2.4+)
//                  //
//   case F_SETLEASE: goto takes_int;
//   case F_GETLEASE: goto takes_void;

//                    // File and directory change notification
//                    //
//   case F_NOTIFY: goto takes_int;

//                  // Changing pipe capacity (Linux 2.6.35+)
//                  //
//   case F_SETPIPE_SZ: goto takes_int;
//   case F_GETPIPE_SZ: goto takes_void;

//                      // File sealing (Linux 3.17+)
//                      //
//   case F_ADD_SEALS: goto takes_int;
//   case F_GET_SEALS: goto takes_void;

//                     // File read/write hints (Linux 4.13+)
//                     //
//   case F_GET_RW_HINT: goto takes_uint64_t_ptr;
//   case F_SET_RW_HINT: goto takes_uint64_t_ptr;
//   case F_GET_FILE_RW_HINT: goto takes_uint64_t_ptr;
//   case F_SET_FILE_RW_HINT: goto takes_uint64_t_ptr;

//   default: fprintf(stderr, "fcntl64 workaround got unknown F_XXX constant");
//   }

// takes_void:
//   va_end(va);
//   return _OLD_NAME(fcntl)(fd, cmd);

// takes_int:
//   result = _OLD_NAME(fcntl)(fd, cmd, va_arg(va, int));
//   va_end(va);
//   return result;

// takes_flock_ptr_INCOMPATIBLE:
//   //
//   // !!! This is the breaking case: the size of the flock
//   // structure changed to accommodate larger files.  If you
//   // need this, you'll have to define a compatibility struct
//   // with the older glibc and make your own entry point using it,
//   // then call fcntl64() with it directly (bear in mind that has
//   // been remapped to the old fcntl())
//   //
//   fprintf(stderr, "fcntl64 hack can't use glibc flock directly");
//   exit(1);

// takes_f_owner_ex_ptr:
//   result = _OLD_NAME(fcntl)(fd, cmd, va_arg(va, struct f_owner_ex*));
//   va_end(va);
//   return result;

// takes_uint64_t_ptr:
//   result = _OLD_NAME(fcntl)(fd, cmd, va_arg(va, uint64_t*));
//   va_end(va);
//   return result;
// }

