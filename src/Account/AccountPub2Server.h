﻿//
// *****************************************************************************
// Copyright(c) 2017-2025 Juphoon System Software Co., LTD. All rights reserved.
// *****************************************************************************
//
// Auto generated from: AccountPub2.def
// Warning: do not edit this file.
//

#ifndef __Account_AccountPub2Server_h
#define __Account_AccountPub2Server_h

#include "Account/AccountPub2Pub.h"

namespace Account
{

class AccountServer : virtual public Common::ObjectServer
{
public:
    virtual bool __ex(const Common::ServerCallPtr& __call,const Common::String& __cmd,const Common::IputStreamPtr& __iput);

    virtual void sendOnlineMessage_begin(const Common::ServerCallPtr& __call,const Common::String& type,const Common::StrStrMap& params,const Common::Stream& message) = 0;

    static void sendOnlineMessage_end(const Common::ServerCallPtr& __call,bool __ret);

    static inline void sendOnlineMessage_end(const Common::ServerCallPtr &__call,const Common::CallError &error)
    {
        __call->setError(error);
        sendOnlineMessage_end(__call,false);
    }

private:
    void __cmd_sendOnlineMessage(const Common::ServerCallPtr& __call,const Common::IputStreamPtr& __iput);
};

};//namespace: Account

#endif //__Account_AccountPub2Server_h
