﻿//
// *****************************************************************************
// Copyright(c) 2017-2025 Juphoon System Software Co., LTD. All rights reserved.
// *****************************************************************************
//
// Auto generated from: AccountPub2.def
// Warning: do not edit this file.
//

#include "Account/AccountPub2Agent.h"

namespace Account
{

bool AccountAgent::sendOnlineMessage(const Common::String& type,const Common::StrStrMap& params,const Common::Stream& message,const Common::CallParamsPtr& __params) const throw()
{
    try
    {
        int __loop = 0;
        while (1)
        {
            Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
            Common::VerListPtr __vers = __agent->verList("sendOnlineMessage.Account.Account");
            short __ver = 0;
            if (__vers)
            {
                __ver = __vers->ver(true);
                if (__ver > 0) __ver = 0;
            }
            switch (__ver)
            {
            case 0:
                __oput->write((short)1);
                __oput->write((short)__ver);
                __oput->write(type);
                Common::__write_StrStrMap(__oput,params);
                __oput->write(message);
                break;
            default:
                throw Common::CallException(Common::ObjectAgent::versionError("sendOnlineMessage.Account.Account"));
            }
            Common::IputStreamPtr __iput;
            Common::CallError __error;
            int __rslt = __agent->ex_sync("sendOnlineMessage.Account.Account",__oput,__iput,__params,__error);
            if (__rslt == -1)
                throw Common::CallException(__error);
            if (__rslt>>16)
            {
                Assert((__rslt>>16) == VersionException);
                __loop ++;
                if (__loop >= 3)
                    throw Common::CallException(Common::ObjectAgent::versionError("sendOnlineMessage.Account.Account"));
                continue;
            }
            bool __ret;
            switch (__rslt)
            {
            case 0:
                __iput->read(__ret);
                break;
            default:
                throw Common::CallException(Common::ObjectAgent::versionError("sendOnlineMessage.Account.Account"));
            }
            Common::ObjectAgent::processFinal(__iput);
            return __ret;
        }
    }
    catch (const Common::CallException& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
    catch (const Common::Exception& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
}

void AccountAgent::sendOnlineMessage_begin(const Common::AgentAsyncPtr& __async,const Common::String& type,const Common::StrStrMap& params,const Common::Stream& message,const Common::CallParamsPtr& __params,const Common::ObjectPtr& __userdata) const throw()
{
    class __AgentAsync : public Common::AgentAsync
    {
    public:
        __AgentAsync(const Common::ObjectAgentPtr& __agent,const Common::AgentAsyncPtr& __async,const Common::String& type,const Common::StrStrMap& params,const Common::Stream& message,const Common::CallParamsPtr& __params,const Common::ObjectPtr& __userdata)
            : x__agent(__agent),x__async(__async),x_type(type),x_params(params),x_message(message),x__params(__params),x__userdata(__userdata),x__loop(0) {}

        void start()
        {
            Common::AgentAsyncPtr pthis = this;
            try
            {
                Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
                Common::VerListPtr __vers = x__agent->verList("sendOnlineMessage.Account.Account");
                short __ver = 0;
                if (__vers)
                {
                    __ver = __vers->ver(true);
                    if (__ver > 0) __ver = 0;
                }
                switch (__ver)
                {
                case 0:
                    __oput->write((short)1);
                    __oput->write((short)__ver);
                    __oput->write(x_type);
                    Common::__write_StrStrMap(__oput,x_params);
                    __oput->write(x_message);
                    break;
                default:
                    throw Common::CallException(Common::ObjectAgent::versionError("sendOnlineMessage.Account.Account"));
                }
                x__agent->ex_async(this,"sendOnlineMessage.Account.Account",__oput,x__params,0);
            }
            catch (const Common::CallException& ex)
            {
                Common::ObjectAgent::throwException(x__async,ex,x__userdata);
            }
            catch (const Common::Exception& ex)
            {
                Common::ObjectAgent::throwException(x__async,ex,x__userdata);
            }
        }
        void cmdResult(int __rslt,const Common::IputStreamPtr& __iput,const Common::ObjectPtr& __userdata)
        {
            if ((__rslt>>16)!=VersionException)
            {
                if (x__async) x__async->cmdResult(__rslt,__iput,x__userdata);
                return;
            }
            x__loop ++;
            if (x__loop >= 3)
            {
                if (x__async)
                    Common::ObjectAgent::throwException(x__async, Common::CallException(Common::ObjectAgent::versionError("sendOnlineMessage.Account.Account")), x__userdata);
                return;
            }
            start();
        }
    private:
        Common::ObjectAgentPtr x__agent;
        Common::AgentAsyncPtr x__async;
        Common::String x_type;
        Common::StrStrMap x_params;
        Common::Stream x_message;
        Common::CallParamsPtr x__params;
        Common::ObjectPtr x__userdata;
        int x__loop;
    };
    (new __AgentAsync(__agent,__async,type,params,message,__params,__userdata))->start();
}

bool AccountAgent::sendOnlineMessage_end(int __rslt,const Common::IputStreamPtr& __iput) throw()
{
    try
    {
        Common::CallError __error;
        if (Common::ObjectAgent::processException(__rslt,__iput,__error))
            throw Common::CallException(__error);
        Assert((__rslt>>16) == 0);
        bool __ret;
        switch (__rslt)
        {
        case 0:
            __iput->read(__ret);
            break;
        default:
            throw Common::CallException(Common::ObjectAgent::versionError("sendOnlineMessage.Account.Account"));
        }
        Common::ObjectAgent::processFinal(__iput);
        return __ret;
    }
    catch (const Common::CallException& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
    catch (const Common::Exception& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
}

};//namespace: Account
