// Generated by MockTools.py. All rights reserved by Juphoon System Software.

#include "gmock/gmock.h"
#include "Account/AccountPub2Server.h"

#ifndef __Account_AccountPub2ServerMock_h__
#define __Account_AccountPub2ServerMock_h__

namespace ModuleTest
{
    class AccountServerMockBase;
    typedef Common::Handle<AccountServerMockBase> AccountServerMockBasePtr;
    class AccountServerMockBase : public Account::AccountServer
    {
    public:
        virtual bool __ex(const Common::ServerCallPtr &__call, const Common::String &__cmd, const Common::IputStreamPtr &__iput) override
        {
            return Account::AccountServer::__ex(__call, __cmd, __iput);
        }

        MOCK_METHOD(void, sendOnlineMessage_begin, (const Common::ServerCallPtr &__call, const Common::String &type, const Common::StrStrMap &params, const Common::Stream &message), (override));

        void delegateToBad()
        {
            ON_CALL(*this, sendOnlineMessage_begin).WillByDefault([this](const Common::ServerCallPtr &__call, const Common::String &type, const Common::StrStrMap &params, const Common::Stream &message) {
                sendOnlineMessage_end(__call, false);
            });
        }

        void delegateToNice()
        {
            ON_CALL(*this, sendOnlineMessage_begin).WillByDefault([this](const Common::ServerCallPtr &__call, const Common::String &type, const Common::StrStrMap &params, const Common::Stream &message) {
                sendOnlineMessage_end(__call, true);
            });
        }
    };
}

#endif // __Account_AccountPub2ServerMock_h__