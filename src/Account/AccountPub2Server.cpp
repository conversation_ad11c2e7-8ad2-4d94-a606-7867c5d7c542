﻿//
// *****************************************************************************
// Copyright(c) 2017-2025 Juphoon System Software Co., LTD. All rights reserved.
// *****************************************************************************
//
// Auto generated from: AccountPub2.def
// Warning: do not edit this file.
//

#include "Account/AccountPub2Server.h"

namespace Account
{

bool AccountServer::__ex(const Common::ServerCallPtr& __call,const Common::String& __cmd,const Common::IputStreamPtr& __iput)
{
    if (__cmd == "sendOnlineMessage.Account.Account") { __cmd_sendOnlineMessage(__call,__iput);return true;}
    return false;
}

void AccountServer::sendOnlineMessage_end(const Common::ServerCallPtr& __call,bool __ret)
{
    Common::VerListPtr __vers = __call->verList();
    Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
    short __ver = (__vers)?__vers->ver(true):0;
    switch (__ver)
    {
    case 0:
        __oput->write(__ret);
        break;
    }
    __call->cmdResult(__ver,__oput);
}

void AccountServer::__cmd_sendOnlineMessage(const Common::ServerCallPtr& __call,const Common::IputStreamPtr& __iput)
{
    do
    {
        Common::VerListPtr __vers = __call->verList();
        Common::String type;Common::StrStrMap params;Common::Stream message;
        switch (__vers->ver(false))
        {
        case 0:
            __iput->read(type);
            Common::__read_StrStrMap(__iput,params);
            __iput->read(message);
            break;
        default: goto __ver_err;
        }
        __start(false);
        sendOnlineMessage_begin(__call,type,params,message);
        return;
    } while(0);
__ver_err:
    Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
    __oput->write((short)1);
    __oput->write((short)0);
    __call->cmdResult(VersionException<<16,__oput);
}

};//namespace: Account
