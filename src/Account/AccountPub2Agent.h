﻿//
// *****************************************************************************
// Copyright(c) 2017-2025 Juphoon System Software Co., LTD. All rights reserved.
// *****************************************************************************
//
// Auto generated from: AccountPub2.def
// Warning: do not edit this file.
//

#ifndef __Account_AccountPub2Agent_h
#define __Account_AccountPub2Agent_h

#include "Account/AccountPub2Pub.h"

namespace Account
{

/* Account/account */
class AccountAgent : public Common::Agent
{
public:
    AccountAgent(int zero = 0) : Common::Agent(zero) {}
    AccountAgent(const Common::Agent& agent) : Common::Agent(agent) {}
    AccountAgent(const Common::ObjectAgentPtr& agent) : Common::Agent(agent) {}

    // bool getUserId(out string userId) onexception(false);
    // bool getSessions(out Common.StrSet sessions) onexception(false);
    // bool getUserIdAndSessions(out string userId,out Common.StrSet sessions) onexception(false);
    // bool postOnlineMessage(string type,Common.StrStrMap params,stream message,string exceptSession = "",out int postCount) async onexception(false);
    // 只要有一个session send成功(recvOnlineMessage成功或者push成功),接口返回true.
    bool sendOnlineMessage(const Common::String& type,const Common::StrStrMap& params,const Common::Stream& message,const Common::CallParamsPtr& __params = 0) const throw();
    void sendOnlineMessage_begin(const Common::AgentAsyncPtr& __async,const Common::String& type,const Common::StrStrMap& params,const Common::Stream& message,const Common::CallParamsPtr& __params = 0,const Common::ObjectPtr& __userdata = 0) const throw();
    static bool sendOnlineMessage_end(int __rslt,const Common::IputStreamPtr& __iput) throw();
};

};//namespace: Account

#endif //__Account_AccountPub2Agent_h
