﻿//
// *****************************************************************************
// Copyright(c) 2017-2022 Juphoon System Software Co., LTD. All rights reserved.
// *****************************************************************************
//
// Auto generated from: MpCallEb.def
// Warning: do not edit this file.
//

#include "AcdGateway/MpCallEbServer.h"

namespace AcdGateway
{

bool AcdGatewayServer::__ex(const Common::ServerCallPtr& __call,const Common::String& __cmd,const Common::IputStreamPtr& __iput)
{
    if (__cmd == "getWaitSize.AcdGateway.AcdGateway") { __cmd_getWaitSize(__call,__iput);return true;}
    if (__cmd == "getQueueLength.AcdGateway.AcdGateway") { __cmd_getQueueLength(__call,__iput);return true;}
    return false;
}

void AcdGatewayServer::getWaitSize_end(const Common::ServerCallPtr& __call,bool __ret,const Common::StrStrMap& outParams)
{
    Common::VerListPtr __vers = __call->verList();
    Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
    short __ver = (__vers)?__vers->ver(true):0;
    switch (__ver)
    {
    case 0:
        __oput->write(__ret);
        Common::__write_StrStrMap(__oput,outParams);
        break;
    }
    __call->cmdResult(__ver,__oput);
}

void AcdGatewayServer::getQueueLength_end(const Common::ServerCallPtr& __call,bool __ret,const Common::StrStrMap& outParams)
{
    Common::VerListPtr __vers = __call->verList();
    Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
    short __ver = (__vers)?__vers->ver(true):0;
    switch (__ver)
    {
    case 0:
        __oput->write(__ret);
        Common::__write_StrStrMap(__oput,outParams);
        break;
    }
    __call->cmdResult(__ver,__oput);
}

void AcdGatewayServer::__cmd_getWaitSize(const Common::ServerCallPtr& __call,const Common::IputStreamPtr& __iput)
{
    do
    {
        Common::VerListPtr __vers = __call->verList();
        Common::String httpAddress;Common::StrStrMap inParams;
        switch (__vers->ver(false))
        {
        case 0:
            __iput->read(httpAddress);
            Common::__read_StrStrMap(__iput,inParams);
            break;
        default: goto __ver_err;
        }
        __start(false);
        getWaitSize_begin(__call,httpAddress,inParams);
        return;
    } while(0);
__ver_err:
    Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
    __oput->write((short)1);
    __oput->write((short)0);
    __call->cmdResult(VersionException<<16,__oput);
}

void AcdGatewayServer::__cmd_getQueueLength(const Common::ServerCallPtr& __call,const Common::IputStreamPtr& __iput)
{
    do
    {
        Common::VerListPtr __vers = __call->verList();
        int vcid=0;int skill=0;Common::StrStrMap inParams;
        switch (__vers->ver(false))
        {
        case 0:
            __iput->read(vcid);
            __iput->read(skill);
            Common::__read_StrStrMap(__iput,inParams);
            break;
        default: goto __ver_err;
        }
        __start(false);
        getQueueLength_begin(__call,vcid,skill,inParams);
        return;
    } while(0);
__ver_err:
    Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
    __oput->write((short)1);
    __oput->write((short)0);
    __call->cmdResult(VersionException<<16,__oput);
}

};//namespace: AcdGateway
