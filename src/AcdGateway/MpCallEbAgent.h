﻿//
// *****************************************************************************
// Copyright(c) 2017-2022 Juphoon System Software Co., LTD. All rights reserved.
// *****************************************************************************
//
// Auto generated from: MpCallEb.def
// Warning: do not edit this file.
//

#ifndef __AcdGateway_MpCallEbAgent_h
#define __AcdGateway_MpCallEbAgent_h

#include "AcdGateway/MpCallEbPub.h"

namespace AcdGateway
{

class AcdGatewayAgent : public Common::Agent
{
public:
    AcdGatewayAgent(int zero = 0) : Common::Agent(zero) {}
    AcdGatewayAgent(const Common::Agent& agent) : Common::Agent(agent) {}
    AcdGatewayAgent(const Common::ObjectAgentPtr& agent) : Common::Agent(agent) {}

    bool getWaitSize(const Common::String& httpAddress,const Common::StrStrMap& inParams,Common::StrStrMap& outParams,const Common::CallParamsPtr& __params = 0) const throw();
    void getWaitSize_begin(const Common::AgentAsyncPtr& __async,const Common::String& httpAddress,const Common::StrStrMap& inParams,const Common::CallParamsPtr& __params = 0,const Common::ObjectPtr& __userdata = 0) const throw();
    static bool getWaitSize_end(int __rslt,const Common::IputStreamPtr& __iput,Common::StrStrMap& outParams) throw();

    bool getQueueLength(int vcid,int skill,const Common::StrStrMap& inParams,Common::StrStrMap& outParams,const Common::CallParamsPtr& __params = 0) const throw();
    void getQueueLength_begin(const Common::AgentAsyncPtr& __async,int vcid,int skill,const Common::StrStrMap& inParams,const Common::CallParamsPtr& __params = 0,const Common::ObjectPtr& __userdata = 0) const throw();
    static bool getQueueLength_end(int __rslt,const Common::IputStreamPtr& __iput,Common::StrStrMap& outParams) throw();
};

};//namespace: AcdGateway

#endif //__AcdGateway_MpCallEbAgent_h
