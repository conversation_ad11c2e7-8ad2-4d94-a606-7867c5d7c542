﻿//
// *****************************************************************************
// Copyright(c) 2017-2022 Juphoon System Software Co., LTD. All rights reserved.
// *****************************************************************************
//
// Auto generated from: MpCallEb.def
// Warning: do not edit this file.
//

#include "AcdGateway/MpCallEbAgent.h"

namespace AcdGateway
{

bool AcdGatewayAgent::getWaitSize(const Common::String& httpAddress,const Common::StrStrMap& inParams,Common::StrStrMap& outParams,const Common::CallParamsPtr& __params) const throw()
{
    try
    {
        int __loop = 0;
        while (1)
        {
            Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
            Common::VerListPtr __vers = __agent->verList("getWaitSize.AcdGateway.AcdGateway");
            short __ver = 0;
            if (__vers)
            {
                __ver = __vers->ver(true);
                if (__ver > 0) __ver = 0;
            }
            switch (__ver)
            {
            case 0:
                __oput->write((short)1);
                __oput->write((short)__ver);
                __oput->write(httpAddress);
                Common::__write_StrStrMap(__oput,inParams);
                break;
            default:
                throw Common::CallException(Common::ObjectAgent::versionError("getWaitSize.AcdGateway.AcdGateway"));
            }
            Common::IputStreamPtr __iput;
            Common::CallError __error;
            int __rslt = __agent->ex_sync("getWaitSize.AcdGateway.AcdGateway",__oput,__iput,__params,__error);
            if (__rslt == -1)
                throw Common::CallException(__error);
            if (__rslt>>16)
            {
                Assert((__rslt>>16) == VersionException);
                __loop ++;
                if (__loop >= 3)
                    throw Common::CallException(Common::ObjectAgent::versionError("getWaitSize.AcdGateway.AcdGateway"));
                continue;
            }
            bool __ret;
            switch (__rslt)
            {
            case 0:
                __iput->read(__ret);
                Common::__read_StrStrMap(__iput,outParams);
                break;
            default:
                throw Common::CallException(Common::ObjectAgent::versionError("getWaitSize.AcdGateway.AcdGateway"));
            }
            Common::ObjectAgent::processFinal(__iput);
            return __ret;
        }
    }
    catch (const Common::CallException& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
    catch (const Common::Exception& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
}

void AcdGatewayAgent::getWaitSize_begin(const Common::AgentAsyncPtr& __async,const Common::String& httpAddress,const Common::StrStrMap& inParams,const Common::CallParamsPtr& __params,const Common::ObjectPtr& __userdata) const throw()
{
    class __AgentAsync : public Common::AgentAsync
    {
    public:
        __AgentAsync(const Common::ObjectAgentPtr& __agent,const Common::AgentAsyncPtr& __async,const Common::String& httpAddress,const Common::StrStrMap& inParams,const Common::CallParamsPtr& __params,const Common::ObjectPtr& __userdata)
            : x__agent(__agent),x__async(__async),x_httpAddress(httpAddress),x_inParams(inParams),x__params(__params),x__userdata(__userdata),x__loop(0) {}

        void start()
        {
            Common::AgentAsyncPtr pthis = this;
            try
            {
                Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
                Common::VerListPtr __vers = x__agent->verList("getWaitSize.AcdGateway.AcdGateway");
                short __ver = 0;
                if (__vers)
                {
                    __ver = __vers->ver(true);
                    if (__ver > 0) __ver = 0;
                }
                switch (__ver)
                {
                case 0:
                    __oput->write((short)1);
                    __oput->write((short)__ver);
                    __oput->write(x_httpAddress);
                    Common::__write_StrStrMap(__oput,x_inParams);
                    break;
                default:
                    throw Common::CallException(Common::ObjectAgent::versionError("getWaitSize.AcdGateway.AcdGateway"));
                }
                x__agent->ex_async(this,"getWaitSize.AcdGateway.AcdGateway",__oput,x__params,0);
            }
            catch (const Common::CallException& ex)
            {
                Common::ObjectAgent::throwException(x__async,ex,x__userdata);
            }
            catch (const Common::Exception& ex)
            {
                Common::ObjectAgent::throwException(x__async,ex,x__userdata);
            }
        }
        void cmdResult(int __rslt,const Common::IputStreamPtr& __iput,const Common::ObjectPtr& __userdata)
        {
            if ((__rslt>>16)!=VersionException)
            {
                if (x__async) x__async->cmdResult(__rslt,__iput,x__userdata);
                return;
            }
            x__loop ++;
            if (x__loop >= 3)
            {
                if (x__async)
                    Common::ObjectAgent::throwException(x__async, Common::CallException(Common::ObjectAgent::versionError("getWaitSize.AcdGateway.AcdGateway")), x__userdata);
                return;
            }
            start();
        }
    private:
        Common::ObjectAgentPtr x__agent;
        Common::AgentAsyncPtr x__async;
        Common::String x_httpAddress;
        Common::StrStrMap x_inParams;
        Common::CallParamsPtr x__params;
        Common::ObjectPtr x__userdata;
        int x__loop;
    };
    (new __AgentAsync(__agent,__async,httpAddress,inParams,__params,__userdata))->start();
}

bool AcdGatewayAgent::getWaitSize_end(int __rslt,const Common::IputStreamPtr& __iput,Common::StrStrMap& outParams) throw()
{
    try
    {
        Common::CallError __error;
        if (Common::ObjectAgent::processException(__rslt,__iput,__error))
            throw Common::CallException(__error);
        Assert((__rslt>>16) == 0);
        bool __ret;
        switch (__rslt)
        {
        case 0:
            __iput->read(__ret);
            Common::__read_StrStrMap(__iput,outParams);
            break;
        default:
            throw Common::CallException(Common::ObjectAgent::versionError("getWaitSize.AcdGateway.AcdGateway"));
        }
        Common::ObjectAgent::processFinal(__iput);
        return __ret;
    }
    catch (const Common::CallException& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
    catch (const Common::Exception& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
}

bool AcdGatewayAgent::getQueueLength(int vcid,int skill,const Common::StrStrMap& inParams,Common::StrStrMap& outParams,const Common::CallParamsPtr& __params) const throw()
{
    try
    {
        int __loop = 0;
        while (1)
        {
            Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
            Common::VerListPtr __vers = __agent->verList("getQueueLength.AcdGateway.AcdGateway");
            short __ver = 0;
            if (__vers)
            {
                __ver = __vers->ver(true);
                if (__ver > 0) __ver = 0;
            }
            switch (__ver)
            {
            case 0:
                __oput->write((short)1);
                __oput->write((short)__ver);
                __oput->write(vcid);
                __oput->write(skill);
                Common::__write_StrStrMap(__oput,inParams);
                break;
            default:
                throw Common::CallException(Common::ObjectAgent::versionError("getQueueLength.AcdGateway.AcdGateway"));
            }
            Common::IputStreamPtr __iput;
            Common::CallError __error;
            int __rslt = __agent->ex_sync("getQueueLength.AcdGateway.AcdGateway",__oput,__iput,__params,__error);
            if (__rslt == -1)
                throw Common::CallException(__error);
            if (__rslt>>16)
            {
                Assert((__rslt>>16) == VersionException);
                __loop ++;
                if (__loop >= 3)
                    throw Common::CallException(Common::ObjectAgent::versionError("getQueueLength.AcdGateway.AcdGateway"));
                continue;
            }
            bool __ret;
            switch (__rslt)
            {
            case 0:
                __iput->read(__ret);
                Common::__read_StrStrMap(__iput,outParams);
                break;
            default:
                throw Common::CallException(Common::ObjectAgent::versionError("getQueueLength.AcdGateway.AcdGateway"));
            }
            Common::ObjectAgent::processFinal(__iput);
            return __ret;
        }
    }
    catch (const Common::CallException& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
    catch (const Common::Exception& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
}

void AcdGatewayAgent::getQueueLength_begin(const Common::AgentAsyncPtr& __async,int vcid,int skill,const Common::StrStrMap& inParams,const Common::CallParamsPtr& __params,const Common::ObjectPtr& __userdata) const throw()
{
    class __AgentAsync : public Common::AgentAsync
    {
    public:
        __AgentAsync(const Common::ObjectAgentPtr& __agent,const Common::AgentAsyncPtr& __async,int vcid,int skill,const Common::StrStrMap& inParams,const Common::CallParamsPtr& __params,const Common::ObjectPtr& __userdata)
            : x__agent(__agent),x__async(__async),x_vcid(vcid),x_skill(skill),x_inParams(inParams),x__params(__params),x__userdata(__userdata),x__loop(0) {}

        void start()
        {
            Common::AgentAsyncPtr pthis = this;
            try
            {
                Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
                Common::VerListPtr __vers = x__agent->verList("getQueueLength.AcdGateway.AcdGateway");
                short __ver = 0;
                if (__vers)
                {
                    __ver = __vers->ver(true);
                    if (__ver > 0) __ver = 0;
                }
                switch (__ver)
                {
                case 0:
                    __oput->write((short)1);
                    __oput->write((short)__ver);
                    __oput->write(x_vcid);
                    __oput->write(x_skill);
                    Common::__write_StrStrMap(__oput,x_inParams);
                    break;
                default:
                    throw Common::CallException(Common::ObjectAgent::versionError("getQueueLength.AcdGateway.AcdGateway"));
                }
                x__agent->ex_async(this,"getQueueLength.AcdGateway.AcdGateway",__oput,x__params,0);
            }
            catch (const Common::CallException& ex)
            {
                Common::ObjectAgent::throwException(x__async,ex,x__userdata);
            }
            catch (const Common::Exception& ex)
            {
                Common::ObjectAgent::throwException(x__async,ex,x__userdata);
            }
        }
        void cmdResult(int __rslt,const Common::IputStreamPtr& __iput,const Common::ObjectPtr& __userdata)
        {
            if ((__rslt>>16)!=VersionException)
            {
                if (x__async) x__async->cmdResult(__rslt,__iput,x__userdata);
                return;
            }
            x__loop ++;
            if (x__loop >= 3)
            {
                if (x__async)
                    Common::ObjectAgent::throwException(x__async, Common::CallException(Common::ObjectAgent::versionError("getQueueLength.AcdGateway.AcdGateway")), x__userdata);
                return;
            }
            start();
        }
    private:
        Common::ObjectAgentPtr x__agent;
        Common::AgentAsyncPtr x__async;
        int x_vcid;
        int x_skill;
        Common::StrStrMap x_inParams;
        Common::CallParamsPtr x__params;
        Common::ObjectPtr x__userdata;
        int x__loop;
    };
    (new __AgentAsync(__agent,__async,vcid,skill,inParams,__params,__userdata))->start();
}

bool AcdGatewayAgent::getQueueLength_end(int __rslt,const Common::IputStreamPtr& __iput,Common::StrStrMap& outParams) throw()
{
    try
    {
        Common::CallError __error;
        if (Common::ObjectAgent::processException(__rslt,__iput,__error))
            throw Common::CallException(__error);
        Assert((__rslt>>16) == 0);
        bool __ret;
        switch (__rslt)
        {
        case 0:
            __iput->read(__ret);
            Common::__read_StrStrMap(__iput,outParams);
            break;
        default:
            throw Common::CallException(Common::ObjectAgent::versionError("getQueueLength.AcdGateway.AcdGateway"));
        }
        Common::ObjectAgent::processFinal(__iput);
        return __ret;
    }
    catch (const Common::CallException& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
    catch (const Common::Exception& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
}

};//namespace: AcdGateway
