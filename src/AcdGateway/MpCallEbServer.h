﻿//
// *****************************************************************************
// Copyright(c) 2017-2022 Juphoon System Software Co., LTD. All rights reserved.
// *****************************************************************************
//
// Auto generated from: MpCallEb.def
// Warning: do not edit this file.
//

#ifndef __AcdGateway_MpCallEbServer_h
#define __AcdGateway_MpCallEbServer_h

#include "AcdGateway/MpCallEbPub.h"

namespace AcdGateway
{

class AcdGatewayServer : virtual public Common::ObjectServer
{
public:
    virtual bool __ex(const Common::ServerCallPtr& __call,const Common::String& __cmd,const Common::IputStreamPtr& __iput);

    virtual void getWaitSize_begin(const Common::ServerCallPtr& __call,const Common::String& httpAddress,const Common::StrStrMap& inParams) = 0;
    virtual void getQueueLength_begin(const Common::ServerCallPtr& __call,int vcid,int skill,const Common::StrStrMap& inParams) = 0;

    static void getWaitSize_end(const Common::ServerCallPtr& __call,bool __ret,const Common::StrStrMap& outParams);
    static void getQueueLength_end(const Common::ServerCallPtr& __call,bool __ret,const Common::StrStrMap& outParams);

    static inline void getWaitSize_end(const Common::ServerCallPtr &__call,const Common::CallError &error)
    {
        __call->setError(error);
        getWaitSize_end(__call,false,Common::StrStrMap());
    }
    static inline void getQueueLength_end(const Common::ServerCallPtr &__call,const Common::CallError &error)
    {
        __call->setError(error);
        getQueueLength_end(__call,false,Common::StrStrMap());
    }

private:
    void __cmd_getWaitSize(const Common::ServerCallPtr& __call,const Common::IputStreamPtr& __iput);
    void __cmd_getQueueLength(const Common::ServerCallPtr& __call,const Common::IputStreamPtr& __iput);
};

};//namespace: AcdGateway

#endif //__AcdGateway_MpCallEbServer_h
