//
// *****************************************************************************
// Copyright (c) 2025 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//

#pragma once

#include "Common/Util.h"
#include "SimpleRtpSessionPub.h"

namespace SimpleRtpSession
{

class SimpleRtpSession : public Common::Shared
{
public:
    virtual void close() = 0;
    virtual bool enableVideo(bool enable) = 0;
    virtual std::string genOffer() = 0;
    virtual std::string genAnswer(const std::string &offer) = 0;
    virtual bool setAnswer(const std::string &answer) = 0;
    virtual bool getStats(RtpSessionStats &stats) = 0;
};

typedef Common::Handle<SimpleRtpSession> SimpleRtpSessionPtr;

class SimpleRtpSessionManager;
typedef Common::Handle<SimpleRtpSessionManager> SimpleRtpSessionManagerPtr;

class SimpleRtpSessionManager : public Common::Shared
{
public:
    struct PortRange
    {
        PortRange(unsigned short min, unsigned short max) : min(min), max(max) {}

        unsigned short min;
        unsigned short max;
    };

    static SimpleRtpSessionManagerPtr create(const PortRange &portRange, bool echo);

    virtual SimpleRtpSessionPtr createSession(bool withVideo) = 0;
};

} // namespace SimpleRtpSession
