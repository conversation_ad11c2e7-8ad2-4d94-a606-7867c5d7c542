//
// *****************************************************************************
// Copyright (c) 2025 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//

#include "RtpFileReaderI.h"
#include "Common/Common.h"
#include <cstdio>

#define TRY(expr)                                                                  \
    do                                                                             \
    {                                                                              \
        if (!(expr))                                                               \
        {                                                                          \
            UTIL_LOGFMT_DBG("RtpFileReader", "FAIL at " __FILE__ ":%d", __LINE__); \
            return false;                                                          \
        }                                                                          \
    } while (0)

namespace SimpleRtpSession
{

bool RtpFileReaderBase::open(const std::string &name, const std::string &filePath)
{
    _file = fopen(filePath.c_str(), "rb");
    if (!_file)
    {
        UTIL_LOGFMT_ERR("RtpFileReader", "content:open failed name:%s filePath:%s", name.c_str(), filePath.c_str());
        return false;
    }

    _filePath = filePath;
    _name = name;
    return true;
}

bool RtpFileReaderBase::open(const std::string &name, const unsigned char *data, unsigned int size)
{
    _file = fmemopen((void *)data, size, "rb");
    if (!_file)
    {
        UTIL_LOGFMT_ERR("RtpFileReader", "content:open failed name:%s data size:%d", name.c_str(), size);
        return false;
    }

    _name = name;
    return true;
}

void RtpFileReaderBase::close()
{
    if (_file)
    {
        fclose(_file);
        _file = nullptr;
    }
}

bool RtpFileReaderRtpDump::open(const std::string &name, const std::string &filePath)
{
    if (!RtpFileReaderBase::open(name, filePath))
        return false;

    if (!readHeader())
    {
        close();
        return false;
    }

    UTIL_LOGFMT_IFO("RtpFileReader", "content:rtpdump file open success name:%s filePath:%s", _name.c_str(), _filePath.c_str());
    return true;
}

bool RtpFileReaderRtpDump::open(const std::string &name, const unsigned char *data, unsigned int size)
{
    if (!RtpFileReaderBase::open(name, data, size))
        return false;

    if (!readHeader())
    {
        close();
        return false;
    }

    UTIL_LOGFMT_IFO("RtpFileReader", "content:rtpdump file open success name:%s size:%d", _name.c_str(), size);
    return true;
}

bool RtpFileReaderRtpDump::nextPacket(Packet &packet)
{
    uint8_t *rtp_data = packet.data;
    packet.size = Packet::kMaxPacketBufferSize;

    uint16_t len;
    uint16_t plen;
    uint32_t offset;
    if (!readPacketHeader(len, plen, offset))
    {
        UTIL_LOGFMT_DBG("RtpFileReader", "name:%s reset to file head", _name.c_str());
        fseek(_file, 0, SEEK_SET);
        if (!readHeader())
            return false;

        if (!readPacketHeader(len, plen, offset))
            return false;
    }

    // Use 'len' here because a 'plen' of 0 specifies rtcp.
    len -= kPacketHeaderSize;
    if (packet.size < len)
    {
        UTIL_LOGFMT_DBG("RtpFileReader", "name:%s ERROR: packet.size < len %d < %d", _name.c_str(), packet.size, len);
        return false;
    }

    if (fread(rtp_data, 1, len, _file) != len)
    {
        UTIL_LOGFMT_DBG("RtpFileReader", "name:%s ERROR: fread failed, len:%d", _name.c_str(), len);
        return false;
    }

    packet.size = len;
    packet.timestamp = offset;

    return true;
}

bool RtpFileReaderRtpDump::readHeader()
{
    char firstline[kHeaderLength + 1] = {0};
    if (fgets(firstline, kHeaderLength, _file) == NULL)
    {
        UTIL_LOGFMT_DBG("RtpFileReader", "name:%s ERROR: Can't read from file", _name.c_str());
        return false;
    }

    if (strncmp(firstline, "#!rtpplay", 9) == 0)
    {
        if (strncmp(firstline, "#!rtpplay1.0", 12) != 0)
        {
            UTIL_LOGFMT_DBG("RtpFileReader", "name:%s ERROR: wrong rtpplay version, must be 1.0", _name.c_str());
            return false;
        }
    }
    else if (strncmp(firstline, "#!RTPencode", 11) == 0)
    {
        if (strncmp(firstline, "#!RTPencode1.0", 14) != 0)
        {
            UTIL_LOGFMT_DBG("RtpFileReader", "name:%s ERROR: wrong RTPencode version, must be 1.0", _name.c_str());
            return false;
        }
    }
    else
    {
        UTIL_LOGFMT_DBG("RtpFileReader", "name:%s ERROR: wrong file format of input file", _name.c_str());
        return false;
    }

    uint32_t start_sec;
    uint32_t start_usec;
    uint32_t source;
    uint16_t port;
    uint16_t padding;
    TRY(read(start_sec));
    TRY(read(start_usec));
    TRY(read(source));
    TRY(read(port));
    TRY(read(padding));

    return true;
}

bool RtpFileReaderRtpDump::readPacketHeader(uint16_t &len, uint16_t &plen, uint32_t &offset)
{
    TRY(read(len));
    TRY(read(plen));
    TRY(read(offset));
    return true;
}

bool RtpFileReaderH264::readHeader()
{
    if (_headerSize > 0)
    {
        fseek(_file, _headerSize, SEEK_SET);
        return true;
    }

    if (!RtpFileReaderRtpDump::readHeader())
        return false;

    Packet packet;
    while (nextPacket(packet))
    {
        if (packet.size < 13)
            continue;

        // seek for sps
        if ((packet.data[12] & 0x1f) == 0x07)
        {
            _headerSize = ftell(_file) - (packet.size + kPacketHeaderSize);
            fseek(_file, _headerSize, SEEK_SET);
            return true;
        }
    }

    UTIL_LOGFMT_ERR("RtpFileReader", "name:%s ERROR:no sps found.", _name.c_str());
    return false;
}

RtpFileReaderPtr RtpFileReader::create(const std::string &name, const std::string &filePath)
{
    try
    {
        Common::Handle<RtpFileReaderBase> reader = (name == "H264") ? new RtpFileReaderH264() : new RtpFileReaderRtpDump();
        if (reader->open(name, filePath))
        {
            UTIL_LOGFMT_IFO("RtpFileReader", "content:create rtpdump reader success name:%s filePath:%s", name.c_str(), filePath.c_str());
            return reader;
        }

        UTIL_LOGFMT_ERR("RtpFileReader", "content:create failed name:%s filePath:%s", name.c_str(), filePath.c_str());
        return nullptr;
    }
    catch (const std::exception &e)
    {
        UTIL_LOGFMT_ERR("RtpFileReader", "content:create failed name:%s filePath:%s error:%s", name.c_str(), filePath.c_str(), e.what());
        return nullptr;
    }
}

RtpFileReaderPtr RtpFileReader::create(const std::string &name, const unsigned char *data, unsigned int size)
{
    try
    {
        Common::Handle<RtpFileReaderBase> reader = (name == "H264") ? new RtpFileReaderH264() : new RtpFileReaderRtpDump();
        if (reader->open(name, data, size))
        {
            UTIL_LOGFMT_IFO("RtpFileReader", "content:create rtpdump reader success name:%s size:%d", name.c_str(), size);
            return reader;
        }

        UTIL_LOGFMT_ERR("RtpFileReader", "content:create failed name:%s size:%d", name.c_str(), size);
        return nullptr;
    }
    catch (const std::exception &e)
    {
        UTIL_LOGFMT_ERR("RtpFileReader", "content:create failed name:%s size:%d error:%s", name.c_str(), size, e.what());
        return nullptr;
    }
}

} // namespace SimpleRtpSession
