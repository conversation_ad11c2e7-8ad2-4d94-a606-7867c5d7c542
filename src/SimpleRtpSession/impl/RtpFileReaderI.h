//
// *****************************************************************************
// Copyright (c) 2025 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//

#pragma once

#include "RtpFileReader.h"

namespace SimpleRtpSession
{

class RtpFileReaderBase : public RtpFileReader
{
public:
    RtpFileReaderBase() : _file(nullptr) {}
    virtual ~RtpFileReaderBase() { close(); }

    virtual bool open(const std::string &name, const std::string &filePath);
    virtual bool open(const std::string &name, const unsigned char *data, unsigned int size);
    virtual void close();

protected:
    template <typename T>
    bool read(T &value)
    {
        value = 0;
        for (int i = 0; i < sizeof(value); i++)
        {
            value <<= 8;
            uint8_t byte;
            if (fread(&byte, 1, 1, _file) != 1)
                return false;
            value |= byte;
        }

        return true;
    }

protected:
    std::string _name;
    std::string _filePath;
    FILE *_file;
};

class RtpFileReaderRtpDump : public RtpFileReaderBase
{
public:
    virtual bool open(const std::string &name, const std::string &filePath) override;
    virtual bool open(const std::string &name, const unsigned char *data, unsigned int size) override;
    virtual bool nextPacket(Packet &packet) override;

protected:
    static const int kHeaderLength = 40;
    static const uint16_t kPacketHeaderSize = 8;

    virtual bool readHeader();
    virtual bool readPacketHeader(uint16_t &len, uint16_t &plen, uint32_t &offset);
};

class RtpFileReaderH264 : public RtpFileReaderRtpDump
{
public:
    RtpFileReaderH264() : _headerSize(0) {}

protected:
    virtual bool readHeader() override;

private:
    long _headerSize;
};

} // namespace SimpleRtpSession
