//
// *****************************************************************************
// Copyright (c) 2025 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//

#pragma once

#include "Common/Net.h"
#include "RtpFileSender.h"
#include "SimpleRtpSession/SimpleRtpSession.h"
#include "SimpleRtpSession/base/RtpStats.h"
#include "SimpleSipSession/OfferAnswerModel.h"
#include "Util/PortManager.h"

namespace SimpleRtpSession
{

class Connection : public Common::NetReceiver
{
public:
    explicit Connection(const Common::NetReceiverPtr &listener)
        : _listener(listener)
        , _isRtp(false)
    {
    }

    void setSender(const Common::NetSenderPtr &sender);
    void close();

    bool isRtp() const { return _isRtp; }
    int send(const uint8_t *data, size_t size);

    // implement NetReceiver
    virtual void recv(const unsigned char *data, int dataLen) override;

private:
    bool _isRtp;
    Common::NetReceiverPtr _listener;
    Common::NetSenderPtr _sender;
};

typedef Common::Handle<Connection> ConnectionPtr;

class Stream : public Common::NetReceiver, public RtpFileSender::Listener
{
public:
    Stream(bool audio, const SipMpCall::PortsPtr &ports, bool withRtpSender = true)
        : _audio(audio)
        , _ports(ports)
        , _withRtpSender(withRtpSender)
    {
    }

    bool open(const Common::NetDriverPtr &netDriver);
    void close();
    bool getStats(uint8_t &payloadType, uint8_t &lastPayloadType, RtpStatsTool::NetworkStats &overallNetworkStats, RtpStatsTool::NetworkStats &currentNetworkStats, RtpStatsTool::RtpStats &overallRtpStats, RtpStatsTool::RtpStats &currentRtpStats);

    Common::String getLocalIp();
    int rtpPort() const { return (*_ports)[0]; }
    bool applySdp(const SimpleSipSession::OfferAnswerModel &offerAnswerModel);
    virtual ConnectionPtr createConnection(const Common::NetReceiverPtr &listener) { return new Connection(listener); }

    // implement NetReceiver
    virtual void recv(const unsigned char *data, int dataLen) override;
    virtual Common::NetReceiverPtr recvConnection(const Common::NetSenderPtr &sender) override;

    // implement RtpFileSender::Listener
    virtual void onSendPacket(const uint8_t *data, size_t size) override;

private:
    Common::NetDriverPtr _netDriver;
    bool _audio;
    bool _withRtpSender;
    SipMpCall::PortsPtr _ports;
    Common::NetSenderPtr _rtpListener;
    Common::NetSenderPtr _rtcpListener;
    ConnectionPtr _rtpConn;
    ConnectionPtr _rtcpConn;
    ConnectionPtr _rtpOutgoingConn;
    RtpFileSenderPtr _rtpFileSender;
    std::string _remoteHost;
    int _remotePort;
    RtpStatsTool _rtpStatsTool;
};

typedef Common::Handle<Stream> StreamPtr;

class SimpleRtpSessionI : public SimpleRtpSession
{
public:
    SimpleRtpSessionI() : _currentSdp(nullptr) {}

    bool open(const Common::NetDriverPtr &netDriver, const SipMpCall::PortsPtr &audioPorts, const SipMpCall::PortsPtr &videoPorts, bool withVideo);
    virtual StreamPtr createStream(bool audio, const SipMpCall::PortsPtr &ports) { return new Stream(audio, ports); }

    // implement SimpleRtpSession
    virtual void close() override;
    virtual bool enableVideo(bool enable) override;
    virtual std::string genOffer() override;
    virtual std::string genAnswer(const std::string &offer) override;
    virtual bool setAnswer(const std::string &answer) override;
    virtual bool getStats(RtpSessionStats &stats) override;

private:
    const std::string *_currentSdp;
    SimpleSipSession::OfferAnswerModel _offerAnswerModel;
    StreamPtr _audioStream;
    StreamPtr _videoStream;
};

class SimpleRtpSessionManagerI : public SimpleRtpSessionManager
{
public:
    SimpleRtpSessionManagerI(bool echo) : _echo(echo) {}
    virtual ~SimpleRtpSessionManagerI();

    bool init(const PortRange &portRange);

    // implement SimpleRtpSessionManager
    virtual SimpleRtpSessionPtr createSession(bool withVideo) override;

private:
    SipMpCall::PortManagerPtr _portManager;
    Common::NetDriverPtr _netDriver;
    bool _echo;
};

} // namespace SimpleRtpSession
