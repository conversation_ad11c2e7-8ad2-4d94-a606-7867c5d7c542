//
// *****************************************************************************
// Copyright (c) 2025 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//

#pragma once

#include <string>
#include "Common/Util.h"

namespace SimpleRtpSession
{

class RtpFileSender;
typedef Common::Handle<RtpFileSender> RtpFileSenderPtr;

class RtpFileSender : virtual public Common::Shared
{
public:
    class Listener : virtual public Common::Shared
    {
    public:
        virtual void onSendPacket(const uint8_t *data, size_t size) = 0;
    };

    typedef Common::Handle<Listener> ListenerPtr;

    static RtpFileSenderPtr create(const ListenerPtr &listener, bool audio);

    virtual void close() = 0;
    virtual bool start(const std::string &name, const std::string &filePath, unsigned char payloadType) = 0;
    virtual bool start(const std::string &name, const unsigned char *data, unsigned int size, unsigned char payloadType) = 0;
    virtual void stop() = 0;
};

} // namespace SimpleRtpSession
