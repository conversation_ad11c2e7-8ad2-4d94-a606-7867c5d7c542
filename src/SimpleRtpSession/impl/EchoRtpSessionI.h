#pragma once

#include "SimpleRtpSessionI.h"

namespace SimpleRtpSession
{

class EchoConnection : public Connection
{
public:
    explicit EchoConnection(const Common::NetReceiverPtr &listener)
        : Connection(listener)
    {
    }

    virtual void recv(const unsigned char *data, int dataLen) override
    {
        Connection::send(data, dataLen);
        Connection::recv(data, dataLen);
    }
};

class EchoStream : public Stream
{
public:
    EchoStream(bool audio, const SipMpCall::PortsPtr &ports)
        : Stream(audio, ports, false)
    {
    }

    virtual ConnectionPtr createConnection(const Common::NetReceiverPtr &listener) override { return new EchoConnection(listener); }
};

class EchoRtpSessionI : public SimpleRtpSessionI
{
public:
    virtual StreamPtr createStream(bool audio, const SipMpCall::PortsPtr &ports) override { return new EchoStream(audio, ports); }
};

}
