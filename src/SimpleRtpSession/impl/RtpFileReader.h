//
// *****************************************************************************
// Copyright (c) 2025 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//

#pragma once

#include <stdint.h>
#include <stddef.h>
#include <string>
#include "Common/Util.h"

namespace SimpleRtpSession
{

class RtpFileReader;
typedef Common::Handle<RtpFileReader> RtpFileReaderPtr;

class RtpFileReader : public Common::Shared
{
public:
    struct Packet
    {
        static const size_t kMaxPacketBufferSize = 1500;
        uint8_t data[kMaxPacketBufferSize];
        size_t size;
        uint32_t timestamp;
    };

    static RtpFileReaderPtr create(const std::string &name, const std::string &filePath);
    static RtpFileReaderPtr create(const std::string &name, const unsigned char *data, unsigned int size);

    virtual bool nextPacket(Packet &packet) = 0;
};

} // namespace SimpleRtpSession
