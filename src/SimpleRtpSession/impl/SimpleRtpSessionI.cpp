
//
// *****************************************************************************
// Copyright (c) 2025 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//

#include "SimpleRtpSessionI.h"
#include "SimpleRtpSession/base/RtpStats.h"
#include "SimpleRtpSession/rtp/AMR.rtp.h"
#include "SimpleRtpSession/rtp/H264.rtp.h"
#include "SimpleRtpSession/rtp/PCMU.rtp.h"
#include "SimpleRtpSession/rtp/PCMA.rtp.h"

#include "EchoRtpSessionI.h"

static const std::string AudioSdp = "\
v=0\r\n\
o=- 423353741 423353741 IN IP4 *************\r\n\
s=-\r\n\
c=IN IP4 *************\r\n\
t=0 0\r\n\
m=audio 20476 RTP/AVP 0 8 103 101\r\n\
a=rtpmap:0 PCMU/8000\r\n\
a=rtpmap:8 PCMA/8000\r\n\
a=rtpmap:103 AMR/8000\r\n\
a=fmtp:103 mode-set=0,1,2,3,4,5,6;octet-align=1\r\n\
a=ptime:20\r\n\
a=rtpmap:101 telephone-event/8000\r\n\
a=fmtp:101 0-15\r\n\
a=sendrecv\r\n\
";
static const std::string VideoSdp = "\
v=0\r\n\
o=- 423353741 423353741 IN IP4 *************\r\n\
s=-\r\n\
c=IN IP4 *************\r\n\
t=0 0\r\n\
m=audio 20476 RTP/AVP 0 8 103 101\r\n\
a=rtpmap:0 PCMU/8000\r\n\
a=rtpmap:8 PCMA/8000\r\n\
a=rtpmap:103 AMR/8000\r\n\
a=fmtp:103 mode-set=0,1,2,3,4,5,6;octet-align=1\r\n\
a=ptime:20\r\n\
a=rtpmap:101 telephone-event/8000\r\n\
a=fmtp:101 0-15\r\n\
a=sendrecv\r\n\
m=video 20506 RTP/AVP 121\r\n\
a=rtpmap:121 H264/90000\r\n\
a=fmtp:121 profile-level-id=42801F; packetization-mode=1\r\n\
a=sendrecv\r\n\
";

static std::string getRtpFilePath(const std::string &codec, const unsigned char *&data, unsigned int &size)
{
    if (codec == "AMR")
    {
        data = AMR_rtp;
        size = AMR_rtp_len;
        return "AMR";
    }
    else if (codec == "H264")
    {
        data = H264_rtp;
        size = H264_rtp_len;
        return "H264";
    }
    else if (codec == "PCMU")
    {
        data = PCMU_rtp;
        size = PCMU_rtp_len;
        return "PCMU";
    }
    else if (codec == "PCMA")
    {
        data = PCMA_rtp;
        size = PCMA_rtp_len;
        return "PCMA";
    }

    return "";
}

namespace SimpleRtpSession
{

void Connection::setSender(const Common::NetSenderPtr &sender)
{
    _sender = sender;
    Common::String host;
    int port;
    _sender->getLocal(host, port);
    _isRtp = (port % 2 == 0);
}

void Connection::close()
{
    if (_sender)
    {
        _sender->close();
        _sender = 0;
    }

    _listener = 0;
}

int Connection::send(const uint8_t *data, size_t size)
{
    if (_sender)
    {
        UTIL_LOGFMT_VBS("SimpleRtpSession", "content:send %s dataLen: %d", _isRtp ? "rtp" : "rtcp", size);
        return _sender->send(data, size);
    }

    return 0;
}

void Connection::recv(const unsigned char *data, int dataLen)
{
    Common::NetReceiverPtr listener = _listener;
    if (listener)
        listener->recv(data, dataLen);
}

bool Stream::open(const Common::NetDriverPtr &netDriver)
{
    _rtpListener = netDriver->listen("udp", "", (*_ports)[0], this, true);
    if (!_rtpListener)
    {
        UTIL_LOGFMT_ERR("SimpleRtpSession", "content:listen rtp failed port:%d", (*_ports)[0]);
        return false;
    }

    _rtcpListener = netDriver->listen("udp", "", (*_ports)[1], this, true);
    if (!_rtcpListener)
    {
        UTIL_LOGFMT_ERR("SimpleRtpSession", "content:listen rtcp failed port:%d", (*_ports)[1]);
        close();
        return false;
    }

    _netDriver = netDriver;
    UTIL_LOGFMT_IFO("SimpleRtpSession", "content:open rtp/rtcp port:%d/%d", (*_ports)[0], (*_ports)[1]);
    return true;
}

void Stream::close()
{
    UTIL_LOGFMT_IFO("SimpleRtpSession", "content:close rtp/rtcp port:%d/%d", (*_ports)[0], (*_ports)[1]);

    if (_rtpFileSender)
    {
        _rtpFileSender->close();
        _rtpFileSender = 0;
    }

    if (_rtpListener)
    {
        _rtpListener->close();
        _rtpListener = 0;
    }

    if (_rtcpListener)
    {
        _rtcpListener->close();
        _rtcpListener = 0;
    }

    if (_rtpOutgoingConn)
    {
        _rtpOutgoingConn->close();
        _rtpOutgoingConn = 0;
    }

    if (_rtpConn)
    {
        _rtpConn->close();
        _rtpConn = 0;
    }

    if (_rtcpConn)
    {
        _rtcpConn->close();
        _rtcpConn = 0;
    }

    _ports = 0;
    _netDriver = 0;
}

bool Stream::getStats(uint8_t &payloadType, uint8_t &lastPayloadType, RtpStatsTool::NetworkStats &overallNetworkStats, RtpStatsTool::NetworkStats &currentNetworkStats, RtpStatsTool::RtpStats &overallRtpStats, RtpStatsTool::RtpStats &currentRtpStats)
{
    return _rtpStatsTool.getStats(payloadType, lastPayloadType, overallNetworkStats, currentNetworkStats, overallRtpStats, currentRtpStats);
}

Common::String Stream::getLocalIp()
{
    if (!_rtpListener)
        return "";

    Common::String host;
    int port;
    return _rtpListener->getLocal(host, port) ? host : "";
}

bool Stream::applySdp(const SimpleSipSession::OfferAnswerModel &offerAnswerModel)
{
    Common::NetDriverPtr netDriver = _netDriver;
    if (!netDriver)
    {
        UTIL_LOGFMT_ERR("SimpleRtpSession", "content:net driver is null port:%d", (*_ports)[0]);
        return false;
    }

    std::string codec;
    int payloadType;
    if (!offerAnswerModel.getCodec(_audio, codec, payloadType))
    {
        UTIL_LOGFMT_ERR("SimpleRtpSession", "content:get codec failed port:%d", (*_ports)[0]);
        return false;
    }

    if (!offerAnswerModel.getRemoteHostPort(_audio, _remoteHost, _remotePort))
    {
        UTIL_LOGFMT_ERR("SimpleRtpSession", "content:get remote host port failed port:%d", (*_ports)[0]);
        return false;
    }

    if (!_rtpOutgoingConn)
    {
        ConnectionPtr conn = createConnection(this);
        Common::NetSenderPtr sender = netDriver->connect("udp", "", (*_ports)[0], _remoteHost.c_str(), _remotePort, conn);
        if (!sender)
        {
            UTIL_LOGFMT_WRN("SimpleRtpSession", "content:connect failed port:%d to:%s:%d", (*_ports)[0], _remoteHost.c_str(), _remotePort);
        }
        else
        {
            conn->setSender(sender);
            _rtpOutgoingConn = conn;
        }
    }

    if (!_withRtpSender)
    {
        UTIL_LOGFMT_IFO("SimpleRtpSession", "content:apply sdp codec:%s payloadType:%d port:%d to:%s:%d", codec.c_str(), payloadType, (*_ports)[0], _remoteHost.c_str(), _remotePort);
        return true;
    }

    const unsigned char *data = nullptr;
    unsigned int size = 0;
    std::string filePath = getRtpFilePath(codec, data, size);
    if (filePath.empty())
    {
        UTIL_LOGFMT_ERR("SimpleRtpSession", "content:get rtp file path failed codec:%s port:%d", codec.c_str(), (*_ports)[0]);
        return false;
    }

    if (!_rtpFileSender)
    {
        _rtpFileSender = RtpFileSender::create(this, _audio);
        if (!_rtpFileSender)
        {
            UTIL_LOGFMT_ERR("SimpleRtpSession", "content:create rtp file sender failed port:%d", (*_ports)[0]);
            return false;
        }
    }
    else
    {
        _rtpFileSender->stop();
    }

    if (!_rtpFileSender->start(filePath, data, size, payloadType))
    {
        UTIL_LOGFMT_ERR("SimpleRtpSession", "content:start rtp file sender failed filePath:%s payloadType:%d port:%d", filePath.c_str(), payloadType, (*_ports)[0]);
        return false;
    }

    UTIL_LOGFMT_IFO("SimpleRtpSession", "content:apply sdp codec:%s payloadType:%d port:%d to:%s:%d", codec.c_str(), payloadType, (*_ports)[0], _remoteHost.c_str(), _remotePort);
    return true;
}

void Stream::recv(const unsigned char *data, int dataLen)
{
    _rtpStatsTool.process(data, dataLen);
}

Common::NetReceiverPtr Stream::recvConnection(const Common::NetSenderPtr &sender)
{
    ConnectionPtr conn = createConnection(this);
    Common::String host;
    int port;
    sender->getRemote(host, port);
    conn->setSender(sender);
    if (conn->isRtp())
    {
        UTIL_LOGFMT_IFO("SimpleRtpSession", "content:recv rtp connection port:%d from:%s:%d", (*_ports)[0], host.c_str(), port);
        _rtpConn = conn;
    }
    else
    {
        UTIL_LOGFMT_IFO("SimpleRtpSession", "content:recv rtcp connection port:%d from:%s:%d", (*_ports)[1], host.c_str(), port);
        _rtcpConn = conn;
    }

    return conn;
}

void Stream::onSendPacket(const uint8_t *data, size_t size)
{
    ConnectionPtr conn = _rtpConn;
    if (conn)
    {
        conn->send(data, size);
        return;
    }

    conn = _rtpOutgoingConn;
    if (conn)
    {
        conn->send(data, size);
        return;
    }

    UTIL_LOGFMT_WRN("SimpleRtpSession", "content:on send packet failed, port:%d", (*_ports)[0]);
}

bool SimpleRtpSessionI::open(const Common::NetDriverPtr &netDriver, const SipMpCall::PortsPtr &audioPorts, const SipMpCall::PortsPtr &videoPorts, bool withVideo)
{
    _currentSdp = withVideo ? (&VideoSdp) : (&AudioSdp);
    _offerAnswerModel.init(*_currentSdp);

    _audioStream = createStream(true, audioPorts);
    if (!_audioStream || !_audioStream->open(netDriver))
    {
        UTIL_LOGFMT_ERR("SimpleRtpSession", "content:init audio stream failed port:%d", (*audioPorts)[0]);
        return false;
    }

    _offerAnswerModel.setOption(SimpleSipSession::OfferAnswerModel::OptionIpv4, _audioStream->getLocalIp().c_str());
    _offerAnswerModel.setOption(SimpleSipSession::OfferAnswerModel::OptionAudioPort, std::to_string((*audioPorts)[0]));

    _videoStream = createStream(false, videoPorts);
    if (!_videoStream || !_videoStream->open(netDriver))
    {
        UTIL_LOGFMT_ERR("SimpleRtpSession", "content:init video stream failed port:%d", (*videoPorts)[0]);
        _audioStream->close();
        _audioStream = 0;
        return false;
    }

    if (withVideo)
        _offerAnswerModel.setOption(SimpleSipSession::OfferAnswerModel::OptionVideoPort, std::to_string((*videoPorts)[0]));

    return true;
}

void SimpleRtpSessionI::close()
{
    if (_audioStream)
    {
        _audioStream->close();
        _audioStream = 0;
    }

    if (_videoStream)
    {
        _videoStream->close();
        _videoStream = 0;
    }
}

bool SimpleRtpSessionI::enableVideo(bool enable)
{
    if (enable)
    {
        if (_currentSdp == &AudioSdp)
        {
            _currentSdp = &VideoSdp;
            _offerAnswerModel.init(*_currentSdp);

            _offerAnswerModel.setOption(SimpleSipSession::OfferAnswerModel::OptionIpv4, _audioStream->getLocalIp().c_str());
            _offerAnswerModel.setOption(SimpleSipSession::OfferAnswerModel::OptionAudioPort, std::to_string(_audioStream->rtpPort()));
        }

        _offerAnswerModel.setOption(SimpleSipSession::OfferAnswerModel::OptionVideoPort, std::to_string(_videoStream->rtpPort()));
        _offerAnswerModel.setOption(SimpleSipSession::OfferAnswerModel::OptionVideoState, std::to_string(SimpleSipSession::SdpMstream::StateSendRecv));
    }
    else
    {
        _offerAnswerModel.setOption(SimpleSipSession::OfferAnswerModel::OptionVideoState, std::to_string(SimpleSipSession::SdpMstream::StateInactive));
        _offerAnswerModel.setOption(SimpleSipSession::OfferAnswerModel::OptionVideoPort, "");
    }

    return true;
}

std::string SimpleRtpSessionI::genOffer()
{
    return _offerAnswerModel.genOffer();
}

std::string SimpleRtpSessionI::genAnswer(const std::string &offer)
{
    std::string answer = _offerAnswerModel.genAnswer(offer);

    _audioStream->applySdp(_offerAnswerModel);
    if (_currentSdp == &VideoSdp)
        _videoStream->applySdp(_offerAnswerModel);

    return answer;
}

bool SimpleRtpSessionI::setAnswer(const std::string &answer)
{
    if (!_offerAnswerModel.setAnswer(answer))
        return false;

    _audioStream->applySdp(_offerAnswerModel);
    if (_currentSdp == &VideoSdp)
        _videoStream->applySdp(_offerAnswerModel);

    return true;
}

bool SimpleRtpSessionI::getStats(RtpSessionStats &stats)
{
    #define __COPY_NETWORK_STATS__(__to, __from) \
        __to.count = __from.count; \
        __to.bytes = __from.bytes; \
        __to.bps = __from.bps; \
        __to.pps = __from.pps;

    #define __COPY_RTP_STATS__(__to, __from) \
        __to.count = __from.count; \
        __to.bytes = __from.bytes; \
        __to.lost = __from.lost; \
        __to.bps = __from.bps; \
        __to.pps = __from.pps; \
        __to.lostRate = __from.lostRate;

    uint8_t payloadType;
    uint8_t lastPayloadType;
    RtpStatsTool::NetworkStats overallNetworkStats;
    RtpStatsTool::NetworkStats currentNetworkStats;
    RtpStatsTool::RtpStats overallRtpStats;
    RtpStatsTool::RtpStats currentRtpStats;

    if (_audioStream->getStats(payloadType, lastPayloadType, overallNetworkStats, currentNetworkStats, overallRtpStats, currentRtpStats))
    {
        stats.audio.payloadType = payloadType;
        stats.audio.lastPayloadType = lastPayloadType;
        __COPY_NETWORK_STATS__(stats.audio.overallNetwork, overallNetworkStats);
        __COPY_NETWORK_STATS__(stats.audio.currentNetwork, currentNetworkStats);
        __COPY_RTP_STATS__(stats.audio.overallRtp, overallRtpStats);
        __COPY_RTP_STATS__(stats.audio.currentRtp, currentRtpStats);
    }

    if (_videoStream && _videoStream->getStats(payloadType, lastPayloadType, overallNetworkStats, currentNetworkStats, overallRtpStats, currentRtpStats))
    {
        stats.video.payloadType = payloadType;
        stats.video.lastPayloadType = lastPayloadType;
        __COPY_NETWORK_STATS__(stats.video.overallNetwork, overallNetworkStats);
        __COPY_NETWORK_STATS__(stats.video.currentNetwork, currentNetworkStats);
        __COPY_RTP_STATS__(stats.video.overallRtp, overallRtpStats);
        __COPY_RTP_STATS__(stats.video.currentRtp, currentRtpStats);
    }

    return true;
}

SimpleRtpSessionManagerI::~SimpleRtpSessionManagerI()
{
    _portManager = 0;
    _netDriver->shutdown();
    _netDriver = 0;
}

bool SimpleRtpSessionManagerI::init(const PortRange &portRange)
{
    _netDriver = Common::NetDriver::create(10000);
    if (!_netDriver)
    {
        UTIL_LOG_ERR("SimpleRtpSession", "content:create net driver failed");
        return false;
    }

    _portManager = SipMpCall::PortManager::create(portRange.min, portRange.max);
    return _portManager != 0;
}

SimpleRtpSessionPtr SimpleRtpSessionManagerI::createSession(bool withVideo)
{
    if (!_netDriver ||!_portManager)
    {
        UTIL_LOG_ERR("SimpleRtpSession", "content:init port manager failed");
        return 0;
    }

    SipMpCall::PortsPtr audioPorts = _portManager->allocatePort();
    if (!audioPorts)
    {
        UTIL_LOG_ERR("SimpleRtpSession", "content:allocate audio port failed");
        return 0;
    }

    SipMpCall::PortsPtr videoPorts = _portManager->allocatePort();
    videoPorts = _portManager->allocatePort();
    if (!videoPorts)
    {
        UTIL_LOG_ERR("SimpleRtpSession", "content:allocate video port failed");
        return 0;
    }

    SimpleRtpSessionI *session = _echo ? new EchoRtpSessionI() : new SimpleRtpSessionI();
    if (!session || !session->open(_netDriver, audioPorts, videoPorts, !_echo && withVideo))
    {
        UTIL_LOGFMT_ERR("SimpleRtpSession", "content:open %s failed audio port:%d video port:%d", _echo ? "echo session" : "session", (*audioPorts)[0], (*videoPorts)[0]);
        return 0;
    }

    UTIL_LOGFMT_IFO("SimpleRtpSession", "content:create %s audio port:%d video port:%d", _echo ? "echo session" : "session", (*audioPorts)[0], (*videoPorts)[0]);
    return session;
}

SimpleRtpSessionManagerPtr SimpleRtpSessionManager::create(const PortRange &portRange, bool echo)
{
    try
    {
        SimpleRtpSessionManagerI *manager = new SimpleRtpSessionManagerI(echo);
        if (!manager->init(portRange))
        {
            UTIL_LOG_ERR("SimpleRtpSession", "content:init manager failed");
            delete manager;
            return 0;
        }

        return manager;
    }
    catch (const std::exception &e)
    {
        UTIL_LOGFMT_ERR("SimpleRtpSession", "content:create manager failed %s", e.what());
        return 0;
    }
}

} // namespace SimpleRtpSession
