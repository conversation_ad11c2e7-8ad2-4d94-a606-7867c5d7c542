//
// *****************************************************************************
// Copyright (c) 2025 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//

#pragma once

#include "RtpFileSender.h"
#include "RtpFileReader.h"
#include <cstdint>

namespace SimpleRtpSession
{

class RtpFileSenderI : public RtpFileSender, public Common::Thread
{
public:
    RtpFileSenderI(const ListenerPtr &listener, bool audio)
        : _listener(listener)
        , _reader(nullptr)
        , _payloadType(0)
        , _running(false)
        , _sequenceNumber(0)
        , _timestamp(0)
        , _lastTimestamp(0)
        , _lastFileTimestamp(0xFFFFFFFF)
        , _lastTimestampInterval(0)
        , _audio(audio)
    {
    }

    bool open();

    // implement RtpFileSender
    virtual void close() override;
    virtual bool start(const std::string &name, const std::string &filePath, unsigned char payloadType) override;
    virtual bool start(const std::string &name, const unsigned char *data, unsigned int size, unsigned char payloadType) override;
    virtual void stop() override;

    // implement Thread
    virtual void onRun() override;

private:
    bool _audio;
    ListenerPtr _listener;
    RtpFileReaderPtr _reader;

    uint8_t _payloadType;
    bool _running;
    uint16_t _sequenceNumber;

    uint32_t _timestamp;
    uint32_t _lastTimestamp;
    uint32_t _lastFileTimestamp;
    uint32_t _lastTimestampInterval;
};

} // namespace SimpleRtpSession
