//
// *****************************************************************************
// Copyright (c) 2025 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//

#include "RtpFileSenderI.h"
#include "Common/Util.h"
#include "SimpleRtpSession/impl/RtpFileReader.h"

template <typename T>
void write(uint8_t *data, size_t offset, T value)
{
    for (size_t i = 0; i < sizeof(T); i++)
    {
        data[i + offset] = (value >> (8 * (sizeof(T) - i - 1))) & 0xFF;
    }
}

template <typename T>
T read(uint8_t *data, size_t offset)
{
    T value = 0;
    for (size_t i = 0; i < sizeof(T); i++)
    {
        value = (value << 8) | data[i + offset];
    }

    return value;
}

static void writeRtpHeader(uint8_t *data, size_t size, uint8_t payloadType, uint16_t sequenceNumber, uint32_t timestamp)
{
    write<uint8_t>(data, 1, ((data[1] & 0x80) | (payloadType & 0x7F)));
    write<uint16_t>(data, 2, sequenceNumber);
    write<uint32_t>(data, 4, timestamp);
}

namespace SimpleRtpSession
{

bool RtpFileSenderI::open()
{
    return startRun(0);
}

void RtpFileSenderI::close()
{
    stop();

    if (isRunning())
        stopRun(true);

    _reader = 0;
    _listener = 0;
}

bool RtpFileSenderI::start(const std::string &name, const std::string &filePath, unsigned char payloadType)
{
    if (!isRunning())
        return false;

    _reader = RtpFileReader::create(name, filePath);
    if (!_reader)
        return false;

    _payloadType = payloadType;
    _sequenceNumber = 0;
    _lastFileTimestamp = 0xFFFFFFFF;
    _lastTimestamp = 0;
    _lastTimestampInterval = 0;
    _running = true;

    return true;
}

bool RtpFileSenderI::start(const std::string &name, const unsigned char *data, unsigned int size, unsigned char payloadType)
{
    if (!isRunning())
        return false;

    _reader = RtpFileReader::create(name, data, size);
    if (!_reader)
        return false;

    _payloadType = payloadType;
    _sequenceNumber = 0;
    _lastFileTimestamp = 0xFFFFFFFF;
    _lastTimestamp = 0;
    _lastTimestampInterval = 0;
    _running = true;

    return true;
}

void RtpFileSenderI::stop()
{
    _running = false;
}

void RtpFileSenderI::onRun()
{
    while (isRunning())
    {
        if (!_running)
        {
            Common::sleep(100);
            continue;
        }

        RtpFileReader::Packet packet;
        RtpFileReaderPtr reader = _reader;
        if (!reader || !reader->nextPacket(packet))
        {
            Common::sleep(100);
            continue;
        }

        // skip rtcp
        if (packet.data[1] == 192 || packet.data[1] == 195 || (packet.data[1] >= 200 && packet.data[1] <= 207))
            continue;

        uint32_t fileTimestamp = read<uint32_t>(packet.data, 4);

        if (_lastFileTimestamp == 0xFFFFFFFF)
        {
            _lastFileTimestamp = fileTimestamp;
            _timestamp = fileTimestamp;
            _sequenceNumber = read<uint16_t>(packet.data, 2);
        }
        else
        {
            uint32_t interval = fileTimestamp - _lastFileTimestamp;
            _lastFileTimestamp = fileTimestamp;
            if (interval > 0)
            {
                bool reset = (_audio && interval > 2000) || (!_audio && interval > 10000);
                if (!reset)
                    _lastTimestampInterval = interval;
                _timestamp += _lastTimestampInterval;
            }
        }

        int waitMs = packet.timestamp - _lastTimestamp;
        if (_lastTimestamp == 0 || waitMs < 0)
        {
            waitMs = 0;
            _lastTimestamp = packet.timestamp;
        }

        if (waitMs > 10)
        {
            Common::sleep(waitMs);
            _lastTimestamp = packet.timestamp;
        }

        if (_audio)
            writeRtpHeader(packet.data, packet.size, _payloadType, _sequenceNumber++, _timestamp);
        else
            writeRtpHeader(packet.data, packet.size, _payloadType, _sequenceNumber++, _timestamp);

        UTIL_LOGFMT_VBS("RtpFileSender", "waitMs: %d pt:%d seq:%d ts:%d %s", waitMs, _payloadType, _sequenceNumber, _timestamp, (packet.data[1] & 0x80) ? "mark" : "");
        _listener->onSendPacket(packet.data, packet.size);
    }
}

RtpFileSenderPtr RtpFileSender::create(const ListenerPtr &listener, bool audio)
{
    try
    {
        Common::Handle<RtpFileSenderI> sender = new RtpFileSenderI(listener, audio);
        if (!sender->open())
        {
            UTIL_LOG_ERR("RtpFileSender", "content:create error");
            return nullptr;
        }

        return sender;
    }
    catch (const std::exception &e)
    {
        UTIL_LOGFMT_ERR("RtpFileSender", "content:create failed error:%s", e.what());
        return nullptr;
    }
}

} // namespace SimpleRtpSession
