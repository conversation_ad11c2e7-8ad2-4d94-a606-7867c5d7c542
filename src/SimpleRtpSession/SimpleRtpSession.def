
module SimpleRtpSession
{
    struct NetworkStats
    {
        long count;
        long bytes;
        int bps;
        int pps;
    };

    struct RtpNetworkStats
    {
        long count;
        long bytes;
        int bps;
        int pps;
        long lost;
        int lostRate;
    };

    struct RtpStreamStats
    {
        int payloadType;
        int lastPayloadType;
        NetworkStats overallNetwork;
        NetworkStats currentNetwork;
        RtpNetworkStats overallRtp;
        RtpNetworkStats currentRtp;
    };

    struct RtpSessionStats
    {
        RtpStreamStats audio;
        RtpStreamStats video;
    };
}