//
// *****************************************************************************
// Copyright (c) 2025 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//

#pragma once

#include <stdint.h>
#include <cstddef>

namespace SimpleRtpSession
{

class RtpMessage
{
public:
    RtpMessage();

    bool init(const uint8_t *buffer, size_t size);

    // rtp header
    uint8_t getVersion() const;
    bool getPadding() const;
    bool getExtension() const;
    uint8_t getCsrcCount() const;
    bool getMarker() const;
    uint8_t getPayloadType() const;
    uint16_t getSequenceNumber() const;
    uint32_t getTimestamp() const;
    uint32_t getSsrc() const;
    uint32_t getCsrc(int index) const;
    const uint8_t *getPayload(size_t *size) const;

    // rtcp header
    bool isRtcp() const;
    uint8_t getRtcpType() const;
    uint16_t getLength() const;

private:
    const uint8_t *_buffer;
    size_t _size;
};

} // namespace SimpleRtpSession