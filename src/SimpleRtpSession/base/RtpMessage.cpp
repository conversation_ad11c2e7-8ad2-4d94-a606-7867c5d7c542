//
// *****************************************************************************
// Copyright (c) 2025 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//

#include "RtpMessage.h"

template <typename T>
T read(const uint8_t *buffer, size_t offset, size_t size)
{
    T value = 0;
    for (size_t i = 0; i < size; i++)
    {
        value = (value << 8) | buffer[offset + i];
    }

    return value;
}

namespace SimpleRtpSession
{

RtpMessage::RtpMessage()
    : _buffer(nullptr)
    , _size(0)
{
}

bool RtpMessage::init(const uint8_t *buffer, size_t size)
{
    if (buffer == nullptr || size < 12)
        return false;

    _buffer = buffer;
    _size = size;

    if (isRtcp())
        return true;

    if (getVersion() != 2)
        return false;

    if (_size < 12 + getCsrcCount() * 4)
        return false;

    return true;
}

bool RtpMessage::isRtcp() const
{
    if (getVersion() != 2)
        return false;

    uint8_t rtcpType = getRtcpType();
    if (rtcpType != 192 && rtcpType != 195 && (rtcpType < 200 || rtcpType > 207))
        return false;

    return true;
}

uint8_t RtpMessage::getRtcpType() const
{
    return _buffer[1];
}

uint16_t RtpMessage::getLength() const
{
    return read<uint16_t>(_buffer, 2, 2);
}

uint8_t RtpMessage::getVersion() const
{
    return _buffer[0] >> 6;
}

bool RtpMessage::getPadding() const
{
    return (_buffer[0] & 0x20) != 0;
}

bool RtpMessage::getExtension() const
{
    return (_buffer[0] & 0x10) != 0;
}

uint8_t RtpMessage::getCsrcCount() const
{
    return (_buffer[0] & 0x0F);
}

bool RtpMessage::getMarker() const
{
    return (_buffer[1] & 0x80) != 0;
}

uint8_t RtpMessage::getPayloadType() const
{
    return _buffer[1] & 0x7F;
}

uint16_t RtpMessage::getSequenceNumber() const
{
    return read<uint16_t>(_buffer, 2, 2);
}

uint32_t RtpMessage::getTimestamp() const
{
    return read<uint32_t>(_buffer, 4, 4);
}

uint32_t RtpMessage::getSsrc() const
{
    return read<uint32_t>(_buffer, 8, 4);
}

uint32_t RtpMessage::getCsrc(int index) const
{
    return read<uint32_t>(_buffer, 12 + index * 4, 4);
}

const uint8_t *RtpMessage::getPayload(size_t *size) const
{
    *size = _size - 12 - getCsrcCount() * 4;
    return _buffer + 12 + getCsrcCount() * 4;
}

} // namespace SimpleRtpSession