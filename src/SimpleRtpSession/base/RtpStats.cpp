//
// *****************************************************************************
// Copyright (c) 2025 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//

#include "RtpStats.h"
#include "Common/Util.h"
#include "RtpMessage.h"
#include <cstdint>

namespace SimpleRtpSession
{

void StatsTool::add(int value)
{
    _total += value;
    _avgTool.totalAdd(value);
}

void StatsTool::getStats(int64_t &total, int &avg)
{
    total = _total;
    avg = _avgTool.totalAvg();
}

void StatsTool::getStats(int64_t &total, int64_t &avg)
{
    total = _total;
    avg = _avgTool.totalAvg();
}

bool RtpStatsTool::process(const uint8_t *data, size_t size)
{
    std::lock_guard<std::mutex> lock(_mutex);

    if (_startTicksMs == 0)
        _startTicksMs = Common::getMonoTicks();

    _networkCount.add(1);
    _networkBytes.add(size);

    RtpMessage message;
    if (!message.init(data, size))
    {
        if (_payloadType != 0xff)
        {
            _lastPayloadType = _payloadType;
            _payloadType = 0xff;
        }
        return false;
    }

    if (message.isRtcp())
        return true;

    uint8_t payloadType = message.getPayloadType();
    if (_payloadType != payloadType)
    {
        _lastPayloadType = _payloadType;
        _payloadType = payloadType;
    }

    _rtpCount.add(1);
    _rtpBytes.add(size);

    int16_t sequenceNumber = message.getSequenceNumber();
    if (_lastSequenceNumber >= 0 && _lastSequenceNumber > sequenceNumber + 1)
        _rtpLost.add(_lastSequenceNumber - (sequenceNumber + 1));
    _lastSequenceNumber = sequenceNumber;

    return true;
}

bool RtpStatsTool::getStats(uint8_t &payloadType, uint8_t &lastPayloadType, NetworkStats &overallNetworkStats, NetworkStats &currentNetworkStats, RtpStats &overallRtpStats, RtpStats &currentRtpStats)
{
    std::lock_guard<std::mutex> lock(_mutex);

    Common::Ulong currentTicksMs = Common::getMonoTicks();
    if (_startTicksMs == 0 || currentTicksMs == _startTicksMs)
        return false;

    payloadType = _payloadType;
    lastPayloadType = _lastPayloadType;

    _networkCount.getStats(overallNetworkStats.count, currentNetworkStats.count);
    _networkBytes.getStats(overallNetworkStats.bytes, currentNetworkStats.bytes);
    _rtpCount.getStats(overallRtpStats.count, currentRtpStats.count);
    _rtpBytes.getStats(overallRtpStats.bytes, currentRtpStats.bytes);
    _rtpLost.getStats(overallRtpStats.lost, currentRtpStats.lost);

    overallNetworkStats.bps = overallNetworkStats.bytes * 8000 / (currentTicksMs - _startTicksMs);
    currentNetworkStats.bps = currentNetworkStats.bytes * 8;
    overallNetworkStats.pps = overallNetworkStats.count * 1000 / (currentTicksMs - _startTicksMs);
    currentNetworkStats.pps = currentNetworkStats.count;

    overallRtpStats.bps = overallRtpStats.bytes * 8000 / (currentTicksMs - _startTicksMs);
    currentRtpStats.bps = currentRtpStats.bytes * 8;
    overallRtpStats.pps = overallRtpStats.count * 1000 / (currentTicksMs - _startTicksMs);
    currentRtpStats.pps = currentRtpStats.count;
    overallRtpStats.lostRate = overallRtpStats.count > 0 ? overallRtpStats.lost * 100 / overallRtpStats.count : 0;
    currentRtpStats.lostRate = currentRtpStats.count > 0 ? currentRtpStats.lost * 100 / currentRtpStats.count : 0;

    return true;
}

} // namespace SimpleRtpSession
