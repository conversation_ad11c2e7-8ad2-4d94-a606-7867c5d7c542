//
// *****************************************************************************
// Copyright (c) 2025 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//

#pragma once

#include <cstdint>
#include <mutex>
#include "Common/Util.h"

namespace SimpleRtpSession
{

class StatsTool
{
public:
    StatsTool() : _total(0) {}

    void add(int value);
    void getStats(int64_t &total, int &avg);
    void getStats(int64_t &total, int64_t &avg);

private:
    int64_t _total;
    Common::TotalTool _avgTool;
};

class RtpStatsTool
{
public:
    struct NetworkStats
    {
        NetworkStats() : count(0), bytes(0), bps(0), pps(0) {}

        int64_t count;
        int64_t bytes;
        int bps;
        int pps;
    };

    struct RtpStats
    {
        RtpStats() : count(0), bytes(0), lost(0), bps(0), pps(0), lostRate(0) {}

        int64_t count;
        int64_t bytes;
        int64_t lost;
        int bps;
        int pps;
        int lostRate;
    };

public:
    RtpStatsTool()
        : _lastSequenceNumber(-1)
        , _startTicksMs(0)
        , _payloadType(0xff)
        , _lastPayloadType(0xff)
    {
    }

    bool process(const uint8_t *data, size_t size);
    bool getStats(uint8_t &payloadType, uint8_t &lastPayloadType, NetworkStats &overallNetworkStats, NetworkStats &currentNetworkStats, RtpStats &overallRtpStats, RtpStats &currentRtpStats);

private:
    std::mutex _mutex;
    Common::Ulong _startTicksMs;
    int16_t _lastSequenceNumber;
    uint8_t _lastPayloadType;
    uint8_t _payloadType;

    StatsTool _networkCount;
    StatsTool _networkBytes;
    StatsTool _rtpCount;
    StatsTool _rtpBytes;
    StatsTool _rtpLost;

};

} // namespace SimpleRtpSession
