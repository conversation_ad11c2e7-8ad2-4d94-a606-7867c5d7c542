﻿//
// *****************************************************************************
// Copyright(c) 2017-2025 Juphoon System Software Co., LTD. All rights reserved.
// *****************************************************************************
//
// Auto generated from: src/SimpleRtpSession/SimpleRtpSession.def
// Warning: do not edit this file.
//

#ifndef __SimpleRtpSession_SimpleRtpSessionPub_h
#define __SimpleRtpSession_SimpleRtpSessionPub_h

#include "Common/Common.h"

namespace SimpleRtpSession
{

class NetworkStats
{
public:
    NetworkStats();
    NetworkStats(Common::Long,Common::Long,int,int);

    bool operator<(const NetworkStats&) const;
    bool operator==(const NetworkStats&) const;
    bool operator!=(const NetworkStats&__obj) const { return !operator==(__obj);}
    void __write(const Common::OputStreamPtr&) const;
    void __read(const Common::IputStreamPtr&);
    void __textWrite(const Common::OputStreamPtr&,const Common::String&) const;
    bool __textRead(const Common::IputStreamPtr&,const Common::String&,int = 0);
public:
    Common::Long count;
    Common::Long bytes;
    int bps;
    int pps;
};
void __write_NetworkStats(const Common::OputStreamPtr&,const SimpleRtpSession::NetworkStats&);
void __read_NetworkStats(const Common::IputStreamPtr&,SimpleRtpSession::NetworkStats&);
void __textWrite_NetworkStats(const Common::OputStreamPtr&,const Common::String&,const SimpleRtpSession::NetworkStats&);
bool __textRead_NetworkStats(const Common::IputStreamPtr&,const Common::String&,SimpleRtpSession::NetworkStats&,int = 0);

class RtpNetworkStats
{
public:
    RtpNetworkStats();
    RtpNetworkStats(Common::Long,Common::Long,int,int,Common::Long,int);

    bool operator<(const RtpNetworkStats&) const;
    bool operator==(const RtpNetworkStats&) const;
    bool operator!=(const RtpNetworkStats&__obj) const { return !operator==(__obj);}
    void __write(const Common::OputStreamPtr&) const;
    void __read(const Common::IputStreamPtr&);
    void __textWrite(const Common::OputStreamPtr&,const Common::String&) const;
    bool __textRead(const Common::IputStreamPtr&,const Common::String&,int = 0);
public:
    Common::Long count;
    Common::Long bytes;
    int bps;
    int pps;
    Common::Long lost;
    int lostRate;
};
void __write_RtpNetworkStats(const Common::OputStreamPtr&,const SimpleRtpSession::RtpNetworkStats&);
void __read_RtpNetworkStats(const Common::IputStreamPtr&,SimpleRtpSession::RtpNetworkStats&);
void __textWrite_RtpNetworkStats(const Common::OputStreamPtr&,const Common::String&,const SimpleRtpSession::RtpNetworkStats&);
bool __textRead_RtpNetworkStats(const Common::IputStreamPtr&,const Common::String&,SimpleRtpSession::RtpNetworkStats&,int = 0);

class RtpStreamStats
{
public:
    RtpStreamStats();
    RtpStreamStats(int,int,const SimpleRtpSession::NetworkStats&,const SimpleRtpSession::NetworkStats&,const SimpleRtpSession::RtpNetworkStats&,const SimpleRtpSession::RtpNetworkStats&);

    bool operator<(const RtpStreamStats&) const;
    bool operator==(const RtpStreamStats&) const;
    bool operator!=(const RtpStreamStats&__obj) const { return !operator==(__obj);}
    void __write(const Common::OputStreamPtr&) const;
    void __read(const Common::IputStreamPtr&);
    void __textWrite(const Common::OputStreamPtr&,const Common::String&) const;
    bool __textRead(const Common::IputStreamPtr&,const Common::String&,int = 0);
public:
    int payloadType;
    int lastPayloadType;
    SimpleRtpSession::NetworkStats overallNetwork;
    SimpleRtpSession::NetworkStats currentNetwork;
    SimpleRtpSession::RtpNetworkStats overallRtp;
    SimpleRtpSession::RtpNetworkStats currentRtp;
};
void __write_RtpStreamStats(const Common::OputStreamPtr&,const SimpleRtpSession::RtpStreamStats&);
void __read_RtpStreamStats(const Common::IputStreamPtr&,SimpleRtpSession::RtpStreamStats&);
void __textWrite_RtpStreamStats(const Common::OputStreamPtr&,const Common::String&,const SimpleRtpSession::RtpStreamStats&);
bool __textRead_RtpStreamStats(const Common::IputStreamPtr&,const Common::String&,SimpleRtpSession::RtpStreamStats&,int = 0);

class RtpSessionStats
{
public:
    RtpSessionStats();
    RtpSessionStats(const SimpleRtpSession::RtpStreamStats&,const SimpleRtpSession::RtpStreamStats&);

    bool operator<(const RtpSessionStats&) const;
    bool operator==(const RtpSessionStats&) const;
    bool operator!=(const RtpSessionStats&__obj) const { return !operator==(__obj);}
    void __write(const Common::OputStreamPtr&) const;
    void __read(const Common::IputStreamPtr&);
    void __textWrite(const Common::OputStreamPtr&,const Common::String&) const;
    bool __textRead(const Common::IputStreamPtr&,const Common::String&,int = 0);
public:
    SimpleRtpSession::RtpStreamStats audio;
    SimpleRtpSession::RtpStreamStats video;
};
void __write_RtpSessionStats(const Common::OputStreamPtr&,const SimpleRtpSession::RtpSessionStats&);
void __read_RtpSessionStats(const Common::IputStreamPtr&,SimpleRtpSession::RtpSessionStats&);
void __textWrite_RtpSessionStats(const Common::OputStreamPtr&,const Common::String&,const SimpleRtpSession::RtpSessionStats&);
bool __textRead_RtpSessionStats(const Common::IputStreamPtr&,const Common::String&,SimpleRtpSession::RtpSessionStats&,int = 0);

};//namespace: SimpleRtpSession

#endif //__SimpleRtpSession_SimpleRtpSessionPub_h
