﻿//
// *****************************************************************************
// Copyright(c) 2017-2025 Juphoon System Software Co., LTD. All rights reserved.
// *****************************************************************************
//
// Auto generated from: src/SimpleRtpSession/SimpleRtpSession.def
// Warning: do not edit this file.
//

#include "SimpleRtpSession/SimpleRtpSessionPub.h"

namespace SimpleRtpSession
{

NetworkStats::NetworkStats() :
    count(0),
    bytes(0),
    bps(0),
    pps(0)
{
}

NetworkStats::NetworkStats(Common::Long x_count,Common::Long x_bytes,int x_bps,int x_pps) :
    count(x_count),bytes(x_bytes),bps(x_bps),pps(x_pps)
{
}

bool NetworkStats::operator<(const NetworkStats&__obj) const
{
    if (this == &__obj) return false;
    if (count < __obj.count) return true;
    if (__obj.count < count) return false;
    if (bytes < __obj.bytes) return true;
    if (__obj.bytes < bytes) return false;
    if (bps < __obj.bps) return true;
    if (__obj.bps < bps) return false;
    if (pps < __obj.pps) return true;
    if (__obj.pps < pps) return false;
    return false;
}

bool NetworkStats::operator==(const NetworkStats&__obj) const
{
    if (this == &__obj) return true;
    if (count != __obj.count) return false;
    if (bytes != __obj.bytes) return false;
    if (bps != __obj.bps) return false;
    if (pps != __obj.pps) return false;
    return true;
}

void NetworkStats::__write(const Common::OputStreamPtr& __oput) const
{
    __write_NetworkStats(__oput,*this);
}

void NetworkStats::__read(const Common::IputStreamPtr& __iput)
{
    __read_NetworkStats(__iput,*this);
}

void NetworkStats::__textWrite(const Common::OputStreamPtr& __oput,const Common::String& __name) const
{
    __textWrite_NetworkStats(__oput,__name,*this);
}

bool NetworkStats::__textRead(const Common::IputStreamPtr& __iput,const Common::String& __name,int __idx)
{
    return __textRead_NetworkStats(__iput,__name,*this,__idx);
}

void __write_NetworkStats(const Common::OputStreamPtr& __oput,const SimpleRtpSession::NetworkStats& __obj)
{
    __oput->write(__obj.count);
    __oput->write(__obj.bytes);
    __oput->write(__obj.bps);
    __oput->write(__obj.pps);
}

void __read_NetworkStats(const Common::IputStreamPtr& __iput,SimpleRtpSession::NetworkStats& __obj)
{
    __iput->read(__obj.count);
    __iput->read(__obj.bytes);
    __iput->read(__obj.bps);
    __iput->read(__obj.pps);
}

void __textWrite_NetworkStats(const Common::OputStreamPtr& __oput,const Common::String& __name,const SimpleRtpSession::NetworkStats& __obj)
{
    __oput->textStart(__name);
    __oput->textWrite("count",__obj.count);
    __oput->textWrite("bytes",__obj.bytes);
    __oput->textWrite("bps",__obj.bps);
    __oput->textWrite("pps",__obj.pps);
    __oput->textEnd();
}

bool __textRead_NetworkStats(const Common::IputStreamPtr& __iput,const Common::String& __name,SimpleRtpSession::NetworkStats& __obj,int __idx)
{
    if (!__iput->textStart(__name,__idx)) return false;
    __iput->textRead("count",__obj.count,0);
    __iput->textRead("bytes",__obj.bytes,0);
    __iput->textRead("bps",__obj.bps,0);
    __iput->textRead("pps",__obj.pps,0);
    __iput->textEnd();
    return true;
}

RtpNetworkStats::RtpNetworkStats() :
    count(0),
    bytes(0),
    bps(0),
    pps(0),
    lost(0),
    lostRate(0)
{
}

RtpNetworkStats::RtpNetworkStats(Common::Long x_count,Common::Long x_bytes,int x_bps,int x_pps,Common::Long x_lost,int x_lostRate) :
    count(x_count),bytes(x_bytes),bps(x_bps),pps(x_pps),lost(x_lost),lostRate(x_lostRate)
{
}

bool RtpNetworkStats::operator<(const RtpNetworkStats&__obj) const
{
    if (this == &__obj) return false;
    if (count < __obj.count) return true;
    if (__obj.count < count) return false;
    if (bytes < __obj.bytes) return true;
    if (__obj.bytes < bytes) return false;
    if (bps < __obj.bps) return true;
    if (__obj.bps < bps) return false;
    if (pps < __obj.pps) return true;
    if (__obj.pps < pps) return false;
    if (lost < __obj.lost) return true;
    if (__obj.lost < lost) return false;
    if (lostRate < __obj.lostRate) return true;
    if (__obj.lostRate < lostRate) return false;
    return false;
}

bool RtpNetworkStats::operator==(const RtpNetworkStats&__obj) const
{
    if (this == &__obj) return true;
    if (count != __obj.count) return false;
    if (bytes != __obj.bytes) return false;
    if (bps != __obj.bps) return false;
    if (pps != __obj.pps) return false;
    if (lost != __obj.lost) return false;
    if (lostRate != __obj.lostRate) return false;
    return true;
}

void RtpNetworkStats::__write(const Common::OputStreamPtr& __oput) const
{
    __write_RtpNetworkStats(__oput,*this);
}

void RtpNetworkStats::__read(const Common::IputStreamPtr& __iput)
{
    __read_RtpNetworkStats(__iput,*this);
}

void RtpNetworkStats::__textWrite(const Common::OputStreamPtr& __oput,const Common::String& __name) const
{
    __textWrite_RtpNetworkStats(__oput,__name,*this);
}

bool RtpNetworkStats::__textRead(const Common::IputStreamPtr& __iput,const Common::String& __name,int __idx)
{
    return __textRead_RtpNetworkStats(__iput,__name,*this,__idx);
}

void __write_RtpNetworkStats(const Common::OputStreamPtr& __oput,const SimpleRtpSession::RtpNetworkStats& __obj)
{
    __oput->write(__obj.count);
    __oput->write(__obj.bytes);
    __oput->write(__obj.bps);
    __oput->write(__obj.pps);
    __oput->write(__obj.lost);
    __oput->write(__obj.lostRate);
}

void __read_RtpNetworkStats(const Common::IputStreamPtr& __iput,SimpleRtpSession::RtpNetworkStats& __obj)
{
    __iput->read(__obj.count);
    __iput->read(__obj.bytes);
    __iput->read(__obj.bps);
    __iput->read(__obj.pps);
    __iput->read(__obj.lost);
    __iput->read(__obj.lostRate);
}

void __textWrite_RtpNetworkStats(const Common::OputStreamPtr& __oput,const Common::String& __name,const SimpleRtpSession::RtpNetworkStats& __obj)
{
    __oput->textStart(__name);
    __oput->textWrite("count",__obj.count);
    __oput->textWrite("bytes",__obj.bytes);
    __oput->textWrite("bps",__obj.bps);
    __oput->textWrite("pps",__obj.pps);
    __oput->textWrite("lost",__obj.lost);
    __oput->textWrite("lostRate",__obj.lostRate);
    __oput->textEnd();
}

bool __textRead_RtpNetworkStats(const Common::IputStreamPtr& __iput,const Common::String& __name,SimpleRtpSession::RtpNetworkStats& __obj,int __idx)
{
    if (!__iput->textStart(__name,__idx)) return false;
    __iput->textRead("count",__obj.count,0);
    __iput->textRead("bytes",__obj.bytes,0);
    __iput->textRead("bps",__obj.bps,0);
    __iput->textRead("pps",__obj.pps,0);
    __iput->textRead("lost",__obj.lost,0);
    __iput->textRead("lostRate",__obj.lostRate,0);
    __iput->textEnd();
    return true;
}

RtpStreamStats::RtpStreamStats() :
    payloadType(0),
    lastPayloadType(0)
{
}

RtpStreamStats::RtpStreamStats(int x_payloadType,int x_lastPayloadType,const SimpleRtpSession::NetworkStats& x_overallNetwork,const SimpleRtpSession::NetworkStats& x_currentNetwork,const SimpleRtpSession::RtpNetworkStats& x_overallRtp,const SimpleRtpSession::RtpNetworkStats& x_currentRtp) :
    payloadType(x_payloadType),lastPayloadType(x_lastPayloadType),overallNetwork(x_overallNetwork),currentNetwork(x_currentNetwork),overallRtp(x_overallRtp),currentRtp(x_currentRtp)
{
}

bool RtpStreamStats::operator<(const RtpStreamStats&__obj) const
{
    if (this == &__obj) return false;
    if (payloadType < __obj.payloadType) return true;
    if (__obj.payloadType < payloadType) return false;
    if (lastPayloadType < __obj.lastPayloadType) return true;
    if (__obj.lastPayloadType < lastPayloadType) return false;
    if (overallNetwork < __obj.overallNetwork) return true;
    if (__obj.overallNetwork < overallNetwork) return false;
    if (currentNetwork < __obj.currentNetwork) return true;
    if (__obj.currentNetwork < currentNetwork) return false;
    if (overallRtp < __obj.overallRtp) return true;
    if (__obj.overallRtp < overallRtp) return false;
    if (currentRtp < __obj.currentRtp) return true;
    if (__obj.currentRtp < currentRtp) return false;
    return false;
}

bool RtpStreamStats::operator==(const RtpStreamStats&__obj) const
{
    if (this == &__obj) return true;
    if (payloadType != __obj.payloadType) return false;
    if (lastPayloadType != __obj.lastPayloadType) return false;
    if (overallNetwork != __obj.overallNetwork) return false;
    if (currentNetwork != __obj.currentNetwork) return false;
    if (overallRtp != __obj.overallRtp) return false;
    if (currentRtp != __obj.currentRtp) return false;
    return true;
}

void RtpStreamStats::__write(const Common::OputStreamPtr& __oput) const
{
    __write_RtpStreamStats(__oput,*this);
}

void RtpStreamStats::__read(const Common::IputStreamPtr& __iput)
{
    __read_RtpStreamStats(__iput,*this);
}

void RtpStreamStats::__textWrite(const Common::OputStreamPtr& __oput,const Common::String& __name) const
{
    __textWrite_RtpStreamStats(__oput,__name,*this);
}

bool RtpStreamStats::__textRead(const Common::IputStreamPtr& __iput,const Common::String& __name,int __idx)
{
    return __textRead_RtpStreamStats(__iput,__name,*this,__idx);
}

void __write_RtpStreamStats(const Common::OputStreamPtr& __oput,const SimpleRtpSession::RtpStreamStats& __obj)
{
    __oput->write(__obj.payloadType);
    __oput->write(__obj.lastPayloadType);
    SimpleRtpSession::__write_NetworkStats(__oput,__obj.overallNetwork);
    SimpleRtpSession::__write_NetworkStats(__oput,__obj.currentNetwork);
    SimpleRtpSession::__write_RtpNetworkStats(__oput,__obj.overallRtp);
    SimpleRtpSession::__write_RtpNetworkStats(__oput,__obj.currentRtp);
}

void __read_RtpStreamStats(const Common::IputStreamPtr& __iput,SimpleRtpSession::RtpStreamStats& __obj)
{
    __iput->read(__obj.payloadType);
    __iput->read(__obj.lastPayloadType);
    SimpleRtpSession::__read_NetworkStats(__iput,__obj.overallNetwork);
    SimpleRtpSession::__read_NetworkStats(__iput,__obj.currentNetwork);
    SimpleRtpSession::__read_RtpNetworkStats(__iput,__obj.overallRtp);
    SimpleRtpSession::__read_RtpNetworkStats(__iput,__obj.currentRtp);
}

void __textWrite_RtpStreamStats(const Common::OputStreamPtr& __oput,const Common::String& __name,const SimpleRtpSession::RtpStreamStats& __obj)
{
    __oput->textStart(__name);
    __oput->textWrite("payloadType",__obj.payloadType);
    __oput->textWrite("lastPayloadType",__obj.lastPayloadType);
    SimpleRtpSession::__textWrite_NetworkStats(__oput,"overallNetwork",__obj.overallNetwork);
    SimpleRtpSession::__textWrite_NetworkStats(__oput,"currentNetwork",__obj.currentNetwork);
    SimpleRtpSession::__textWrite_RtpNetworkStats(__oput,"overallRtp",__obj.overallRtp);
    SimpleRtpSession::__textWrite_RtpNetworkStats(__oput,"currentRtp",__obj.currentRtp);
    __oput->textEnd();
}

bool __textRead_RtpStreamStats(const Common::IputStreamPtr& __iput,const Common::String& __name,SimpleRtpSession::RtpStreamStats& __obj,int __idx)
{
    if (!__iput->textStart(__name,__idx)) return false;
    __iput->textRead("payloadType",__obj.payloadType,0);
    __iput->textRead("lastPayloadType",__obj.lastPayloadType,0);
    SimpleRtpSession::__textRead_NetworkStats(__iput,"overallNetwork",__obj.overallNetwork,0);
    SimpleRtpSession::__textRead_NetworkStats(__iput,"currentNetwork",__obj.currentNetwork,0);
    SimpleRtpSession::__textRead_RtpNetworkStats(__iput,"overallRtp",__obj.overallRtp,0);
    SimpleRtpSession::__textRead_RtpNetworkStats(__iput,"currentRtp",__obj.currentRtp,0);
    __iput->textEnd();
    return true;
}

RtpSessionStats::RtpSessionStats()
{
}

RtpSessionStats::RtpSessionStats(const SimpleRtpSession::RtpStreamStats& x_audio,const SimpleRtpSession::RtpStreamStats& x_video) :
    audio(x_audio),video(x_video)
{
}

bool RtpSessionStats::operator<(const RtpSessionStats&__obj) const
{
    if (this == &__obj) return false;
    if (audio < __obj.audio) return true;
    if (__obj.audio < audio) return false;
    if (video < __obj.video) return true;
    if (__obj.video < video) return false;
    return false;
}

bool RtpSessionStats::operator==(const RtpSessionStats&__obj) const
{
    if (this == &__obj) return true;
    if (audio != __obj.audio) return false;
    if (video != __obj.video) return false;
    return true;
}

void RtpSessionStats::__write(const Common::OputStreamPtr& __oput) const
{
    __write_RtpSessionStats(__oput,*this);
}

void RtpSessionStats::__read(const Common::IputStreamPtr& __iput)
{
    __read_RtpSessionStats(__iput,*this);
}

void RtpSessionStats::__textWrite(const Common::OputStreamPtr& __oput,const Common::String& __name) const
{
    __textWrite_RtpSessionStats(__oput,__name,*this);
}

bool RtpSessionStats::__textRead(const Common::IputStreamPtr& __iput,const Common::String& __name,int __idx)
{
    return __textRead_RtpSessionStats(__iput,__name,*this,__idx);
}

void __write_RtpSessionStats(const Common::OputStreamPtr& __oput,const SimpleRtpSession::RtpSessionStats& __obj)
{
    SimpleRtpSession::__write_RtpStreamStats(__oput,__obj.audio);
    SimpleRtpSession::__write_RtpStreamStats(__oput,__obj.video);
}

void __read_RtpSessionStats(const Common::IputStreamPtr& __iput,SimpleRtpSession::RtpSessionStats& __obj)
{
    SimpleRtpSession::__read_RtpStreamStats(__iput,__obj.audio);
    SimpleRtpSession::__read_RtpStreamStats(__iput,__obj.video);
}

void __textWrite_RtpSessionStats(const Common::OputStreamPtr& __oput,const Common::String& __name,const SimpleRtpSession::RtpSessionStats& __obj)
{
    __oput->textStart(__name);
    SimpleRtpSession::__textWrite_RtpStreamStats(__oput,"audio",__obj.audio);
    SimpleRtpSession::__textWrite_RtpStreamStats(__oput,"video",__obj.video);
    __oput->textEnd();
}

bool __textRead_RtpSessionStats(const Common::IputStreamPtr& __iput,const Common::String& __name,SimpleRtpSession::RtpSessionStats& __obj,int __idx)
{
    if (!__iput->textStart(__name,__idx)) return false;
    SimpleRtpSession::__textRead_RtpStreamStats(__iput,"audio",__obj.audio,0);
    SimpleRtpSession::__textRead_RtpStreamStats(__iput,"video",__obj.video,0);
    __iput->textEnd();
    return true;
}

};//namespace: SimpleRtpSession
