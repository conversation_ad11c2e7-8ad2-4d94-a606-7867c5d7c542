# embeded rtp files
FILE(GLOB_RECURSE EMBEDED_CONFIG_RESOURCES "rtp/*.rtp")
FOREACH(INPUT_FILE ${EMBEDED_CONFIG_RESOURCES})
    SET(OUTPUT_FILE ${INPUT_FILE}.h)
    LIST(APPEND EMBEDED_CONFIG_SRC ${OUTPUT_FILE})
ENDFOREACH()

ADD_CUSTOM_COMMAND(
    OUTPUT cfg/__header.h ${EMBEDED_CONFIG_SRC}
    COMMAND ${CMAKE_CURRENT_SOURCE_DIR}/rtp/update.sh
    COMMENT "Generate embeded rtp file"
)

# library
aux_source_directory(impl SIMPLE_RTP_SESSION_SRC)
aux_source_directory(base SIMPLE_RTP_SESSION_BASE_SRC)
add_library(SimpleRtpSession
    SimpleRtpSessionPub.cpp
    ../SimpleSipSession/base/OfferAnswerModel.cpp
    ${SIMPLE_RTP_SESSION_BASE_SRC}
    ${SIMPLE_RTP_SESSION_SRC}
    ${EMBEDED_CONFIG_SRC}
)
target_include_directories(SimpleRtpSession PRIVATE
    ${DEFAULT_INCLUDE_DIRECTORIES}
    ${PROJECT_SOURCE_DIR}/3rd/jssi/inc
)
target_compile_definitions(SimpleRtpSession PRIVATE
    ${DEFAULT_COMPILE_DEFINITIONS}
)

# UnitTest
aux_source_directory(test SIMPLE_RTP_SESSION_TEST_SRC)
add_executable(SimpleRtpSessionUnitTest ${SIMPLE_RTP_SESSION_TEST_SRC})
target_include_directories(SimpleRtpSessionUnitTest PRIVATE
    ${DEFAULT_INCLUDE_DIRECTORIES}
    ${PROJECT_SOURCE_DIR}/3rd
)
target_compile_definitions(SimpleRtpSessionUnitTest PRIVATE
    ${DEFAULT_COMPILE_DEFINITIONS}
)

target_link_libraries(SimpleRtpSessionUnitTest SimpleRtpSession Util JsmLog
    ${DEFAULT_SERVER_LIBRARIES}
    $ENV{JssSharedPath}/lib/${DEFAULT_LIB_PATH}/libgtest.a
    $ENV{JssSharedPath}/lib/${DEFAULT_LIB_PATH}/libgmock.a
    $ENV{JssSharedPath}/lib/${DEFAULT_LIB_PATH}/libgtest_main.a
    ${DEFAULT_SYS_LIBRARIES}
)
include(GoogleTest)
gtest_discover_tests(SimpleRtpSessionUnitTest)
