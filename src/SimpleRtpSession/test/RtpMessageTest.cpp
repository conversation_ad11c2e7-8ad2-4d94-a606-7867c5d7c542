//
// *****************************************************************************
// Copyright (c) 2025 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//

#include "gtest/gtest.h"
#include "SimpleRtpSession/base/RtpMessage.h"

TEST(RtpMessageTest, PCMA)
{
    const uint8_t buffer[] = {
        0x80, 0x08, 0x12, 0x34, 0x56, 0x78, 0x90, 0xAB, 0xCD, 0xEF, 0x01, 0x23, 0xCC, 0xCC, 0xCC, 0xCC
    };

    SimpleRtpSession::RtpMessage message;
    EXPECT_TRUE(message.init(buffer, sizeof(buffer)));
    EXPECT_EQ(message.getVersion(), 2);
    EXPECT_EQ(message.getPayloadType(), 0x08);
    EXPECT_EQ(message.getSequenceNumber(), 0x1234);
    EXPECT_EQ(message.getTimestamp(), 0x567890AB);
    EXPECT_EQ(message.getSsrc(), 0xCDEF0123);
    EXPECT_EQ(message.getCsrcCount(), 0);
    EXPECT_EQ(message.getMarker(), false);
    size_t payloadSize;
    const uint8_t *payload = message.getPayload(&payloadSize);
    EXPECT_EQ(payloadSize, 4);
    EXPECT_EQ(payload, buffer + 12);
}

TEST(RtpMessageTest, H264)
{
    const uint8_t buffer[] = {
        0x80, 0xF2, 0x12, 0x34, 0x56, 0x78, 0x90, 0xAB, 0xCD, 0xEF, 0x01, 0x23, 0xCC, 0xCC, 0xCC, 0xCC
    };

    SimpleRtpSession::RtpMessage message;
    EXPECT_TRUE(message.init(buffer, sizeof(buffer)));
    EXPECT_EQ(message.getVersion(), 2);
    EXPECT_EQ(message.getPayloadType(), 0x72);
    EXPECT_EQ(message.getSequenceNumber(), 0x1234);
    EXPECT_EQ(message.getTimestamp(), 0x567890AB);
    EXPECT_EQ(message.getSsrc(), 0xCDEF0123);
    EXPECT_EQ(message.getCsrcCount(), 0);
    EXPECT_EQ(message.getMarker(), true);
    size_t payloadSize;
    const uint8_t *payload = message.getPayload(&payloadSize);
    EXPECT_EQ(payloadSize, 4);
    EXPECT_EQ(payload, buffer + 12);
}
