//
// *****************************************************************************
// Copyright (c) 2025 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//

#include "gtest/gtest.h"
#include "Common/Util.h"
#include "SimpleRtpSession/SimpleRtpSession.h"

class RtpSessionTest : public ::testing::Test
{
protected:
    void SetUp() override
    {
        _manager = SimpleRtpSession::SimpleRtpSessionManager::create(SimpleRtpSession::SimpleRtpSessionManager::PortRange(10000, 19999), false);
        ASSERT_TRUE(_manager);
    }

    void TearDown() override
    {
        _manager = nullptr;
    }

    SimpleRtpSession::SimpleRtpSessionManagerPtr _manager;
};

TEST_F(RtpSessionTest, Audio)
{
    Common::setLogLevel(3);
    Common::setLogPrint(true);

    SimpleRtpSession::SimpleRtpSessionPtr caller = _manager->createSession(false);
    SimpleRtpSession::SimpleRtpSessionPtr callee = _manager->createSession(false);
    ASSERT_TRUE(caller);
    ASSERT_TRUE(callee);

    std::string offer = caller->genOffer();
    std::string answer = callee->genAnswer(offer);
    Common::sleep(100);
    ASSERT_TRUE(caller->setAnswer(answer));

    for (int i = 0; i < 10; i++)
    {
        Common::sleep(1000);

        SimpleRtpSession::RtpSessionStats stats;
        EXPECT_TRUE(caller->getStats(stats));
        std::cout << "caller stats: " << stats.audio.payloadType << ", " << stats.audio.lastPayloadType << ", " << stats.audio.overallNetwork.bps << ", " << stats.audio.currentNetwork.bps << ", " << stats.audio.overallRtp.bps << ", " << stats.audio.currentRtp.bps << std::endl;
        EXPECT_EQ(stats.audio.payloadType, 0);
        EXPECT_NEAR(stats.audio.overallNetwork.bps, 64 * 1000, 6400);
        EXPECT_NEAR(stats.audio.currentNetwork.bps, 64 * 1000, 6400);
        EXPECT_NEAR(stats.audio.overallRtp.bps, 64 * 1000, 6400);
        EXPECT_NEAR(stats.audio.currentRtp.bps, 64 * 1000, 6400);

        EXPECT_TRUE(callee->getStats(stats));
        std::cout << "callee stats: " << stats.audio.payloadType << ", " << stats.audio.lastPayloadType << ", " << stats.audio.overallNetwork.bps << ", " << stats.audio.currentNetwork.bps << ", " << stats.audio.overallRtp.bps << ", " << stats.audio.currentRtp.bps << std::endl;
        EXPECT_EQ(stats.audio.payloadType, 0);
        EXPECT_NEAR(stats.audio.overallNetwork.bps, 64 * 1000, 6400);
        EXPECT_NEAR(stats.audio.currentNetwork.bps, 64 * 1000, 6400);
        EXPECT_NEAR(stats.audio.overallRtp.bps, 64 * 1000, 6400);
        EXPECT_NEAR(stats.audio.currentRtp.bps, 64 * 1000, 6400);
    }

    caller->close();
    callee->close();
}

TEST_F(RtpSessionTest, Video)
{
    Common::setLogLevel(3);
    Common::setLogPrint(true);

    SimpleRtpSession::SimpleRtpSessionPtr caller = _manager->createSession(true);
    SimpleRtpSession::SimpleRtpSessionPtr callee = _manager->createSession(true);
    ASSERT_TRUE(caller);
    ASSERT_TRUE(callee);

    std::string offer = caller->genOffer();
    std::string answer = callee->genAnswer(offer);
    ASSERT_TRUE(caller->setAnswer(answer));

    for (int i = 0; i < 10; i++)
    {
        Common::sleep(1000);

        SimpleRtpSession::RtpSessionStats stats;
        EXPECT_TRUE(caller->getStats(stats));
        std::cout << "caller stats: " << stats.video.payloadType << ", " << stats.video.lastPayloadType << ", " << stats.video.overallNetwork.bps << ", " << stats.video.currentNetwork.bps << ", " << stats.video.overallRtp.bps << ", " << stats.video.currentRtp.bps << std::endl;
        EXPECT_EQ(stats.video.payloadType, 121);
        EXPECT_NEAR(stats.video.overallNetwork.bps, 440 * 1024, 60 * 1024);
        EXPECT_NEAR(stats.video.currentNetwork.bps, 440 * 1024, 60 * 1024);
        EXPECT_NEAR(stats.video.overallRtp.bps, 440 * 1024, 60 * 1024);
        EXPECT_NEAR(stats.video.currentRtp.bps, 440 * 1024, 60 * 1024);

        EXPECT_TRUE(callee->getStats(stats));
        std::cout << "callee stats: " << stats.video.payloadType << ", " << stats.video.lastPayloadType << ", " << stats.video.overallNetwork.bps << ", " << stats.video.currentNetwork.bps << ", " << stats.video.overallRtp.bps << ", " << stats.video.currentRtp.bps << std::endl;
        EXPECT_EQ(stats.video.payloadType, 121);
        EXPECT_NEAR(stats.video.overallNetwork.bps, 440 * 1024, 60 * 1024);
        EXPECT_NEAR(stats.video.currentNetwork.bps, 440 * 1024, 60 * 1024);
        EXPECT_NEAR(stats.video.overallRtp.bps, 440 * 1024, 60 * 1024);
        EXPECT_NEAR(stats.video.currentRtp.bps, 440 * 1024, 60 * 1024);
    }

    caller->close();
    callee->close();
}

TEST_F(RtpSessionTest, Audio2Video)
{
    Common::setLogLevel(3);
    Common::setLogPrint(true);

    SimpleRtpSession::SimpleRtpSessionPtr caller = _manager->createSession(false);
    SimpleRtpSession::SimpleRtpSessionPtr callee = _manager->createSession(false);
    ASSERT_TRUE(caller);
    ASSERT_TRUE(callee);

    std::string offer = caller->genOffer();
    std::string answer = callee->genAnswer(offer);
    Common::sleep(100);
    ASSERT_TRUE(caller->setAnswer(answer));

    for (int i = 0; i < 10; i++)
    {
        Common::sleep(1000);

        SimpleRtpSession::RtpSessionStats stats;
        EXPECT_TRUE(caller->getStats(stats));
        std::cout << "caller stats audio: " << stats.audio.payloadType << ", " << stats.audio.lastPayloadType << ", " << stats.audio.overallNetwork.bps << ", " << stats.audio.currentNetwork.bps << ", " << stats.audio.overallRtp.bps << ", " << stats.audio.currentRtp.bps << std::endl;
        EXPECT_EQ(stats.audio.payloadType, 0);
        EXPECT_NEAR(stats.audio.overallNetwork.bps, 64 * 1000, 6400);
        EXPECT_NEAR(stats.audio.currentNetwork.bps, 64 * 1000, 6400);
        EXPECT_NEAR(stats.audio.overallRtp.bps, 64 * 1000, 6400);
        EXPECT_NEAR(stats.audio.currentRtp.bps, 64 * 1000, 6400);

        EXPECT_TRUE(callee->getStats(stats));
        std::cout << "callee stats audio: " << stats.audio.payloadType << ", " << stats.audio.lastPayloadType << ", " << stats.audio.overallNetwork.bps << ", " << stats.audio.currentNetwork.bps << ", " << stats.audio.overallRtp.bps << ", " << stats.audio.currentRtp.bps << std::endl;
        EXPECT_EQ(stats.audio.payloadType, 0);
        EXPECT_NEAR(stats.audio.overallNetwork.bps, 64 * 1000, 6400);
        EXPECT_NEAR(stats.audio.currentNetwork.bps, 64 * 1000, 6400);
        EXPECT_NEAR(stats.audio.overallRtp.bps, 64 * 1000, 6400);
        EXPECT_NEAR(stats.audio.currentRtp.bps, 64 * 1000, 6400);
    }

    caller->enableVideo(true);
    callee->enableVideo(true);

    offer = caller->genOffer();
    answer = callee->genAnswer(offer);
    Common::sleep(100);
    ASSERT_TRUE(caller->setAnswer(answer));

    for (int i = 0; i < 10; i++)
    {
        Common::sleep(1000);

        SimpleRtpSession::RtpSessionStats stats;
        EXPECT_TRUE(caller->getStats(stats));
        std::cout << "caller stats audio: " << stats.audio.payloadType << ", " << stats.audio.lastPayloadType << ", " << stats.audio.overallNetwork.bps << ", " << stats.audio.currentNetwork.bps << ", " << stats.audio.overallRtp.bps << ", " << stats.audio.currentRtp.bps;
        std::cout << " video: " << stats.video.payloadType << ", " << stats.video.lastPayloadType << ", " << stats.video.overallNetwork.bps << ", " << stats.video.currentNetwork.bps << ", " << stats.video.overallRtp.bps << ", " << stats.video.currentRtp.bps << std::endl;
        EXPECT_EQ(stats.audio.payloadType, 0);
        EXPECT_NEAR(stats.audio.overallNetwork.bps, 64 * 1000, 6400);
        EXPECT_NEAR(stats.audio.currentNetwork.bps, 64 * 1000, 6400);
        EXPECT_NEAR(stats.audio.overallRtp.bps, 64 * 1000, 6400);
        EXPECT_NEAR(stats.audio.currentRtp.bps, 64 * 1000, 6400);
        EXPECT_EQ(stats.video.payloadType, 121);
        EXPECT_NEAR(stats.video.overallNetwork.bps, 440 * 1024, 60 * 1024);
        EXPECT_NEAR(stats.video.currentNetwork.bps, 440 * 1024, 60 * 1024);
        EXPECT_NEAR(stats.video.overallRtp.bps, 440 * 1024, 60 * 1024);
        EXPECT_NEAR(stats.video.currentRtp.bps, 440 * 1024, 60 * 1024);

        EXPECT_TRUE(callee->getStats(stats));
        std::cout << "callee stats audio: " << stats.audio.payloadType << ", " << stats.audio.lastPayloadType << ", " << stats.audio.overallNetwork.bps << ", " << stats.audio.currentNetwork.bps << ", " << stats.audio.overallRtp.bps << ", " << stats.audio.currentRtp.bps;
        std::cout << " video: " << stats.video.payloadType << ", " << stats.video.lastPayloadType << ", " << stats.video.overallNetwork.bps << ", " << stats.video.currentNetwork.bps << ", " << stats.video.overallRtp.bps << ", " << stats.video.currentRtp.bps << std::endl;
        EXPECT_EQ(stats.audio.payloadType, 0);
        EXPECT_NEAR(stats.audio.overallNetwork.bps, 64 * 1000, 6400);
        EXPECT_NEAR(stats.audio.currentNetwork.bps, 64 * 1000, 6400);
        EXPECT_NEAR(stats.audio.overallRtp.bps, 64 * 1000, 6400);
        EXPECT_NEAR(stats.audio.currentRtp.bps, 64 * 1000, 6400);
        EXPECT_EQ(stats.video.payloadType, 121);
        EXPECT_NEAR(stats.video.overallNetwork.bps, 440 * 1024, 60 * 1024);
        EXPECT_NEAR(stats.video.currentNetwork.bps, 440 * 1024, 60 * 1024);
        EXPECT_NEAR(stats.video.overallRtp.bps, 440 * 1024, 60 * 1024);
        EXPECT_NEAR(stats.video.currentRtp.bps, 440 * 1024, 60 * 1024);
    }

    caller->close();
    callee->close();
}