#include "gmock/gmock.h"
#include "gtest/gtest.h"

#include "Common/Util.h"
#include "SimpleRtpSession/SimpleRtpSession.h"

using ::testing::_;
using ::testing::StrEq;

class EchoRtpSessionTest : public ::testing::Test
{
protected:
    void SetUp() override
    {
        _manager = SimpleRtpSession::SimpleRtpSessionManager::create(SimpleRtpSession::SimpleRtpSessionManager::PortRange(10000, 19999), true);
        ASSERT_TRUE(_manager);
    }

    void TearDown() override
    {
        _manager = nullptr;
    }

    SimpleRtpSession::SimpleRtpSessionManagerPtr _manager;
};

TEST_F(EchoRtpSessionTest, EchoRtpSession_Audio)
{
    Common::setLogLevel(3);
    Common::setLogPrint(true);

    SimpleRtpSession::SimpleRtpSessionPtr caller = _manager->createSession(false);
    SimpleRtpSession::SimpleRtpSessionPtr callee = _manager->createSession(false);
    ASSERT_TRUE(caller);
    ASSERT_TRUE(callee);

    std::string offer = caller->genOffer();
    std::string answer = callee->genAnswer(offer);
    Common::sleep(100);
    ASSERT_TRUE(caller->setAnswer(answer));

    caller->close();
    callee->close();
}