//
// *****************************************************************************
// Copyright (c) 2025 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//

#include "gtest/gtest.h"
#include "SimpleRtpSession/impl/RtpFileSender.h"
#include "SimpleRtpSession/rtp/PCMU.rtp.h"
#include "SimpleRtpSession/rtp/AMR.rtp.h"
#include "SimpleRtpSession/rtp/H264.rtp.h"
#include "SimpleRtpSession/base/RtpMessage.h"

template <typename T>
T read(const uint8_t *data, size_t offset)
{
    T value = 0;
    for (size_t i = 0; i < sizeof(T); i++)
    {
        value = (value << 8) | data[i + offset];
    }

    return value;
}

class RtpFileSenderTestChecker : public SimpleRtpSession::RtpFileSender::Listener
{
public:
    RtpFileSenderTestChecker()
        : _startTicks(Common::getCurTicks())
    {
    }

    virtual void onSendPacket(const uint8_t *data, size_t size)
    {
        SimpleRtpSession::RtpMessage rtpMessage;
        rtpMessage.init(data, size);
        if (rtpMessage.isRtcp())
        {
            printf("rtcp packet.size: %zu pt:%d\n", size, rtpMessage.getRtcpType());
            return;
        }

        _payloadTypes.push_back(rtpMessage.getPayloadType());
        _sequenceNumbers.push_back(rtpMessage.getSequenceNumber());
        _ssrcs.push_back(rtpMessage.getSsrc());

        uint32_t timestamp = read<uint32_t>(data, 4);
        if (_timestamps.empty())
            _timestamps.push_back(timestamp);
        else if (timestamp != _timestamps.back())
            _timestamps.push_back(timestamp);
    }

    bool checkPayloadType(uint8_t payloadType)
    {
        if (_payloadTypes.empty())
            return false;

        for (size_t i = 0; i < _payloadTypes.size(); i++)
        {
            if (_payloadTypes[i] != payloadType)
            {
                std::cout << "payloadType: " << (int)_payloadTypes[i] << " expected: " << (int)payloadType << std::endl;
                return false;
            }
        }

        return true;
    }

    bool checkSequenceNumber()
    {
        if (_sequenceNumbers.empty())
            return false;

        for (size_t i = 1; i < _sequenceNumbers.size(); i++)
        {
            if (_sequenceNumbers[i] != _sequenceNumbers[i - 1] + 1)
            {
                std::cout << "sequenceNumber: " << _sequenceNumbers[i] << " expected: " << _sequenceNumbers[i - 1] + 1 << std::endl;
                return false;
            }
        }

        return true;
    }

    bool checkTimestamp(uint32_t span)
    {
        if (_timestamps.empty())
            return false;

        for (size_t i = 1; i < _timestamps.size(); i++)
        {
            if (_timestamps[i] - _timestamps[i - 1] > span)
            {
                std::cout << "timestamp: " << _timestamps[i] << " expected: " << _timestamps[i - 1] + span << std::endl;
                return false;
            }
        }

        return true;
    }

    bool checkSsrc()
    {
        if (_ssrcs.empty())
            return false;

        for (size_t i = 1; i < _ssrcs.size(); i++)
        {
            if (_ssrcs[i] != _ssrcs[i - 1])
            {
                std::cout << "ssrc: " << _ssrcs[i] << " expected: " << _ssrcs[i - 1] << std::endl;
                return false;
            }
        }

        return true;
    }

    unsigned int getAverageDuration()
    {
        if (_timestamps.empty())
            return 0;

        int frameCount = 0;
        for (size_t i = 1; i < _timestamps.size(); i++)
        {
            if (_timestamps[i] != _timestamps[i - 1])
                frameCount++;
        }

        return (Common::getCurTicks() - _startTicks) / frameCount;
    }

    unsigned int _startTicks;
    std::vector<uint8_t> _payloadTypes;
    std::vector<uint16_t> _sequenceNumbers;
    std::vector<uint32_t> _timestamps;
    std::vector<uint32_t> _ssrcs;
};

TEST(RtpFileSenderTest, PCMU)
{
    Common::setLogLevel(3);
    Common::setLogPrint(true);

    Common::Handle<RtpFileSenderTestChecker> checker = new RtpFileSenderTestChecker();
    SimpleRtpSession::RtpFileSenderPtr sender = SimpleRtpSession::RtpFileSender::create(checker, true);
    ASSERT_TRUE(sender);
    sender->start("PCMU", PCMU_rtp, PCMU_rtp_len, 0);
    Common::sleep(20000);
    sender->close();

    std::cout << "average duration: " << checker->getAverageDuration() << std::endl;
    EXPECT_NEAR(checker->getAverageDuration(), 40, 5);
    EXPECT_TRUE(checker->checkPayloadType(0));
    EXPECT_TRUE(checker->checkSequenceNumber());
    EXPECT_TRUE(checker->checkTimestamp(8000 * 40 / 1000)); // 40ms
    EXPECT_TRUE(checker->checkSsrc());
}

TEST(RtpFileSenderTest, AMR)
{
    Common::setLogLevel(3);
    Common::setLogPrint(true);

    Common::Handle<RtpFileSenderTestChecker> checker = new RtpFileSenderTestChecker();
    SimpleRtpSession::RtpFileSenderPtr sender = SimpleRtpSession::RtpFileSender::create(checker, true);
    ASSERT_TRUE(sender);
    sender->start("AMR", AMR_rtp, AMR_rtp_len, 105);
    Common::sleep(40000);
    sender->close();

    std::cout << "average duration: " << checker->getAverageDuration() << std::endl;
    EXPECT_NEAR(checker->getAverageDuration(), 200, 10);
    EXPECT_TRUE(checker->checkPayloadType(105));
    EXPECT_TRUE(checker->checkSequenceNumber());
    EXPECT_TRUE(checker->checkTimestamp(8000 * 200 / 1000)); // 200ms
    EXPECT_TRUE(checker->checkSsrc());
}

TEST(RtpFileSenderTest, H264)
{
    Common::setLogLevel(3);
    Common::setLogPrint(true);

    Common::Handle<RtpFileSenderTestChecker> checker = new RtpFileSenderTestChecker();
    SimpleRtpSession::RtpFileSenderPtr sender = SimpleRtpSession::RtpFileSender::create(checker, false);
    ASSERT_TRUE(sender);
    sender->start("H264", H264_rtp, H264_rtp_len, 114);
    Common::sleep(10000);
    sender->close();

    std::cout << "average duration: " << checker->getAverageDuration() << std::endl;
    EXPECT_NEAR(checker->getAverageDuration(), 36, 5);
    EXPECT_TRUE(checker->checkPayloadType(114));
    EXPECT_TRUE(checker->checkSequenceNumber());
    EXPECT_TRUE(checker->checkTimestamp(90000 / 20)); // 20 fps
    EXPECT_TRUE(checker->checkSsrc());
}