//
// *****************************************************************************
// Copyright (c) 2025 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//

#include "gtest/gtest.h"
#include "SimpleRtpSession/impl/RtpFileReader.h"
#include "Common/Util.h"
#include "SimpleRtpSession/rtp/PCMU.rtp.h"
#include "SimpleRtpSession/rtp/AMR.rtp.h"
#include "SimpleRtpSession/rtp/H264.rtp.h"
#include "SimpleRtpSession/base/RtpMessage.h"

static void printPacket(SimpleRtpSession::RtpFileReader::Packet &packet)
{
    SimpleRtpSession::RtpMessage rtpMessage;
    rtpMessage.init(packet.data, packet.size);
    if (rtpMessage.isRtcp())
        printf("rtcp packet.size: %-5zu toff:%-5d pt:%-3d len:%-5u\n",
               packet.size, packet.timestamp, rtpMessage.getRtcpType(), rtpMessage.getLength());
    else
        printf("rtp  packet.size: %-5zu toff:%-5d ver:%-2d ext:%-1d csrc:%-2d mark:%-1d pt:%-3d seq:%-8u ts:%-10u ssrc:%-10u\n",
               packet.size, packet.timestamp, rtpMessage.getVersion(), rtpMessage.getExtension(), rtpMessage.getCsrcCount(),
               rtpMessage.getMarker(), rtpMessage.getPayloadType(), rtpMessage.getSequenceNumber(), rtpMessage.getTimestamp(), rtpMessage.getSsrc());
}

TEST(RtpFileReaderTest, PCMU)
{
    Common::setLogLevel(3);
    Common::setLogPrint(true);
    SimpleRtpSession::RtpFileReaderPtr reader = SimpleRtpSession::RtpFileReader::create("PCMU", PCMU_rtp, PCMU_rtp_len);
    ASSERT_TRUE(reader);
    SimpleRtpSession::RtpFileReader::Packet packet;
    for (int i = 0; i < 10000; i++)
    {
        ASSERT_TRUE(reader->nextPacket(packet));
        ASSERT_GT(packet.size, 0);
        printPacket(packet);
    }
}

TEST(RtpFileReaderTest, AMR)
{
    Common::setLogLevel(3);
    Common::setLogPrint(true);
    SimpleRtpSession::RtpFileReaderPtr reader = SimpleRtpSession::RtpFileReader::create("AMR", AMR_rtp, AMR_rtp_len);
    ASSERT_TRUE(reader);
    SimpleRtpSession::RtpFileReader::Packet packet;
    for (int i = 0; i < 10000; i++)
    {
        ASSERT_TRUE(reader->nextPacket(packet));
        ASSERT_GT(packet.size, 0);
        printPacket(packet);
    }
}

TEST(RtpFileReaderTest, H264)
{
    Common::setLogLevel(3);
    Common::setLogPrint(true);
    SimpleRtpSession::RtpFileReaderPtr reader = SimpleRtpSession::RtpFileReader::create("H264", H264_rtp, H264_rtp_len);
    ASSERT_TRUE(reader);
    SimpleRtpSession::RtpFileReader::Packet packet;
    for (int i = 0; i < 10000; i++)
    {
        ASSERT_TRUE(reader->nextPacket(packet));
        ASSERT_GT(packet.size, 0);
        printPacket(packet);
    }
}