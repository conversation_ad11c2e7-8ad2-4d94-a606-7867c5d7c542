﻿//
// *****************************************************************************
// Copyright(c) 2017-2022 Juphoon System Software Co., LTD. All rights reserved.
// *****************************************************************************
//
// Auto generated from: MpCallEb.def
// Warning: do not edit this file.
//

#ifndef __MpCallEb_MpCallEbPub_h
#define __MpCallEb_MpCallEbPub_h

#include "Common/Common.h"

namespace MpCallEb
{

class AgentCallNumber
{
public:
    AgentCallNumber();
    AgentCallNumber(const Common::String&,const Common::String&);

    bool operator<(const AgentCallNumber&) const;
    bool operator==(const AgentCallNumber&) const;
    bool operator!=(const AgentCallNumber&__obj) const { return !operator==(__obj);}
    void __write(const Common::OputStreamPtr&) const;
    void __read(const Common::IputStreamPtr&);
    void __textWrite(const Common::OputStreamPtr&,const Common::String&) const;
    bool __textRead(const Common::IputStreamPtr&,const Common::String&,int = 0);
public:
    Common::String account;
    Common::String number;
};
void __write_AgentCallNumber(const Common::OputStreamPtr&,const MpCallEb::AgentCallNumber&);
void __read_AgentCallNumber(const Common::IputStreamPtr&,MpCallEb::AgentCallNumber&);
void __textWrite_AgentCallNumber(const Common::OputStreamPtr&,const Common::String&,const MpCallEb::AgentCallNumber&);
bool __textRead_AgentCallNumber(const Common::IputStreamPtr&,const Common::String&,MpCallEb::AgentCallNumber&,int = 0);

class SessionInfo
{
public:
    SessionInfo();
    SessionInfo(const Common::String&,const Common::String&);

    bool operator<(const SessionInfo&) const;
    bool operator==(const SessionInfo&) const;
    bool operator!=(const SessionInfo&__obj) const { return !operator==(__obj);}
    void __write(const Common::OputStreamPtr&) const;
    void __read(const Common::IputStreamPtr&);
    void __textWrite(const Common::OputStreamPtr&,const Common::String&) const;
    bool __textRead(const Common::IputStreamPtr&,const Common::String&,int = 0);
public:
    Common::String sessionId;
    Common::String peerAccount;
};
void __write_SessionInfo(const Common::OputStreamPtr&,const MpCallEb::SessionInfo&);
void __read_SessionInfo(const Common::IputStreamPtr&,MpCallEb::SessionInfo&);
void __textWrite_SessionInfo(const Common::OputStreamPtr&,const Common::String&,const MpCallEb::SessionInfo&);
bool __textRead_SessionInfo(const Common::IputStreamPtr&,const Common::String&,MpCallEb::SessionInfo&,int = 0);

typedef map<Common::String,MpCallEb::SessionInfo> SessionInfoMap;
void __write_SessionInfoMap(const Common::OputStreamPtr&,const MpCallEb::SessionInfoMap&);
void __read_SessionInfoMap(const Common::IputStreamPtr&,MpCallEb::SessionInfoMap&);
void __textWrite_SessionInfoMap(const Common::OputStreamPtr&,const Common::String&,const MpCallEb::SessionInfoMap&);
bool __textRead_SessionInfoMap(const Common::IputStreamPtr&,const Common::String&,MpCallEb::SessionInfoMap&,int = 0);

class CallRecord
{
public:
    CallRecord();
    CallRecord(const Common::String&,const Common::String&,const Common::String&,const Common::String&,const MpCallEb::SessionInfoMap&);

    bool operator<(const CallRecord&) const;
    bool operator==(const CallRecord&) const;
    bool operator!=(const CallRecord&__obj) const { return !operator==(__obj);}
    void __write(const Common::OputStreamPtr&) const;
    void __read(const Common::IputStreamPtr&);
    void __textWrite(const Common::OputStreamPtr&,const Common::String&) const;
    bool __textRead(const Common::IputStreamPtr&,const Common::String&,int = 0);
public:
    Common::String callId;
    Common::String serialNumber;
    Common::String passthroughInfo;
    Common::String token;
    MpCallEb::SessionInfoMap sessions;
};
void __write_CallRecord(const Common::OputStreamPtr&,const MpCallEb::CallRecord&);
void __read_CallRecord(const Common::IputStreamPtr&,MpCallEb::CallRecord&);
void __textWrite_CallRecord(const Common::OputStreamPtr&,const Common::String&,const MpCallEb::CallRecord&);
bool __textRead_CallRecord(const Common::IputStreamPtr&,const Common::String&,MpCallEb::CallRecord&,int = 0);

class OpenApiRequstHead
{
public:
    OpenApiRequstHead();
    OpenApiRequstHead(const Common::String&,const Common::String&,const Common::String&,const Common::String&);

    bool operator<(const OpenApiRequstHead&) const;
    bool operator==(const OpenApiRequstHead&) const;
    bool operator!=(const OpenApiRequstHead&__obj) const { return !operator==(__obj);}
    void __write(const Common::OputStreamPtr&) const;
    void __read(const Common::IputStreamPtr&);
    void __textWrite(const Common::OputStreamPtr&,const Common::String&) const;
    bool __textRead(const Common::IputStreamPtr&,const Common::String&,int = 0);
public:
    Common::String IDNO;
    Common::String ReqName;
    Common::String Terminal;
    Common::String InvokeId;
};
void __write_OpenApiRequstHead(const Common::OputStreamPtr&,const MpCallEb::OpenApiRequstHead&);
void __read_OpenApiRequstHead(const Common::IputStreamPtr&,MpCallEb::OpenApiRequstHead&);
void __textWrite_OpenApiRequstHead(const Common::OputStreamPtr&,const Common::String&,const MpCallEb::OpenApiRequstHead&);
bool __textRead_OpenApiRequstHead(const Common::IputStreamPtr&,const Common::String&,MpCallEb::OpenApiRequstHead&,int = 0);

class OpenApiRequstBody
{
public:
    OpenApiRequstBody();
    OpenApiRequstBody(const Common::String&,const Common::String&);

    bool operator<(const OpenApiRequstBody&) const;
    bool operator==(const OpenApiRequstBody&) const;
    bool operator!=(const OpenApiRequstBody&__obj) const { return !operator==(__obj);}
    void __write(const Common::OputStreamPtr&) const;
    void __read(const Common::IputStreamPtr&);
    void __textWrite(const Common::OputStreamPtr&,const Common::String&) const;
    bool __textRead(const Common::IputStreamPtr&,const Common::String&,int = 0);
public:
    Common::String FileName;
    Common::String ObjectId;
};
void __write_OpenApiRequstBody(const Common::OputStreamPtr&,const MpCallEb::OpenApiRequstBody&);
void __read_OpenApiRequstBody(const Common::IputStreamPtr&,MpCallEb::OpenApiRequstBody&);
void __textWrite_OpenApiRequstBody(const Common::OputStreamPtr&,const Common::String&,const MpCallEb::OpenApiRequstBody&);
bool __textRead_OpenApiRequstBody(const Common::IputStreamPtr&,const Common::String&,MpCallEb::OpenApiRequstBody&,int = 0);

class OpenApiRequest
{
public:
    OpenApiRequest();
    OpenApiRequest(const MpCallEb::OpenApiRequstHead&,const MpCallEb::OpenApiRequstBody&);

    bool operator<(const OpenApiRequest&) const;
    bool operator==(const OpenApiRequest&) const;
    bool operator!=(const OpenApiRequest&__obj) const { return !operator==(__obj);}
    void __write(const Common::OputStreamPtr&) const;
    void __read(const Common::IputStreamPtr&);
    void __textWrite(const Common::OputStreamPtr&,const Common::String&) const;
    bool __textRead(const Common::IputStreamPtr&,const Common::String&,int = 0);
public:
    MpCallEb::OpenApiRequstHead head;
    MpCallEb::OpenApiRequstBody body;
};
void __write_OpenApiRequest(const Common::OputStreamPtr&,const MpCallEb::OpenApiRequest&);
void __read_OpenApiRequest(const Common::IputStreamPtr&,MpCallEb::OpenApiRequest&);
void __textWrite_OpenApiRequest(const Common::OputStreamPtr&,const Common::String&,const MpCallEb::OpenApiRequest&);
bool __textRead_OpenApiRequest(const Common::IputStreamPtr&,const Common::String&,MpCallEb::OpenApiRequest&,int = 0);

class OpenApiResponseHead
{
public:
    OpenApiResponseHead();
    OpenApiResponseHead(const Common::String&,const Common::String&,const Common::String&,const Common::String&);

    bool operator<(const OpenApiResponseHead&) const;
    bool operator==(const OpenApiResponseHead&) const;
    bool operator!=(const OpenApiResponseHead&__obj) const { return !operator==(__obj);}
    void __write(const Common::OputStreamPtr&) const;
    void __read(const Common::IputStreamPtr&);
    void __textWrite(const Common::OputStreamPtr&,const Common::String&) const;
    bool __textRead(const Common::IputStreamPtr&,const Common::String&,int = 0);
public:
    Common::String Result;
    Common::String ReqName;
    Common::String Terminal;
    Common::String InvokeId;
};
void __write_OpenApiResponseHead(const Common::OputStreamPtr&,const MpCallEb::OpenApiResponseHead&);
void __read_OpenApiResponseHead(const Common::IputStreamPtr&,MpCallEb::OpenApiResponseHead&);
void __textWrite_OpenApiResponseHead(const Common::OputStreamPtr&,const Common::String&,const MpCallEb::OpenApiResponseHead&);
bool __textRead_OpenApiResponseHead(const Common::IputStreamPtr&,const Common::String&,MpCallEb::OpenApiResponseHead&,int = 0);

class OpenApiResponseBody
{
public:
    OpenApiResponseBody();
    OpenApiResponseBody(const Common::String&);

    bool operator<(const OpenApiResponseBody&) const;
    bool operator==(const OpenApiResponseBody&) const;
    bool operator!=(const OpenApiResponseBody&__obj) const { return !operator==(__obj);}
    void __write(const Common::OputStreamPtr&) const;
    void __read(const Common::IputStreamPtr&);
    void __textWrite(const Common::OputStreamPtr&,const Common::String&) const;
    bool __textRead(const Common::IputStreamPtr&,const Common::String&,int = 0);
public:
    Common::String detail;
};
void __write_OpenApiResponseBody(const Common::OputStreamPtr&,const MpCallEb::OpenApiResponseBody&);
void __read_OpenApiResponseBody(const Common::IputStreamPtr&,MpCallEb::OpenApiResponseBody&);
void __textWrite_OpenApiResponseBody(const Common::OputStreamPtr&,const Common::String&,const MpCallEb::OpenApiResponseBody&);
bool __textRead_OpenApiResponseBody(const Common::IputStreamPtr&,const Common::String&,MpCallEb::OpenApiResponseBody&,int = 0);

class OpenApiResponse
{
public:
    OpenApiResponse();
    OpenApiResponse(const MpCallEb::OpenApiResponseHead&,const MpCallEb::OpenApiResponseBody&);

    bool operator<(const OpenApiResponse&) const;
    bool operator==(const OpenApiResponse&) const;
    bool operator!=(const OpenApiResponse&__obj) const { return !operator==(__obj);}
    void __write(const Common::OputStreamPtr&) const;
    void __read(const Common::IputStreamPtr&);
    void __textWrite(const Common::OputStreamPtr&,const Common::String&) const;
    bool __textRead(const Common::IputStreamPtr&,const Common::String&,int = 0);
public:
    MpCallEb::OpenApiResponseHead head;
    MpCallEb::OpenApiResponseBody body;
};
void __write_OpenApiResponse(const Common::OputStreamPtr&,const MpCallEb::OpenApiResponse&);
void __read_OpenApiResponse(const Common::IputStreamPtr&,MpCallEb::OpenApiResponse&);
void __textWrite_OpenApiResponse(const Common::OputStreamPtr&,const Common::String&,const MpCallEb::OpenApiResponse&);
bool __textRead_OpenApiResponse(const Common::IputStreamPtr&,const Common::String&,MpCallEb::OpenApiResponse&,int = 0);

};//namespace: MpCallEb

#endif //__MpCallEb_MpCallEbPub_h
