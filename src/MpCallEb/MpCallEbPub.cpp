﻿//
// *****************************************************************************
// Copyright(c) 2017-2022 Juphoon System Software Co., LTD. All rights reserved.
// *****************************************************************************
//
// Auto generated from: MpCallEb.def
// Warning: do not edit this file.
//

#include "MpCallEb/MpCallEbPub.h"

namespace MpCallEb
{

AgentCallNumber::AgentCallNumber()
{
}

AgentCallNumber::AgentCallNumber(const Common::String& x_account,const Common::String& x_number) :
    account(x_account),number(x_number)
{
}

bool AgentCallNumber::operator<(const AgentCallNumber&__obj) const
{
    if (this == &__obj) return false;
    if (account < __obj.account) return true;
    if (__obj.account < account) return false;
    if (number < __obj.number) return true;
    if (__obj.number < number) return false;
    return false;
}

bool AgentCallNumber::operator==(const AgentCallNumber&__obj) const
{
    if (this == &__obj) return true;
    if (account != __obj.account) return false;
    if (number != __obj.number) return false;
    return true;
}

void AgentCallNumber::__write(const Common::OputStreamPtr& __oput) const
{
    __write_AgentCallNumber(__oput,*this);
}

void AgentCallNumber::__read(const Common::IputStreamPtr& __iput)
{
    __read_AgentCallNumber(__iput,*this);
}

void AgentCallNumber::__textWrite(const Common::OputStreamPtr& __oput,const Common::String& __name) const
{
    __textWrite_AgentCallNumber(__oput,__name,*this);
}

bool AgentCallNumber::__textRead(const Common::IputStreamPtr& __iput,const Common::String& __name,int __idx)
{
    return __textRead_AgentCallNumber(__iput,__name,*this,__idx);
}

void __write_AgentCallNumber(const Common::OputStreamPtr& __oput,const MpCallEb::AgentCallNumber& __obj)
{
    __oput->write(__obj.account);
    __oput->write(__obj.number);
}

void __read_AgentCallNumber(const Common::IputStreamPtr& __iput,MpCallEb::AgentCallNumber& __obj)
{
    __iput->read(__obj.account);
    __iput->read(__obj.number);
}

void __textWrite_AgentCallNumber(const Common::OputStreamPtr& __oput,const Common::String& __name,const MpCallEb::AgentCallNumber& __obj)
{
    __oput->textStart(__name);
    __oput->textWrite("account",__obj.account);
    __oput->textWrite("number",__obj.number);
    __oput->textEnd();
}

bool __textRead_AgentCallNumber(const Common::IputStreamPtr& __iput,const Common::String& __name,MpCallEb::AgentCallNumber& __obj,int __idx)
{
    if (!__iput->textStart(__name,__idx)) return false;
    __iput->textRead("account",__obj.account,0);
    __iput->textRead("number",__obj.number,0);
    __iput->textEnd();
    return true;
}

SessionInfo::SessionInfo()
{
}

SessionInfo::SessionInfo(const Common::String& x_sessionId,const Common::String& x_peerAccount) :
    sessionId(x_sessionId),peerAccount(x_peerAccount)
{
}

bool SessionInfo::operator<(const SessionInfo&__obj) const
{
    if (this == &__obj) return false;
    if (sessionId < __obj.sessionId) return true;
    if (__obj.sessionId < sessionId) return false;
    if (peerAccount < __obj.peerAccount) return true;
    if (__obj.peerAccount < peerAccount) return false;
    return false;
}

bool SessionInfo::operator==(const SessionInfo&__obj) const
{
    if (this == &__obj) return true;
    if (sessionId != __obj.sessionId) return false;
    if (peerAccount != __obj.peerAccount) return false;
    return true;
}

void SessionInfo::__write(const Common::OputStreamPtr& __oput) const
{
    __write_SessionInfo(__oput,*this);
}

void SessionInfo::__read(const Common::IputStreamPtr& __iput)
{
    __read_SessionInfo(__iput,*this);
}

void SessionInfo::__textWrite(const Common::OputStreamPtr& __oput,const Common::String& __name) const
{
    __textWrite_SessionInfo(__oput,__name,*this);
}

bool SessionInfo::__textRead(const Common::IputStreamPtr& __iput,const Common::String& __name,int __idx)
{
    return __textRead_SessionInfo(__iput,__name,*this,__idx);
}

void __write_SessionInfo(const Common::OputStreamPtr& __oput,const MpCallEb::SessionInfo& __obj)
{
    __oput->write(__obj.sessionId);
    __oput->write(__obj.peerAccount);
}

void __read_SessionInfo(const Common::IputStreamPtr& __iput,MpCallEb::SessionInfo& __obj)
{
    __iput->read(__obj.sessionId);
    __iput->read(__obj.peerAccount);
}

void __textWrite_SessionInfo(const Common::OputStreamPtr& __oput,const Common::String& __name,const MpCallEb::SessionInfo& __obj)
{
    __oput->textStart(__name);
    __oput->textWrite("sessionId",__obj.sessionId);
    __oput->textWrite("peerAccount",__obj.peerAccount);
    __oput->textEnd();
}

bool __textRead_SessionInfo(const Common::IputStreamPtr& __iput,const Common::String& __name,MpCallEb::SessionInfo& __obj,int __idx)
{
    if (!__iput->textStart(__name,__idx)) return false;
    __iput->textRead("sessionId",__obj.sessionId,0);
    __iput->textRead("peerAccount",__obj.peerAccount,0);
    __iput->textEnd();
    return true;
}
void __write_SessionInfoMap(const Common::OputStreamPtr& __oput,const MpCallEb::SessionInfoMap& __obj)
{
    __oput->write((int)__obj.size());
    map<Common::String,MpCallEb::SessionInfo>::const_iterator it1;
    for (it1 = __obj.begin();it1 != __obj.end();it1++)
    {
        __oput->write(it1->first);
        MpCallEb::__write_SessionInfo(__oput,it1->second);
    }
}

void __read_SessionInfoMap(const Common::IputStreamPtr& __iput,MpCallEb::SessionInfoMap& __obj)
{
    __obj.clear();
    int size;__iput->read(size);
    for (int i=0;i< size;i++)
    {
        Common::String k;MpCallEb::SessionInfo v;
        __iput->read(k);
        MpCallEb::__read_SessionInfo(__iput,v);
        __obj.insert(make_pair(k,v));
    }
}

void __textWrite_SessionInfoMap(const Common::OputStreamPtr& __oput,const Common::String& __name,const MpCallEb::SessionInfoMap& __obj)
{
    __oput->textStart(__name);
    map<Common::String,MpCallEb::SessionInfo>::const_iterator it1;
    for (it1 = __obj.begin();it1 != __obj.end();it1++)
        MpCallEb::__textWrite_SessionInfo(__oput,it1->first,it1->second);
    __oput->textEnd();
}

bool __textRead_SessionInfoMap(const Common::IputStreamPtr& __iput,const Common::String& __name,MpCallEb::SessionInfoMap& __obj,int __idx)
{
    __obj.clear();
    if (__iput->textStart(__name,__idx))
    {
        set<Common::String> ks;
        set<Common::String>::iterator it1;
        __iput->textList(ks);
        for (it1 = ks.begin();it1 != ks.end();it1++)
        {
            MpCallEb::SessionInfo v;
            if (MpCallEb::__textRead_SessionInfo(__iput,*it1,v,0))
                __obj.insert(make_pair(*it1,v));
        }
        __iput->textEnd();
    }
    return true;
}


CallRecord::CallRecord()
{
}

CallRecord::CallRecord(const Common::String& x_callId,const Common::String& x_serialNumber,const Common::String& x_passthroughInfo,const Common::String& x_token,const MpCallEb::SessionInfoMap& x_sessions) :
    callId(x_callId),serialNumber(x_serialNumber),passthroughInfo(x_passthroughInfo),token(x_token),sessions(x_sessions)
{
}

bool CallRecord::operator<(const CallRecord&__obj) const
{
    if (this == &__obj) return false;
    if (callId < __obj.callId) return true;
    if (__obj.callId < callId) return false;
    if (serialNumber < __obj.serialNumber) return true;
    if (__obj.serialNumber < serialNumber) return false;
    if (passthroughInfo < __obj.passthroughInfo) return true;
    if (__obj.passthroughInfo < passthroughInfo) return false;
    if (token < __obj.token) return true;
    if (__obj.token < token) return false;
    if (sessions < __obj.sessions) return true;
    if (__obj.sessions < sessions) return false;
    return false;
}

bool CallRecord::operator==(const CallRecord&__obj) const
{
    if (this == &__obj) return true;
    if (callId != __obj.callId) return false;
    if (serialNumber != __obj.serialNumber) return false;
    if (passthroughInfo != __obj.passthroughInfo) return false;
    if (token != __obj.token) return false;
    if (sessions != __obj.sessions) return false;
    return true;
}

void CallRecord::__write(const Common::OputStreamPtr& __oput) const
{
    __write_CallRecord(__oput,*this);
}

void CallRecord::__read(const Common::IputStreamPtr& __iput)
{
    __read_CallRecord(__iput,*this);
}

void CallRecord::__textWrite(const Common::OputStreamPtr& __oput,const Common::String& __name) const
{
    __textWrite_CallRecord(__oput,__name,*this);
}

bool CallRecord::__textRead(const Common::IputStreamPtr& __iput,const Common::String& __name,int __idx)
{
    return __textRead_CallRecord(__iput,__name,*this,__idx);
}

void __write_CallRecord(const Common::OputStreamPtr& __oput,const MpCallEb::CallRecord& __obj)
{
    __oput->write(__obj.callId);
    __oput->write(__obj.serialNumber);
    __oput->write(__obj.passthroughInfo);
    __oput->write(__obj.token);
    MpCallEb::__write_SessionInfoMap(__oput,__obj.sessions);
}

void __read_CallRecord(const Common::IputStreamPtr& __iput,MpCallEb::CallRecord& __obj)
{
    __iput->read(__obj.callId);
    __iput->read(__obj.serialNumber);
    __iput->read(__obj.passthroughInfo);
    __iput->read(__obj.token);
    MpCallEb::__read_SessionInfoMap(__iput,__obj.sessions);
}

void __textWrite_CallRecord(const Common::OputStreamPtr& __oput,const Common::String& __name,const MpCallEb::CallRecord& __obj)
{
    __oput->textStart(__name);
    __oput->textWrite("callId",__obj.callId);
    __oput->textWrite("serialNumber",__obj.serialNumber);
    __oput->textWrite("passthroughInfo",__obj.passthroughInfo);
    __oput->textWrite("token",__obj.token);
    MpCallEb::__textWrite_SessionInfoMap(__oput,"sessions",__obj.sessions);
    __oput->textEnd();
}

bool __textRead_CallRecord(const Common::IputStreamPtr& __iput,const Common::String& __name,MpCallEb::CallRecord& __obj,int __idx)
{
    if (!__iput->textStart(__name,__idx)) return false;
    __iput->textRead("callId",__obj.callId,0);
    __iput->textRead("serialNumber",__obj.serialNumber,0);
    __iput->textRead("passthroughInfo",__obj.passthroughInfo,0);
    __iput->textRead("token",__obj.token,0);
    MpCallEb::__textRead_SessionInfoMap(__iput,"sessions",__obj.sessions,0);
    __iput->textEnd();
    return true;
}

OpenApiRequstHead::OpenApiRequstHead()
{
}

OpenApiRequstHead::OpenApiRequstHead(const Common::String& x_IDNO,const Common::String& x_ReqName,const Common::String& x_Terminal,const Common::String& x_InvokeId) :
    IDNO(x_IDNO),ReqName(x_ReqName),Terminal(x_Terminal),InvokeId(x_InvokeId)
{
}

bool OpenApiRequstHead::operator<(const OpenApiRequstHead&__obj) const
{
    if (this == &__obj) return false;
    if (IDNO < __obj.IDNO) return true;
    if (__obj.IDNO < IDNO) return false;
    if (ReqName < __obj.ReqName) return true;
    if (__obj.ReqName < ReqName) return false;
    if (Terminal < __obj.Terminal) return true;
    if (__obj.Terminal < Terminal) return false;
    if (InvokeId < __obj.InvokeId) return true;
    if (__obj.InvokeId < InvokeId) return false;
    return false;
}

bool OpenApiRequstHead::operator==(const OpenApiRequstHead&__obj) const
{
    if (this == &__obj) return true;
    if (IDNO != __obj.IDNO) return false;
    if (ReqName != __obj.ReqName) return false;
    if (Terminal != __obj.Terminal) return false;
    if (InvokeId != __obj.InvokeId) return false;
    return true;
}

void OpenApiRequstHead::__write(const Common::OputStreamPtr& __oput) const
{
    __write_OpenApiRequstHead(__oput,*this);
}

void OpenApiRequstHead::__read(const Common::IputStreamPtr& __iput)
{
    __read_OpenApiRequstHead(__iput,*this);
}

void OpenApiRequstHead::__textWrite(const Common::OputStreamPtr& __oput,const Common::String& __name) const
{
    __textWrite_OpenApiRequstHead(__oput,__name,*this);
}

bool OpenApiRequstHead::__textRead(const Common::IputStreamPtr& __iput,const Common::String& __name,int __idx)
{
    return __textRead_OpenApiRequstHead(__iput,__name,*this,__idx);
}

void __write_OpenApiRequstHead(const Common::OputStreamPtr& __oput,const MpCallEb::OpenApiRequstHead& __obj)
{
    __oput->write(__obj.IDNO);
    __oput->write(__obj.ReqName);
    __oput->write(__obj.Terminal);
    __oput->write(__obj.InvokeId);
}

void __read_OpenApiRequstHead(const Common::IputStreamPtr& __iput,MpCallEb::OpenApiRequstHead& __obj)
{
    __iput->read(__obj.IDNO);
    __iput->read(__obj.ReqName);
    __iput->read(__obj.Terminal);
    __iput->read(__obj.InvokeId);
}

void __textWrite_OpenApiRequstHead(const Common::OputStreamPtr& __oput,const Common::String& __name,const MpCallEb::OpenApiRequstHead& __obj)
{
    __oput->textStart(__name);
    __oput->textWrite("IDNO",__obj.IDNO);
    __oput->textWrite("ReqName",__obj.ReqName);
    __oput->textWrite("Terminal",__obj.Terminal);
    __oput->textWrite("InvokeId",__obj.InvokeId);
    __oput->textEnd();
}

bool __textRead_OpenApiRequstHead(const Common::IputStreamPtr& __iput,const Common::String& __name,MpCallEb::OpenApiRequstHead& __obj,int __idx)
{
    if (!__iput->textStart(__name,__idx)) return false;
    __iput->textRead("IDNO",__obj.IDNO,0);
    __iput->textRead("ReqName",__obj.ReqName,0);
    __iput->textRead("Terminal",__obj.Terminal,0);
    __iput->textRead("InvokeId",__obj.InvokeId,0);
    __iput->textEnd();
    return true;
}

OpenApiRequstBody::OpenApiRequstBody()
{
}

OpenApiRequstBody::OpenApiRequstBody(const Common::String& x_FileName,const Common::String& x_ObjectId) :
    FileName(x_FileName),ObjectId(x_ObjectId)
{
}

bool OpenApiRequstBody::operator<(const OpenApiRequstBody&__obj) const
{
    if (this == &__obj) return false;
    if (FileName < __obj.FileName) return true;
    if (__obj.FileName < FileName) return false;
    if (ObjectId < __obj.ObjectId) return true;
    if (__obj.ObjectId < ObjectId) return false;
    return false;
}

bool OpenApiRequstBody::operator==(const OpenApiRequstBody&__obj) const
{
    if (this == &__obj) return true;
    if (FileName != __obj.FileName) return false;
    if (ObjectId != __obj.ObjectId) return false;
    return true;
}

void OpenApiRequstBody::__write(const Common::OputStreamPtr& __oput) const
{
    __write_OpenApiRequstBody(__oput,*this);
}

void OpenApiRequstBody::__read(const Common::IputStreamPtr& __iput)
{
    __read_OpenApiRequstBody(__iput,*this);
}

void OpenApiRequstBody::__textWrite(const Common::OputStreamPtr& __oput,const Common::String& __name) const
{
    __textWrite_OpenApiRequstBody(__oput,__name,*this);
}

bool OpenApiRequstBody::__textRead(const Common::IputStreamPtr& __iput,const Common::String& __name,int __idx)
{
    return __textRead_OpenApiRequstBody(__iput,__name,*this,__idx);
}

void __write_OpenApiRequstBody(const Common::OputStreamPtr& __oput,const MpCallEb::OpenApiRequstBody& __obj)
{
    __oput->write(__obj.FileName);
    __oput->write(__obj.ObjectId);
}

void __read_OpenApiRequstBody(const Common::IputStreamPtr& __iput,MpCallEb::OpenApiRequstBody& __obj)
{
    __iput->read(__obj.FileName);
    __iput->read(__obj.ObjectId);
}

void __textWrite_OpenApiRequstBody(const Common::OputStreamPtr& __oput,const Common::String& __name,const MpCallEb::OpenApiRequstBody& __obj)
{
    __oput->textStart(__name);
    __oput->textWrite("FileName",__obj.FileName);
    __oput->textWrite("ObjectId",__obj.ObjectId);
    __oput->textEnd();
}

bool __textRead_OpenApiRequstBody(const Common::IputStreamPtr& __iput,const Common::String& __name,MpCallEb::OpenApiRequstBody& __obj,int __idx)
{
    if (!__iput->textStart(__name,__idx)) return false;
    __iput->textRead("FileName",__obj.FileName,0);
    __iput->textRead("ObjectId",__obj.ObjectId,0);
    __iput->textEnd();
    return true;
}

OpenApiRequest::OpenApiRequest()
{
}

OpenApiRequest::OpenApiRequest(const MpCallEb::OpenApiRequstHead& x_head,const MpCallEb::OpenApiRequstBody& x_body) :
    head(x_head),body(x_body)
{
}

bool OpenApiRequest::operator<(const OpenApiRequest&__obj) const
{
    if (this == &__obj) return false;
    if (head < __obj.head) return true;
    if (__obj.head < head) return false;
    if (body < __obj.body) return true;
    if (__obj.body < body) return false;
    return false;
}

bool OpenApiRequest::operator==(const OpenApiRequest&__obj) const
{
    if (this == &__obj) return true;
    if (head != __obj.head) return false;
    if (body != __obj.body) return false;
    return true;
}

void OpenApiRequest::__write(const Common::OputStreamPtr& __oput) const
{
    __write_OpenApiRequest(__oput,*this);
}

void OpenApiRequest::__read(const Common::IputStreamPtr& __iput)
{
    __read_OpenApiRequest(__iput,*this);
}

void OpenApiRequest::__textWrite(const Common::OputStreamPtr& __oput,const Common::String& __name) const
{
    __textWrite_OpenApiRequest(__oput,__name,*this);
}

bool OpenApiRequest::__textRead(const Common::IputStreamPtr& __iput,const Common::String& __name,int __idx)
{
    return __textRead_OpenApiRequest(__iput,__name,*this,__idx);
}

void __write_OpenApiRequest(const Common::OputStreamPtr& __oput,const MpCallEb::OpenApiRequest& __obj)
{
    MpCallEb::__write_OpenApiRequstHead(__oput,__obj.head);
    MpCallEb::__write_OpenApiRequstBody(__oput,__obj.body);
}

void __read_OpenApiRequest(const Common::IputStreamPtr& __iput,MpCallEb::OpenApiRequest& __obj)
{
    MpCallEb::__read_OpenApiRequstHead(__iput,__obj.head);
    MpCallEb::__read_OpenApiRequstBody(__iput,__obj.body);
}

void __textWrite_OpenApiRequest(const Common::OputStreamPtr& __oput,const Common::String& __name,const MpCallEb::OpenApiRequest& __obj)
{
    __oput->textStart(__name);
    MpCallEb::__textWrite_OpenApiRequstHead(__oput,"head",__obj.head);
    MpCallEb::__textWrite_OpenApiRequstBody(__oput,"body",__obj.body);
    __oput->textEnd();
}

bool __textRead_OpenApiRequest(const Common::IputStreamPtr& __iput,const Common::String& __name,MpCallEb::OpenApiRequest& __obj,int __idx)
{
    if (!__iput->textStart(__name,__idx)) return false;
    MpCallEb::__textRead_OpenApiRequstHead(__iput,"head",__obj.head,0);
    MpCallEb::__textRead_OpenApiRequstBody(__iput,"body",__obj.body,0);
    __iput->textEnd();
    return true;
}

OpenApiResponseHead::OpenApiResponseHead()
{
}

OpenApiResponseHead::OpenApiResponseHead(const Common::String& x_Result,const Common::String& x_ReqName,const Common::String& x_Terminal,const Common::String& x_InvokeId) :
    Result(x_Result),ReqName(x_ReqName),Terminal(x_Terminal),InvokeId(x_InvokeId)
{
}

bool OpenApiResponseHead::operator<(const OpenApiResponseHead&__obj) const
{
    if (this == &__obj) return false;
    if (Result < __obj.Result) return true;
    if (__obj.Result < Result) return false;
    if (ReqName < __obj.ReqName) return true;
    if (__obj.ReqName < ReqName) return false;
    if (Terminal < __obj.Terminal) return true;
    if (__obj.Terminal < Terminal) return false;
    if (InvokeId < __obj.InvokeId) return true;
    if (__obj.InvokeId < InvokeId) return false;
    return false;
}

bool OpenApiResponseHead::operator==(const OpenApiResponseHead&__obj) const
{
    if (this == &__obj) return true;
    if (Result != __obj.Result) return false;
    if (ReqName != __obj.ReqName) return false;
    if (Terminal != __obj.Terminal) return false;
    if (InvokeId != __obj.InvokeId) return false;
    return true;
}

void OpenApiResponseHead::__write(const Common::OputStreamPtr& __oput) const
{
    __write_OpenApiResponseHead(__oput,*this);
}

void OpenApiResponseHead::__read(const Common::IputStreamPtr& __iput)
{
    __read_OpenApiResponseHead(__iput,*this);
}

void OpenApiResponseHead::__textWrite(const Common::OputStreamPtr& __oput,const Common::String& __name) const
{
    __textWrite_OpenApiResponseHead(__oput,__name,*this);
}

bool OpenApiResponseHead::__textRead(const Common::IputStreamPtr& __iput,const Common::String& __name,int __idx)
{
    return __textRead_OpenApiResponseHead(__iput,__name,*this,__idx);
}

void __write_OpenApiResponseHead(const Common::OputStreamPtr& __oput,const MpCallEb::OpenApiResponseHead& __obj)
{
    __oput->write(__obj.Result);
    __oput->write(__obj.ReqName);
    __oput->write(__obj.Terminal);
    __oput->write(__obj.InvokeId);
}

void __read_OpenApiResponseHead(const Common::IputStreamPtr& __iput,MpCallEb::OpenApiResponseHead& __obj)
{
    __iput->read(__obj.Result);
    __iput->read(__obj.ReqName);
    __iput->read(__obj.Terminal);
    __iput->read(__obj.InvokeId);
}

void __textWrite_OpenApiResponseHead(const Common::OputStreamPtr& __oput,const Common::String& __name,const MpCallEb::OpenApiResponseHead& __obj)
{
    __oput->textStart(__name);
    __oput->textWrite("Result",__obj.Result);
    __oput->textWrite("ReqName",__obj.ReqName);
    __oput->textWrite("Terminal",__obj.Terminal);
    __oput->textWrite("InvokeId",__obj.InvokeId);
    __oput->textEnd();
}

bool __textRead_OpenApiResponseHead(const Common::IputStreamPtr& __iput,const Common::String& __name,MpCallEb::OpenApiResponseHead& __obj,int __idx)
{
    if (!__iput->textStart(__name,__idx)) return false;
    __iput->textRead("Result",__obj.Result,0);
    __iput->textRead("ReqName",__obj.ReqName,0);
    __iput->textRead("Terminal",__obj.Terminal,0);
    __iput->textRead("InvokeId",__obj.InvokeId,0);
    __iput->textEnd();
    return true;
}

OpenApiResponseBody::OpenApiResponseBody()
{
}

OpenApiResponseBody::OpenApiResponseBody(const Common::String& x_detail) :
    detail(x_detail)
{
}

bool OpenApiResponseBody::operator<(const OpenApiResponseBody&__obj) const
{
    if (this == &__obj) return false;
    if (detail < __obj.detail) return true;
    if (__obj.detail < detail) return false;
    return false;
}

bool OpenApiResponseBody::operator==(const OpenApiResponseBody&__obj) const
{
    if (this == &__obj) return true;
    if (detail != __obj.detail) return false;
    return true;
}

void OpenApiResponseBody::__write(const Common::OputStreamPtr& __oput) const
{
    __write_OpenApiResponseBody(__oput,*this);
}

void OpenApiResponseBody::__read(const Common::IputStreamPtr& __iput)
{
    __read_OpenApiResponseBody(__iput,*this);
}

void OpenApiResponseBody::__textWrite(const Common::OputStreamPtr& __oput,const Common::String& __name) const
{
    __textWrite_OpenApiResponseBody(__oput,__name,*this);
}

bool OpenApiResponseBody::__textRead(const Common::IputStreamPtr& __iput,const Common::String& __name,int __idx)
{
    return __textRead_OpenApiResponseBody(__iput,__name,*this,__idx);
}

void __write_OpenApiResponseBody(const Common::OputStreamPtr& __oput,const MpCallEb::OpenApiResponseBody& __obj)
{
    __oput->write(__obj.detail);
}

void __read_OpenApiResponseBody(const Common::IputStreamPtr& __iput,MpCallEb::OpenApiResponseBody& __obj)
{
    __iput->read(__obj.detail);
}

void __textWrite_OpenApiResponseBody(const Common::OputStreamPtr& __oput,const Common::String& __name,const MpCallEb::OpenApiResponseBody& __obj)
{
    __oput->textStart(__name);
    __oput->textWrite("detail",__obj.detail);
    __oput->textEnd();
}

bool __textRead_OpenApiResponseBody(const Common::IputStreamPtr& __iput,const Common::String& __name,MpCallEb::OpenApiResponseBody& __obj,int __idx)
{
    if (!__iput->textStart(__name,__idx)) return false;
    __iput->textRead("detail",__obj.detail,0);
    __iput->textEnd();
    return true;
}

OpenApiResponse::OpenApiResponse()
{
}

OpenApiResponse::OpenApiResponse(const MpCallEb::OpenApiResponseHead& x_head,const MpCallEb::OpenApiResponseBody& x_body) :
    head(x_head),body(x_body)
{
}

bool OpenApiResponse::operator<(const OpenApiResponse&__obj) const
{
    if (this == &__obj) return false;
    if (head < __obj.head) return true;
    if (__obj.head < head) return false;
    if (body < __obj.body) return true;
    if (__obj.body < body) return false;
    return false;
}

bool OpenApiResponse::operator==(const OpenApiResponse&__obj) const
{
    if (this == &__obj) return true;
    if (head != __obj.head) return false;
    if (body != __obj.body) return false;
    return true;
}

void OpenApiResponse::__write(const Common::OputStreamPtr& __oput) const
{
    __write_OpenApiResponse(__oput,*this);
}

void OpenApiResponse::__read(const Common::IputStreamPtr& __iput)
{
    __read_OpenApiResponse(__iput,*this);
}

void OpenApiResponse::__textWrite(const Common::OputStreamPtr& __oput,const Common::String& __name) const
{
    __textWrite_OpenApiResponse(__oput,__name,*this);
}

bool OpenApiResponse::__textRead(const Common::IputStreamPtr& __iput,const Common::String& __name,int __idx)
{
    return __textRead_OpenApiResponse(__iput,__name,*this,__idx);
}

void __write_OpenApiResponse(const Common::OputStreamPtr& __oput,const MpCallEb::OpenApiResponse& __obj)
{
    MpCallEb::__write_OpenApiResponseHead(__oput,__obj.head);
    MpCallEb::__write_OpenApiResponseBody(__oput,__obj.body);
}

void __read_OpenApiResponse(const Common::IputStreamPtr& __iput,MpCallEb::OpenApiResponse& __obj)
{
    MpCallEb::__read_OpenApiResponseHead(__iput,__obj.head);
    MpCallEb::__read_OpenApiResponseBody(__iput,__obj.body);
}

void __textWrite_OpenApiResponse(const Common::OputStreamPtr& __oput,const Common::String& __name,const MpCallEb::OpenApiResponse& __obj)
{
    __oput->textStart(__name);
    MpCallEb::__textWrite_OpenApiResponseHead(__oput,"head",__obj.head);
    MpCallEb::__textWrite_OpenApiResponseBody(__oput,"body",__obj.body);
    __oput->textEnd();
}

bool __textRead_OpenApiResponse(const Common::IputStreamPtr& __iput,const Common::String& __name,MpCallEb::OpenApiResponse& __obj,int __idx)
{
    if (!__iput->textStart(__name,__idx)) return false;
    MpCallEb::__textRead_OpenApiResponseHead(__iput,"head",__obj.head,0);
    MpCallEb::__textRead_OpenApiResponseBody(__iput,"body",__obj.body,0);
    __iput->textEnd();
    return true;
}

};//namespace: MpCallEb
