﻿//
// *****************************************************************************
// Copyright(c) 2017-2022 Juphoon System Software Co., LTD. All rights reserved.
// *****************************************************************************
//
// Auto generated from: MpCallEb.def
// Warning: do not edit this file.
//

#ifndef __MpCallEb_MpCallEbAgent_h
#define __MpCallEb_MpCallEbAgent_h

#include "MpCallEb/MpCallEbPub.h"

namespace MpCallEb
{

class OpenApiAgent : public Common::Agent
{
public:
    OpenApiAgent(int zero = 0) : Common::Agent(zero) {}
    OpenApiAgent(const Common::Agent& agent) : Common::Agent(agent) {}
    OpenApiAgent(const Common::ObjectAgentPtr& agent) : Common::Agent(agent) {}

    bool bindSession(const Common::String& serialNumber,const Common::String& callId,const Common::CallParamsPtr& __params = 0) const throw();
    void bindSession_begin(const Common::AgentAsyncPtr& __async,const Common::String& serialNumber,const Common::String& callId,const Common::CallParamsPtr& __params = 0,const Common::ObjectPtr& __userdata = 0) const throw();
    static bool bindSession_end(int __rslt,const Common::IputStreamPtr& __iput) throw();

    bool notifyAnswer(const Common::String& serialNumber,const Common::String& calleeNumber,const Common::CallParamsPtr& __params = 0) const throw();
    void notifyAnswer_begin(const Common::AgentAsyncPtr& __async,const Common::String& serialNumber,const Common::String& calleeNumber,const Common::CallParamsPtr& __params = 0,const Common::ObjectPtr& __userdata = 0) const throw();
    static bool notifyAnswer_end(int __rslt,const Common::IputStreamPtr& __iput) throw();

    bool notifyFilePlay(const Common::String& serialNumber,const Common::String& calleeNumber,bool start,const Common::String& filename,const Common::CallParamsPtr& __params = 0) const throw();
    void notifyFilePlay_begin(const Common::AgentAsyncPtr& __async,const Common::String& serialNumber,const Common::String& calleeNumber,bool start,const Common::String& filename,const Common::CallParamsPtr& __params = 0,const Common::ObjectPtr& __userdata = 0) const throw();
    static bool notifyFilePlay_end(int __rslt,const Common::IputStreamPtr& __iput) throw();
};

};//namespace: MpCallEb

#endif //__MpCallEb_MpCallEbAgent_h
