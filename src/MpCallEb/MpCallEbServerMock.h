// Generated by MockTools.py. All rights reserved by Juphoon System Software.

#include "gmock/gmock.h"
#include "MpCallEb/MpCallEbServer.h"

#ifndef __MpCallEb_MpCallEbServerMock_h__
#define __MpCallEb_MpCallEbServerMock_h__

namespace ModuleTest
{
    class OpenApiServerMockBase;
    typedef Common::Handle<OpenApiServerMockBase> OpenApiServerMockBasePtr;
    class OpenApiServerMockBase : public MpCallEb::OpenApiServer
    {
    public:
        virtual bool __ex(const Common::ServerCallPtr &__call, const Common::String &__cmd, const Common::IputStreamPtr &__iput)
        {
            return MpCallEb::OpenApiServer::__ex(__call, __cmd, __iput);
        }

        MOCK_METHOD(void, bindSession_begin, (const Common::ServerCallPtr &__call, const Common::String &serialNumber, const Common::String &callId), (override));
        MOCK_METHOD(void, notifyAnswer_begin, (const Common::ServerCallPtr &__call, const Common::String &serialNumber, const Common::String &calleeNumber), (override));
        MOCK_METHOD(void, notifyFilePlay_begin, (const Common::ServerCallPtr &__call, const Common::String &serialNumber, const Common::String &calleeNumber, bool start, const Common::String &filename), (override));

        void delegateToBad()
        {
            ON_CALL(*this, bindSession_begin).WillByDefault([this](const Common::ServerCallPtr &__call, const Common::String &serialNumber, const Common::String &callId) {
                bindSession_end(__call, false);
            });
            ON_CALL(*this, notifyAnswer_begin).WillByDefault([this](const Common::ServerCallPtr &__call, const Common::String &serialNumber, const Common::String &calleeNumber) {
                notifyAnswer_end(__call, false);
            });
            ON_CALL(*this, notifyFilePlay_begin).WillByDefault([this](const Common::ServerCallPtr &__call, const Common::String &serialNumber, const Common::String &calleeNumber, bool start, const Common::String &filename) {
                notifyFilePlay_end(__call, false);
            });
        }

        void delegateToNice()
        {
            ON_CALL(*this, bindSession_begin).WillByDefault([this](const Common::ServerCallPtr &__call, const Common::String &serialNumber, const Common::String &callId) {
                bindSession_end(__call, true);
            });
            ON_CALL(*this, notifyAnswer_begin).WillByDefault([this](const Common::ServerCallPtr &__call, const Common::String &serialNumber, const Common::String &calleeNumber) {
                notifyAnswer_end(__call, true);
            });
            ON_CALL(*this, notifyFilePlay_begin).WillByDefault([this](const Common::ServerCallPtr &__call, const Common::String &serialNumber, const Common::String &calleeNumber, bool start, const Common::String &filename) {
                notifyFilePlay_end(__call, true);
            });
        }
    };
}

#endif // __MpCallEb_MpCallEbServerMock_h__