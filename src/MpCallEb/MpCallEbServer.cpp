﻿//
// *****************************************************************************
// Copyright(c) 2017-2022 Juphoon System Software Co., LTD. All rights reserved.
// *****************************************************************************
//
// Auto generated from: MpCallEb.def
// Warning: do not edit this file.
//

#include "MpCallEb/MpCallEbServer.h"

namespace MpCallEb
{

bool OpenApiServer::__ex(const Common::ServerCallPtr& __call,const Common::String& __cmd,const Common::IputStreamPtr& __iput)
{
    if (__cmd == "bindSession.OpenApi.MpCallEb") { __cmd_bindSession(__call,__iput);return true;}
    if (__cmd == "notifyAnswer.OpenApi.MpCallEb") { __cmd_notifyAnswer(__call,__iput);return true;}
    if (__cmd == "notifyFilePlay.OpenApi.MpCallEb") { __cmd_notifyFilePlay(__call,__iput);return true;}
    return false;
}

void OpenApiServer::bindSession_end(const Common::ServerCallPtr& __call,bool __ret)
{
    Common::VerListPtr __vers = __call->verList();
    Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
    short __ver = (__vers)?__vers->ver(true):0;
    switch (__ver)
    {
    case 0:
        __oput->write(__ret);
        break;
    }
    __call->cmdResult(__ver,__oput);
}

void OpenApiServer::notifyAnswer_end(const Common::ServerCallPtr& __call,bool __ret)
{
    Common::VerListPtr __vers = __call->verList();
    Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
    short __ver = (__vers)?__vers->ver(true):0;
    switch (__ver)
    {
    case 0:
        __oput->write(__ret);
        break;
    }
    __call->cmdResult(__ver,__oput);
}

void OpenApiServer::notifyFilePlay_end(const Common::ServerCallPtr& __call,bool __ret)
{
    Common::VerListPtr __vers = __call->verList();
    Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
    short __ver = (__vers)?__vers->ver(true):0;
    switch (__ver)
    {
    case 0:
        __oput->write(__ret);
        break;
    }
    __call->cmdResult(__ver,__oput);
}

void OpenApiServer::__cmd_bindSession(const Common::ServerCallPtr& __call,const Common::IputStreamPtr& __iput)
{
    do
    {
        Common::VerListPtr __vers = __call->verList();
        Common::String serialNumber;Common::String callId;
        switch (__vers->ver(false))
        {
        case 0:
            __iput->read(serialNumber);
            __iput->read(callId);
            break;
        default: goto __ver_err;
        }
        __start(false);
        bindSession_begin(__call,serialNumber,callId);
        return;
    } while(0);
__ver_err:
    Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
    __oput->write((short)1);
    __oput->write((short)0);
    __call->cmdResult(VersionException<<16,__oput);
}

void OpenApiServer::__cmd_notifyAnswer(const Common::ServerCallPtr& __call,const Common::IputStreamPtr& __iput)
{
    do
    {
        Common::VerListPtr __vers = __call->verList();
        Common::String serialNumber;Common::String calleeNumber;
        switch (__vers->ver(false))
        {
        case 0:
            __iput->read(serialNumber);
            __iput->read(calleeNumber);
            break;
        default: goto __ver_err;
        }
        __start(false);
        notifyAnswer_begin(__call,serialNumber,calleeNumber);
        return;
    } while(0);
__ver_err:
    Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
    __oput->write((short)1);
    __oput->write((short)0);
    __call->cmdResult(VersionException<<16,__oput);
}

void OpenApiServer::__cmd_notifyFilePlay(const Common::ServerCallPtr& __call,const Common::IputStreamPtr& __iput)
{
    do
    {
        Common::VerListPtr __vers = __call->verList();
        Common::String serialNumber;Common::String calleeNumber;bool start=false;Common::String filename;
        switch (__vers->ver(false))
        {
        case 0:
            __iput->read(serialNumber);
            __iput->read(calleeNumber);
            __iput->read(start);
            __iput->read(filename);
            break;
        default: goto __ver_err;
        }
        __start(false);
        notifyFilePlay_begin(__call,serialNumber,calleeNumber,start,filename);
        return;
    } while(0);
__ver_err:
    Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
    __oput->write((short)1);
    __oput->write((short)0);
    __call->cmdResult(VersionException<<16,__oput);
}

};//namespace: MpCallEb
