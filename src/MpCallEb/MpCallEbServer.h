﻿//
// *****************************************************************************
// Copyright(c) 2017-2022 Juphoon System Software Co., LTD. All rights reserved.
// *****************************************************************************
//
// Auto generated from: MpCallEb.def
// Warning: do not edit this file.
//

#ifndef __MpCallEb_MpCallEbServer_h
#define __MpCallEb_MpCallEbServer_h

#include "MpCallEb/MpCallEbPub.h"

namespace MpCallEb
{

class OpenApiServer : virtual public Common::ObjectServer
{
public:
    virtual bool __ex(const Common::ServerCallPtr& __call,const Common::String& __cmd,const Common::IputStreamPtr& __iput);

    virtual void bindSession_begin(const Common::ServerCallPtr& __call,const Common::String& serialNumber,const Common::String& callId) = 0;
    virtual void notifyAnswer_begin(const Common::ServerCallPtr& __call,const Common::String& serialNumber,const Common::String& calleeNumber) = 0;
    virtual void notifyFilePlay_begin(const Common::ServerCallPtr& __call,const Common::String& serialNumber,const Common::String& calleeNumber,bool start,const Common::String& filename) = 0;

    static void bindSession_end(const Common::ServerCallPtr& __call,bool __ret);
    static void notifyAnswer_end(const Common::ServerCallPtr& __call,bool __ret);
    static void notifyFilePlay_end(const Common::ServerCallPtr& __call,bool __ret);

    static inline void bindSession_end(const Common::ServerCallPtr &__call,const Common::CallError &error)
    {
        __call->setError(error);
        bindSession_end(__call,false);
    }
    static inline void notifyAnswer_end(const Common::ServerCallPtr &__call,const Common::CallError &error)
    {
        __call->setError(error);
        notifyAnswer_end(__call,false);
    }
    static inline void notifyFilePlay_end(const Common::ServerCallPtr &__call,const Common::CallError &error)
    {
        __call->setError(error);
        notifyFilePlay_end(__call,false);
    }

private:
    void __cmd_bindSession(const Common::ServerCallPtr& __call,const Common::IputStreamPtr& __iput);
    void __cmd_notifyAnswer(const Common::ServerCallPtr& __call,const Common::IputStreamPtr& __iput);
    void __cmd_notifyFilePlay(const Common::ServerCallPtr& __call,const Common::IputStreamPtr& __iput);
};

};//namespace: MpCallEb

#endif //__MpCallEb_MpCallEbServer_h
