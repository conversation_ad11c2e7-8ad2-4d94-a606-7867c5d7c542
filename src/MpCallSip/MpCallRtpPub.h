﻿//
// *****************************************************************************
// Copyright(c) 2017-2025 Juphoon System Software Co., LTD. All rights reserved.
// *****************************************************************************
//
// Auto generated from: MpCallRtp.def
// Warning: do not edit this file.
//

#ifndef __MpCallSip_MpCallRtpPub_h
#define __MpCallSip_MpCallRtpPub_h

#include "Common/Common.h"
#include "Common/TypesPub.h"

namespace MpCallSip
{

/** 媒体方向 */
enum MediaDirection
{
    MediaInactive = 0,
    MediaSendOnly = 1,
    MediaRecvOnly = 2,
    MediaSendRecv = 3,
};
const char* MediaDirection_toString(MpCallSip::MediaDirection val);

class RtpRunnerConfig
{
public:
    RtpRunnerConfig();
    RtpRunnerConfig(int,int,const Common::String&,int,const Common::String&,const Common::String&,const Common::String&,const Common::String&,int,int,bool,int,int,bool,bool,const Common::String&);

    bool operator<(const RtpRunnerConfig&) const;
    bool operator==(const RtpRunnerConfig&) const;
    bool operator!=(const RtpRunnerConfig&__obj) const { return !operator==(__obj);}
    void __write(const Common::OputStreamPtr&) const;
    void __read(const Common::IputStreamPtr&);
    void __textWrite(const Common::OputStreamPtr&,const Common::String&) const;
    bool __textRead(const Common::IputStreamPtr&,const Common::String&,int = 0);
public:
    int domainId;
    int appId;
    // name in room
    Common::String name;
    int extraRole;
    // room info, oid&roomId || confNum || Udid
    Common::String oid;
    Common::String roomId;
    Common::String confNum;
    Common::String Udid;
    int audioExcludeRoleMask;
    int audioExcludeRole;
    bool audioMute;
    int videoExcludeRoleMask;
    int videoExcludeRole;
    bool videoMute;
    bool screenSharingMode;
    Common::String bindingUser;
};
void __write_RtpRunnerConfig(const Common::OputStreamPtr&,const MpCallSip::RtpRunnerConfig&);
void __read_RtpRunnerConfig(const Common::IputStreamPtr&,MpCallSip::RtpRunnerConfig&);
void __textWrite_RtpRunnerConfig(const Common::OputStreamPtr&,const Common::String&,const MpCallSip::RtpRunnerConfig&);
bool __textRead_RtpRunnerConfig(const Common::IputStreamPtr&,const Common::String&,MpCallSip::RtpRunnerConfig&,int = 0);

class SdpParams
{
public:
    SdpParams();
    SdpParams(MpCallSip::MediaDirection,MpCallSip::MediaDirection,bool);

    bool operator<(const SdpParams&) const;
    bool operator==(const SdpParams&) const;
    bool operator!=(const SdpParams&__obj) const { return !operator==(__obj);}
    void __write(const Common::OputStreamPtr&) const;
    void __read(const Common::IputStreamPtr&);
    void __textWrite(const Common::OputStreamPtr&,const Common::String&) const;
    bool __textRead(const Common::IputStreamPtr&,const Common::String&,int = 0);
public:
    MpCallSip::MediaDirection videoDirection;
    MpCallSip::MediaDirection audioDirection;
    bool precondition;
};
void __write_SdpParams(const Common::OputStreamPtr&,const MpCallSip::SdpParams&);
void __read_SdpParams(const Common::IputStreamPtr&,MpCallSip::SdpParams&);
void __textWrite_SdpParams(const Common::OputStreamPtr&,const Common::String&,const MpCallSip::SdpParams&);
bool __textRead_SdpParams(const Common::IputStreamPtr&,const Common::String&,MpCallSip::SdpParams&,int = 0);

class RtpAudioSendStats
{
public:
    RtpAudioSendStats();
    RtpAudioSendStats(Common::Long,Common::Long,int,int,int);

    bool operator<(const RtpAudioSendStats&) const;
    bool operator==(const RtpAudioSendStats&) const;
    bool operator!=(const RtpAudioSendStats&__obj) const { return !operator==(__obj);}
    void __write(const Common::OputStreamPtr&) const;
    void __read(const Common::IputStreamPtr&);
    void __textWrite(const Common::OputStreamPtr&,const Common::String&) const;
    bool __textRead(const Common::IputStreamPtr&,const Common::String&,int = 0);
public:
    Common::Long totalPackets;
    Common::Long lostPackets;
    int lostPercent;
    int jitter;
    int bitrate;
};
void __write_RtpAudioSendStats(const Common::OputStreamPtr&,const MpCallSip::RtpAudioSendStats&);
void __read_RtpAudioSendStats(const Common::IputStreamPtr&,MpCallSip::RtpAudioSendStats&);
void __textWrite_RtpAudioSendStats(const Common::OputStreamPtr&,const Common::String&,const MpCallSip::RtpAudioSendStats&);
bool __textRead_RtpAudioSendStats(const Common::IputStreamPtr&,const Common::String&,MpCallSip::RtpAudioSendStats&,int = 0);

class RtpAudioRecvStats
{
public:
    RtpAudioRecvStats();
    RtpAudioRecvStats(Common::Long,Common::Long,int,int,int,float);

    bool operator<(const RtpAudioRecvStats&) const;
    bool operator==(const RtpAudioRecvStats&) const;
    bool operator!=(const RtpAudioRecvStats&__obj) const { return !operator==(__obj);}
    void __write(const Common::OputStreamPtr&) const;
    void __read(const Common::IputStreamPtr&);
    void __textWrite(const Common::OputStreamPtr&,const Common::String&) const;
    bool __textRead(const Common::IputStreamPtr&,const Common::String&,int = 0);
public:
    Common::Long totalPackets;
    Common::Long lostPackets;
    int lostPercent;
    int jitter;
    int bitrate;
    float tmos;
};
void __write_RtpAudioRecvStats(const Common::OputStreamPtr&,const MpCallSip::RtpAudioRecvStats&);
void __read_RtpAudioRecvStats(const Common::IputStreamPtr&,MpCallSip::RtpAudioRecvStats&);
void __textWrite_RtpAudioRecvStats(const Common::OputStreamPtr&,const Common::String&,const MpCallSip::RtpAudioRecvStats&);
bool __textRead_RtpAudioRecvStats(const Common::IputStreamPtr&,const Common::String&,MpCallSip::RtpAudioRecvStats&,int = 0);

class RtpAudioGeneranlStats
{
public:
    RtpAudioGeneranlStats();
    RtpAudioGeneranlStats(int,const Common::String&);

    bool operator<(const RtpAudioGeneranlStats&) const;
    bool operator==(const RtpAudioGeneranlStats&) const;
    bool operator!=(const RtpAudioGeneranlStats&__obj) const { return !operator==(__obj);}
    void __write(const Common::OputStreamPtr&) const;
    void __read(const Common::IputStreamPtr&);
    void __textWrite(const Common::OputStreamPtr&,const Common::String&) const;
    bool __textRead(const Common::IputStreamPtr&,const Common::String&,int = 0);
public:
    int rtt;
    Common::String networkStatus;
};
void __write_RtpAudioGeneranlStats(const Common::OputStreamPtr&,const MpCallSip::RtpAudioGeneranlStats&);
void __read_RtpAudioGeneranlStats(const Common::IputStreamPtr&,MpCallSip::RtpAudioGeneranlStats&);
void __textWrite_RtpAudioGeneranlStats(const Common::OputStreamPtr&,const Common::String&,const MpCallSip::RtpAudioGeneranlStats&);
bool __textRead_RtpAudioGeneranlStats(const Common::IputStreamPtr&,const Common::String&,MpCallSip::RtpAudioGeneranlStats&,int = 0);

class RtpAudioConfigStats
{
public:
    RtpAudioConfigStats();
    RtpAudioConfigStats(const Common::String&,int,const Common::String&,int,const Common::String&,int,int,int);

    bool operator<(const RtpAudioConfigStats&) const;
    bool operator==(const RtpAudioConfigStats&) const;
    bool operator!=(const RtpAudioConfigStats&__obj) const { return !operator==(__obj);}
    void __write(const Common::OputStreamPtr&) const;
    void __read(const Common::IputStreamPtr&);
    void __textWrite(const Common::OputStreamPtr&,const Common::String&) const;
    bool __textRead(const Common::IputStreamPtr&,const Common::String&,int = 0);
public:
    Common::String localIp;
    int localPort;
    Common::String remoteIp;
    int remotePort;
    Common::String codec;
    int payload;
    int bitrate;
    int packetLen;
};
void __write_RtpAudioConfigStats(const Common::OputStreamPtr&,const MpCallSip::RtpAudioConfigStats&);
void __read_RtpAudioConfigStats(const Common::IputStreamPtr&,MpCallSip::RtpAudioConfigStats&);
void __textWrite_RtpAudioConfigStats(const Common::OputStreamPtr&,const Common::String&,const MpCallSip::RtpAudioConfigStats&);
bool __textRead_RtpAudioConfigStats(const Common::IputStreamPtr&,const Common::String&,MpCallSip::RtpAudioConfigStats&,int = 0);

class RtpAudioStats
{
public:
    RtpAudioStats();
    RtpAudioStats(const MpCallSip::RtpAudioSendStats&,const MpCallSip::RtpAudioRecvStats&,const MpCallSip::RtpAudioGeneranlStats&,const MpCallSip::RtpAudioConfigStats&);

    bool operator<(const RtpAudioStats&) const;
    bool operator==(const RtpAudioStats&) const;
    bool operator!=(const RtpAudioStats&__obj) const { return !operator==(__obj);}
    void __write(const Common::OputStreamPtr&) const;
    void __read(const Common::IputStreamPtr&);
    void __textWrite(const Common::OputStreamPtr&,const Common::String&) const;
    bool __textRead(const Common::IputStreamPtr&,const Common::String&,int = 0);
public:
    MpCallSip::RtpAudioSendStats send;
    MpCallSip::RtpAudioRecvStats recv;
    MpCallSip::RtpAudioGeneranlStats general;
    MpCallSip::RtpAudioConfigStats config;
};
void __write_RtpAudioStats(const Common::OputStreamPtr&,const MpCallSip::RtpAudioStats&);
void __read_RtpAudioStats(const Common::IputStreamPtr&,MpCallSip::RtpAudioStats&);
void __textWrite_RtpAudioStats(const Common::OputStreamPtr&,const Common::String&,const MpCallSip::RtpAudioStats&);
bool __textRead_RtpAudioStats(const Common::IputStreamPtr&,const Common::String&,MpCallSip::RtpAudioStats&,int = 0);

class RtpVideoSendStats
{
public:
    RtpVideoSendStats();
    RtpVideoSendStats(Common::Long,Common::Long,int,int,int,const Common::String&,int,const Common::String&,float,float,int);

    bool operator<(const RtpVideoSendStats&) const;
    bool operator==(const RtpVideoSendStats&) const;
    bool operator!=(const RtpVideoSendStats&__obj) const { return !operator==(__obj);}
    void __write(const Common::OputStreamPtr&) const;
    void __read(const Common::IputStreamPtr&);
    void __textWrite(const Common::OputStreamPtr&,const Common::String&) const;
    bool __textRead(const Common::IputStreamPtr&,const Common::String&,int = 0);
public:
    Common::Long totalPackets;
    Common::Long lostPackets;
    int lostPercent;
    int jitter;
    int frameRate;
    Common::String resolution;
    int bitrate;
    Common::String codec;
    float spmos;
    float pnsr;
    int encodeTime;
};
void __write_RtpVideoSendStats(const Common::OputStreamPtr&,const MpCallSip::RtpVideoSendStats&);
void __read_RtpVideoSendStats(const Common::IputStreamPtr&,MpCallSip::RtpVideoSendStats&);
void __textWrite_RtpVideoSendStats(const Common::OputStreamPtr&,const Common::String&,const MpCallSip::RtpVideoSendStats&);
bool __textRead_RtpVideoSendStats(const Common::IputStreamPtr&,const Common::String&,MpCallSip::RtpVideoSendStats&,int = 0);

class RtpVideoRecvStats
{
public:
    RtpVideoRecvStats();
    RtpVideoRecvStats(Common::Long,Common::Long,int,int,int,const Common::String&,int,const Common::String&,float,int);

    bool operator<(const RtpVideoRecvStats&) const;
    bool operator==(const RtpVideoRecvStats&) const;
    bool operator!=(const RtpVideoRecvStats&__obj) const { return !operator==(__obj);}
    void __write(const Common::OputStreamPtr&) const;
    void __read(const Common::IputStreamPtr&);
    void __textWrite(const Common::OputStreamPtr&,const Common::String&) const;
    bool __textRead(const Common::IputStreamPtr&,const Common::String&,int = 0);
public:
    Common::Long totalPackets;
    Common::Long lostPackets;
    int lostPercent;
    int jitter;
    int frameRate;
    Common::String resolution;
    int bitrate;
    Common::String codec;
    float pvmos;
    int decodeTime;
};
void __write_RtpVideoRecvStats(const Common::OputStreamPtr&,const MpCallSip::RtpVideoRecvStats&);
void __read_RtpVideoRecvStats(const Common::IputStreamPtr&,MpCallSip::RtpVideoRecvStats&);
void __textWrite_RtpVideoRecvStats(const Common::OputStreamPtr&,const Common::String&,const MpCallSip::RtpVideoRecvStats&);
bool __textRead_RtpVideoRecvStats(const Common::IputStreamPtr&,const Common::String&,MpCallSip::RtpVideoRecvStats&,int = 0);

class RtpVideoGeneralStats
{
public:
    RtpVideoGeneralStats();
    RtpVideoGeneralStats(const Common::String&,int,int,int,const Common::String&);

    bool operator<(const RtpVideoGeneralStats&) const;
    bool operator==(const RtpVideoGeneralStats&) const;
    bool operator!=(const RtpVideoGeneralStats&__obj) const { return !operator==(__obj);}
    void __write(const Common::OputStreamPtr&) const;
    void __read(const Common::IputStreamPtr&);
    void __textWrite(const Common::OputStreamPtr&,const Common::String&) const;
    bool __textRead(const Common::IputStreamPtr&,const Common::String&,int = 0);
public:
    Common::String captureResolution;
    int captureFrameRate;
    int renderFrameRate;
    int rtt;
    Common::String networkStatus;
};
void __write_RtpVideoGeneralStats(const Common::OputStreamPtr&,const MpCallSip::RtpVideoGeneralStats&);
void __read_RtpVideoGeneralStats(const Common::IputStreamPtr&,MpCallSip::RtpVideoGeneralStats&);
void __textWrite_RtpVideoGeneralStats(const Common::OputStreamPtr&,const Common::String&,const MpCallSip::RtpVideoGeneralStats&);
bool __textRead_RtpVideoGeneralStats(const Common::IputStreamPtr&,const Common::String&,MpCallSip::RtpVideoGeneralStats&,int = 0);

class RtpVideoConfigStatus
{
public:
    RtpVideoConfigStatus();
    RtpVideoConfigStatus(const Common::String&,int,const Common::String&,int,const Common::String&,int,int,int,const Common::String&);

    bool operator<(const RtpVideoConfigStatus&) const;
    bool operator==(const RtpVideoConfigStatus&) const;
    bool operator!=(const RtpVideoConfigStatus&__obj) const { return !operator==(__obj);}
    void __write(const Common::OputStreamPtr&) const;
    void __read(const Common::IputStreamPtr&);
    void __textWrite(const Common::OputStreamPtr&,const Common::String&) const;
    bool __textRead(const Common::IputStreamPtr&,const Common::String&,int = 0);
public:
    Common::String localIp;
    int localPort;
    Common::String remoteIp;
    int remotePort;
    Common::String codec;
    int payload;
    int bitrate;
    int frameRate;
    Common::String resolution;
};
void __write_RtpVideoConfigStatus(const Common::OputStreamPtr&,const MpCallSip::RtpVideoConfigStatus&);
void __read_RtpVideoConfigStatus(const Common::IputStreamPtr&,MpCallSip::RtpVideoConfigStatus&);
void __textWrite_RtpVideoConfigStatus(const Common::OputStreamPtr&,const Common::String&,const MpCallSip::RtpVideoConfigStatus&);
bool __textRead_RtpVideoConfigStatus(const Common::IputStreamPtr&,const Common::String&,MpCallSip::RtpVideoConfigStatus&,int = 0);

class RtpVideoStats
{
public:
    RtpVideoStats();
    RtpVideoStats(const MpCallSip::RtpVideoSendStats&,const MpCallSip::RtpVideoRecvStats&,const MpCallSip::RtpVideoGeneralStats&,const MpCallSip::RtpVideoConfigStatus&);

    bool operator<(const RtpVideoStats&) const;
    bool operator==(const RtpVideoStats&) const;
    bool operator!=(const RtpVideoStats&__obj) const { return !operator==(__obj);}
    void __write(const Common::OputStreamPtr&) const;
    void __read(const Common::IputStreamPtr&);
    void __textWrite(const Common::OputStreamPtr&,const Common::String&) const;
    bool __textRead(const Common::IputStreamPtr&,const Common::String&,int = 0);
public:
    MpCallSip::RtpVideoSendStats send;
    MpCallSip::RtpVideoRecvStats recv;
    MpCallSip::RtpVideoGeneralStats general;
    MpCallSip::RtpVideoConfigStatus config;
};
void __write_RtpVideoStats(const Common::OputStreamPtr&,const MpCallSip::RtpVideoStats&);
void __read_RtpVideoStats(const Common::IputStreamPtr&,MpCallSip::RtpVideoStats&);
void __textWrite_RtpVideoStats(const Common::OputStreamPtr&,const Common::String&,const MpCallSip::RtpVideoStats&);
bool __textRead_RtpVideoStats(const Common::IputStreamPtr&,const Common::String&,MpCallSip::RtpVideoStats&,int = 0);

class RtpStats
{
public:
    RtpStats();
    RtpStats(const MpCallSip::RtpAudioStats&,const MpCallSip::RtpVideoStats&);

    bool operator<(const RtpStats&) const;
    bool operator==(const RtpStats&) const;
    bool operator!=(const RtpStats&__obj) const { return !operator==(__obj);}
    void __write(const Common::OputStreamPtr&) const;
    void __read(const Common::IputStreamPtr&);
    void __textWrite(const Common::OputStreamPtr&,const Common::String&) const;
    bool __textRead(const Common::IputStreamPtr&,const Common::String&,int = 0);
public:
    MpCallSip::RtpAudioStats audio;
    MpCallSip::RtpVideoStats video;
};
void __write_RtpStats(const Common::OputStreamPtr&,const MpCallSip::RtpStats&);
void __read_RtpStats(const Common::IputStreamPtr&,MpCallSip::RtpStats&);
void __textWrite_RtpStats(const Common::OputStreamPtr&,const Common::String&,const MpCallSip::RtpStats&);
bool __textRead_RtpStats(const Common::IputStreamPtr&,const Common::String&,MpCallSip::RtpStats&,int = 0);

};//namespace: MpCallSip

#endif //__MpCallSip_MpCallRtpPub_h
