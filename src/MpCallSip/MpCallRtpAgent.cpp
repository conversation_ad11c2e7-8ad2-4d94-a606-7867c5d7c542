﻿//
// *****************************************************************************
// Copyright(c) 2017-2025 Juphoon System Software Co., LTD. All rights reserved.
// *****************************************************************************
//
// Auto generated from: MpCallRtp.def
// Warning: do not edit this file.
//

#include "MpCallSip/MpCallRtpAgent.h"

namespace MpCallSip
{

void RtpRunnerMonitorAgent::onError(const Common::String& reason,const Common::CallParamsPtr& __params) const
{
    try
    {
        int __loop = 0;
        while (1)
        {
            Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
            Common::VerListPtr __vers = __agent->verList("onError.RtpRunnerMonitor.MpCallSip");
            short __ver = 0;
            if (__vers)
            {
                __ver = __vers->ver(true);
                if (__ver > 0) __ver = 0;
            }
            switch (__ver)
            {
            case 0:
                __oput->write((short)1);
                __oput->write((short)__ver);
                __oput->write(reason);
                break;
            default:
                throw Common::CallException(Common::ObjectAgent::versionError("onError.RtpRunnerMonitor.MpCallSip"));
            }
            Common::IputStreamPtr __iput;
            Common::CallError __error;
            int __rslt = __agent->ex_sync("onError.RtpRunnerMonitor.MpCallSip",__oput,__iput,__params,__error);
            if (__rslt == -1)
                throw Common::CallException(__error);
            if (__rslt>>16)
            {
                Assert((__rslt>>16) == VersionException);
                __loop ++;
                if (__loop >= 3)
                    throw Common::CallException(Common::ObjectAgent::versionError("onError.RtpRunnerMonitor.MpCallSip"));
                continue;
            }
            switch (__rslt)
            {
            case 0:
                break;
            default:
                throw Common::CallException(Common::ObjectAgent::versionError("onError.RtpRunnerMonitor.MpCallSip"));
            }
            Common::ObjectAgent::processFinal(__iput);
            return;
        }
    }
    catch (const Common::CallException& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        throw;
    }
    catch (const Common::Exception& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        throw;
    }
}

void RtpRunnerMonitorAgent::onError_begin(const Common::AgentAsyncPtr& __async,const Common::String& reason,const Common::CallParamsPtr& __params,const Common::ObjectPtr& __userdata) const throw()
{
    class __AgentAsync : public Common::AgentAsync
    {
    public:
        __AgentAsync(const Common::ObjectAgentPtr& __agent,const Common::AgentAsyncPtr& __async,const Common::String& reason,const Common::CallParamsPtr& __params,const Common::ObjectPtr& __userdata)
            : x__agent(__agent),x__async(__async),x_reason(reason),x__params(__params),x__userdata(__userdata),x__loop(0) {}

        void start()
        {
            Common::AgentAsyncPtr pthis = this;
            try
            {
                Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
                Common::VerListPtr __vers = x__agent->verList("onError.RtpRunnerMonitor.MpCallSip");
                short __ver = 0;
                if (__vers)
                {
                    __ver = __vers->ver(true);
                    if (__ver > 0) __ver = 0;
                }
                switch (__ver)
                {
                case 0:
                    __oput->write((short)1);
                    __oput->write((short)__ver);
                    __oput->write(x_reason);
                    break;
                default:
                    throw Common::CallException(Common::ObjectAgent::versionError("onError.RtpRunnerMonitor.MpCallSip"));
                }
                x__agent->ex_async(this,"onError.RtpRunnerMonitor.MpCallSip",__oput,x__params,0);
            }
            catch (const Common::CallException& ex)
            {
                Common::ObjectAgent::throwException(x__async,ex,x__userdata);
            }
            catch (const Common::Exception& ex)
            {
                Common::ObjectAgent::throwException(x__async,ex,x__userdata);
            }
        }
        void cmdResult(int __rslt,const Common::IputStreamPtr& __iput,const Common::ObjectPtr& __userdata)
        {
            if ((__rslt>>16)!=VersionException)
            {
                if (x__async) x__async->cmdResult(__rslt,__iput,x__userdata);
                return;
            }
            x__loop ++;
            if (x__loop >= 3)
            {
                if (x__async)
                    Common::ObjectAgent::throwException(x__async, Common::CallException(Common::ObjectAgent::versionError("onError.RtpRunnerMonitor.MpCallSip")), x__userdata);
                return;
            }
            start();
        }
    private:
        Common::ObjectAgentPtr x__agent;
        Common::AgentAsyncPtr x__async;
        Common::String x_reason;
        Common::CallParamsPtr x__params;
        Common::ObjectPtr x__userdata;
        int x__loop;
    };
    (new __AgentAsync(__agent,__async,reason,__params,__userdata))->start();
}

void RtpRunnerMonitorAgent::onError_end(int __rslt,const Common::IputStreamPtr& __iput)
{
    try
    {
        Common::CallError __error;
        if (Common::ObjectAgent::processException(__rslt,__iput,__error))
            throw Common::CallException(__error);
        Assert((__rslt>>16) == 0);
        switch (__rslt)
        {
        case 0:
            break;
        default:
            throw Common::CallException(Common::ObjectAgent::versionError("onError.RtpRunnerMonitor.MpCallSip"));
        }
        Common::ObjectAgent::processFinal(__iput);
        return;
    }
    catch (const Common::CallException& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        throw;
    }
    catch (const Common::Exception& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        throw;
    }
}

void RtpRunnerMonitorAgent::onNetworkStatusChanged(const Common::StrIntMap& networkStatus,const Common::CallParamsPtr& __params) const
{
    try
    {
        int __loop = 0;
        while (1)
        {
            Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
            Common::VerListPtr __vers = __agent->verList("onNetworkStatusChanged.RtpRunnerMonitor.MpCallSip");
            short __ver = 0;
            if (__vers)
            {
                __ver = __vers->ver(true);
                if (__ver > 0) __ver = 0;
            }
            switch (__ver)
            {
            case 0:
                __oput->write((short)1);
                __oput->write((short)__ver);
                Common::__write_StrIntMap(__oput,networkStatus);
                break;
            default:
                throw Common::CallException(Common::ObjectAgent::versionError("onNetworkStatusChanged.RtpRunnerMonitor.MpCallSip"));
            }
            Common::IputStreamPtr __iput;
            Common::CallError __error;
            int __rslt = __agent->ex_sync("onNetworkStatusChanged.RtpRunnerMonitor.MpCallSip",__oput,__iput,__params,__error);
            if (__rslt == -1)
                throw Common::CallException(__error);
            if (__rslt>>16)
            {
                Assert((__rslt>>16) == VersionException);
                __loop ++;
                if (__loop >= 3)
                    throw Common::CallException(Common::ObjectAgent::versionError("onNetworkStatusChanged.RtpRunnerMonitor.MpCallSip"));
                continue;
            }
            switch (__rslt)
            {
            case 0:
                break;
            default:
                throw Common::CallException(Common::ObjectAgent::versionError("onNetworkStatusChanged.RtpRunnerMonitor.MpCallSip"));
            }
            Common::ObjectAgent::processFinal(__iput);
            return;
        }
    }
    catch (const Common::CallException& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        throw;
    }
    catch (const Common::Exception& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        throw;
    }
}

void RtpRunnerMonitorAgent::onNetworkStatusChanged_begin(const Common::AgentAsyncPtr& __async,const Common::StrIntMap& networkStatus,const Common::CallParamsPtr& __params,const Common::ObjectPtr& __userdata) const throw()
{
    class __AgentAsync : public Common::AgentAsync
    {
    public:
        __AgentAsync(const Common::ObjectAgentPtr& __agent,const Common::AgentAsyncPtr& __async,const Common::StrIntMap& networkStatus,const Common::CallParamsPtr& __params,const Common::ObjectPtr& __userdata)
            : x__agent(__agent),x__async(__async),x_networkStatus(networkStatus),x__params(__params),x__userdata(__userdata),x__loop(0) {}

        void start()
        {
            Common::AgentAsyncPtr pthis = this;
            try
            {
                Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
                Common::VerListPtr __vers = x__agent->verList("onNetworkStatusChanged.RtpRunnerMonitor.MpCallSip");
                short __ver = 0;
                if (__vers)
                {
                    __ver = __vers->ver(true);
                    if (__ver > 0) __ver = 0;
                }
                switch (__ver)
                {
                case 0:
                    __oput->write((short)1);
                    __oput->write((short)__ver);
                    Common::__write_StrIntMap(__oput,x_networkStatus);
                    break;
                default:
                    throw Common::CallException(Common::ObjectAgent::versionError("onNetworkStatusChanged.RtpRunnerMonitor.MpCallSip"));
                }
                x__agent->ex_async(this,"onNetworkStatusChanged.RtpRunnerMonitor.MpCallSip",__oput,x__params,0);
            }
            catch (const Common::CallException& ex)
            {
                Common::ObjectAgent::throwException(x__async,ex,x__userdata);
            }
            catch (const Common::Exception& ex)
            {
                Common::ObjectAgent::throwException(x__async,ex,x__userdata);
            }
        }
        void cmdResult(int __rslt,const Common::IputStreamPtr& __iput,const Common::ObjectPtr& __userdata)
        {
            if ((__rslt>>16)!=VersionException)
            {
                if (x__async) x__async->cmdResult(__rslt,__iput,x__userdata);
                return;
            }
            x__loop ++;
            if (x__loop >= 3)
            {
                if (x__async)
                    Common::ObjectAgent::throwException(x__async, Common::CallException(Common::ObjectAgent::versionError("onNetworkStatusChanged.RtpRunnerMonitor.MpCallSip")), x__userdata);
                return;
            }
            start();
        }
    private:
        Common::ObjectAgentPtr x__agent;
        Common::AgentAsyncPtr x__async;
        Common::StrIntMap x_networkStatus;
        Common::CallParamsPtr x__params;
        Common::ObjectPtr x__userdata;
        int x__loop;
    };
    (new __AgentAsync(__agent,__async,networkStatus,__params,__userdata))->start();
}

void RtpRunnerMonitorAgent::onNetworkStatusChanged_end(int __rslt,const Common::IputStreamPtr& __iput)
{
    try
    {
        Common::CallError __error;
        if (Common::ObjectAgent::processException(__rslt,__iput,__error))
            throw Common::CallException(__error);
        Assert((__rslt>>16) == 0);
        switch (__rslt)
        {
        case 0:
            break;
        default:
            throw Common::CallException(Common::ObjectAgent::versionError("onNetworkStatusChanged.RtpRunnerMonitor.MpCallSip"));
        }
        Common::ObjectAgent::processFinal(__iput);
        return;
    }
    catch (const Common::CallException& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        throw;
    }
    catch (const Common::Exception& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        throw;
    }
}

bool RtpRunnerAgent::callout(const Common::String& targetOid,const Common::String& monitorOid,const MpCallSip::RtpRunnerConfig& config,const MpCallSip::SdpParams& sdpParams,Common::String& offerSdp,const Common::CallParamsPtr& __params) const throw()
{
    try
    {
        int __loop = 0;
        while (1)
        {
            Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
            Common::VerListPtr __vers = __agent->verList("callout.RtpRunner.MpCallSip");
            short __ver = 0;
            if (__vers)
            {
                __ver = __vers->ver(true);
                if (__ver > 0) __ver = 0;
            }
            switch (__ver)
            {
            case 0:
                __oput->write((short)1);
                __oput->write((short)__ver);
                __oput->write(targetOid);
                __oput->write(monitorOid);
                MpCallSip::__write_RtpRunnerConfig(__oput,config);
                MpCallSip::__write_SdpParams(__oput,sdpParams);
                break;
            default:
                throw Common::CallException(Common::ObjectAgent::versionError("callout.RtpRunner.MpCallSip"));
            }
            Common::IputStreamPtr __iput;
            Common::CallError __error;
            int __rslt = __agent->ex_sync("callout.RtpRunner.MpCallSip",__oput,__iput,__params,__error);
            if (__rslt == -1)
                throw Common::CallException(__error);
            if (__rslt>>16)
            {
                Assert((__rslt>>16) == VersionException);
                __loop ++;
                if (__loop >= 3)
                    throw Common::CallException(Common::ObjectAgent::versionError("callout.RtpRunner.MpCallSip"));
                continue;
            }
            bool __ret;
            switch (__rslt)
            {
            case 0:
                __iput->read(__ret);
                __iput->read(offerSdp);
                break;
            default:
                throw Common::CallException(Common::ObjectAgent::versionError("callout.RtpRunner.MpCallSip"));
            }
            Common::ObjectAgent::processFinal(__iput);
            return __ret;
        }
    }
    catch (const Common::CallException& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
    catch (const Common::Exception& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
}

void RtpRunnerAgent::callout_begin(const Common::AgentAsyncPtr& __async,const Common::String& targetOid,const Common::String& monitorOid,const MpCallSip::RtpRunnerConfig& config,const MpCallSip::SdpParams& sdpParams,const Common::CallParamsPtr& __params,const Common::ObjectPtr& __userdata) const throw()
{
    class __AgentAsync : public Common::AgentAsync
    {
    public:
        __AgentAsync(const Common::ObjectAgentPtr& __agent,const Common::AgentAsyncPtr& __async,const Common::String& targetOid,const Common::String& monitorOid,const MpCallSip::RtpRunnerConfig& config,const MpCallSip::SdpParams& sdpParams,const Common::CallParamsPtr& __params,const Common::ObjectPtr& __userdata)
            : x__agent(__agent),x__async(__async),x_targetOid(targetOid),x_monitorOid(monitorOid),x_config(config),x_sdpParams(sdpParams),x__params(__params),x__userdata(__userdata),x__loop(0) {}

        void start()
        {
            Common::AgentAsyncPtr pthis = this;
            try
            {
                Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
                Common::VerListPtr __vers = x__agent->verList("callout.RtpRunner.MpCallSip");
                short __ver = 0;
                if (__vers)
                {
                    __ver = __vers->ver(true);
                    if (__ver > 0) __ver = 0;
                }
                switch (__ver)
                {
                case 0:
                    __oput->write((short)1);
                    __oput->write((short)__ver);
                    __oput->write(x_targetOid);
                    __oput->write(x_monitorOid);
                    MpCallSip::__write_RtpRunnerConfig(__oput,x_config);
                    MpCallSip::__write_SdpParams(__oput,x_sdpParams);
                    break;
                default:
                    throw Common::CallException(Common::ObjectAgent::versionError("callout.RtpRunner.MpCallSip"));
                }
                x__agent->ex_async(this,"callout.RtpRunner.MpCallSip",__oput,x__params,0);
            }
            catch (const Common::CallException& ex)
            {
                Common::ObjectAgent::throwException(x__async,ex,x__userdata);
            }
            catch (const Common::Exception& ex)
            {
                Common::ObjectAgent::throwException(x__async,ex,x__userdata);
            }
        }
        void cmdResult(int __rslt,const Common::IputStreamPtr& __iput,const Common::ObjectPtr& __userdata)
        {
            if ((__rslt>>16)!=VersionException)
            {
                if (x__async) x__async->cmdResult(__rslt,__iput,x__userdata);
                return;
            }
            x__loop ++;
            if (x__loop >= 3)
            {
                if (x__async)
                    Common::ObjectAgent::throwException(x__async, Common::CallException(Common::ObjectAgent::versionError("callout.RtpRunner.MpCallSip")), x__userdata);
                return;
            }
            start();
        }
    private:
        Common::ObjectAgentPtr x__agent;
        Common::AgentAsyncPtr x__async;
        Common::String x_targetOid;
        Common::String x_monitorOid;
        MpCallSip::RtpRunnerConfig x_config;
        MpCallSip::SdpParams x_sdpParams;
        Common::CallParamsPtr x__params;
        Common::ObjectPtr x__userdata;
        int x__loop;
    };
    (new __AgentAsync(__agent,__async,targetOid,monitorOid,config,sdpParams,__params,__userdata))->start();
}

bool RtpRunnerAgent::callout_end(int __rslt,const Common::IputStreamPtr& __iput,Common::String& offerSdp) throw()
{
    try
    {
        Common::CallError __error;
        if (Common::ObjectAgent::processException(__rslt,__iput,__error))
            throw Common::CallException(__error);
        Assert((__rslt>>16) == 0);
        bool __ret;
        switch (__rslt)
        {
        case 0:
            __iput->read(__ret);
            __iput->read(offerSdp);
            break;
        default:
            throw Common::CallException(Common::ObjectAgent::versionError("callout.RtpRunner.MpCallSip"));
        }
        Common::ObjectAgent::processFinal(__iput);
        return __ret;
    }
    catch (const Common::CallException& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
    catch (const Common::Exception& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
}

bool RtpRunnerAgent::callin(const Common::String& targetOid,const Common::String& monitorOid,const MpCallSip::RtpRunnerConfig& config,const Common::String& peerSdp,const Common::CallParamsPtr& __params) const throw()
{
    try
    {
        int __loop = 0;
        while (1)
        {
            Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
            Common::VerListPtr __vers = __agent->verList("callin.RtpRunner.MpCallSip");
            short __ver = 0;
            if (__vers)
            {
                __ver = __vers->ver(true);
                if (__ver > 0) __ver = 0;
            }
            switch (__ver)
            {
            case 0:
                __oput->write((short)1);
                __oput->write((short)__ver);
                __oput->write(targetOid);
                __oput->write(monitorOid);
                MpCallSip::__write_RtpRunnerConfig(__oput,config);
                __oput->write(peerSdp);
                break;
            default:
                throw Common::CallException(Common::ObjectAgent::versionError("callin.RtpRunner.MpCallSip"));
            }
            Common::IputStreamPtr __iput;
            Common::CallError __error;
            int __rslt = __agent->ex_sync("callin.RtpRunner.MpCallSip",__oput,__iput,__params,__error);
            if (__rslt == -1)
                throw Common::CallException(__error);
            if (__rslt>>16)
            {
                Assert((__rslt>>16) == VersionException);
                __loop ++;
                if (__loop >= 3)
                    throw Common::CallException(Common::ObjectAgent::versionError("callin.RtpRunner.MpCallSip"));
                continue;
            }
            bool __ret;
            switch (__rslt)
            {
            case 0:
                __iput->read(__ret);
                break;
            default:
                throw Common::CallException(Common::ObjectAgent::versionError("callin.RtpRunner.MpCallSip"));
            }
            Common::ObjectAgent::processFinal(__iput);
            return __ret;
        }
    }
    catch (const Common::CallException& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
    catch (const Common::Exception& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
}

void RtpRunnerAgent::callin_begin(const Common::AgentAsyncPtr& __async,const Common::String& targetOid,const Common::String& monitorOid,const MpCallSip::RtpRunnerConfig& config,const Common::String& peerSdp,const Common::CallParamsPtr& __params,const Common::ObjectPtr& __userdata) const throw()
{
    class __AgentAsync : public Common::AgentAsync
    {
    public:
        __AgentAsync(const Common::ObjectAgentPtr& __agent,const Common::AgentAsyncPtr& __async,const Common::String& targetOid,const Common::String& monitorOid,const MpCallSip::RtpRunnerConfig& config,const Common::String& peerSdp,const Common::CallParamsPtr& __params,const Common::ObjectPtr& __userdata)
            : x__agent(__agent),x__async(__async),x_targetOid(targetOid),x_monitorOid(monitorOid),x_config(config),x_peerSdp(peerSdp),x__params(__params),x__userdata(__userdata),x__loop(0) {}

        void start()
        {
            Common::AgentAsyncPtr pthis = this;
            try
            {
                Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
                Common::VerListPtr __vers = x__agent->verList("callin.RtpRunner.MpCallSip");
                short __ver = 0;
                if (__vers)
                {
                    __ver = __vers->ver(true);
                    if (__ver > 0) __ver = 0;
                }
                switch (__ver)
                {
                case 0:
                    __oput->write((short)1);
                    __oput->write((short)__ver);
                    __oput->write(x_targetOid);
                    __oput->write(x_monitorOid);
                    MpCallSip::__write_RtpRunnerConfig(__oput,x_config);
                    __oput->write(x_peerSdp);
                    break;
                default:
                    throw Common::CallException(Common::ObjectAgent::versionError("callin.RtpRunner.MpCallSip"));
                }
                x__agent->ex_async(this,"callin.RtpRunner.MpCallSip",__oput,x__params,0);
            }
            catch (const Common::CallException& ex)
            {
                Common::ObjectAgent::throwException(x__async,ex,x__userdata);
            }
            catch (const Common::Exception& ex)
            {
                Common::ObjectAgent::throwException(x__async,ex,x__userdata);
            }
        }
        void cmdResult(int __rslt,const Common::IputStreamPtr& __iput,const Common::ObjectPtr& __userdata)
        {
            if ((__rslt>>16)!=VersionException)
            {
                if (x__async) x__async->cmdResult(__rslt,__iput,x__userdata);
                return;
            }
            x__loop ++;
            if (x__loop >= 3)
            {
                if (x__async)
                    Common::ObjectAgent::throwException(x__async, Common::CallException(Common::ObjectAgent::versionError("callin.RtpRunner.MpCallSip")), x__userdata);
                return;
            }
            start();
        }
    private:
        Common::ObjectAgentPtr x__agent;
        Common::AgentAsyncPtr x__async;
        Common::String x_targetOid;
        Common::String x_monitorOid;
        MpCallSip::RtpRunnerConfig x_config;
        Common::String x_peerSdp;
        Common::CallParamsPtr x__params;
        Common::ObjectPtr x__userdata;
        int x__loop;
    };
    (new __AgentAsync(__agent,__async,targetOid,monitorOid,config,peerSdp,__params,__userdata))->start();
}

bool RtpRunnerAgent::callin_end(int __rslt,const Common::IputStreamPtr& __iput) throw()
{
    try
    {
        Common::CallError __error;
        if (Common::ObjectAgent::processException(__rslt,__iput,__error))
            throw Common::CallException(__error);
        Assert((__rslt>>16) == 0);
        bool __ret;
        switch (__rslt)
        {
        case 0:
            __iput->read(__ret);
            break;
        default:
            throw Common::CallException(Common::ObjectAgent::versionError("callin.RtpRunner.MpCallSip"));
        }
        Common::ObjectAgent::processFinal(__iput);
        return __ret;
    }
    catch (const Common::CallException& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
    catch (const Common::Exception& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
}

bool RtpRunnerAgent::callinAndOffer(const Common::String& targetOid,const Common::String& monitorOid,const MpCallSip::RtpRunnerConfig& config,const MpCallSip::SdpParams& sdpParams,Common::String& localSdp,const Common::CallParamsPtr& __params) const throw()
{
    try
    {
        int __loop = 0;
        while (1)
        {
            Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
            Common::VerListPtr __vers = __agent->verList("callinAndOffer.RtpRunner.MpCallSip");
            short __ver = 0;
            if (__vers)
            {
                __ver = __vers->ver(true);
                if (__ver > 0) __ver = 0;
            }
            switch (__ver)
            {
            case 0:
                __oput->write((short)1);
                __oput->write((short)__ver);
                __oput->write(targetOid);
                __oput->write(monitorOid);
                MpCallSip::__write_RtpRunnerConfig(__oput,config);
                MpCallSip::__write_SdpParams(__oput,sdpParams);
                break;
            default:
                throw Common::CallException(Common::ObjectAgent::versionError("callinAndOffer.RtpRunner.MpCallSip"));
            }
            Common::IputStreamPtr __iput;
            Common::CallError __error;
            int __rslt = __agent->ex_sync("callinAndOffer.RtpRunner.MpCallSip",__oput,__iput,__params,__error);
            if (__rslt == -1)
                throw Common::CallException(__error);
            if (__rslt>>16)
            {
                Assert((__rslt>>16) == VersionException);
                __loop ++;
                if (__loop >= 3)
                    throw Common::CallException(Common::ObjectAgent::versionError("callinAndOffer.RtpRunner.MpCallSip"));
                continue;
            }
            bool __ret;
            switch (__rslt)
            {
            case 0:
                __iput->read(__ret);
                __iput->read(localSdp);
                break;
            default:
                throw Common::CallException(Common::ObjectAgent::versionError("callinAndOffer.RtpRunner.MpCallSip"));
            }
            Common::ObjectAgent::processFinal(__iput);
            return __ret;
        }
    }
    catch (const Common::CallException& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
    catch (const Common::Exception& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
}

void RtpRunnerAgent::callinAndOffer_begin(const Common::AgentAsyncPtr& __async,const Common::String& targetOid,const Common::String& monitorOid,const MpCallSip::RtpRunnerConfig& config,const MpCallSip::SdpParams& sdpParams,const Common::CallParamsPtr& __params,const Common::ObjectPtr& __userdata) const throw()
{
    class __AgentAsync : public Common::AgentAsync
    {
    public:
        __AgentAsync(const Common::ObjectAgentPtr& __agent,const Common::AgentAsyncPtr& __async,const Common::String& targetOid,const Common::String& monitorOid,const MpCallSip::RtpRunnerConfig& config,const MpCallSip::SdpParams& sdpParams,const Common::CallParamsPtr& __params,const Common::ObjectPtr& __userdata)
            : x__agent(__agent),x__async(__async),x_targetOid(targetOid),x_monitorOid(monitorOid),x_config(config),x_sdpParams(sdpParams),x__params(__params),x__userdata(__userdata),x__loop(0) {}

        void start()
        {
            Common::AgentAsyncPtr pthis = this;
            try
            {
                Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
                Common::VerListPtr __vers = x__agent->verList("callinAndOffer.RtpRunner.MpCallSip");
                short __ver = 0;
                if (__vers)
                {
                    __ver = __vers->ver(true);
                    if (__ver > 0) __ver = 0;
                }
                switch (__ver)
                {
                case 0:
                    __oput->write((short)1);
                    __oput->write((short)__ver);
                    __oput->write(x_targetOid);
                    __oput->write(x_monitorOid);
                    MpCallSip::__write_RtpRunnerConfig(__oput,x_config);
                    MpCallSip::__write_SdpParams(__oput,x_sdpParams);
                    break;
                default:
                    throw Common::CallException(Common::ObjectAgent::versionError("callinAndOffer.RtpRunner.MpCallSip"));
                }
                x__agent->ex_async(this,"callinAndOffer.RtpRunner.MpCallSip",__oput,x__params,0);
            }
            catch (const Common::CallException& ex)
            {
                Common::ObjectAgent::throwException(x__async,ex,x__userdata);
            }
            catch (const Common::Exception& ex)
            {
                Common::ObjectAgent::throwException(x__async,ex,x__userdata);
            }
        }
        void cmdResult(int __rslt,const Common::IputStreamPtr& __iput,const Common::ObjectPtr& __userdata)
        {
            if ((__rslt>>16)!=VersionException)
            {
                if (x__async) x__async->cmdResult(__rslt,__iput,x__userdata);
                return;
            }
            x__loop ++;
            if (x__loop >= 3)
            {
                if (x__async)
                    Common::ObjectAgent::throwException(x__async, Common::CallException(Common::ObjectAgent::versionError("callinAndOffer.RtpRunner.MpCallSip")), x__userdata);
                return;
            }
            start();
        }
    private:
        Common::ObjectAgentPtr x__agent;
        Common::AgentAsyncPtr x__async;
        Common::String x_targetOid;
        Common::String x_monitorOid;
        MpCallSip::RtpRunnerConfig x_config;
        MpCallSip::SdpParams x_sdpParams;
        Common::CallParamsPtr x__params;
        Common::ObjectPtr x__userdata;
        int x__loop;
    };
    (new __AgentAsync(__agent,__async,targetOid,monitorOid,config,sdpParams,__params,__userdata))->start();
}

bool RtpRunnerAgent::callinAndOffer_end(int __rslt,const Common::IputStreamPtr& __iput,Common::String& localSdp) throw()
{
    try
    {
        Common::CallError __error;
        if (Common::ObjectAgent::processException(__rslt,__iput,__error))
            throw Common::CallException(__error);
        Assert((__rslt>>16) == 0);
        bool __ret;
        switch (__rslt)
        {
        case 0:
            __iput->read(__ret);
            __iput->read(localSdp);
            break;
        default:
            throw Common::CallException(Common::ObjectAgent::versionError("callinAndOffer.RtpRunner.MpCallSip"));
        }
        Common::ObjectAgent::processFinal(__iput);
        return __ret;
    }
    catch (const Common::CallException& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
    catch (const Common::Exception& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
}

bool RtpRunnerAgent::callinAndAnswer(const Common::String& targetOid,const Common::String& monitorOid,const MpCallSip::RtpRunnerConfig& config,const Common::String& peerSdp,const MpCallSip::SdpParams& sdpParams,Common::String& localSdp,const Common::CallParamsPtr& __params) const throw()
{
    try
    {
        int __loop = 0;
        while (1)
        {
            Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
            Common::VerListPtr __vers = __agent->verList("callinAndAnswer.RtpRunner.MpCallSip");
            short __ver = 0;
            if (__vers)
            {
                __ver = __vers->ver(true);
                if (__ver > 0) __ver = 0;
            }
            switch (__ver)
            {
            case 0:
                __oput->write((short)1);
                __oput->write((short)__ver);
                __oput->write(targetOid);
                __oput->write(monitorOid);
                MpCallSip::__write_RtpRunnerConfig(__oput,config);
                __oput->write(peerSdp);
                MpCallSip::__write_SdpParams(__oput,sdpParams);
                break;
            default:
                throw Common::CallException(Common::ObjectAgent::versionError("callinAndAnswer.RtpRunner.MpCallSip"));
            }
            Common::IputStreamPtr __iput;
            Common::CallError __error;
            int __rslt = __agent->ex_sync("callinAndAnswer.RtpRunner.MpCallSip",__oput,__iput,__params,__error);
            if (__rslt == -1)
                throw Common::CallException(__error);
            if (__rslt>>16)
            {
                Assert((__rslt>>16) == VersionException);
                __loop ++;
                if (__loop >= 3)
                    throw Common::CallException(Common::ObjectAgent::versionError("callinAndAnswer.RtpRunner.MpCallSip"));
                continue;
            }
            bool __ret;
            switch (__rslt)
            {
            case 0:
                __iput->read(__ret);
                __iput->read(localSdp);
                break;
            default:
                throw Common::CallException(Common::ObjectAgent::versionError("callinAndAnswer.RtpRunner.MpCallSip"));
            }
            Common::ObjectAgent::processFinal(__iput);
            return __ret;
        }
    }
    catch (const Common::CallException& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
    catch (const Common::Exception& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
}

void RtpRunnerAgent::callinAndAnswer_begin(const Common::AgentAsyncPtr& __async,const Common::String& targetOid,const Common::String& monitorOid,const MpCallSip::RtpRunnerConfig& config,const Common::String& peerSdp,const MpCallSip::SdpParams& sdpParams,const Common::CallParamsPtr& __params,const Common::ObjectPtr& __userdata) const throw()
{
    class __AgentAsync : public Common::AgentAsync
    {
    public:
        __AgentAsync(const Common::ObjectAgentPtr& __agent,const Common::AgentAsyncPtr& __async,const Common::String& targetOid,const Common::String& monitorOid,const MpCallSip::RtpRunnerConfig& config,const Common::String& peerSdp,const MpCallSip::SdpParams& sdpParams,const Common::CallParamsPtr& __params,const Common::ObjectPtr& __userdata)
            : x__agent(__agent),x__async(__async),x_targetOid(targetOid),x_monitorOid(monitorOid),x_config(config),x_peerSdp(peerSdp),x_sdpParams(sdpParams),x__params(__params),x__userdata(__userdata),x__loop(0) {}

        void start()
        {
            Common::AgentAsyncPtr pthis = this;
            try
            {
                Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
                Common::VerListPtr __vers = x__agent->verList("callinAndAnswer.RtpRunner.MpCallSip");
                short __ver = 0;
                if (__vers)
                {
                    __ver = __vers->ver(true);
                    if (__ver > 0) __ver = 0;
                }
                switch (__ver)
                {
                case 0:
                    __oput->write((short)1);
                    __oput->write((short)__ver);
                    __oput->write(x_targetOid);
                    __oput->write(x_monitorOid);
                    MpCallSip::__write_RtpRunnerConfig(__oput,x_config);
                    __oput->write(x_peerSdp);
                    MpCallSip::__write_SdpParams(__oput,x_sdpParams);
                    break;
                default:
                    throw Common::CallException(Common::ObjectAgent::versionError("callinAndAnswer.RtpRunner.MpCallSip"));
                }
                x__agent->ex_async(this,"callinAndAnswer.RtpRunner.MpCallSip",__oput,x__params,0);
            }
            catch (const Common::CallException& ex)
            {
                Common::ObjectAgent::throwException(x__async,ex,x__userdata);
            }
            catch (const Common::Exception& ex)
            {
                Common::ObjectAgent::throwException(x__async,ex,x__userdata);
            }
        }
        void cmdResult(int __rslt,const Common::IputStreamPtr& __iput,const Common::ObjectPtr& __userdata)
        {
            if ((__rslt>>16)!=VersionException)
            {
                if (x__async) x__async->cmdResult(__rslt,__iput,x__userdata);
                return;
            }
            x__loop ++;
            if (x__loop >= 3)
            {
                if (x__async)
                    Common::ObjectAgent::throwException(x__async, Common::CallException(Common::ObjectAgent::versionError("callinAndAnswer.RtpRunner.MpCallSip")), x__userdata);
                return;
            }
            start();
        }
    private:
        Common::ObjectAgentPtr x__agent;
        Common::AgentAsyncPtr x__async;
        Common::String x_targetOid;
        Common::String x_monitorOid;
        MpCallSip::RtpRunnerConfig x_config;
        Common::String x_peerSdp;
        MpCallSip::SdpParams x_sdpParams;
        Common::CallParamsPtr x__params;
        Common::ObjectPtr x__userdata;
        int x__loop;
    };
    (new __AgentAsync(__agent,__async,targetOid,monitorOid,config,peerSdp,sdpParams,__params,__userdata))->start();
}

bool RtpRunnerAgent::callinAndAnswer_end(int __rslt,const Common::IputStreamPtr& __iput,Common::String& localSdp) throw()
{
    try
    {
        Common::CallError __error;
        if (Common::ObjectAgent::processException(__rslt,__iput,__error))
            throw Common::CallException(__error);
        Assert((__rslt>>16) == 0);
        bool __ret;
        switch (__rslt)
        {
        case 0:
            __iput->read(__ret);
            __iput->read(localSdp);
            break;
        default:
            throw Common::CallException(Common::ObjectAgent::versionError("callinAndAnswer.RtpRunner.MpCallSip"));
        }
        Common::ObjectAgent::processFinal(__iput);
        return __ret;
    }
    catch (const Common::CallException& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
    catch (const Common::Exception& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
}

bool RtpRunnerAgent::close(const Common::CallParamsPtr& __params) const throw()
{
    try
    {
        int __loop = 0;
        while (1)
        {
            Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
            Common::VerListPtr __vers = __agent->verList("close.RtpRunner.MpCallSip");
            short __ver = 0;
            if (__vers)
            {
                __ver = __vers->ver(true);
                if (__ver > 0) __ver = 0;
            }
            switch (__ver)
            {
            case 0:
                __oput->write((short)1);
                __oput->write((short)__ver);
                break;
            default:
                throw Common::CallException(Common::ObjectAgent::versionError("close.RtpRunner.MpCallSip"));
            }
            Common::IputStreamPtr __iput;
            Common::CallError __error;
            int __rslt = __agent->ex_sync("close.RtpRunner.MpCallSip",__oput,__iput,__params,__error);
            if (__rslt == -1)
                throw Common::CallException(__error);
            if (__rslt>>16)
            {
                Assert((__rslt>>16) == VersionException);
                __loop ++;
                if (__loop >= 3)
                    throw Common::CallException(Common::ObjectAgent::versionError("close.RtpRunner.MpCallSip"));
                continue;
            }
            bool __ret;
            switch (__rslt)
            {
            case 0:
                __iput->read(__ret);
                break;
            default:
                throw Common::CallException(Common::ObjectAgent::versionError("close.RtpRunner.MpCallSip"));
            }
            Common::ObjectAgent::processFinal(__iput);
            return __ret;
        }
    }
    catch (const Common::CallException& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
    catch (const Common::Exception& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
}

void RtpRunnerAgent::close_begin(const Common::AgentAsyncPtr& __async,const Common::CallParamsPtr& __params,const Common::ObjectPtr& __userdata) const throw()
{
    class __AgentAsync : public Common::AgentAsync
    {
    public:
        __AgentAsync(const Common::ObjectAgentPtr& __agent,const Common::AgentAsyncPtr& __async,const Common::CallParamsPtr& __params,const Common::ObjectPtr& __userdata)
            : x__agent(__agent),x__async(__async),x__params(__params),x__userdata(__userdata),x__loop(0) {}

        void start()
        {
            Common::AgentAsyncPtr pthis = this;
            try
            {
                Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
                Common::VerListPtr __vers = x__agent->verList("close.RtpRunner.MpCallSip");
                short __ver = 0;
                if (__vers)
                {
                    __ver = __vers->ver(true);
                    if (__ver > 0) __ver = 0;
                }
                switch (__ver)
                {
                case 0:
                    __oput->write((short)1);
                    __oput->write((short)__ver);
                    break;
                default:
                    throw Common::CallException(Common::ObjectAgent::versionError("close.RtpRunner.MpCallSip"));
                }
                x__agent->ex_async(this,"close.RtpRunner.MpCallSip",__oput,x__params,0);
            }
            catch (const Common::CallException& ex)
            {
                Common::ObjectAgent::throwException(x__async,ex,x__userdata);
            }
            catch (const Common::Exception& ex)
            {
                Common::ObjectAgent::throwException(x__async,ex,x__userdata);
            }
        }
        void cmdResult(int __rslt,const Common::IputStreamPtr& __iput,const Common::ObjectPtr& __userdata)
        {
            if ((__rslt>>16)!=VersionException)
            {
                if (x__async) x__async->cmdResult(__rslt,__iput,x__userdata);
                return;
            }
            x__loop ++;
            if (x__loop >= 3)
            {
                if (x__async)
                    Common::ObjectAgent::throwException(x__async, Common::CallException(Common::ObjectAgent::versionError("close.RtpRunner.MpCallSip")), x__userdata);
                return;
            }
            start();
        }
    private:
        Common::ObjectAgentPtr x__agent;
        Common::AgentAsyncPtr x__async;
        Common::CallParamsPtr x__params;
        Common::ObjectPtr x__userdata;
        int x__loop;
    };
    (new __AgentAsync(__agent,__async,__params,__userdata))->start();
}

bool RtpRunnerAgent::close_end(int __rslt,const Common::IputStreamPtr& __iput) throw()
{
    try
    {
        Common::CallError __error;
        if (Common::ObjectAgent::processException(__rslt,__iput,__error))
            throw Common::CallException(__error);
        Assert((__rslt>>16) == 0);
        bool __ret;
        switch (__rslt)
        {
        case 0:
            __iput->read(__ret);
            break;
        default:
            throw Common::CallException(Common::ObjectAgent::versionError("close.RtpRunner.MpCallSip"));
        }
        Common::ObjectAgent::processFinal(__iput);
        return __ret;
    }
    catch (const Common::CallException& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
    catch (const Common::Exception& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
}

bool RtpRunnerAgent::genOffer(const MpCallSip::SdpParams& sdpParams,Common::String& offerSdp,const Common::CallParamsPtr& __params) const throw()
{
    try
    {
        int __loop = 0;
        while (1)
        {
            Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
            Common::VerListPtr __vers = __agent->verList("genOffer.RtpRunner.MpCallSip");
            short __ver = 0;
            if (__vers)
            {
                __ver = __vers->ver(true);
                if (__ver > 0) __ver = 0;
            }
            switch (__ver)
            {
            case 0:
                __oput->write((short)1);
                __oput->write((short)__ver);
                MpCallSip::__write_SdpParams(__oput,sdpParams);
                break;
            default:
                throw Common::CallException(Common::ObjectAgent::versionError("genOffer.RtpRunner.MpCallSip"));
            }
            Common::IputStreamPtr __iput;
            Common::CallError __error;
            int __rslt = __agent->ex_sync("genOffer.RtpRunner.MpCallSip",__oput,__iput,__params,__error);
            if (__rslt == -1)
                throw Common::CallException(__error);
            if (__rslt>>16)
            {
                Assert((__rslt>>16) == VersionException);
                __loop ++;
                if (__loop >= 3)
                    throw Common::CallException(Common::ObjectAgent::versionError("genOffer.RtpRunner.MpCallSip"));
                continue;
            }
            bool __ret;
            switch (__rslt)
            {
            case 0:
                __iput->read(__ret);
                __iput->read(offerSdp);
                break;
            default:
                throw Common::CallException(Common::ObjectAgent::versionError("genOffer.RtpRunner.MpCallSip"));
            }
            Common::ObjectAgent::processFinal(__iput);
            return __ret;
        }
    }
    catch (const Common::CallException& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
    catch (const Common::Exception& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
}

void RtpRunnerAgent::genOffer_begin(const Common::AgentAsyncPtr& __async,const MpCallSip::SdpParams& sdpParams,const Common::CallParamsPtr& __params,const Common::ObjectPtr& __userdata) const throw()
{
    class __AgentAsync : public Common::AgentAsync
    {
    public:
        __AgentAsync(const Common::ObjectAgentPtr& __agent,const Common::AgentAsyncPtr& __async,const MpCallSip::SdpParams& sdpParams,const Common::CallParamsPtr& __params,const Common::ObjectPtr& __userdata)
            : x__agent(__agent),x__async(__async),x_sdpParams(sdpParams),x__params(__params),x__userdata(__userdata),x__loop(0) {}

        void start()
        {
            Common::AgentAsyncPtr pthis = this;
            try
            {
                Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
                Common::VerListPtr __vers = x__agent->verList("genOffer.RtpRunner.MpCallSip");
                short __ver = 0;
                if (__vers)
                {
                    __ver = __vers->ver(true);
                    if (__ver > 0) __ver = 0;
                }
                switch (__ver)
                {
                case 0:
                    __oput->write((short)1);
                    __oput->write((short)__ver);
                    MpCallSip::__write_SdpParams(__oput,x_sdpParams);
                    break;
                default:
                    throw Common::CallException(Common::ObjectAgent::versionError("genOffer.RtpRunner.MpCallSip"));
                }
                x__agent->ex_async(this,"genOffer.RtpRunner.MpCallSip",__oput,x__params,0);
            }
            catch (const Common::CallException& ex)
            {
                Common::ObjectAgent::throwException(x__async,ex,x__userdata);
            }
            catch (const Common::Exception& ex)
            {
                Common::ObjectAgent::throwException(x__async,ex,x__userdata);
            }
        }
        void cmdResult(int __rslt,const Common::IputStreamPtr& __iput,const Common::ObjectPtr& __userdata)
        {
            if ((__rslt>>16)!=VersionException)
            {
                if (x__async) x__async->cmdResult(__rslt,__iput,x__userdata);
                return;
            }
            x__loop ++;
            if (x__loop >= 3)
            {
                if (x__async)
                    Common::ObjectAgent::throwException(x__async, Common::CallException(Common::ObjectAgent::versionError("genOffer.RtpRunner.MpCallSip")), x__userdata);
                return;
            }
            start();
        }
    private:
        Common::ObjectAgentPtr x__agent;
        Common::AgentAsyncPtr x__async;
        MpCallSip::SdpParams x_sdpParams;
        Common::CallParamsPtr x__params;
        Common::ObjectPtr x__userdata;
        int x__loop;
    };
    (new __AgentAsync(__agent,__async,sdpParams,__params,__userdata))->start();
}

bool RtpRunnerAgent::genOffer_end(int __rslt,const Common::IputStreamPtr& __iput,Common::String& offerSdp) throw()
{
    try
    {
        Common::CallError __error;
        if (Common::ObjectAgent::processException(__rslt,__iput,__error))
            throw Common::CallException(__error);
        Assert((__rslt>>16) == 0);
        bool __ret;
        switch (__rslt)
        {
        case 0:
            __iput->read(__ret);
            __iput->read(offerSdp);
            break;
        default:
            throw Common::CallException(Common::ObjectAgent::versionError("genOffer.RtpRunner.MpCallSip"));
        }
        Common::ObjectAgent::processFinal(__iput);
        return __ret;
    }
    catch (const Common::CallException& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
    catch (const Common::Exception& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
}

bool RtpRunnerAgent::setOffer(const Common::String& offerSdp,const Common::CallParamsPtr& __params) const throw()
{
    try
    {
        int __loop = 0;
        while (1)
        {
            Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
            Common::VerListPtr __vers = __agent->verList("setOffer.RtpRunner.MpCallSip");
            short __ver = 0;
            if (__vers)
            {
                __ver = __vers->ver(true);
                if (__ver > 0) __ver = 0;
            }
            switch (__ver)
            {
            case 0:
                __oput->write((short)1);
                __oput->write((short)__ver);
                __oput->write(offerSdp);
                break;
            default:
                throw Common::CallException(Common::ObjectAgent::versionError("setOffer.RtpRunner.MpCallSip"));
            }
            Common::IputStreamPtr __iput;
            Common::CallError __error;
            int __rslt = __agent->ex_sync("setOffer.RtpRunner.MpCallSip",__oput,__iput,__params,__error);
            if (__rslt == -1)
                throw Common::CallException(__error);
            if (__rslt>>16)
            {
                Assert((__rslt>>16) == VersionException);
                __loop ++;
                if (__loop >= 3)
                    throw Common::CallException(Common::ObjectAgent::versionError("setOffer.RtpRunner.MpCallSip"));
                continue;
            }
            bool __ret;
            switch (__rslt)
            {
            case 0:
                __iput->read(__ret);
                break;
            default:
                throw Common::CallException(Common::ObjectAgent::versionError("setOffer.RtpRunner.MpCallSip"));
            }
            Common::ObjectAgent::processFinal(__iput);
            return __ret;
        }
    }
    catch (const Common::CallException& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
    catch (const Common::Exception& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
}

void RtpRunnerAgent::setOffer_begin(const Common::AgentAsyncPtr& __async,const Common::String& offerSdp,const Common::CallParamsPtr& __params,const Common::ObjectPtr& __userdata) const throw()
{
    class __AgentAsync : public Common::AgentAsync
    {
    public:
        __AgentAsync(const Common::ObjectAgentPtr& __agent,const Common::AgentAsyncPtr& __async,const Common::String& offerSdp,const Common::CallParamsPtr& __params,const Common::ObjectPtr& __userdata)
            : x__agent(__agent),x__async(__async),x_offerSdp(offerSdp),x__params(__params),x__userdata(__userdata),x__loop(0) {}

        void start()
        {
            Common::AgentAsyncPtr pthis = this;
            try
            {
                Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
                Common::VerListPtr __vers = x__agent->verList("setOffer.RtpRunner.MpCallSip");
                short __ver = 0;
                if (__vers)
                {
                    __ver = __vers->ver(true);
                    if (__ver > 0) __ver = 0;
                }
                switch (__ver)
                {
                case 0:
                    __oput->write((short)1);
                    __oput->write((short)__ver);
                    __oput->write(x_offerSdp);
                    break;
                default:
                    throw Common::CallException(Common::ObjectAgent::versionError("setOffer.RtpRunner.MpCallSip"));
                }
                x__agent->ex_async(this,"setOffer.RtpRunner.MpCallSip",__oput,x__params,0);
            }
            catch (const Common::CallException& ex)
            {
                Common::ObjectAgent::throwException(x__async,ex,x__userdata);
            }
            catch (const Common::Exception& ex)
            {
                Common::ObjectAgent::throwException(x__async,ex,x__userdata);
            }
        }
        void cmdResult(int __rslt,const Common::IputStreamPtr& __iput,const Common::ObjectPtr& __userdata)
        {
            if ((__rslt>>16)!=VersionException)
            {
                if (x__async) x__async->cmdResult(__rslt,__iput,x__userdata);
                return;
            }
            x__loop ++;
            if (x__loop >= 3)
            {
                if (x__async)
                    Common::ObjectAgent::throwException(x__async, Common::CallException(Common::ObjectAgent::versionError("setOffer.RtpRunner.MpCallSip")), x__userdata);
                return;
            }
            start();
        }
    private:
        Common::ObjectAgentPtr x__agent;
        Common::AgentAsyncPtr x__async;
        Common::String x_offerSdp;
        Common::CallParamsPtr x__params;
        Common::ObjectPtr x__userdata;
        int x__loop;
    };
    (new __AgentAsync(__agent,__async,offerSdp,__params,__userdata))->start();
}

bool RtpRunnerAgent::setOffer_end(int __rslt,const Common::IputStreamPtr& __iput) throw()
{
    try
    {
        Common::CallError __error;
        if (Common::ObjectAgent::processException(__rslt,__iput,__error))
            throw Common::CallException(__error);
        Assert((__rslt>>16) == 0);
        bool __ret;
        switch (__rslt)
        {
        case 0:
            __iput->read(__ret);
            break;
        default:
            throw Common::CallException(Common::ObjectAgent::versionError("setOffer.RtpRunner.MpCallSip"));
        }
        Common::ObjectAgent::processFinal(__iput);
        return __ret;
    }
    catch (const Common::CallException& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
    catch (const Common::Exception& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
}

bool RtpRunnerAgent::setOfferAndAnswer(const Common::String& offerSdp,const MpCallSip::SdpParams& sdpParams,Common::String& answerSdp,const Common::CallParamsPtr& __params) const throw()
{
    try
    {
        int __loop = 0;
        while (1)
        {
            Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
            Common::VerListPtr __vers = __agent->verList("setOfferAndAnswer.RtpRunner.MpCallSip");
            short __ver = 0;
            if (__vers)
            {
                __ver = __vers->ver(true);
                if (__ver > 0) __ver = 0;
            }
            switch (__ver)
            {
            case 0:
                __oput->write((short)1);
                __oput->write((short)__ver);
                __oput->write(offerSdp);
                MpCallSip::__write_SdpParams(__oput,sdpParams);
                break;
            default:
                throw Common::CallException(Common::ObjectAgent::versionError("setOfferAndAnswer.RtpRunner.MpCallSip"));
            }
            Common::IputStreamPtr __iput;
            Common::CallError __error;
            int __rslt = __agent->ex_sync("setOfferAndAnswer.RtpRunner.MpCallSip",__oput,__iput,__params,__error);
            if (__rslt == -1)
                throw Common::CallException(__error);
            if (__rslt>>16)
            {
                Assert((__rslt>>16) == VersionException);
                __loop ++;
                if (__loop >= 3)
                    throw Common::CallException(Common::ObjectAgent::versionError("setOfferAndAnswer.RtpRunner.MpCallSip"));
                continue;
            }
            bool __ret;
            switch (__rslt)
            {
            case 0:
                __iput->read(__ret);
                __iput->read(answerSdp);
                break;
            default:
                throw Common::CallException(Common::ObjectAgent::versionError("setOfferAndAnswer.RtpRunner.MpCallSip"));
            }
            Common::ObjectAgent::processFinal(__iput);
            return __ret;
        }
    }
    catch (const Common::CallException& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
    catch (const Common::Exception& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
}

void RtpRunnerAgent::setOfferAndAnswer_begin(const Common::AgentAsyncPtr& __async,const Common::String& offerSdp,const MpCallSip::SdpParams& sdpParams,const Common::CallParamsPtr& __params,const Common::ObjectPtr& __userdata) const throw()
{
    class __AgentAsync : public Common::AgentAsync
    {
    public:
        __AgentAsync(const Common::ObjectAgentPtr& __agent,const Common::AgentAsyncPtr& __async,const Common::String& offerSdp,const MpCallSip::SdpParams& sdpParams,const Common::CallParamsPtr& __params,const Common::ObjectPtr& __userdata)
            : x__agent(__agent),x__async(__async),x_offerSdp(offerSdp),x_sdpParams(sdpParams),x__params(__params),x__userdata(__userdata),x__loop(0) {}

        void start()
        {
            Common::AgentAsyncPtr pthis = this;
            try
            {
                Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
                Common::VerListPtr __vers = x__agent->verList("setOfferAndAnswer.RtpRunner.MpCallSip");
                short __ver = 0;
                if (__vers)
                {
                    __ver = __vers->ver(true);
                    if (__ver > 0) __ver = 0;
                }
                switch (__ver)
                {
                case 0:
                    __oput->write((short)1);
                    __oput->write((short)__ver);
                    __oput->write(x_offerSdp);
                    MpCallSip::__write_SdpParams(__oput,x_sdpParams);
                    break;
                default:
                    throw Common::CallException(Common::ObjectAgent::versionError("setOfferAndAnswer.RtpRunner.MpCallSip"));
                }
                x__agent->ex_async(this,"setOfferAndAnswer.RtpRunner.MpCallSip",__oput,x__params,0);
            }
            catch (const Common::CallException& ex)
            {
                Common::ObjectAgent::throwException(x__async,ex,x__userdata);
            }
            catch (const Common::Exception& ex)
            {
                Common::ObjectAgent::throwException(x__async,ex,x__userdata);
            }
        }
        void cmdResult(int __rslt,const Common::IputStreamPtr& __iput,const Common::ObjectPtr& __userdata)
        {
            if ((__rslt>>16)!=VersionException)
            {
                if (x__async) x__async->cmdResult(__rslt,__iput,x__userdata);
                return;
            }
            x__loop ++;
            if (x__loop >= 3)
            {
                if (x__async)
                    Common::ObjectAgent::throwException(x__async, Common::CallException(Common::ObjectAgent::versionError("setOfferAndAnswer.RtpRunner.MpCallSip")), x__userdata);
                return;
            }
            start();
        }
    private:
        Common::ObjectAgentPtr x__agent;
        Common::AgentAsyncPtr x__async;
        Common::String x_offerSdp;
        MpCallSip::SdpParams x_sdpParams;
        Common::CallParamsPtr x__params;
        Common::ObjectPtr x__userdata;
        int x__loop;
    };
    (new __AgentAsync(__agent,__async,offerSdp,sdpParams,__params,__userdata))->start();
}

bool RtpRunnerAgent::setOfferAndAnswer_end(int __rslt,const Common::IputStreamPtr& __iput,Common::String& answerSdp) throw()
{
    try
    {
        Common::CallError __error;
        if (Common::ObjectAgent::processException(__rslt,__iput,__error))
            throw Common::CallException(__error);
        Assert((__rslt>>16) == 0);
        bool __ret;
        switch (__rslt)
        {
        case 0:
            __iput->read(__ret);
            __iput->read(answerSdp);
            break;
        default:
            throw Common::CallException(Common::ObjectAgent::versionError("setOfferAndAnswer.RtpRunner.MpCallSip"));
        }
        Common::ObjectAgent::processFinal(__iput);
        return __ret;
    }
    catch (const Common::CallException& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
    catch (const Common::Exception& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
}

bool RtpRunnerAgent::genAnswer(const MpCallSip::SdpParams& sdpParams,Common::String& answerSdp,const Common::CallParamsPtr& __params) const throw()
{
    try
    {
        int __loop = 0;
        while (1)
        {
            Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
            Common::VerListPtr __vers = __agent->verList("genAnswer.RtpRunner.MpCallSip");
            short __ver = 0;
            if (__vers)
            {
                __ver = __vers->ver(true);
                if (__ver > 0) __ver = 0;
            }
            switch (__ver)
            {
            case 0:
                __oput->write((short)1);
                __oput->write((short)__ver);
                MpCallSip::__write_SdpParams(__oput,sdpParams);
                break;
            default:
                throw Common::CallException(Common::ObjectAgent::versionError("genAnswer.RtpRunner.MpCallSip"));
            }
            Common::IputStreamPtr __iput;
            Common::CallError __error;
            int __rslt = __agent->ex_sync("genAnswer.RtpRunner.MpCallSip",__oput,__iput,__params,__error);
            if (__rslt == -1)
                throw Common::CallException(__error);
            if (__rslt>>16)
            {
                Assert((__rslt>>16) == VersionException);
                __loop ++;
                if (__loop >= 3)
                    throw Common::CallException(Common::ObjectAgent::versionError("genAnswer.RtpRunner.MpCallSip"));
                continue;
            }
            bool __ret;
            switch (__rslt)
            {
            case 0:
                __iput->read(__ret);
                __iput->read(answerSdp);
                break;
            default:
                throw Common::CallException(Common::ObjectAgent::versionError("genAnswer.RtpRunner.MpCallSip"));
            }
            Common::ObjectAgent::processFinal(__iput);
            return __ret;
        }
    }
    catch (const Common::CallException& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
    catch (const Common::Exception& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
}

void RtpRunnerAgent::genAnswer_begin(const Common::AgentAsyncPtr& __async,const MpCallSip::SdpParams& sdpParams,const Common::CallParamsPtr& __params,const Common::ObjectPtr& __userdata) const throw()
{
    class __AgentAsync : public Common::AgentAsync
    {
    public:
        __AgentAsync(const Common::ObjectAgentPtr& __agent,const Common::AgentAsyncPtr& __async,const MpCallSip::SdpParams& sdpParams,const Common::CallParamsPtr& __params,const Common::ObjectPtr& __userdata)
            : x__agent(__agent),x__async(__async),x_sdpParams(sdpParams),x__params(__params),x__userdata(__userdata),x__loop(0) {}

        void start()
        {
            Common::AgentAsyncPtr pthis = this;
            try
            {
                Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
                Common::VerListPtr __vers = x__agent->verList("genAnswer.RtpRunner.MpCallSip");
                short __ver = 0;
                if (__vers)
                {
                    __ver = __vers->ver(true);
                    if (__ver > 0) __ver = 0;
                }
                switch (__ver)
                {
                case 0:
                    __oput->write((short)1);
                    __oput->write((short)__ver);
                    MpCallSip::__write_SdpParams(__oput,x_sdpParams);
                    break;
                default:
                    throw Common::CallException(Common::ObjectAgent::versionError("genAnswer.RtpRunner.MpCallSip"));
                }
                x__agent->ex_async(this,"genAnswer.RtpRunner.MpCallSip",__oput,x__params,0);
            }
            catch (const Common::CallException& ex)
            {
                Common::ObjectAgent::throwException(x__async,ex,x__userdata);
            }
            catch (const Common::Exception& ex)
            {
                Common::ObjectAgent::throwException(x__async,ex,x__userdata);
            }
        }
        void cmdResult(int __rslt,const Common::IputStreamPtr& __iput,const Common::ObjectPtr& __userdata)
        {
            if ((__rslt>>16)!=VersionException)
            {
                if (x__async) x__async->cmdResult(__rslt,__iput,x__userdata);
                return;
            }
            x__loop ++;
            if (x__loop >= 3)
            {
                if (x__async)
                    Common::ObjectAgent::throwException(x__async, Common::CallException(Common::ObjectAgent::versionError("genAnswer.RtpRunner.MpCallSip")), x__userdata);
                return;
            }
            start();
        }
    private:
        Common::ObjectAgentPtr x__agent;
        Common::AgentAsyncPtr x__async;
        MpCallSip::SdpParams x_sdpParams;
        Common::CallParamsPtr x__params;
        Common::ObjectPtr x__userdata;
        int x__loop;
    };
    (new __AgentAsync(__agent,__async,sdpParams,__params,__userdata))->start();
}

bool RtpRunnerAgent::genAnswer_end(int __rslt,const Common::IputStreamPtr& __iput,Common::String& answerSdp) throw()
{
    try
    {
        Common::CallError __error;
        if (Common::ObjectAgent::processException(__rslt,__iput,__error))
            throw Common::CallException(__error);
        Assert((__rslt>>16) == 0);
        bool __ret;
        switch (__rslt)
        {
        case 0:
            __iput->read(__ret);
            __iput->read(answerSdp);
            break;
        default:
            throw Common::CallException(Common::ObjectAgent::versionError("genAnswer.RtpRunner.MpCallSip"));
        }
        Common::ObjectAgent::processFinal(__iput);
        return __ret;
    }
    catch (const Common::CallException& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
    catch (const Common::Exception& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
}

bool RtpRunnerAgent::setAnswer(const Common::String& answerSdp,const Common::CallParamsPtr& __params) const throw()
{
    try
    {
        int __loop = 0;
        while (1)
        {
            Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
            Common::VerListPtr __vers = __agent->verList("setAnswer.RtpRunner.MpCallSip");
            short __ver = 0;
            if (__vers)
            {
                __ver = __vers->ver(true);
                if (__ver > 0) __ver = 0;
            }
            switch (__ver)
            {
            case 0:
                __oput->write((short)1);
                __oput->write((short)__ver);
                __oput->write(answerSdp);
                break;
            default:
                throw Common::CallException(Common::ObjectAgent::versionError("setAnswer.RtpRunner.MpCallSip"));
            }
            Common::IputStreamPtr __iput;
            Common::CallError __error;
            int __rslt = __agent->ex_sync("setAnswer.RtpRunner.MpCallSip",__oput,__iput,__params,__error);
            if (__rslt == -1)
                throw Common::CallException(__error);
            if (__rslt>>16)
            {
                Assert((__rslt>>16) == VersionException);
                __loop ++;
                if (__loop >= 3)
                    throw Common::CallException(Common::ObjectAgent::versionError("setAnswer.RtpRunner.MpCallSip"));
                continue;
            }
            bool __ret;
            switch (__rslt)
            {
            case 0:
                __iput->read(__ret);
                break;
            default:
                throw Common::CallException(Common::ObjectAgent::versionError("setAnswer.RtpRunner.MpCallSip"));
            }
            Common::ObjectAgent::processFinal(__iput);
            return __ret;
        }
    }
    catch (const Common::CallException& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
    catch (const Common::Exception& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
}

void RtpRunnerAgent::setAnswer_begin(const Common::AgentAsyncPtr& __async,const Common::String& answerSdp,const Common::CallParamsPtr& __params,const Common::ObjectPtr& __userdata) const throw()
{
    class __AgentAsync : public Common::AgentAsync
    {
    public:
        __AgentAsync(const Common::ObjectAgentPtr& __agent,const Common::AgentAsyncPtr& __async,const Common::String& answerSdp,const Common::CallParamsPtr& __params,const Common::ObjectPtr& __userdata)
            : x__agent(__agent),x__async(__async),x_answerSdp(answerSdp),x__params(__params),x__userdata(__userdata),x__loop(0) {}

        void start()
        {
            Common::AgentAsyncPtr pthis = this;
            try
            {
                Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
                Common::VerListPtr __vers = x__agent->verList("setAnswer.RtpRunner.MpCallSip");
                short __ver = 0;
                if (__vers)
                {
                    __ver = __vers->ver(true);
                    if (__ver > 0) __ver = 0;
                }
                switch (__ver)
                {
                case 0:
                    __oput->write((short)1);
                    __oput->write((short)__ver);
                    __oput->write(x_answerSdp);
                    break;
                default:
                    throw Common::CallException(Common::ObjectAgent::versionError("setAnswer.RtpRunner.MpCallSip"));
                }
                x__agent->ex_async(this,"setAnswer.RtpRunner.MpCallSip",__oput,x__params,0);
            }
            catch (const Common::CallException& ex)
            {
                Common::ObjectAgent::throwException(x__async,ex,x__userdata);
            }
            catch (const Common::Exception& ex)
            {
                Common::ObjectAgent::throwException(x__async,ex,x__userdata);
            }
        }
        void cmdResult(int __rslt,const Common::IputStreamPtr& __iput,const Common::ObjectPtr& __userdata)
        {
            if ((__rslt>>16)!=VersionException)
            {
                if (x__async) x__async->cmdResult(__rslt,__iput,x__userdata);
                return;
            }
            x__loop ++;
            if (x__loop >= 3)
            {
                if (x__async)
                    Common::ObjectAgent::throwException(x__async, Common::CallException(Common::ObjectAgent::versionError("setAnswer.RtpRunner.MpCallSip")), x__userdata);
                return;
            }
            start();
        }
    private:
        Common::ObjectAgentPtr x__agent;
        Common::AgentAsyncPtr x__async;
        Common::String x_answerSdp;
        Common::CallParamsPtr x__params;
        Common::ObjectPtr x__userdata;
        int x__loop;
    };
    (new __AgentAsync(__agent,__async,answerSdp,__params,__userdata))->start();
}

bool RtpRunnerAgent::setAnswer_end(int __rslt,const Common::IputStreamPtr& __iput) throw()
{
    try
    {
        Common::CallError __error;
        if (Common::ObjectAgent::processException(__rslt,__iput,__error))
            throw Common::CallException(__error);
        Assert((__rslt>>16) == 0);
        bool __ret;
        switch (__rslt)
        {
        case 0:
            __iput->read(__ret);
            break;
        default:
            throw Common::CallException(Common::ObjectAgent::versionError("setAnswer.RtpRunner.MpCallSip"));
        }
        Common::ObjectAgent::processFinal(__iput);
        return __ret;
    }
    catch (const Common::CallException& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
    catch (const Common::Exception& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
}

bool RtpRunnerAgent::setTalking(bool talking,const Common::CallParamsPtr& __params) const throw()
{
    try
    {
        int __loop = 0;
        while (1)
        {
            Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
            Common::VerListPtr __vers = __agent->verList("setTalking.RtpRunner.MpCallSip");
            short __ver = 0;
            if (__vers)
            {
                __ver = __vers->ver(true);
                if (__ver > 0) __ver = 0;
            }
            switch (__ver)
            {
            case 0:
                __oput->write((short)1);
                __oput->write((short)__ver);
                __oput->write(talking);
                break;
            default:
                throw Common::CallException(Common::ObjectAgent::versionError("setTalking.RtpRunner.MpCallSip"));
            }
            Common::IputStreamPtr __iput;
            Common::CallError __error;
            int __rslt = __agent->ex_sync("setTalking.RtpRunner.MpCallSip",__oput,__iput,__params,__error);
            if (__rslt == -1)
                throw Common::CallException(__error);
            if (__rslt>>16)
            {
                Assert((__rslt>>16) == VersionException);
                __loop ++;
                if (__loop >= 3)
                    throw Common::CallException(Common::ObjectAgent::versionError("setTalking.RtpRunner.MpCallSip"));
                continue;
            }
            bool __ret;
            switch (__rslt)
            {
            case 0:
                __iput->read(__ret);
                break;
            default:
                throw Common::CallException(Common::ObjectAgent::versionError("setTalking.RtpRunner.MpCallSip"));
            }
            Common::ObjectAgent::processFinal(__iput);
            return __ret;
        }
    }
    catch (const Common::CallException& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
    catch (const Common::Exception& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
}

void RtpRunnerAgent::setTalking_begin(const Common::AgentAsyncPtr& __async,bool talking,const Common::CallParamsPtr& __params,const Common::ObjectPtr& __userdata) const throw()
{
    class __AgentAsync : public Common::AgentAsync
    {
    public:
        __AgentAsync(const Common::ObjectAgentPtr& __agent,const Common::AgentAsyncPtr& __async,bool talking,const Common::CallParamsPtr& __params,const Common::ObjectPtr& __userdata)
            : x__agent(__agent),x__async(__async),x_talking(talking),x__params(__params),x__userdata(__userdata),x__loop(0) {}

        void start()
        {
            Common::AgentAsyncPtr pthis = this;
            try
            {
                Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
                Common::VerListPtr __vers = x__agent->verList("setTalking.RtpRunner.MpCallSip");
                short __ver = 0;
                if (__vers)
                {
                    __ver = __vers->ver(true);
                    if (__ver > 0) __ver = 0;
                }
                switch (__ver)
                {
                case 0:
                    __oput->write((short)1);
                    __oput->write((short)__ver);
                    __oput->write(x_talking);
                    break;
                default:
                    throw Common::CallException(Common::ObjectAgent::versionError("setTalking.RtpRunner.MpCallSip"));
                }
                x__agent->ex_async(this,"setTalking.RtpRunner.MpCallSip",__oput,x__params,0);
            }
            catch (const Common::CallException& ex)
            {
                Common::ObjectAgent::throwException(x__async,ex,x__userdata);
            }
            catch (const Common::Exception& ex)
            {
                Common::ObjectAgent::throwException(x__async,ex,x__userdata);
            }
        }
        void cmdResult(int __rslt,const Common::IputStreamPtr& __iput,const Common::ObjectPtr& __userdata)
        {
            if ((__rslt>>16)!=VersionException)
            {
                if (x__async) x__async->cmdResult(__rslt,__iput,x__userdata);
                return;
            }
            x__loop ++;
            if (x__loop >= 3)
            {
                if (x__async)
                    Common::ObjectAgent::throwException(x__async, Common::CallException(Common::ObjectAgent::versionError("setTalking.RtpRunner.MpCallSip")), x__userdata);
                return;
            }
            start();
        }
    private:
        Common::ObjectAgentPtr x__agent;
        Common::AgentAsyncPtr x__async;
        bool x_talking;
        Common::CallParamsPtr x__params;
        Common::ObjectPtr x__userdata;
        int x__loop;
    };
    (new __AgentAsync(__agent,__async,talking,__params,__userdata))->start();
}

bool RtpRunnerAgent::setTalking_end(int __rslt,const Common::IputStreamPtr& __iput) throw()
{
    try
    {
        Common::CallError __error;
        if (Common::ObjectAgent::processException(__rslt,__iput,__error))
            throw Common::CallException(__error);
        Assert((__rslt>>16) == 0);
        bool __ret;
        switch (__rslt)
        {
        case 0:
            __iput->read(__ret);
            break;
        default:
            throw Common::CallException(Common::ObjectAgent::versionError("setTalking.RtpRunner.MpCallSip"));
        }
        Common::ObjectAgent::processFinal(__iput);
        return __ret;
    }
    catch (const Common::CallException& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
    catch (const Common::Exception& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
}

bool RtpRunnerAgent::getRtpStatus(MpCallSip::RtpStats& stats,const Common::CallParamsPtr& __params) const throw()
{
    try
    {
        int __loop = 0;
        while (1)
        {
            Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
            Common::VerListPtr __vers = __agent->verList("getRtpStatus.RtpRunner.MpCallSip");
            short __ver = 0;
            if (__vers)
            {
                __ver = __vers->ver(true);
                if (__ver > 0) __ver = 0;
            }
            switch (__ver)
            {
            case 0:
                __oput->write((short)1);
                __oput->write((short)__ver);
                break;
            default:
                throw Common::CallException(Common::ObjectAgent::versionError("getRtpStatus.RtpRunner.MpCallSip"));
            }
            Common::IputStreamPtr __iput;
            Common::CallError __error;
            int __rslt = __agent->ex_sync("getRtpStatus.RtpRunner.MpCallSip",__oput,__iput,__params,__error);
            if (__rslt == -1)
                throw Common::CallException(__error);
            if (__rslt>>16)
            {
                Assert((__rslt>>16) == VersionException);
                __loop ++;
                if (__loop >= 3)
                    throw Common::CallException(Common::ObjectAgent::versionError("getRtpStatus.RtpRunner.MpCallSip"));
                continue;
            }
            bool __ret;
            switch (__rslt)
            {
            case 0:
                __iput->read(__ret);
                MpCallSip::__read_RtpStats(__iput,stats);
                break;
            default:
                throw Common::CallException(Common::ObjectAgent::versionError("getRtpStatus.RtpRunner.MpCallSip"));
            }
            Common::ObjectAgent::processFinal(__iput);
            return __ret;
        }
    }
    catch (const Common::CallException& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
    catch (const Common::Exception& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
}

void RtpRunnerAgent::getRtpStatus_begin(const Common::AgentAsyncPtr& __async,const Common::CallParamsPtr& __params,const Common::ObjectPtr& __userdata) const throw()
{
    class __AgentAsync : public Common::AgentAsync
    {
    public:
        __AgentAsync(const Common::ObjectAgentPtr& __agent,const Common::AgentAsyncPtr& __async,const Common::CallParamsPtr& __params,const Common::ObjectPtr& __userdata)
            : x__agent(__agent),x__async(__async),x__params(__params),x__userdata(__userdata),x__loop(0) {}

        void start()
        {
            Common::AgentAsyncPtr pthis = this;
            try
            {
                Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
                Common::VerListPtr __vers = x__agent->verList("getRtpStatus.RtpRunner.MpCallSip");
                short __ver = 0;
                if (__vers)
                {
                    __ver = __vers->ver(true);
                    if (__ver > 0) __ver = 0;
                }
                switch (__ver)
                {
                case 0:
                    __oput->write((short)1);
                    __oput->write((short)__ver);
                    break;
                default:
                    throw Common::CallException(Common::ObjectAgent::versionError("getRtpStatus.RtpRunner.MpCallSip"));
                }
                x__agent->ex_async(this,"getRtpStatus.RtpRunner.MpCallSip",__oput,x__params,0);
            }
            catch (const Common::CallException& ex)
            {
                Common::ObjectAgent::throwException(x__async,ex,x__userdata);
            }
            catch (const Common::Exception& ex)
            {
                Common::ObjectAgent::throwException(x__async,ex,x__userdata);
            }
        }
        void cmdResult(int __rslt,const Common::IputStreamPtr& __iput,const Common::ObjectPtr& __userdata)
        {
            if ((__rslt>>16)!=VersionException)
            {
                if (x__async) x__async->cmdResult(__rslt,__iput,x__userdata);
                return;
            }
            x__loop ++;
            if (x__loop >= 3)
            {
                if (x__async)
                    Common::ObjectAgent::throwException(x__async, Common::CallException(Common::ObjectAgent::versionError("getRtpStatus.RtpRunner.MpCallSip")), x__userdata);
                return;
            }
            start();
        }
    private:
        Common::ObjectAgentPtr x__agent;
        Common::AgentAsyncPtr x__async;
        Common::CallParamsPtr x__params;
        Common::ObjectPtr x__userdata;
        int x__loop;
    };
    (new __AgentAsync(__agent,__async,__params,__userdata))->start();
}

bool RtpRunnerAgent::getRtpStatus_end(int __rslt,const Common::IputStreamPtr& __iput,MpCallSip::RtpStats& stats) throw()
{
    try
    {
        Common::CallError __error;
        if (Common::ObjectAgent::processException(__rslt,__iput,__error))
            throw Common::CallException(__error);
        Assert((__rslt>>16) == 0);
        bool __ret;
        switch (__rslt)
        {
        case 0:
            __iput->read(__ret);
            MpCallSip::__read_RtpStats(__iput,stats);
            break;
        default:
            throw Common::CallException(Common::ObjectAgent::versionError("getRtpStatus.RtpRunner.MpCallSip"));
        }
        Common::ObjectAgent::processFinal(__iput);
        return __ret;
    }
    catch (const Common::CallException& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
    catch (const Common::Exception& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
}

};//namespace: MpCallSip
