﻿//
// *****************************************************************************
// Copyright(c) 2017-2025 Juphoon System Software Co., LTD. All rights reserved.
// *****************************************************************************
//
// Auto generated from: MpCallSipStatus.def
// Warning: do not edit this file.
//

#include "MpCallSip/MpCallSipStatusPub.h"

namespace MpCallSip
{

StatusSessionBriefStatus::StatusSessionBriefStatus() :
    startTimeMs(0),
    durationMs(0)
{
}

StatusSessionBriefStatus::StatusSessionBriefStatus(const Common::String& x_sessionId,const Common::String& x_caller,const Common::String& x_callee,const Common::String& x_callId,const Common::String& x_uui,const Common::String& x_direction,const Common::String& x_state,Common::Long x_startTimeMs,Common::Long x_durationMs,const Common::String& x_termedReason) :
    sessionId(x_sessionId),caller(x_caller),callee(x_callee),callId(x_callId),uui(x_uui),direction(x_direction),state(x_state),startTimeMs(x_startTimeMs),durationMs(x_durationMs),termedReason(x_termedReason)
{
}

bool StatusSessionBriefStatus::operator<(const StatusSessionBriefStatus&__obj) const
{
    if (this == &__obj) return false;
    if (sessionId < __obj.sessionId) return true;
    if (__obj.sessionId < sessionId) return false;
    if (caller < __obj.caller) return true;
    if (__obj.caller < caller) return false;
    if (callee < __obj.callee) return true;
    if (__obj.callee < callee) return false;
    if (callId < __obj.callId) return true;
    if (__obj.callId < callId) return false;
    if (uui < __obj.uui) return true;
    if (__obj.uui < uui) return false;
    if (direction < __obj.direction) return true;
    if (__obj.direction < direction) return false;
    if (state < __obj.state) return true;
    if (__obj.state < state) return false;
    if (startTimeMs < __obj.startTimeMs) return true;
    if (__obj.startTimeMs < startTimeMs) return false;
    if (durationMs < __obj.durationMs) return true;
    if (__obj.durationMs < durationMs) return false;
    if (termedReason < __obj.termedReason) return true;
    if (__obj.termedReason < termedReason) return false;
    return false;
}

bool StatusSessionBriefStatus::operator==(const StatusSessionBriefStatus&__obj) const
{
    if (this == &__obj) return true;
    if (sessionId != __obj.sessionId) return false;
    if (caller != __obj.caller) return false;
    if (callee != __obj.callee) return false;
    if (callId != __obj.callId) return false;
    if (uui != __obj.uui) return false;
    if (direction != __obj.direction) return false;
    if (state != __obj.state) return false;
    if (startTimeMs != __obj.startTimeMs) return false;
    if (durationMs != __obj.durationMs) return false;
    if (termedReason != __obj.termedReason) return false;
    return true;
}

void StatusSessionBriefStatus::__write(const Common::OputStreamPtr& __oput) const
{
    __write_StatusSessionBriefStatus(__oput,*this);
}

void StatusSessionBriefStatus::__read(const Common::IputStreamPtr& __iput)
{
    __read_StatusSessionBriefStatus(__iput,*this);
}

void StatusSessionBriefStatus::__textWrite(const Common::OputStreamPtr& __oput,const Common::String& __name) const
{
    __textWrite_StatusSessionBriefStatus(__oput,__name,*this);
}

bool StatusSessionBriefStatus::__textRead(const Common::IputStreamPtr& __iput,const Common::String& __name,int __idx)
{
    return __textRead_StatusSessionBriefStatus(__iput,__name,*this,__idx);
}

void __write_StatusSessionBriefStatus(const Common::OputStreamPtr& __oput,const MpCallSip::StatusSessionBriefStatus& __obj)
{
    __oput->write(__obj.sessionId);
    __oput->write(__obj.caller);
    __oput->write(__obj.callee);
    __oput->write(__obj.callId);
    __oput->write(__obj.uui);
    __oput->write(__obj.direction);
    __oput->write(__obj.state);
    __oput->write(__obj.startTimeMs);
    __oput->write(__obj.durationMs);
    __oput->write(__obj.termedReason);
}

void __read_StatusSessionBriefStatus(const Common::IputStreamPtr& __iput,MpCallSip::StatusSessionBriefStatus& __obj)
{
    __iput->read(__obj.sessionId);
    __iput->read(__obj.caller);
    __iput->read(__obj.callee);
    __iput->read(__obj.callId);
    __iput->read(__obj.uui);
    __iput->read(__obj.direction);
    __iput->read(__obj.state);
    __iput->read(__obj.startTimeMs);
    __iput->read(__obj.durationMs);
    __iput->read(__obj.termedReason);
}

void __textWrite_StatusSessionBriefStatus(const Common::OputStreamPtr& __oput,const Common::String& __name,const MpCallSip::StatusSessionBriefStatus& __obj)
{
    __oput->textStart(__name);
    __oput->textWrite("sessionId",__obj.sessionId);
    __oput->textWrite("caller",__obj.caller);
    __oput->textWrite("callee",__obj.callee);
    __oput->textWrite("callId",__obj.callId);
    __oput->textWrite("uui",__obj.uui);
    __oput->textWrite("direction",__obj.direction);
    __oput->textWrite("state",__obj.state);
    __oput->textWrite("startTimeMs",__obj.startTimeMs);
    __oput->textWrite("durationMs",__obj.durationMs);
    __oput->textWrite("termedReason",__obj.termedReason);
    __oput->textEnd();
}

bool __textRead_StatusSessionBriefStatus(const Common::IputStreamPtr& __iput,const Common::String& __name,MpCallSip::StatusSessionBriefStatus& __obj,int __idx)
{
    if (!__iput->textStart(__name,__idx)) return false;
    __iput->textRead("sessionId",__obj.sessionId,0);
    __iput->textRead("caller",__obj.caller,0);
    __iput->textRead("callee",__obj.callee,0);
    __iput->textRead("callId",__obj.callId,0);
    __iput->textRead("uui",__obj.uui,0);
    __iput->textRead("direction",__obj.direction,0);
    __iput->textRead("state",__obj.state,0);
    __iput->textRead("startTimeMs",__obj.startTimeMs,0);
    __iput->textRead("durationMs",__obj.durationMs,0);
    __iput->textRead("termedReason",__obj.termedReason,0);
    __iput->textEnd();
    return true;
}
void __write_SessionBriefStatusVec(const Common::OputStreamPtr& __oput,const MpCallSip::SessionBriefStatusVec& __obj)
{
    __oput->write((int)__obj.size());
    vector<MpCallSip::StatusSessionBriefStatus>::const_iterator it1;
    for (it1 = __obj.begin();it1 != __obj.end();it1++)
        MpCallSip::__write_StatusSessionBriefStatus(__oput,*it1);
}

void __read_SessionBriefStatusVec(const Common::IputStreamPtr& __iput,MpCallSip::SessionBriefStatusVec& __obj)
{
    __obj.clear();
    int size;__iput->read(size);
    for (int i=0;i< size;i++)
    {
        MpCallSip::StatusSessionBriefStatus m;
        MpCallSip::__read_StatusSessionBriefStatus(__iput,m);
        __obj.push_back(m);
    }
}

void __textWrite_SessionBriefStatusVec(const Common::OputStreamPtr& __oput,const Common::String& __name,const MpCallSip::SessionBriefStatusVec& __obj)
{
    __oput->textArray(__name);
    vector<MpCallSip::StatusSessionBriefStatus>::const_iterator it1;
    for (it1 = __obj.begin();it1 != __obj.end();it1++)
        MpCallSip::__textWrite_StatusSessionBriefStatus(__oput,__name,*it1);
}

bool __textRead_SessionBriefStatusVec(const Common::IputStreamPtr& __iput,const Common::String& __name,MpCallSip::SessionBriefStatusVec& __obj)
{
    __obj.clear();
    int size = __iput->textCount(__name);
    for (int i=0;i<size;i++)
    {
        MpCallSip::StatusSessionBriefStatus m;
        if (MpCallSip::__textRead_StatusSessionBriefStatus(__iput,__name,m,i))
            __obj.push_back(m);
    }
    return true;
}


ResponseAllCallStatus::ResponseAllCallStatus() :
    count(0)
{
}

ResponseAllCallStatus::ResponseAllCallStatus(int x_count,const MpCallSip::SessionBriefStatusVec& x_sessions) :
    count(x_count),sessions(x_sessions)
{
}

bool ResponseAllCallStatus::operator<(const ResponseAllCallStatus&__obj) const
{
    if (this == &__obj) return false;
    if (count < __obj.count) return true;
    if (__obj.count < count) return false;
    if (sessions < __obj.sessions) return true;
    if (__obj.sessions < sessions) return false;
    return false;
}

bool ResponseAllCallStatus::operator==(const ResponseAllCallStatus&__obj) const
{
    if (this == &__obj) return true;
    if (count != __obj.count) return false;
    if (sessions != __obj.sessions) return false;
    return true;
}

void ResponseAllCallStatus::__write(const Common::OputStreamPtr& __oput) const
{
    __write_ResponseAllCallStatus(__oput,*this);
}

void ResponseAllCallStatus::__read(const Common::IputStreamPtr& __iput)
{
    __read_ResponseAllCallStatus(__iput,*this);
}

void ResponseAllCallStatus::__textWrite(const Common::OputStreamPtr& __oput,const Common::String& __name) const
{
    __textWrite_ResponseAllCallStatus(__oput,__name,*this);
}

bool ResponseAllCallStatus::__textRead(const Common::IputStreamPtr& __iput,const Common::String& __name,int __idx)
{
    return __textRead_ResponseAllCallStatus(__iput,__name,*this,__idx);
}

void __write_ResponseAllCallStatus(const Common::OputStreamPtr& __oput,const MpCallSip::ResponseAllCallStatus& __obj)
{
    __oput->write(__obj.count);
    MpCallSip::__write_SessionBriefStatusVec(__oput,__obj.sessions);
}

void __read_ResponseAllCallStatus(const Common::IputStreamPtr& __iput,MpCallSip::ResponseAllCallStatus& __obj)
{
    __iput->read(__obj.count);
    MpCallSip::__read_SessionBriefStatusVec(__iput,__obj.sessions);
}

void __textWrite_ResponseAllCallStatus(const Common::OputStreamPtr& __oput,const Common::String& __name,const MpCallSip::ResponseAllCallStatus& __obj)
{
    __oput->textStart(__name);
    __oput->textWrite("count",__obj.count);
    MpCallSip::__textWrite_SessionBriefStatusVec(__oput,"sessions",__obj.sessions);
    __oput->textEnd();
}

bool __textRead_ResponseAllCallStatus(const Common::IputStreamPtr& __iput,const Common::String& __name,MpCallSip::ResponseAllCallStatus& __obj,int __idx)
{
    if (!__iput->textStart(__name,__idx)) return false;
    __iput->textRead("count",__obj.count,0);
    MpCallSip::__textRead_SessionBriefStatusVec(__iput,"sessions",__obj.sessions);
    __iput->textEnd();
    return true;
}

SipEvent::SipEvent() :
    timestamp(0)
{
}

SipEvent::SipEvent(const Common::String& x_type,const Common::String& x_time,const Common::String& x_title,const Common::String& x_detail,Common::Long x_timestamp) :
    type(x_type),time(x_time),title(x_title),detail(x_detail),timestamp(x_timestamp)
{
}

bool SipEvent::operator<(const SipEvent&__obj) const
{
    if (this == &__obj) return false;
    if (type < __obj.type) return true;
    if (__obj.type < type) return false;
    if (time < __obj.time) return true;
    if (__obj.time < time) return false;
    if (title < __obj.title) return true;
    if (__obj.title < title) return false;
    if (detail < __obj.detail) return true;
    if (__obj.detail < detail) return false;
    if (timestamp < __obj.timestamp) return true;
    if (__obj.timestamp < timestamp) return false;
    return false;
}

bool SipEvent::operator==(const SipEvent&__obj) const
{
    if (this == &__obj) return true;
    if (type != __obj.type) return false;
    if (time != __obj.time) return false;
    if (title != __obj.title) return false;
    if (detail != __obj.detail) return false;
    if (timestamp != __obj.timestamp) return false;
    return true;
}

void SipEvent::__write(const Common::OputStreamPtr& __oput) const
{
    __write_SipEvent(__oput,*this);
}

void SipEvent::__read(const Common::IputStreamPtr& __iput)
{
    __read_SipEvent(__iput,*this);
}

void SipEvent::__textWrite(const Common::OputStreamPtr& __oput,const Common::String& __name) const
{
    __textWrite_SipEvent(__oput,__name,*this);
}

bool SipEvent::__textRead(const Common::IputStreamPtr& __iput,const Common::String& __name,int __idx)
{
    return __textRead_SipEvent(__iput,__name,*this,__idx);
}

void __write_SipEvent(const Common::OputStreamPtr& __oput,const MpCallSip::SipEvent& __obj)
{
    __oput->write(__obj.type);
    __oput->write(__obj.time);
    __oput->write(__obj.title);
    __oput->write(__obj.detail);
    __oput->write(__obj.timestamp);
}

void __read_SipEvent(const Common::IputStreamPtr& __iput,MpCallSip::SipEvent& __obj)
{
    __iput->read(__obj.type);
    __iput->read(__obj.time);
    __iput->read(__obj.title);
    __iput->read(__obj.detail);
    __iput->read(__obj.timestamp);
}

void __textWrite_SipEvent(const Common::OputStreamPtr& __oput,const Common::String& __name,const MpCallSip::SipEvent& __obj)
{
    __oput->textStart(__name);
    __oput->textWrite("type",__obj.type);
    __oput->textWrite("time",__obj.time);
    __oput->textWrite("title",__obj.title);
    __oput->textWrite("detail",__obj.detail);
    __oput->textWrite("timestamp",__obj.timestamp);
    __oput->textEnd();
}

bool __textRead_SipEvent(const Common::IputStreamPtr& __iput,const Common::String& __name,MpCallSip::SipEvent& __obj,int __idx)
{
    if (!__iput->textStart(__name,__idx)) return false;
    __iput->textRead("type",__obj.type,0);
    __iput->textRead("time",__obj.time,0);
    __iput->textRead("title",__obj.title,0);
    __iput->textRead("detail",__obj.detail,0);
    __iput->textRead("timestamp",__obj.timestamp,0);
    __iput->textEnd();
    return true;
}
void __write_SipEventVec(const Common::OputStreamPtr& __oput,const MpCallSip::SipEventVec& __obj)
{
    __oput->write((int)__obj.size());
    vector<MpCallSip::SipEvent>::const_iterator it1;
    for (it1 = __obj.begin();it1 != __obj.end();it1++)
        MpCallSip::__write_SipEvent(__oput,*it1);
}

void __read_SipEventVec(const Common::IputStreamPtr& __iput,MpCallSip::SipEventVec& __obj)
{
    __obj.clear();
    int size;__iput->read(size);
    for (int i=0;i< size;i++)
    {
        MpCallSip::SipEvent m;
        MpCallSip::__read_SipEvent(__iput,m);
        __obj.push_back(m);
    }
}

void __textWrite_SipEventVec(const Common::OputStreamPtr& __oput,const Common::String& __name,const MpCallSip::SipEventVec& __obj)
{
    __oput->textArray(__name);
    vector<MpCallSip::SipEvent>::const_iterator it1;
    for (it1 = __obj.begin();it1 != __obj.end();it1++)
        MpCallSip::__textWrite_SipEvent(__oput,__name,*it1);
}

bool __textRead_SipEventVec(const Common::IputStreamPtr& __iput,const Common::String& __name,MpCallSip::SipEventVec& __obj)
{
    __obj.clear();
    int size = __iput->textCount(__name);
    for (int i=0;i<size;i++)
    {
        MpCallSip::SipEvent m;
        if (MpCallSip::__textRead_SipEvent(__iput,__name,m,i))
            __obj.push_back(m);
    }
    return true;
}


SipStats::SipStats()
{
}

SipStats::SipStats(const Common::String& x_local,const Common::String& x_remote,const MpCallSip::SipEventVec& x_events) :
    local(x_local),remote(x_remote),events(x_events)
{
}

bool SipStats::operator<(const SipStats&__obj) const
{
    if (this == &__obj) return false;
    if (local < __obj.local) return true;
    if (__obj.local < local) return false;
    if (remote < __obj.remote) return true;
    if (__obj.remote < remote) return false;
    if (events < __obj.events) return true;
    if (__obj.events < events) return false;
    return false;
}

bool SipStats::operator==(const SipStats&__obj) const
{
    if (this == &__obj) return true;
    if (local != __obj.local) return false;
    if (remote != __obj.remote) return false;
    if (events != __obj.events) return false;
    return true;
}

void SipStats::__write(const Common::OputStreamPtr& __oput) const
{
    __write_SipStats(__oput,*this);
}

void SipStats::__read(const Common::IputStreamPtr& __iput)
{
    __read_SipStats(__iput,*this);
}

void SipStats::__textWrite(const Common::OputStreamPtr& __oput,const Common::String& __name) const
{
    __textWrite_SipStats(__oput,__name,*this);
}

bool SipStats::__textRead(const Common::IputStreamPtr& __iput,const Common::String& __name,int __idx)
{
    return __textRead_SipStats(__iput,__name,*this,__idx);
}

void __write_SipStats(const Common::OputStreamPtr& __oput,const MpCallSip::SipStats& __obj)
{
    __oput->write(__obj.local);
    __oput->write(__obj.remote);
    MpCallSip::__write_SipEventVec(__oput,__obj.events);
}

void __read_SipStats(const Common::IputStreamPtr& __iput,MpCallSip::SipStats& __obj)
{
    __iput->read(__obj.local);
    __iput->read(__obj.remote);
    MpCallSip::__read_SipEventVec(__iput,__obj.events);
}

void __textWrite_SipStats(const Common::OputStreamPtr& __oput,const Common::String& __name,const MpCallSip::SipStats& __obj)
{
    __oput->textStart(__name);
    __oput->textWrite("local",__obj.local);
    __oput->textWrite("remote",__obj.remote);
    MpCallSip::__textWrite_SipEventVec(__oput,"events",__obj.events);
    __oput->textEnd();
}

bool __textRead_SipStats(const Common::IputStreamPtr& __iput,const Common::String& __name,MpCallSip::SipStats& __obj,int __idx)
{
    if (!__iput->textStart(__name,__idx)) return false;
    __iput->textRead("local",__obj.local,0);
    __iput->textRead("remote",__obj.remote,0);
    MpCallSip::__textRead_SipEventVec(__iput,"events",__obj.events);
    __iput->textEnd();
    return true;
}
void __write_SipStatusMap(const Common::OputStreamPtr& __oput,const MpCallSip::SipStatusMap& __obj)
{
    __oput->write((int)__obj.size());
    map<Common::String,MpCallSip::SipStats>::const_iterator it1;
    for (it1 = __obj.begin();it1 != __obj.end();it1++)
    {
        __oput->write(it1->first);
        MpCallSip::__write_SipStats(__oput,it1->second);
    }
}

void __read_SipStatusMap(const Common::IputStreamPtr& __iput,MpCallSip::SipStatusMap& __obj)
{
    __obj.clear();
    int size;__iput->read(size);
    for (int i=0;i< size;i++)
    {
        Common::String k;MpCallSip::SipStats v;
        __iput->read(k);
        MpCallSip::__read_SipStats(__iput,v);
        __obj.insert(make_pair(k,v));
    }
}

void __textWrite_SipStatusMap(const Common::OputStreamPtr& __oput,const Common::String& __name,const MpCallSip::SipStatusMap& __obj)
{
    __oput->textStart(__name);
    map<Common::String,MpCallSip::SipStats>::const_iterator it1;
    for (it1 = __obj.begin();it1 != __obj.end();it1++)
        MpCallSip::__textWrite_SipStats(__oput,it1->first,it1->second);
    __oput->textEnd();
}

bool __textRead_SipStatusMap(const Common::IputStreamPtr& __iput,const Common::String& __name,MpCallSip::SipStatusMap& __obj,int __idx)
{
    __obj.clear();
    if (__iput->textStart(__name,__idx))
    {
        set<Common::String> ks;
        set<Common::String>::iterator it1;
        __iput->textList(ks);
        for (it1 = ks.begin();it1 != ks.end();it1++)
        {
            MpCallSip::SipStats v;
            if (MpCallSip::__textRead_SipStats(__iput,*it1,v,0))
                __obj.insert(make_pair(*it1,v));
        }
        __iput->textEnd();
    }
    return true;
}


ResponseStatus::ResponseStatus() :
    startTimeMs(0),
    durationMs(0)
{
}

ResponseStatus::ResponseStatus(const Common::String& x_sessionId,const Common::String& x_caller,const Common::String& x_callee,const Common::String& x_callId,const Common::String& x_direction,const Common::String& x_state,Common::Long x_startTimeMs,Common::Long x_durationMs,const Common::String& x_termedReason,const MpCallSip::SipStatusMap& x_sipStats,const MpCallSip::RtpStats& x_rtpStats) :
    sessionId(x_sessionId),caller(x_caller),callee(x_callee),callId(x_callId),direction(x_direction),state(x_state),startTimeMs(x_startTimeMs),durationMs(x_durationMs),termedReason(x_termedReason),sipStats(x_sipStats),rtpStats(x_rtpStats)
{
}

bool ResponseStatus::operator<(const ResponseStatus&__obj) const
{
    if (this == &__obj) return false;
    if (sessionId < __obj.sessionId) return true;
    if (__obj.sessionId < sessionId) return false;
    if (caller < __obj.caller) return true;
    if (__obj.caller < caller) return false;
    if (callee < __obj.callee) return true;
    if (__obj.callee < callee) return false;
    if (callId < __obj.callId) return true;
    if (__obj.callId < callId) return false;
    if (direction < __obj.direction) return true;
    if (__obj.direction < direction) return false;
    if (state < __obj.state) return true;
    if (__obj.state < state) return false;
    if (startTimeMs < __obj.startTimeMs) return true;
    if (__obj.startTimeMs < startTimeMs) return false;
    if (durationMs < __obj.durationMs) return true;
    if (__obj.durationMs < durationMs) return false;
    if (termedReason < __obj.termedReason) return true;
    if (__obj.termedReason < termedReason) return false;
    if (sipStats < __obj.sipStats) return true;
    if (__obj.sipStats < sipStats) return false;
    if (rtpStats < __obj.rtpStats) return true;
    if (__obj.rtpStats < rtpStats) return false;
    return false;
}

bool ResponseStatus::operator==(const ResponseStatus&__obj) const
{
    if (this == &__obj) return true;
    if (sessionId != __obj.sessionId) return false;
    if (caller != __obj.caller) return false;
    if (callee != __obj.callee) return false;
    if (callId != __obj.callId) return false;
    if (direction != __obj.direction) return false;
    if (state != __obj.state) return false;
    if (startTimeMs != __obj.startTimeMs) return false;
    if (durationMs != __obj.durationMs) return false;
    if (termedReason != __obj.termedReason) return false;
    if (sipStats != __obj.sipStats) return false;
    if (rtpStats != __obj.rtpStats) return false;
    return true;
}

void ResponseStatus::__write(const Common::OputStreamPtr& __oput) const
{
    __write_ResponseStatus(__oput,*this);
}

void ResponseStatus::__read(const Common::IputStreamPtr& __iput)
{
    __read_ResponseStatus(__iput,*this);
}

void ResponseStatus::__textWrite(const Common::OputStreamPtr& __oput,const Common::String& __name) const
{
    __textWrite_ResponseStatus(__oput,__name,*this);
}

bool ResponseStatus::__textRead(const Common::IputStreamPtr& __iput,const Common::String& __name,int __idx)
{
    return __textRead_ResponseStatus(__iput,__name,*this,__idx);
}

void __write_ResponseStatus(const Common::OputStreamPtr& __oput,const MpCallSip::ResponseStatus& __obj)
{
    __oput->write(__obj.sessionId);
    __oput->write(__obj.caller);
    __oput->write(__obj.callee);
    __oput->write(__obj.callId);
    __oput->write(__obj.direction);
    __oput->write(__obj.state);
    __oput->write(__obj.startTimeMs);
    __oput->write(__obj.durationMs);
    __oput->write(__obj.termedReason);
    MpCallSip::__write_SipStatusMap(__oput,__obj.sipStats);
    MpCallSip::__write_RtpStats(__oput,__obj.rtpStats);
}

void __read_ResponseStatus(const Common::IputStreamPtr& __iput,MpCallSip::ResponseStatus& __obj)
{
    __iput->read(__obj.sessionId);
    __iput->read(__obj.caller);
    __iput->read(__obj.callee);
    __iput->read(__obj.callId);
    __iput->read(__obj.direction);
    __iput->read(__obj.state);
    __iput->read(__obj.startTimeMs);
    __iput->read(__obj.durationMs);
    __iput->read(__obj.termedReason);
    MpCallSip::__read_SipStatusMap(__iput,__obj.sipStats);
    MpCallSip::__read_RtpStats(__iput,__obj.rtpStats);
}

void __textWrite_ResponseStatus(const Common::OputStreamPtr& __oput,const Common::String& __name,const MpCallSip::ResponseStatus& __obj)
{
    __oput->textStart(__name);
    __oput->textWrite("sessionId",__obj.sessionId);
    __oput->textWrite("caller",__obj.caller);
    __oput->textWrite("callee",__obj.callee);
    __oput->textWrite("callId",__obj.callId);
    __oput->textWrite("direction",__obj.direction);
    __oput->textWrite("state",__obj.state);
    __oput->textWrite("startTimeMs",__obj.startTimeMs);
    __oput->textWrite("durationMs",__obj.durationMs);
    __oput->textWrite("termedReason",__obj.termedReason);
    MpCallSip::__textWrite_SipStatusMap(__oput,"sipStats",__obj.sipStats);
    MpCallSip::__textWrite_RtpStats(__oput,"rtpStats",__obj.rtpStats);
    __oput->textEnd();
}

bool __textRead_ResponseStatus(const Common::IputStreamPtr& __iput,const Common::String& __name,MpCallSip::ResponseStatus& __obj,int __idx)
{
    if (!__iput->textStart(__name,__idx)) return false;
    __iput->textRead("sessionId",__obj.sessionId,0);
    __iput->textRead("caller",__obj.caller,0);
    __iput->textRead("callee",__obj.callee,0);
    __iput->textRead("callId",__obj.callId,0);
    __iput->textRead("direction",__obj.direction,0);
    __iput->textRead("state",__obj.state,0);
    __iput->textRead("startTimeMs",__obj.startTimeMs,0);
    __iput->textRead("durationMs",__obj.durationMs,0);
    __iput->textRead("termedReason",__obj.termedReason,0);
    MpCallSip::__textRead_SipStatusMap(__iput,"sipStats",__obj.sipStats,0);
    MpCallSip::__textRead_RtpStats(__iput,"rtpStats",__obj.rtpStats,0);
    __iput->textEnd();
    return true;
}

};//namespace: MpCallSip
