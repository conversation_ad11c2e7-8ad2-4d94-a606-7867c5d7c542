// Generated by MockTools.py. All rights reserved by Juphoon System Software.

#include "gmock/gmock.h"
#include "MpCallSip/MpCallRtpServer.h"

#ifndef __MpCall_MpCallRtpServerMock_h__
#define __MpCall_MpCallRtpServerMock_h__

namespace ModuleTest
{
    class RtpRunnerMonitorServerMockBase;
    typedef Common::Handle<RtpRunnerMonitorServerMockBase> RtpRunnerMonitorServerMockBasePtr;
    class RtpRunnerMonitorServerMockBase : public MpCallSip::RtpRunnerMonitorServer
    {
    public:
        virtual bool __ex(const Common::ServerCallPtr &__call, const Common::String &__cmd, const Common::IputStreamPtr &__iput) override
        {
            return MpCallSip::RtpRunnerMonitorServer::__ex(__call, __cmd, __iput);
        }

        MOCK_METHOD(void, onError, (const Common::ServerCallPtr &__call, const Common::String &reason), (override));
        MOCK_METHOD(void, onNetworkStatusChanged, (const Common::ServerCallPtr &__call, const Common::StrIntMap &networkStatus), (override));

        void delegateToBad()
        {
            ON_CALL(*this, onError).WillByDefault([this](const Common::ServerCallPtr &__call, const Common::String &reason) {
                onError_end(__call);
            });
            ON_CALL(*this, onNetworkStatusChanged).WillByDefault([this](const Common::ServerCallPtr &__call, const Common::StrIntMap &networkStatus) {
                onNetworkStatusChanged_end(__call);
            });
        }

        void delegateToNice()
        {
            ON_CALL(*this, onError).WillByDefault([this](const Common::ServerCallPtr &__call, const Common::String &reason) {
                onError_end(__call);
            });
            ON_CALL(*this, onNetworkStatusChanged).WillByDefault([this](const Common::ServerCallPtr &__call, const Common::StrIntMap &networkStatus) {
                onNetworkStatusChanged_end(__call);
            });
        }
    };
    class RtpRunnerServerMockBase;
    typedef Common::Handle<RtpRunnerServerMockBase> RtpRunnerServerMockBasePtr;
    class RtpRunnerServerMockBase : public MpCallSip::RtpRunnerServer
    {
    public:
        virtual bool __ex(const Common::ServerCallPtr &__call, const Common::String &__cmd, const Common::IputStreamPtr &__iput) override
        {
            return MpCallSip::RtpRunnerServer::__ex(__call, __cmd, __iput);
        }

        MOCK_METHOD(void, callout_begin, (const Common::ServerCallPtr &__call, const Common::String &targetOid, const Common::String &monitorOid, const MpCallSip::RtpRunnerConfig &config, const MpCallSip::SdpParams &sdpParams), (override));
        MOCK_METHOD(void, callin_begin, (const Common::ServerCallPtr &__call, const Common::String &targetOid, const Common::String &monitorOid, const MpCallSip::RtpRunnerConfig &config, const Common::String &peerSdp), (override));
        MOCK_METHOD(void, callinAndOffer_begin, (const Common::ServerCallPtr &__call, const Common::String &targetOid, const Common::String &monitorOid, const MpCallSip::RtpRunnerConfig &config, const MpCallSip::SdpParams &sdpParams), (override));
        MOCK_METHOD(void, callinAndAnswer_begin, (const Common::ServerCallPtr &__call, const Common::String &targetOid, const Common::String &monitorOid, const MpCallSip::RtpRunnerConfig &config, const Common::String &peerSdp, const MpCallSip::SdpParams &sdpParams), (override));
        MOCK_METHOD(bool, close, (const Common::ServerCallPtr &__call), (override));
        MOCK_METHOD(bool, genOffer, (const Common::ServerCallPtr &__call, const MpCallSip::SdpParams &sdpParams, Common::String &offerSdp), (override));
        MOCK_METHOD(bool, setOffer, (const Common::ServerCallPtr &__call, const Common::String &offerSdp), (override));
        MOCK_METHOD(bool, setOfferAndAnswer, (const Common::ServerCallPtr &__call, const Common::String &offerSdp, const MpCallSip::SdpParams &sdpParams, Common::String &answerSdp), (override));
        MOCK_METHOD(bool, genAnswer, (const Common::ServerCallPtr &__call, const MpCallSip::SdpParams &sdpParams, Common::String &answerSdp), (override));
        MOCK_METHOD(bool, setAnswer, (const Common::ServerCallPtr &__call, const Common::String &answerSdp), (override));
        MOCK_METHOD(bool, setTalking, (const Common::ServerCallPtr &__call, bool talking), (override));
        MOCK_METHOD(bool, getRtpStatus, (const Common::ServerCallPtr &__call, MpCallSip::RtpStats &stats), (override));

        void delegateToBad()
        {
            ON_CALL(*this, callout_begin).WillByDefault([this](const Common::ServerCallPtr &__call, const Common::String &targetOid, const Common::String &monitorOid, const MpCallSip::RtpRunnerConfig &config, const MpCallSip::SdpParams &sdpParams) {
                callout_end(__call, false, Common::String());
            });
            ON_CALL(*this, callin_begin).WillByDefault([this](const Common::ServerCallPtr &__call, const Common::String &targetOid, const Common::String &monitorOid, const MpCallSip::RtpRunnerConfig &config, const Common::String &peerSdp) {
                callin_end(__call, false);
            });
            ON_CALL(*this, callinAndOffer_begin).WillByDefault([this](const Common::ServerCallPtr &__call, const Common::String &targetOid, const Common::String &monitorOid, const MpCallSip::RtpRunnerConfig &config, const MpCallSip::SdpParams &sdpParams) {
                callinAndOffer_end(__call, false, Common::String());
            });
            ON_CALL(*this, callinAndAnswer_begin).WillByDefault([this](const Common::ServerCallPtr &__call, const Common::String &targetOid, const Common::String &monitorOid, const MpCallSip::RtpRunnerConfig &config, const Common::String &peerSdp, const MpCallSip::SdpParams &sdpParams) {
                callinAndAnswer_end(__call, false, Common::String());
            });
            ON_CALL(*this, close).WillByDefault([this](const Common::ServerCallPtr &__call) {
                return false;
            });
            ON_CALL(*this, genOffer).WillByDefault([this](const Common::ServerCallPtr &__call, const MpCallSip::SdpParams &sdpParams, Common::String &offerSdp) {
                return false;
            });
            ON_CALL(*this, setOffer).WillByDefault([this](const Common::ServerCallPtr &__call, const Common::String &offerSdp) {
                return false;
            });
            ON_CALL(*this, setOfferAndAnswer).WillByDefault([this](const Common::ServerCallPtr &__call, const Common::String &offerSdp, const MpCallSip::SdpParams &sdpParams, Common::String &answerSdp) {
                return false;
            });
            ON_CALL(*this, genAnswer).WillByDefault([this](const Common::ServerCallPtr &__call, const MpCallSip::SdpParams &sdpParams, Common::String &answerSdp) {
                return false;
            });
            ON_CALL(*this, setAnswer).WillByDefault([this](const Common::ServerCallPtr &__call, const Common::String &answerSdp) {
                return false;
            });
            ON_CALL(*this, setTalking).WillByDefault([this](const Common::ServerCallPtr &__call, bool talking) {
                return false;
            });
            ON_CALL(*this, getRtpStatus).WillByDefault([this](const Common::ServerCallPtr &__call, MpCallSip::RtpStats &stats) {
                return false;
            });
        }

        void delegateToNice()
        {
            ON_CALL(*this, callout_begin).WillByDefault([this](const Common::ServerCallPtr &__call, const Common::String &targetOid, const Common::String &monitorOid, const MpCallSip::RtpRunnerConfig &config, const MpCallSip::SdpParams &sdpParams) {
                callout_end(__call, true, Common::String());
            });
            ON_CALL(*this, callin_begin).WillByDefault([this](const Common::ServerCallPtr &__call, const Common::String &targetOid, const Common::String &monitorOid, const MpCallSip::RtpRunnerConfig &config, const Common::String &peerSdp) {
                callin_end(__call, true);
            });
            ON_CALL(*this, callinAndOffer_begin).WillByDefault([this](const Common::ServerCallPtr &__call, const Common::String &targetOid, const Common::String &monitorOid, const MpCallSip::RtpRunnerConfig &config, const MpCallSip::SdpParams &sdpParams) {
                callinAndOffer_end(__call, true, Common::String());
            });
            ON_CALL(*this, callinAndAnswer_begin).WillByDefault([this](const Common::ServerCallPtr &__call, const Common::String &targetOid, const Common::String &monitorOid, const MpCallSip::RtpRunnerConfig &config, const Common::String &peerSdp, const MpCallSip::SdpParams &sdpParams) {
                callinAndAnswer_end(__call, true, Common::String());
            });
            ON_CALL(*this, close).WillByDefault([this](const Common::ServerCallPtr &__call) {
                return true;
            });
            ON_CALL(*this, genOffer).WillByDefault([this](const Common::ServerCallPtr &__call, const MpCallSip::SdpParams &sdpParams, Common::String &offerSdp) {
                return true;
            });
            ON_CALL(*this, setOffer).WillByDefault([this](const Common::ServerCallPtr &__call, const Common::String &offerSdp) {
                return true;
            });
            ON_CALL(*this, setOfferAndAnswer).WillByDefault([this](const Common::ServerCallPtr &__call, const Common::String &offerSdp, const MpCallSip::SdpParams &sdpParams, Common::String &answerSdp) {
                return true;
            });
            ON_CALL(*this, genAnswer).WillByDefault([this](const Common::ServerCallPtr &__call, const MpCallSip::SdpParams &sdpParams, Common::String &answerSdp) {
                return true;
            });
            ON_CALL(*this, setAnswer).WillByDefault([this](const Common::ServerCallPtr &__call, const Common::String &answerSdp) {
                return true;
            });
            ON_CALL(*this, setTalking).WillByDefault([this](const Common::ServerCallPtr &__call, bool talking) {
                return true;
            });
            ON_CALL(*this, getRtpStatus).WillByDefault([this](const Common::ServerCallPtr &__call, MpCallSip::RtpStats &stats) {
                return true;
            });
        }
    };
}

#endif // __MpCall_MpCallRtpServerMock_h__