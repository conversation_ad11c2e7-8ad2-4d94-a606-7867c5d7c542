﻿//
// *****************************************************************************
// Copyright(c) 2017-2025 Juphoon System Software Co., LTD. All rights reserved.
// *****************************************************************************
//
// Auto generated from: MpCallSipStatus.def
// Warning: do not edit this file.
//

#ifndef __MpCallSip_MpCallSipStatusPub_h
#define __MpCallSip_MpCallSipStatusPub_h

#include "Common/Common.h"
#include "MpCallSip/MpCallRtpPub.h"

namespace MpCallSip
{

class StatusSessionBriefStatus
{
public:
    StatusSessionBriefStatus();
    StatusSessionBriefStatus(const Common::String&,const Common::String&,const Common::String&,const Common::String&,const Common::String&,const Common::String&,const Common::String&,Common::Long,Common::Long,const Common::String&);

    bool operator<(const StatusSessionBriefStatus&) const;
    bool operator==(const StatusSessionBriefStatus&) const;
    bool operator!=(const StatusSessionBriefStatus&__obj) const { return !operator==(__obj);}
    void __write(const Common::OputStreamPtr&) const;
    void __read(const Common::IputStreamPtr&);
    void __textWrite(const Common::OputStreamPtr&,const Common::String&) const;
    bool __textRead(const Common::IputStreamPtr&,const Common::String&,int = 0);
public:
    Common::String sessionId;
    Common::String caller;
    Common::String callee;
    Common::String callId;
    Common::String uui;
    Common::String direction;
    Common::String state;
    Common::Long startTimeMs;
    Common::Long durationMs;
    Common::String termedReason;
};
void __write_StatusSessionBriefStatus(const Common::OputStreamPtr&,const MpCallSip::StatusSessionBriefStatus&);
void __read_StatusSessionBriefStatus(const Common::IputStreamPtr&,MpCallSip::StatusSessionBriefStatus&);
void __textWrite_StatusSessionBriefStatus(const Common::OputStreamPtr&,const Common::String&,const MpCallSip::StatusSessionBriefStatus&);
bool __textRead_StatusSessionBriefStatus(const Common::IputStreamPtr&,const Common::String&,MpCallSip::StatusSessionBriefStatus&,int = 0);

typedef vector<MpCallSip::StatusSessionBriefStatus> SessionBriefStatusVec;
void __write_SessionBriefStatusVec(const Common::OputStreamPtr&,const MpCallSip::SessionBriefStatusVec&);
void __read_SessionBriefStatusVec(const Common::IputStreamPtr&,MpCallSip::SessionBriefStatusVec&);
void __textWrite_SessionBriefStatusVec(const Common::OputStreamPtr&,const Common::String&,const MpCallSip::SessionBriefStatusVec&);
bool __textRead_SessionBriefStatusVec(const Common::IputStreamPtr&,const Common::String&,MpCallSip::SessionBriefStatusVec&);

class ResponseAllCallStatus
{
public:
    ResponseAllCallStatus();
    ResponseAllCallStatus(int,const MpCallSip::SessionBriefStatusVec&);

    bool operator<(const ResponseAllCallStatus&) const;
    bool operator==(const ResponseAllCallStatus&) const;
    bool operator!=(const ResponseAllCallStatus&__obj) const { return !operator==(__obj);}
    void __write(const Common::OputStreamPtr&) const;
    void __read(const Common::IputStreamPtr&);
    void __textWrite(const Common::OputStreamPtr&,const Common::String&) const;
    bool __textRead(const Common::IputStreamPtr&,const Common::String&,int = 0);
public:
    int count;
    MpCallSip::SessionBriefStatusVec sessions;
};
void __write_ResponseAllCallStatus(const Common::OputStreamPtr&,const MpCallSip::ResponseAllCallStatus&);
void __read_ResponseAllCallStatus(const Common::IputStreamPtr&,MpCallSip::ResponseAllCallStatus&);
void __textWrite_ResponseAllCallStatus(const Common::OputStreamPtr&,const Common::String&,const MpCallSip::ResponseAllCallStatus&);
bool __textRead_ResponseAllCallStatus(const Common::IputStreamPtr&,const Common::String&,MpCallSip::ResponseAllCallStatus&,int = 0);

class SipEvent
{
public:
    SipEvent();
    SipEvent(const Common::String&,const Common::String&,const Common::String&,const Common::String&,Common::Long);

    bool operator<(const SipEvent&) const;
    bool operator==(const SipEvent&) const;
    bool operator!=(const SipEvent&__obj) const { return !operator==(__obj);}
    void __write(const Common::OputStreamPtr&) const;
    void __read(const Common::IputStreamPtr&);
    void __textWrite(const Common::OputStreamPtr&,const Common::String&) const;
    bool __textRead(const Common::IputStreamPtr&,const Common::String&,int = 0);
public:
    Common::String type;
    Common::String time;
    Common::String title;
    Common::String detail;
    Common::Long timestamp;
};
void __write_SipEvent(const Common::OputStreamPtr&,const MpCallSip::SipEvent&);
void __read_SipEvent(const Common::IputStreamPtr&,MpCallSip::SipEvent&);
void __textWrite_SipEvent(const Common::OputStreamPtr&,const Common::String&,const MpCallSip::SipEvent&);
bool __textRead_SipEvent(const Common::IputStreamPtr&,const Common::String&,MpCallSip::SipEvent&,int = 0);

typedef vector<MpCallSip::SipEvent> SipEventVec;
void __write_SipEventVec(const Common::OputStreamPtr&,const MpCallSip::SipEventVec&);
void __read_SipEventVec(const Common::IputStreamPtr&,MpCallSip::SipEventVec&);
void __textWrite_SipEventVec(const Common::OputStreamPtr&,const Common::String&,const MpCallSip::SipEventVec&);
bool __textRead_SipEventVec(const Common::IputStreamPtr&,const Common::String&,MpCallSip::SipEventVec&);

class SipStats
{
public:
    SipStats();
    SipStats(const Common::String&,const Common::String&,const MpCallSip::SipEventVec&);

    bool operator<(const SipStats&) const;
    bool operator==(const SipStats&) const;
    bool operator!=(const SipStats&__obj) const { return !operator==(__obj);}
    void __write(const Common::OputStreamPtr&) const;
    void __read(const Common::IputStreamPtr&);
    void __textWrite(const Common::OputStreamPtr&,const Common::String&) const;
    bool __textRead(const Common::IputStreamPtr&,const Common::String&,int = 0);
public:
    Common::String local;
    Common::String remote;
    MpCallSip::SipEventVec events;
};
void __write_SipStats(const Common::OputStreamPtr&,const MpCallSip::SipStats&);
void __read_SipStats(const Common::IputStreamPtr&,MpCallSip::SipStats&);
void __textWrite_SipStats(const Common::OputStreamPtr&,const Common::String&,const MpCallSip::SipStats&);
bool __textRead_SipStats(const Common::IputStreamPtr&,const Common::String&,MpCallSip::SipStats&,int = 0);

typedef map<Common::String,MpCallSip::SipStats> SipStatusMap;
void __write_SipStatusMap(const Common::OputStreamPtr&,const MpCallSip::SipStatusMap&);
void __read_SipStatusMap(const Common::IputStreamPtr&,MpCallSip::SipStatusMap&);
void __textWrite_SipStatusMap(const Common::OputStreamPtr&,const Common::String&,const MpCallSip::SipStatusMap&);
bool __textRead_SipStatusMap(const Common::IputStreamPtr&,const Common::String&,MpCallSip::SipStatusMap&,int = 0);

class ResponseStatus
{
public:
    ResponseStatus();
    ResponseStatus(const Common::String&,const Common::String&,const Common::String&,const Common::String&,const Common::String&,const Common::String&,Common::Long,Common::Long,const Common::String&,const MpCallSip::SipStatusMap&,const MpCallSip::RtpStats&);

    bool operator<(const ResponseStatus&) const;
    bool operator==(const ResponseStatus&) const;
    bool operator!=(const ResponseStatus&__obj) const { return !operator==(__obj);}
    void __write(const Common::OputStreamPtr&) const;
    void __read(const Common::IputStreamPtr&);
    void __textWrite(const Common::OputStreamPtr&,const Common::String&) const;
    bool __textRead(const Common::IputStreamPtr&,const Common::String&,int = 0);
public:
    Common::String sessionId;
    Common::String caller;
    Common::String callee;
    Common::String callId;
    Common::String direction;
    Common::String state;
    Common::Long startTimeMs;
    Common::Long durationMs;
    Common::String termedReason;
    MpCallSip::SipStatusMap sipStats;
    MpCallSip::RtpStats rtpStats;
};
void __write_ResponseStatus(const Common::OputStreamPtr&,const MpCallSip::ResponseStatus&);
void __read_ResponseStatus(const Common::IputStreamPtr&,MpCallSip::ResponseStatus&);
void __textWrite_ResponseStatus(const Common::OputStreamPtr&,const Common::String&,const MpCallSip::ResponseStatus&);
bool __textRead_ResponseStatus(const Common::IputStreamPtr&,const Common::String&,MpCallSip::ResponseStatus&,int = 0);

};//namespace: MpCallSip

#endif //__MpCallSip_MpCallSipStatusPub_h
