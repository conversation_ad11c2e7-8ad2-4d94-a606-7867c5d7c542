﻿//
// *****************************************************************************
// Copyright(c) 2017-2025 Juphoon System Software Co., LTD. All rights reserved.
// *****************************************************************************
//
// Auto generated from: MpCallRtp.def
// Warning: do not edit this file.
//

#ifndef __MpCallSip_MpCallRtpAgent_h
#define __MpCallSip_MpCallRtpAgent_h

#include "MpCallSip/MpCallRtpPub.h"

namespace MpCallSip
{

/** RtpRunnerMonitor 接口
 */
class RtpRunnerMonitorAgent : public Common::Agent
{
public:
    RtpRunnerMonitorAgent(int zero = 0) : Common::Agent(zero) {}
    RtpRunnerMonitorAgent(const Common::Agent& agent) : Common::Agent(agent) {}
    RtpRunnerMonitorAgent(const Common::ObjectAgentPtr& agent) : Common::Agent(agent) {}

    void onError(const Common::String& reason,const Common::CallParamsPtr& __params = 0) const;
    void onError_begin(const Common::AgentAsyncPtr& __async,const Common::String& reason,const Common::CallParamsPtr& __params = 0,const Common::ObjectPtr& __userdata = 0) const throw();
    static void onError_end(int __rslt,const Common::IputStreamPtr& __iput);

    void onNetworkStatusChanged(const Common::StrIntMap& networkStatus,const Common::CallParamsPtr& __params = 0) const;
    void onNetworkStatusChanged_begin(const Common::AgentAsyncPtr& __async,const Common::StrIntMap& networkStatus,const Common::CallParamsPtr& __params = 0,const Common::ObjectPtr& __userdata = 0) const throw();
    static void onNetworkStatusChanged_end(int __rslt,const Common::IputStreamPtr& __iput);
};

/** RtpRunner 接口
 */
class RtpRunnerAgent : public Common::Agent
{
public:
    RtpRunnerAgent(int zero = 0) : Common::Agent(zero) {}
    RtpRunnerAgent(const Common::Agent& agent) : Common::Agent(agent) {}
    RtpRunnerAgent(const Common::ObjectAgentPtr& agent) : Common::Agent(agent) {}

    bool callout(const Common::String& targetOid,const Common::String& monitorOid,const MpCallSip::RtpRunnerConfig& config,const MpCallSip::SdpParams& sdpParams,Common::String& offerSdp,const Common::CallParamsPtr& __params = 0) const throw();
    void callout_begin(const Common::AgentAsyncPtr& __async,const Common::String& targetOid,const Common::String& monitorOid,const MpCallSip::RtpRunnerConfig& config,const MpCallSip::SdpParams& sdpParams,const Common::CallParamsPtr& __params = 0,const Common::ObjectPtr& __userdata = 0) const throw();
    static bool callout_end(int __rslt,const Common::IputStreamPtr& __iput,Common::String& offerSdp) throw();

    bool callin(const Common::String& targetOid,const Common::String& monitorOid,const MpCallSip::RtpRunnerConfig& config,const Common::String& peerSdp,const Common::CallParamsPtr& __params = 0) const throw();
    void callin_begin(const Common::AgentAsyncPtr& __async,const Common::String& targetOid,const Common::String& monitorOid,const MpCallSip::RtpRunnerConfig& config,const Common::String& peerSdp,const Common::CallParamsPtr& __params = 0,const Common::ObjectPtr& __userdata = 0) const throw();
    static bool callin_end(int __rslt,const Common::IputStreamPtr& __iput) throw();

    bool callinAndOffer(const Common::String& targetOid,const Common::String& monitorOid,const MpCallSip::RtpRunnerConfig& config,const MpCallSip::SdpParams& sdpParams,Common::String& localSdp,const Common::CallParamsPtr& __params = 0) const throw();
    void callinAndOffer_begin(const Common::AgentAsyncPtr& __async,const Common::String& targetOid,const Common::String& monitorOid,const MpCallSip::RtpRunnerConfig& config,const MpCallSip::SdpParams& sdpParams,const Common::CallParamsPtr& __params = 0,const Common::ObjectPtr& __userdata = 0) const throw();
    static bool callinAndOffer_end(int __rslt,const Common::IputStreamPtr& __iput,Common::String& localSdp) throw();

    bool callinAndAnswer(const Common::String& targetOid,const Common::String& monitorOid,const MpCallSip::RtpRunnerConfig& config,const Common::String& peerSdp,const MpCallSip::SdpParams& sdpParams,Common::String& localSdp,const Common::CallParamsPtr& __params = 0) const throw();
    void callinAndAnswer_begin(const Common::AgentAsyncPtr& __async,const Common::String& targetOid,const Common::String& monitorOid,const MpCallSip::RtpRunnerConfig& config,const Common::String& peerSdp,const MpCallSip::SdpParams& sdpParams,const Common::CallParamsPtr& __params = 0,const Common::ObjectPtr& __userdata = 0) const throw();
    static bool callinAndAnswer_end(int __rslt,const Common::IputStreamPtr& __iput,Common::String& localSdp) throw();

    bool close(const Common::CallParamsPtr& __params = 0) const throw();
    void close_begin(const Common::AgentAsyncPtr& __async,const Common::CallParamsPtr& __params = 0,const Common::ObjectPtr& __userdata = 0) const throw();
    static bool close_end(int __rslt,const Common::IputStreamPtr& __iput) throw();

    bool genOffer(const MpCallSip::SdpParams& sdpParams,Common::String& offerSdp,const Common::CallParamsPtr& __params = 0) const throw();
    void genOffer_begin(const Common::AgentAsyncPtr& __async,const MpCallSip::SdpParams& sdpParams,const Common::CallParamsPtr& __params = 0,const Common::ObjectPtr& __userdata = 0) const throw();
    static bool genOffer_end(int __rslt,const Common::IputStreamPtr& __iput,Common::String& offerSdp) throw();

    bool setOffer(const Common::String& offerSdp,const Common::CallParamsPtr& __params = 0) const throw();
    void setOffer_begin(const Common::AgentAsyncPtr& __async,const Common::String& offerSdp,const Common::CallParamsPtr& __params = 0,const Common::ObjectPtr& __userdata = 0) const throw();
    static bool setOffer_end(int __rslt,const Common::IputStreamPtr& __iput) throw();

    bool setOfferAndAnswer(const Common::String& offerSdp,const MpCallSip::SdpParams& sdpParams,Common::String& answerSdp,const Common::CallParamsPtr& __params = 0) const throw();
    void setOfferAndAnswer_begin(const Common::AgentAsyncPtr& __async,const Common::String& offerSdp,const MpCallSip::SdpParams& sdpParams,const Common::CallParamsPtr& __params = 0,const Common::ObjectPtr& __userdata = 0) const throw();
    static bool setOfferAndAnswer_end(int __rslt,const Common::IputStreamPtr& __iput,Common::String& answerSdp) throw();

    bool genAnswer(const MpCallSip::SdpParams& sdpParams,Common::String& answerSdp,const Common::CallParamsPtr& __params = 0) const throw();
    void genAnswer_begin(const Common::AgentAsyncPtr& __async,const MpCallSip::SdpParams& sdpParams,const Common::CallParamsPtr& __params = 0,const Common::ObjectPtr& __userdata = 0) const throw();
    static bool genAnswer_end(int __rslt,const Common::IputStreamPtr& __iput,Common::String& answerSdp) throw();

    bool setAnswer(const Common::String& answerSdp,const Common::CallParamsPtr& __params = 0) const throw();
    void setAnswer_begin(const Common::AgentAsyncPtr& __async,const Common::String& answerSdp,const Common::CallParamsPtr& __params = 0,const Common::ObjectPtr& __userdata = 0) const throw();
    static bool setAnswer_end(int __rslt,const Common::IputStreamPtr& __iput) throw();

    bool setTalking(bool talking,const Common::CallParamsPtr& __params = 0) const throw();
    void setTalking_begin(const Common::AgentAsyncPtr& __async,bool talking,const Common::CallParamsPtr& __params = 0,const Common::ObjectPtr& __userdata = 0) const throw();
    static bool setTalking_end(int __rslt,const Common::IputStreamPtr& __iput) throw();

    bool getRtpStatus(MpCallSip::RtpStats& stats,const Common::CallParamsPtr& __params = 0) const throw();
    void getRtpStatus_begin(const Common::AgentAsyncPtr& __async,const Common::CallParamsPtr& __params = 0,const Common::ObjectPtr& __userdata = 0) const throw();
    static bool getRtpStatus_end(int __rslt,const Common::IputStreamPtr& __iput,MpCallSip::RtpStats& stats) throw();
};

};//namespace: MpCallSip

#endif //__MpCallSip_MpCallRtpAgent_h
