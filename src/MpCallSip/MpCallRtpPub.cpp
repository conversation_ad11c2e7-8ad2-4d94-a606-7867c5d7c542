﻿//
// *****************************************************************************
// Copyright(c) 2017-2025 Juphoon System Software Co., LTD. All rights reserved.
// *****************************************************************************
//
// Auto generated from: MpCallRtp.def
// Warning: do not edit this file.
//

#include "MpCallSip/MpCallRtpPub.h"

namespace MpCallSip
{
const char* MediaDirection_toString(MpCallSip::MediaDirection val)
{
    switch (val)
    {
    case MediaInactive: return (const char *)"MediaInactive";
    case MediaSendOnly: return (const char *)"MediaSendOnly";
    case MediaRecvOnly: return (const char *)"MediaRecvOnly";
    case MediaSendRecv: return (const char *)"MediaSendRecv";
    default: return (const char *)"unknown";
    }
}

RtpRunnerConfig::RtpRunnerConfig() :
    domainId(0),
    appId(0),
    extraRole(0),
    audioExcludeRoleMask((int)0),
    audioExcludeRole((int)0),
    audioMute(false),
    videoExcludeRoleMask((int)0),
    videoExcludeRole((int)0),
    videoMute(false),
    screenSharingMode(false)
{
}

RtpRunnerConfig::RtpRunnerConfig(int x_domainId,int x_appId,const Common::String& x_name,int x_extraRole,const Common::String& x_oid,const Common::String& x_roomId,const Common::String& x_confNum,const Common::String& x_Udid,int x_audioExcludeRoleMask,int x_audioExcludeRole,bool x_audioMute,int x_videoExcludeRoleMask,int x_videoExcludeRole,bool x_videoMute,bool x_screenSharingMode,const Common::String& x_bindingUser) :
    domainId(x_domainId),appId(x_appId),name(x_name),extraRole(x_extraRole),oid(x_oid),roomId(x_roomId),confNum(x_confNum),Udid(x_Udid),audioExcludeRoleMask(x_audioExcludeRoleMask),audioExcludeRole(x_audioExcludeRole),audioMute(x_audioMute),videoExcludeRoleMask(x_videoExcludeRoleMask),videoExcludeRole(x_videoExcludeRole),videoMute(x_videoMute),screenSharingMode(x_screenSharingMode),bindingUser(x_bindingUser)
{
}

bool RtpRunnerConfig::operator<(const RtpRunnerConfig&__obj) const
{
    if (this == &__obj) return false;
    if (domainId < __obj.domainId) return true;
    if (__obj.domainId < domainId) return false;
    if (appId < __obj.appId) return true;
    if (__obj.appId < appId) return false;
    if (name < __obj.name) return true;
    if (__obj.name < name) return false;
    if (extraRole < __obj.extraRole) return true;
    if (__obj.extraRole < extraRole) return false;
    if (oid < __obj.oid) return true;
    if (__obj.oid < oid) return false;
    if (roomId < __obj.roomId) return true;
    if (__obj.roomId < roomId) return false;
    if (confNum < __obj.confNum) return true;
    if (__obj.confNum < confNum) return false;
    if (Udid < __obj.Udid) return true;
    if (__obj.Udid < Udid) return false;
    if (audioExcludeRoleMask < __obj.audioExcludeRoleMask) return true;
    if (__obj.audioExcludeRoleMask < audioExcludeRoleMask) return false;
    if (audioExcludeRole < __obj.audioExcludeRole) return true;
    if (__obj.audioExcludeRole < audioExcludeRole) return false;
    if (audioMute < __obj.audioMute) return true;
    if (__obj.audioMute < audioMute) return false;
    if (videoExcludeRoleMask < __obj.videoExcludeRoleMask) return true;
    if (__obj.videoExcludeRoleMask < videoExcludeRoleMask) return false;
    if (videoExcludeRole < __obj.videoExcludeRole) return true;
    if (__obj.videoExcludeRole < videoExcludeRole) return false;
    if (videoMute < __obj.videoMute) return true;
    if (__obj.videoMute < videoMute) return false;
    if (screenSharingMode < __obj.screenSharingMode) return true;
    if (__obj.screenSharingMode < screenSharingMode) return false;
    if (bindingUser < __obj.bindingUser) return true;
    if (__obj.bindingUser < bindingUser) return false;
    return false;
}

bool RtpRunnerConfig::operator==(const RtpRunnerConfig&__obj) const
{
    if (this == &__obj) return true;
    if (domainId != __obj.domainId) return false;
    if (appId != __obj.appId) return false;
    if (name != __obj.name) return false;
    if (extraRole != __obj.extraRole) return false;
    if (oid != __obj.oid) return false;
    if (roomId != __obj.roomId) return false;
    if (confNum != __obj.confNum) return false;
    if (Udid != __obj.Udid) return false;
    if (audioExcludeRoleMask != __obj.audioExcludeRoleMask) return false;
    if (audioExcludeRole != __obj.audioExcludeRole) return false;
    if (audioMute != __obj.audioMute) return false;
    if (videoExcludeRoleMask != __obj.videoExcludeRoleMask) return false;
    if (videoExcludeRole != __obj.videoExcludeRole) return false;
    if (videoMute != __obj.videoMute) return false;
    if (screenSharingMode != __obj.screenSharingMode) return false;
    if (bindingUser != __obj.bindingUser) return false;
    return true;
}

void RtpRunnerConfig::__write(const Common::OputStreamPtr& __oput) const
{
    __write_RtpRunnerConfig(__oput,*this);
}

void RtpRunnerConfig::__read(const Common::IputStreamPtr& __iput)
{
    __read_RtpRunnerConfig(__iput,*this);
}

void RtpRunnerConfig::__textWrite(const Common::OputStreamPtr& __oput,const Common::String& __name) const
{
    __textWrite_RtpRunnerConfig(__oput,__name,*this);
}

bool RtpRunnerConfig::__textRead(const Common::IputStreamPtr& __iput,const Common::String& __name,int __idx)
{
    return __textRead_RtpRunnerConfig(__iput,__name,*this,__idx);
}

void __write_RtpRunnerConfig(const Common::OputStreamPtr& __oput,const MpCallSip::RtpRunnerConfig& __obj)
{
    __oput->write(__obj.domainId);
    __oput->write(__obj.appId);
    __oput->write(__obj.name);
    __oput->write(__obj.extraRole);
    __oput->write(__obj.oid);
    __oput->write(__obj.roomId);
    __oput->write(__obj.confNum);
    __oput->write(__obj.Udid);
    __oput->write(__obj.audioExcludeRoleMask);
    __oput->write(__obj.audioExcludeRole);
    __oput->write(__obj.audioMute);
    __oput->write(__obj.videoExcludeRoleMask);
    __oput->write(__obj.videoExcludeRole);
    __oput->write(__obj.videoMute);
    __oput->write(__obj.screenSharingMode);
    __oput->write(__obj.bindingUser);
}

void __read_RtpRunnerConfig(const Common::IputStreamPtr& __iput,MpCallSip::RtpRunnerConfig& __obj)
{
    __iput->read(__obj.domainId);
    __iput->read(__obj.appId);
    __iput->read(__obj.name);
    __iput->read(__obj.extraRole);
    __iput->read(__obj.oid);
    __iput->read(__obj.roomId);
    __iput->read(__obj.confNum);
    __iput->read(__obj.Udid);
    __iput->read(__obj.audioExcludeRoleMask);
    __iput->read(__obj.audioExcludeRole);
    __iput->read(__obj.audioMute);
    __iput->read(__obj.videoExcludeRoleMask);
    __iput->read(__obj.videoExcludeRole);
    __iput->read(__obj.videoMute);
    __iput->read(__obj.screenSharingMode);
    __iput->read(__obj.bindingUser);
}

void __textWrite_RtpRunnerConfig(const Common::OputStreamPtr& __oput,const Common::String& __name,const MpCallSip::RtpRunnerConfig& __obj)
{
    __oput->textStart(__name);
    __oput->textWrite("domainId",__obj.domainId);
    __oput->textWrite("appId",__obj.appId);
    __oput->textWrite("name",__obj.name);
    __oput->textWrite("extraRole",__obj.extraRole);
    __oput->textWrite("oid",__obj.oid);
    __oput->textWrite("roomId",__obj.roomId);
    __oput->textWrite("confNum",__obj.confNum);
    __oput->textWrite("Udid",__obj.Udid);
    __oput->textWrite("audioExcludeRoleMask",__obj.audioExcludeRoleMask);
    __oput->textWrite("audioExcludeRole",__obj.audioExcludeRole);
    __oput->textWrite("audioMute",__obj.audioMute);
    __oput->textWrite("videoExcludeRoleMask",__obj.videoExcludeRoleMask);
    __oput->textWrite("videoExcludeRole",__obj.videoExcludeRole);
    __oput->textWrite("videoMute",__obj.videoMute);
    __oput->textWrite("screenSharingMode",__obj.screenSharingMode);
    __oput->textWrite("bindingUser",__obj.bindingUser);
    __oput->textEnd();
}

bool __textRead_RtpRunnerConfig(const Common::IputStreamPtr& __iput,const Common::String& __name,MpCallSip::RtpRunnerConfig& __obj,int __idx)
{
    if (!__iput->textStart(__name,__idx)) return false;
    __iput->textRead("domainId",__obj.domainId,0);
    __iput->textRead("appId",__obj.appId,0);
    __iput->textRead("name",__obj.name,0);
    __iput->textRead("extraRole",__obj.extraRole,0);
    __iput->textRead("oid",__obj.oid,0);
    __iput->textRead("roomId",__obj.roomId,0);
    __iput->textRead("confNum",__obj.confNum,0);
    __iput->textRead("Udid",__obj.Udid,0);
    __iput->textRead("audioExcludeRoleMask",__obj.audioExcludeRoleMask,0);
    __iput->textRead("audioExcludeRole",__obj.audioExcludeRole,0);
    __iput->textRead("audioMute",__obj.audioMute,0);
    __iput->textRead("videoExcludeRoleMask",__obj.videoExcludeRoleMask,0);
    __iput->textRead("videoExcludeRole",__obj.videoExcludeRole,0);
    __iput->textRead("videoMute",__obj.videoMute,0);
    __iput->textRead("screenSharingMode",__obj.screenSharingMode,0);
    __iput->textRead("bindingUser",__obj.bindingUser,0);
    __iput->textEnd();
    return true;
}

SdpParams::SdpParams() :
    videoDirection(MpCallSip::MediaSendRecv),
    audioDirection(MpCallSip::MediaSendRecv),
    precondition(false)
{
}

SdpParams::SdpParams(MpCallSip::MediaDirection x_videoDirection,MpCallSip::MediaDirection x_audioDirection,bool x_precondition) :
    videoDirection(x_videoDirection),audioDirection(x_audioDirection),precondition(x_precondition)
{
}

bool SdpParams::operator<(const SdpParams&__obj) const
{
    if (this == &__obj) return false;
    if (videoDirection < __obj.videoDirection) return true;
    if (__obj.videoDirection < videoDirection) return false;
    if (audioDirection < __obj.audioDirection) return true;
    if (__obj.audioDirection < audioDirection) return false;
    if (precondition < __obj.precondition) return true;
    if (__obj.precondition < precondition) return false;
    return false;
}

bool SdpParams::operator==(const SdpParams&__obj) const
{
    if (this == &__obj) return true;
    if (videoDirection != __obj.videoDirection) return false;
    if (audioDirection != __obj.audioDirection) return false;
    if (precondition != __obj.precondition) return false;
    return true;
}

void SdpParams::__write(const Common::OputStreamPtr& __oput) const
{
    __write_SdpParams(__oput,*this);
}

void SdpParams::__read(const Common::IputStreamPtr& __iput)
{
    __read_SdpParams(__iput,*this);
}

void SdpParams::__textWrite(const Common::OputStreamPtr& __oput,const Common::String& __name) const
{
    __textWrite_SdpParams(__oput,__name,*this);
}

bool SdpParams::__textRead(const Common::IputStreamPtr& __iput,const Common::String& __name,int __idx)
{
    return __textRead_SdpParams(__iput,__name,*this,__idx);
}

void __write_SdpParams(const Common::OputStreamPtr& __oput,const MpCallSip::SdpParams& __obj)
{
    __oput->write((int)__obj.videoDirection);
    __oput->write((int)__obj.audioDirection);
    __oput->write(__obj.precondition);
}

void __read_SdpParams(const Common::IputStreamPtr& __iput,MpCallSip::SdpParams& __obj)
{
    __iput->read((int&)__obj.videoDirection);
    __iput->read((int&)__obj.audioDirection);
    __iput->read(__obj.precondition);
}

void __textWrite_SdpParams(const Common::OputStreamPtr& __oput,const Common::String& __name,const MpCallSip::SdpParams& __obj)
{
    __oput->textStart(__name);
    __oput->textWrite("videoDirection",(int)__obj.videoDirection);
    __oput->textWrite("audioDirection",(int)__obj.audioDirection);
    __oput->textWrite("precondition",__obj.precondition);
    __oput->textEnd();
}

bool __textRead_SdpParams(const Common::IputStreamPtr& __iput,const Common::String& __name,MpCallSip::SdpParams& __obj,int __idx)
{
    if (!__iput->textStart(__name,__idx)) return false;
    __iput->textRead("videoDirection",(int&)__obj.videoDirection,0);
    __iput->textRead("audioDirection",(int&)__obj.audioDirection,0);
    __iput->textRead("precondition",__obj.precondition,0);
    __iput->textEnd();
    return true;
}

RtpAudioSendStats::RtpAudioSendStats() :
    totalPackets(0),
    lostPackets(0),
    lostPercent(0),
    jitter(0),
    bitrate(0)
{
}

RtpAudioSendStats::RtpAudioSendStats(Common::Long x_totalPackets,Common::Long x_lostPackets,int x_lostPercent,int x_jitter,int x_bitrate) :
    totalPackets(x_totalPackets),lostPackets(x_lostPackets),lostPercent(x_lostPercent),jitter(x_jitter),bitrate(x_bitrate)
{
}

bool RtpAudioSendStats::operator<(const RtpAudioSendStats&__obj) const
{
    if (this == &__obj) return false;
    if (totalPackets < __obj.totalPackets) return true;
    if (__obj.totalPackets < totalPackets) return false;
    if (lostPackets < __obj.lostPackets) return true;
    if (__obj.lostPackets < lostPackets) return false;
    if (lostPercent < __obj.lostPercent) return true;
    if (__obj.lostPercent < lostPercent) return false;
    if (jitter < __obj.jitter) return true;
    if (__obj.jitter < jitter) return false;
    if (bitrate < __obj.bitrate) return true;
    if (__obj.bitrate < bitrate) return false;
    return false;
}

bool RtpAudioSendStats::operator==(const RtpAudioSendStats&__obj) const
{
    if (this == &__obj) return true;
    if (totalPackets != __obj.totalPackets) return false;
    if (lostPackets != __obj.lostPackets) return false;
    if (lostPercent != __obj.lostPercent) return false;
    if (jitter != __obj.jitter) return false;
    if (bitrate != __obj.bitrate) return false;
    return true;
}

void RtpAudioSendStats::__write(const Common::OputStreamPtr& __oput) const
{
    __write_RtpAudioSendStats(__oput,*this);
}

void RtpAudioSendStats::__read(const Common::IputStreamPtr& __iput)
{
    __read_RtpAudioSendStats(__iput,*this);
}

void RtpAudioSendStats::__textWrite(const Common::OputStreamPtr& __oput,const Common::String& __name) const
{
    __textWrite_RtpAudioSendStats(__oput,__name,*this);
}

bool RtpAudioSendStats::__textRead(const Common::IputStreamPtr& __iput,const Common::String& __name,int __idx)
{
    return __textRead_RtpAudioSendStats(__iput,__name,*this,__idx);
}

void __write_RtpAudioSendStats(const Common::OputStreamPtr& __oput,const MpCallSip::RtpAudioSendStats& __obj)
{
    __oput->write(__obj.totalPackets);
    __oput->write(__obj.lostPackets);
    __oput->write(__obj.lostPercent);
    __oput->write(__obj.jitter);
    __oput->write(__obj.bitrate);
}

void __read_RtpAudioSendStats(const Common::IputStreamPtr& __iput,MpCallSip::RtpAudioSendStats& __obj)
{
    __iput->read(__obj.totalPackets);
    __iput->read(__obj.lostPackets);
    __iput->read(__obj.lostPercent);
    __iput->read(__obj.jitter);
    __iput->read(__obj.bitrate);
}

void __textWrite_RtpAudioSendStats(const Common::OputStreamPtr& __oput,const Common::String& __name,const MpCallSip::RtpAudioSendStats& __obj)
{
    __oput->textStart(__name);
    __oput->textWrite("totalPackets",__obj.totalPackets);
    __oput->textWrite("lostPackets",__obj.lostPackets);
    __oput->textWrite("lostPercent",__obj.lostPercent);
    __oput->textWrite("jitter",__obj.jitter);
    __oput->textWrite("bitrate",__obj.bitrate);
    __oput->textEnd();
}

bool __textRead_RtpAudioSendStats(const Common::IputStreamPtr& __iput,const Common::String& __name,MpCallSip::RtpAudioSendStats& __obj,int __idx)
{
    if (!__iput->textStart(__name,__idx)) return false;
    __iput->textRead("totalPackets",__obj.totalPackets,0);
    __iput->textRead("lostPackets",__obj.lostPackets,0);
    __iput->textRead("lostPercent",__obj.lostPercent,0);
    __iput->textRead("jitter",__obj.jitter,0);
    __iput->textRead("bitrate",__obj.bitrate,0);
    __iput->textEnd();
    return true;
}

RtpAudioRecvStats::RtpAudioRecvStats() :
    totalPackets(0),
    lostPackets(0),
    lostPercent(0),
    jitter(0),
    bitrate(0),
    tmos(0.0)
{
}

RtpAudioRecvStats::RtpAudioRecvStats(Common::Long x_totalPackets,Common::Long x_lostPackets,int x_lostPercent,int x_jitter,int x_bitrate,float x_tmos) :
    totalPackets(x_totalPackets),lostPackets(x_lostPackets),lostPercent(x_lostPercent),jitter(x_jitter),bitrate(x_bitrate),tmos(x_tmos)
{
}

bool RtpAudioRecvStats::operator<(const RtpAudioRecvStats&__obj) const
{
    if (this == &__obj) return false;
    if (totalPackets < __obj.totalPackets) return true;
    if (__obj.totalPackets < totalPackets) return false;
    if (lostPackets < __obj.lostPackets) return true;
    if (__obj.lostPackets < lostPackets) return false;
    if (lostPercent < __obj.lostPercent) return true;
    if (__obj.lostPercent < lostPercent) return false;
    if (jitter < __obj.jitter) return true;
    if (__obj.jitter < jitter) return false;
    if (bitrate < __obj.bitrate) return true;
    if (__obj.bitrate < bitrate) return false;
    if (tmos < __obj.tmos) return true;
    if (__obj.tmos < tmos) return false;
    return false;
}

bool RtpAudioRecvStats::operator==(const RtpAudioRecvStats&__obj) const
{
    if (this == &__obj) return true;
    if (totalPackets != __obj.totalPackets) return false;
    if (lostPackets != __obj.lostPackets) return false;
    if (lostPercent != __obj.lostPercent) return false;
    if (jitter != __obj.jitter) return false;
    if (bitrate != __obj.bitrate) return false;
    if (tmos != __obj.tmos) return false;
    return true;
}

void RtpAudioRecvStats::__write(const Common::OputStreamPtr& __oput) const
{
    __write_RtpAudioRecvStats(__oput,*this);
}

void RtpAudioRecvStats::__read(const Common::IputStreamPtr& __iput)
{
    __read_RtpAudioRecvStats(__iput,*this);
}

void RtpAudioRecvStats::__textWrite(const Common::OputStreamPtr& __oput,const Common::String& __name) const
{
    __textWrite_RtpAudioRecvStats(__oput,__name,*this);
}

bool RtpAudioRecvStats::__textRead(const Common::IputStreamPtr& __iput,const Common::String& __name,int __idx)
{
    return __textRead_RtpAudioRecvStats(__iput,__name,*this,__idx);
}

void __write_RtpAudioRecvStats(const Common::OputStreamPtr& __oput,const MpCallSip::RtpAudioRecvStats& __obj)
{
    __oput->write(__obj.totalPackets);
    __oput->write(__obj.lostPackets);
    __oput->write(__obj.lostPercent);
    __oput->write(__obj.jitter);
    __oput->write(__obj.bitrate);
    __oput->write(__obj.tmos);
}

void __read_RtpAudioRecvStats(const Common::IputStreamPtr& __iput,MpCallSip::RtpAudioRecvStats& __obj)
{
    __iput->read(__obj.totalPackets);
    __iput->read(__obj.lostPackets);
    __iput->read(__obj.lostPercent);
    __iput->read(__obj.jitter);
    __iput->read(__obj.bitrate);
    __iput->read(__obj.tmos);
}

void __textWrite_RtpAudioRecvStats(const Common::OputStreamPtr& __oput,const Common::String& __name,const MpCallSip::RtpAudioRecvStats& __obj)
{
    __oput->textStart(__name);
    __oput->textWrite("totalPackets",__obj.totalPackets);
    __oput->textWrite("lostPackets",__obj.lostPackets);
    __oput->textWrite("lostPercent",__obj.lostPercent);
    __oput->textWrite("jitter",__obj.jitter);
    __oput->textWrite("bitrate",__obj.bitrate);
    __oput->textWrite("tmos",__obj.tmos);
    __oput->textEnd();
}

bool __textRead_RtpAudioRecvStats(const Common::IputStreamPtr& __iput,const Common::String& __name,MpCallSip::RtpAudioRecvStats& __obj,int __idx)
{
    if (!__iput->textStart(__name,__idx)) return false;
    __iput->textRead("totalPackets",__obj.totalPackets,0);
    __iput->textRead("lostPackets",__obj.lostPackets,0);
    __iput->textRead("lostPercent",__obj.lostPercent,0);
    __iput->textRead("jitter",__obj.jitter,0);
    __iput->textRead("bitrate",__obj.bitrate,0);
    __iput->textRead("tmos",__obj.tmos,0);
    __iput->textEnd();
    return true;
}

RtpAudioGeneranlStats::RtpAudioGeneranlStats() :
    rtt(0)
{
}

RtpAudioGeneranlStats::RtpAudioGeneranlStats(int x_rtt,const Common::String& x_networkStatus) :
    rtt(x_rtt),networkStatus(x_networkStatus)
{
}

bool RtpAudioGeneranlStats::operator<(const RtpAudioGeneranlStats&__obj) const
{
    if (this == &__obj) return false;
    if (rtt < __obj.rtt) return true;
    if (__obj.rtt < rtt) return false;
    if (networkStatus < __obj.networkStatus) return true;
    if (__obj.networkStatus < networkStatus) return false;
    return false;
}

bool RtpAudioGeneranlStats::operator==(const RtpAudioGeneranlStats&__obj) const
{
    if (this == &__obj) return true;
    if (rtt != __obj.rtt) return false;
    if (networkStatus != __obj.networkStatus) return false;
    return true;
}

void RtpAudioGeneranlStats::__write(const Common::OputStreamPtr& __oput) const
{
    __write_RtpAudioGeneranlStats(__oput,*this);
}

void RtpAudioGeneranlStats::__read(const Common::IputStreamPtr& __iput)
{
    __read_RtpAudioGeneranlStats(__iput,*this);
}

void RtpAudioGeneranlStats::__textWrite(const Common::OputStreamPtr& __oput,const Common::String& __name) const
{
    __textWrite_RtpAudioGeneranlStats(__oput,__name,*this);
}

bool RtpAudioGeneranlStats::__textRead(const Common::IputStreamPtr& __iput,const Common::String& __name,int __idx)
{
    return __textRead_RtpAudioGeneranlStats(__iput,__name,*this,__idx);
}

void __write_RtpAudioGeneranlStats(const Common::OputStreamPtr& __oput,const MpCallSip::RtpAudioGeneranlStats& __obj)
{
    __oput->write(__obj.rtt);
    __oput->write(__obj.networkStatus);
}

void __read_RtpAudioGeneranlStats(const Common::IputStreamPtr& __iput,MpCallSip::RtpAudioGeneranlStats& __obj)
{
    __iput->read(__obj.rtt);
    __iput->read(__obj.networkStatus);
}

void __textWrite_RtpAudioGeneranlStats(const Common::OputStreamPtr& __oput,const Common::String& __name,const MpCallSip::RtpAudioGeneranlStats& __obj)
{
    __oput->textStart(__name);
    __oput->textWrite("rtt",__obj.rtt);
    __oput->textWrite("networkStatus",__obj.networkStatus);
    __oput->textEnd();
}

bool __textRead_RtpAudioGeneranlStats(const Common::IputStreamPtr& __iput,const Common::String& __name,MpCallSip::RtpAudioGeneranlStats& __obj,int __idx)
{
    if (!__iput->textStart(__name,__idx)) return false;
    __iput->textRead("rtt",__obj.rtt,0);
    __iput->textRead("networkStatus",__obj.networkStatus,0);
    __iput->textEnd();
    return true;
}

RtpAudioConfigStats::RtpAudioConfigStats() :
    localPort(0),
    remotePort(0),
    payload(0),
    bitrate(0),
    packetLen(0)
{
}

RtpAudioConfigStats::RtpAudioConfigStats(const Common::String& x_localIp,int x_localPort,const Common::String& x_remoteIp,int x_remotePort,const Common::String& x_codec,int x_payload,int x_bitrate,int x_packetLen) :
    localIp(x_localIp),localPort(x_localPort),remoteIp(x_remoteIp),remotePort(x_remotePort),codec(x_codec),payload(x_payload),bitrate(x_bitrate),packetLen(x_packetLen)
{
}

bool RtpAudioConfigStats::operator<(const RtpAudioConfigStats&__obj) const
{
    if (this == &__obj) return false;
    if (localIp < __obj.localIp) return true;
    if (__obj.localIp < localIp) return false;
    if (localPort < __obj.localPort) return true;
    if (__obj.localPort < localPort) return false;
    if (remoteIp < __obj.remoteIp) return true;
    if (__obj.remoteIp < remoteIp) return false;
    if (remotePort < __obj.remotePort) return true;
    if (__obj.remotePort < remotePort) return false;
    if (codec < __obj.codec) return true;
    if (__obj.codec < codec) return false;
    if (payload < __obj.payload) return true;
    if (__obj.payload < payload) return false;
    if (bitrate < __obj.bitrate) return true;
    if (__obj.bitrate < bitrate) return false;
    if (packetLen < __obj.packetLen) return true;
    if (__obj.packetLen < packetLen) return false;
    return false;
}

bool RtpAudioConfigStats::operator==(const RtpAudioConfigStats&__obj) const
{
    if (this == &__obj) return true;
    if (localIp != __obj.localIp) return false;
    if (localPort != __obj.localPort) return false;
    if (remoteIp != __obj.remoteIp) return false;
    if (remotePort != __obj.remotePort) return false;
    if (codec != __obj.codec) return false;
    if (payload != __obj.payload) return false;
    if (bitrate != __obj.bitrate) return false;
    if (packetLen != __obj.packetLen) return false;
    return true;
}

void RtpAudioConfigStats::__write(const Common::OputStreamPtr& __oput) const
{
    __write_RtpAudioConfigStats(__oput,*this);
}

void RtpAudioConfigStats::__read(const Common::IputStreamPtr& __iput)
{
    __read_RtpAudioConfigStats(__iput,*this);
}

void RtpAudioConfigStats::__textWrite(const Common::OputStreamPtr& __oput,const Common::String& __name) const
{
    __textWrite_RtpAudioConfigStats(__oput,__name,*this);
}

bool RtpAudioConfigStats::__textRead(const Common::IputStreamPtr& __iput,const Common::String& __name,int __idx)
{
    return __textRead_RtpAudioConfigStats(__iput,__name,*this,__idx);
}

void __write_RtpAudioConfigStats(const Common::OputStreamPtr& __oput,const MpCallSip::RtpAudioConfigStats& __obj)
{
    __oput->write(__obj.localIp);
    __oput->write(__obj.localPort);
    __oput->write(__obj.remoteIp);
    __oput->write(__obj.remotePort);
    __oput->write(__obj.codec);
    __oput->write(__obj.payload);
    __oput->write(__obj.bitrate);
    __oput->write(__obj.packetLen);
}

void __read_RtpAudioConfigStats(const Common::IputStreamPtr& __iput,MpCallSip::RtpAudioConfigStats& __obj)
{
    __iput->read(__obj.localIp);
    __iput->read(__obj.localPort);
    __iput->read(__obj.remoteIp);
    __iput->read(__obj.remotePort);
    __iput->read(__obj.codec);
    __iput->read(__obj.payload);
    __iput->read(__obj.bitrate);
    __iput->read(__obj.packetLen);
}

void __textWrite_RtpAudioConfigStats(const Common::OputStreamPtr& __oput,const Common::String& __name,const MpCallSip::RtpAudioConfigStats& __obj)
{
    __oput->textStart(__name);
    __oput->textWrite("localIp",__obj.localIp);
    __oput->textWrite("localPort",__obj.localPort);
    __oput->textWrite("remoteIp",__obj.remoteIp);
    __oput->textWrite("remotePort",__obj.remotePort);
    __oput->textWrite("codec",__obj.codec);
    __oput->textWrite("payload",__obj.payload);
    __oput->textWrite("bitrate",__obj.bitrate);
    __oput->textWrite("packetLen",__obj.packetLen);
    __oput->textEnd();
}

bool __textRead_RtpAudioConfigStats(const Common::IputStreamPtr& __iput,const Common::String& __name,MpCallSip::RtpAudioConfigStats& __obj,int __idx)
{
    if (!__iput->textStart(__name,__idx)) return false;
    __iput->textRead("localIp",__obj.localIp,0);
    __iput->textRead("localPort",__obj.localPort,0);
    __iput->textRead("remoteIp",__obj.remoteIp,0);
    __iput->textRead("remotePort",__obj.remotePort,0);
    __iput->textRead("codec",__obj.codec,0);
    __iput->textRead("payload",__obj.payload,0);
    __iput->textRead("bitrate",__obj.bitrate,0);
    __iput->textRead("packetLen",__obj.packetLen,0);
    __iput->textEnd();
    return true;
}

RtpAudioStats::RtpAudioStats()
{
}

RtpAudioStats::RtpAudioStats(const MpCallSip::RtpAudioSendStats& x_send,const MpCallSip::RtpAudioRecvStats& x_recv,const MpCallSip::RtpAudioGeneranlStats& x_general,const MpCallSip::RtpAudioConfigStats& x_config) :
    send(x_send),recv(x_recv),general(x_general),config(x_config)
{
}

bool RtpAudioStats::operator<(const RtpAudioStats&__obj) const
{
    if (this == &__obj) return false;
    if (send < __obj.send) return true;
    if (__obj.send < send) return false;
    if (recv < __obj.recv) return true;
    if (__obj.recv < recv) return false;
    if (general < __obj.general) return true;
    if (__obj.general < general) return false;
    if (config < __obj.config) return true;
    if (__obj.config < config) return false;
    return false;
}

bool RtpAudioStats::operator==(const RtpAudioStats&__obj) const
{
    if (this == &__obj) return true;
    if (send != __obj.send) return false;
    if (recv != __obj.recv) return false;
    if (general != __obj.general) return false;
    if (config != __obj.config) return false;
    return true;
}

void RtpAudioStats::__write(const Common::OputStreamPtr& __oput) const
{
    __write_RtpAudioStats(__oput,*this);
}

void RtpAudioStats::__read(const Common::IputStreamPtr& __iput)
{
    __read_RtpAudioStats(__iput,*this);
}

void RtpAudioStats::__textWrite(const Common::OputStreamPtr& __oput,const Common::String& __name) const
{
    __textWrite_RtpAudioStats(__oput,__name,*this);
}

bool RtpAudioStats::__textRead(const Common::IputStreamPtr& __iput,const Common::String& __name,int __idx)
{
    return __textRead_RtpAudioStats(__iput,__name,*this,__idx);
}

void __write_RtpAudioStats(const Common::OputStreamPtr& __oput,const MpCallSip::RtpAudioStats& __obj)
{
    MpCallSip::__write_RtpAudioSendStats(__oput,__obj.send);
    MpCallSip::__write_RtpAudioRecvStats(__oput,__obj.recv);
    MpCallSip::__write_RtpAudioGeneranlStats(__oput,__obj.general);
    MpCallSip::__write_RtpAudioConfigStats(__oput,__obj.config);
}

void __read_RtpAudioStats(const Common::IputStreamPtr& __iput,MpCallSip::RtpAudioStats& __obj)
{
    MpCallSip::__read_RtpAudioSendStats(__iput,__obj.send);
    MpCallSip::__read_RtpAudioRecvStats(__iput,__obj.recv);
    MpCallSip::__read_RtpAudioGeneranlStats(__iput,__obj.general);
    MpCallSip::__read_RtpAudioConfigStats(__iput,__obj.config);
}

void __textWrite_RtpAudioStats(const Common::OputStreamPtr& __oput,const Common::String& __name,const MpCallSip::RtpAudioStats& __obj)
{
    __oput->textStart(__name);
    MpCallSip::__textWrite_RtpAudioSendStats(__oput,"send",__obj.send);
    MpCallSip::__textWrite_RtpAudioRecvStats(__oput,"recv",__obj.recv);
    MpCallSip::__textWrite_RtpAudioGeneranlStats(__oput,"general",__obj.general);
    MpCallSip::__textWrite_RtpAudioConfigStats(__oput,"config",__obj.config);
    __oput->textEnd();
}

bool __textRead_RtpAudioStats(const Common::IputStreamPtr& __iput,const Common::String& __name,MpCallSip::RtpAudioStats& __obj,int __idx)
{
    if (!__iput->textStart(__name,__idx)) return false;
    MpCallSip::__textRead_RtpAudioSendStats(__iput,"send",__obj.send,0);
    MpCallSip::__textRead_RtpAudioRecvStats(__iput,"recv",__obj.recv,0);
    MpCallSip::__textRead_RtpAudioGeneranlStats(__iput,"general",__obj.general,0);
    MpCallSip::__textRead_RtpAudioConfigStats(__iput,"config",__obj.config,0);
    __iput->textEnd();
    return true;
}

RtpVideoSendStats::RtpVideoSendStats() :
    totalPackets(0),
    lostPackets(0),
    lostPercent(0),
    jitter(0),
    frameRate(0),
    bitrate(0),
    spmos(0.0),
    pnsr(0.0),
    encodeTime(0)
{
}

RtpVideoSendStats::RtpVideoSendStats(Common::Long x_totalPackets,Common::Long x_lostPackets,int x_lostPercent,int x_jitter,int x_frameRate,const Common::String& x_resolution,int x_bitrate,const Common::String& x_codec,float x_spmos,float x_pnsr,int x_encodeTime) :
    totalPackets(x_totalPackets),lostPackets(x_lostPackets),lostPercent(x_lostPercent),jitter(x_jitter),frameRate(x_frameRate),resolution(x_resolution),bitrate(x_bitrate),codec(x_codec),spmos(x_spmos),pnsr(x_pnsr),encodeTime(x_encodeTime)
{
}

bool RtpVideoSendStats::operator<(const RtpVideoSendStats&__obj) const
{
    if (this == &__obj) return false;
    if (totalPackets < __obj.totalPackets) return true;
    if (__obj.totalPackets < totalPackets) return false;
    if (lostPackets < __obj.lostPackets) return true;
    if (__obj.lostPackets < lostPackets) return false;
    if (lostPercent < __obj.lostPercent) return true;
    if (__obj.lostPercent < lostPercent) return false;
    if (jitter < __obj.jitter) return true;
    if (__obj.jitter < jitter) return false;
    if (frameRate < __obj.frameRate) return true;
    if (__obj.frameRate < frameRate) return false;
    if (resolution < __obj.resolution) return true;
    if (__obj.resolution < resolution) return false;
    if (bitrate < __obj.bitrate) return true;
    if (__obj.bitrate < bitrate) return false;
    if (codec < __obj.codec) return true;
    if (__obj.codec < codec) return false;
    if (spmos < __obj.spmos) return true;
    if (__obj.spmos < spmos) return false;
    if (pnsr < __obj.pnsr) return true;
    if (__obj.pnsr < pnsr) return false;
    if (encodeTime < __obj.encodeTime) return true;
    if (__obj.encodeTime < encodeTime) return false;
    return false;
}

bool RtpVideoSendStats::operator==(const RtpVideoSendStats&__obj) const
{
    if (this == &__obj) return true;
    if (totalPackets != __obj.totalPackets) return false;
    if (lostPackets != __obj.lostPackets) return false;
    if (lostPercent != __obj.lostPercent) return false;
    if (jitter != __obj.jitter) return false;
    if (frameRate != __obj.frameRate) return false;
    if (resolution != __obj.resolution) return false;
    if (bitrate != __obj.bitrate) return false;
    if (codec != __obj.codec) return false;
    if (spmos != __obj.spmos) return false;
    if (pnsr != __obj.pnsr) return false;
    if (encodeTime != __obj.encodeTime) return false;
    return true;
}

void RtpVideoSendStats::__write(const Common::OputStreamPtr& __oput) const
{
    __write_RtpVideoSendStats(__oput,*this);
}

void RtpVideoSendStats::__read(const Common::IputStreamPtr& __iput)
{
    __read_RtpVideoSendStats(__iput,*this);
}

void RtpVideoSendStats::__textWrite(const Common::OputStreamPtr& __oput,const Common::String& __name) const
{
    __textWrite_RtpVideoSendStats(__oput,__name,*this);
}

bool RtpVideoSendStats::__textRead(const Common::IputStreamPtr& __iput,const Common::String& __name,int __idx)
{
    return __textRead_RtpVideoSendStats(__iput,__name,*this,__idx);
}

void __write_RtpVideoSendStats(const Common::OputStreamPtr& __oput,const MpCallSip::RtpVideoSendStats& __obj)
{
    __oput->write(__obj.totalPackets);
    __oput->write(__obj.lostPackets);
    __oput->write(__obj.lostPercent);
    __oput->write(__obj.jitter);
    __oput->write(__obj.frameRate);
    __oput->write(__obj.resolution);
    __oput->write(__obj.bitrate);
    __oput->write(__obj.codec);
    __oput->write(__obj.spmos);
    __oput->write(__obj.pnsr);
    __oput->write(__obj.encodeTime);
}

void __read_RtpVideoSendStats(const Common::IputStreamPtr& __iput,MpCallSip::RtpVideoSendStats& __obj)
{
    __iput->read(__obj.totalPackets);
    __iput->read(__obj.lostPackets);
    __iput->read(__obj.lostPercent);
    __iput->read(__obj.jitter);
    __iput->read(__obj.frameRate);
    __iput->read(__obj.resolution);
    __iput->read(__obj.bitrate);
    __iput->read(__obj.codec);
    __iput->read(__obj.spmos);
    __iput->read(__obj.pnsr);
    __iput->read(__obj.encodeTime);
}

void __textWrite_RtpVideoSendStats(const Common::OputStreamPtr& __oput,const Common::String& __name,const MpCallSip::RtpVideoSendStats& __obj)
{
    __oput->textStart(__name);
    __oput->textWrite("totalPackets",__obj.totalPackets);
    __oput->textWrite("lostPackets",__obj.lostPackets);
    __oput->textWrite("lostPercent",__obj.lostPercent);
    __oput->textWrite("jitter",__obj.jitter);
    __oput->textWrite("frameRate",__obj.frameRate);
    __oput->textWrite("resolution",__obj.resolution);
    __oput->textWrite("bitrate",__obj.bitrate);
    __oput->textWrite("codec",__obj.codec);
    __oput->textWrite("spmos",__obj.spmos);
    __oput->textWrite("pnsr",__obj.pnsr);
    __oput->textWrite("encodeTime",__obj.encodeTime);
    __oput->textEnd();
}

bool __textRead_RtpVideoSendStats(const Common::IputStreamPtr& __iput,const Common::String& __name,MpCallSip::RtpVideoSendStats& __obj,int __idx)
{
    if (!__iput->textStart(__name,__idx)) return false;
    __iput->textRead("totalPackets",__obj.totalPackets,0);
    __iput->textRead("lostPackets",__obj.lostPackets,0);
    __iput->textRead("lostPercent",__obj.lostPercent,0);
    __iput->textRead("jitter",__obj.jitter,0);
    __iput->textRead("frameRate",__obj.frameRate,0);
    __iput->textRead("resolution",__obj.resolution,0);
    __iput->textRead("bitrate",__obj.bitrate,0);
    __iput->textRead("codec",__obj.codec,0);
    __iput->textRead("spmos",__obj.spmos,0);
    __iput->textRead("pnsr",__obj.pnsr,0);
    __iput->textRead("encodeTime",__obj.encodeTime,0);
    __iput->textEnd();
    return true;
}

RtpVideoRecvStats::RtpVideoRecvStats() :
    totalPackets(0),
    lostPackets(0),
    lostPercent(0),
    jitter(0),
    frameRate(0),
    bitrate(0),
    pvmos(0.0),
    decodeTime(0)
{
}

RtpVideoRecvStats::RtpVideoRecvStats(Common::Long x_totalPackets,Common::Long x_lostPackets,int x_lostPercent,int x_jitter,int x_frameRate,const Common::String& x_resolution,int x_bitrate,const Common::String& x_codec,float x_pvmos,int x_decodeTime) :
    totalPackets(x_totalPackets),lostPackets(x_lostPackets),lostPercent(x_lostPercent),jitter(x_jitter),frameRate(x_frameRate),resolution(x_resolution),bitrate(x_bitrate),codec(x_codec),pvmos(x_pvmos),decodeTime(x_decodeTime)
{
}

bool RtpVideoRecvStats::operator<(const RtpVideoRecvStats&__obj) const
{
    if (this == &__obj) return false;
    if (totalPackets < __obj.totalPackets) return true;
    if (__obj.totalPackets < totalPackets) return false;
    if (lostPackets < __obj.lostPackets) return true;
    if (__obj.lostPackets < lostPackets) return false;
    if (lostPercent < __obj.lostPercent) return true;
    if (__obj.lostPercent < lostPercent) return false;
    if (jitter < __obj.jitter) return true;
    if (__obj.jitter < jitter) return false;
    if (frameRate < __obj.frameRate) return true;
    if (__obj.frameRate < frameRate) return false;
    if (resolution < __obj.resolution) return true;
    if (__obj.resolution < resolution) return false;
    if (bitrate < __obj.bitrate) return true;
    if (__obj.bitrate < bitrate) return false;
    if (codec < __obj.codec) return true;
    if (__obj.codec < codec) return false;
    if (pvmos < __obj.pvmos) return true;
    if (__obj.pvmos < pvmos) return false;
    if (decodeTime < __obj.decodeTime) return true;
    if (__obj.decodeTime < decodeTime) return false;
    return false;
}

bool RtpVideoRecvStats::operator==(const RtpVideoRecvStats&__obj) const
{
    if (this == &__obj) return true;
    if (totalPackets != __obj.totalPackets) return false;
    if (lostPackets != __obj.lostPackets) return false;
    if (lostPercent != __obj.lostPercent) return false;
    if (jitter != __obj.jitter) return false;
    if (frameRate != __obj.frameRate) return false;
    if (resolution != __obj.resolution) return false;
    if (bitrate != __obj.bitrate) return false;
    if (codec != __obj.codec) return false;
    if (pvmos != __obj.pvmos) return false;
    if (decodeTime != __obj.decodeTime) return false;
    return true;
}

void RtpVideoRecvStats::__write(const Common::OputStreamPtr& __oput) const
{
    __write_RtpVideoRecvStats(__oput,*this);
}

void RtpVideoRecvStats::__read(const Common::IputStreamPtr& __iput)
{
    __read_RtpVideoRecvStats(__iput,*this);
}

void RtpVideoRecvStats::__textWrite(const Common::OputStreamPtr& __oput,const Common::String& __name) const
{
    __textWrite_RtpVideoRecvStats(__oput,__name,*this);
}

bool RtpVideoRecvStats::__textRead(const Common::IputStreamPtr& __iput,const Common::String& __name,int __idx)
{
    return __textRead_RtpVideoRecvStats(__iput,__name,*this,__idx);
}

void __write_RtpVideoRecvStats(const Common::OputStreamPtr& __oput,const MpCallSip::RtpVideoRecvStats& __obj)
{
    __oput->write(__obj.totalPackets);
    __oput->write(__obj.lostPackets);
    __oput->write(__obj.lostPercent);
    __oput->write(__obj.jitter);
    __oput->write(__obj.frameRate);
    __oput->write(__obj.resolution);
    __oput->write(__obj.bitrate);
    __oput->write(__obj.codec);
    __oput->write(__obj.pvmos);
    __oput->write(__obj.decodeTime);
}

void __read_RtpVideoRecvStats(const Common::IputStreamPtr& __iput,MpCallSip::RtpVideoRecvStats& __obj)
{
    __iput->read(__obj.totalPackets);
    __iput->read(__obj.lostPackets);
    __iput->read(__obj.lostPercent);
    __iput->read(__obj.jitter);
    __iput->read(__obj.frameRate);
    __iput->read(__obj.resolution);
    __iput->read(__obj.bitrate);
    __iput->read(__obj.codec);
    __iput->read(__obj.pvmos);
    __iput->read(__obj.decodeTime);
}

void __textWrite_RtpVideoRecvStats(const Common::OputStreamPtr& __oput,const Common::String& __name,const MpCallSip::RtpVideoRecvStats& __obj)
{
    __oput->textStart(__name);
    __oput->textWrite("totalPackets",__obj.totalPackets);
    __oput->textWrite("lostPackets",__obj.lostPackets);
    __oput->textWrite("lostPercent",__obj.lostPercent);
    __oput->textWrite("jitter",__obj.jitter);
    __oput->textWrite("frameRate",__obj.frameRate);
    __oput->textWrite("resolution",__obj.resolution);
    __oput->textWrite("bitrate",__obj.bitrate);
    __oput->textWrite("codec",__obj.codec);
    __oput->textWrite("pvmos",__obj.pvmos);
    __oput->textWrite("decodeTime",__obj.decodeTime);
    __oput->textEnd();
}

bool __textRead_RtpVideoRecvStats(const Common::IputStreamPtr& __iput,const Common::String& __name,MpCallSip::RtpVideoRecvStats& __obj,int __idx)
{
    if (!__iput->textStart(__name,__idx)) return false;
    __iput->textRead("totalPackets",__obj.totalPackets,0);
    __iput->textRead("lostPackets",__obj.lostPackets,0);
    __iput->textRead("lostPercent",__obj.lostPercent,0);
    __iput->textRead("jitter",__obj.jitter,0);
    __iput->textRead("frameRate",__obj.frameRate,0);
    __iput->textRead("resolution",__obj.resolution,0);
    __iput->textRead("bitrate",__obj.bitrate,0);
    __iput->textRead("codec",__obj.codec,0);
    __iput->textRead("pvmos",__obj.pvmos,0);
    __iput->textRead("decodeTime",__obj.decodeTime,0);
    __iput->textEnd();
    return true;
}

RtpVideoGeneralStats::RtpVideoGeneralStats() :
    captureFrameRate(0),
    renderFrameRate(0),
    rtt(0)
{
}

RtpVideoGeneralStats::RtpVideoGeneralStats(const Common::String& x_captureResolution,int x_captureFrameRate,int x_renderFrameRate,int x_rtt,const Common::String& x_networkStatus) :
    captureResolution(x_captureResolution),captureFrameRate(x_captureFrameRate),renderFrameRate(x_renderFrameRate),rtt(x_rtt),networkStatus(x_networkStatus)
{
}

bool RtpVideoGeneralStats::operator<(const RtpVideoGeneralStats&__obj) const
{
    if (this == &__obj) return false;
    if (captureResolution < __obj.captureResolution) return true;
    if (__obj.captureResolution < captureResolution) return false;
    if (captureFrameRate < __obj.captureFrameRate) return true;
    if (__obj.captureFrameRate < captureFrameRate) return false;
    if (renderFrameRate < __obj.renderFrameRate) return true;
    if (__obj.renderFrameRate < renderFrameRate) return false;
    if (rtt < __obj.rtt) return true;
    if (__obj.rtt < rtt) return false;
    if (networkStatus < __obj.networkStatus) return true;
    if (__obj.networkStatus < networkStatus) return false;
    return false;
}

bool RtpVideoGeneralStats::operator==(const RtpVideoGeneralStats&__obj) const
{
    if (this == &__obj) return true;
    if (captureResolution != __obj.captureResolution) return false;
    if (captureFrameRate != __obj.captureFrameRate) return false;
    if (renderFrameRate != __obj.renderFrameRate) return false;
    if (rtt != __obj.rtt) return false;
    if (networkStatus != __obj.networkStatus) return false;
    return true;
}

void RtpVideoGeneralStats::__write(const Common::OputStreamPtr& __oput) const
{
    __write_RtpVideoGeneralStats(__oput,*this);
}

void RtpVideoGeneralStats::__read(const Common::IputStreamPtr& __iput)
{
    __read_RtpVideoGeneralStats(__iput,*this);
}

void RtpVideoGeneralStats::__textWrite(const Common::OputStreamPtr& __oput,const Common::String& __name) const
{
    __textWrite_RtpVideoGeneralStats(__oput,__name,*this);
}

bool RtpVideoGeneralStats::__textRead(const Common::IputStreamPtr& __iput,const Common::String& __name,int __idx)
{
    return __textRead_RtpVideoGeneralStats(__iput,__name,*this,__idx);
}

void __write_RtpVideoGeneralStats(const Common::OputStreamPtr& __oput,const MpCallSip::RtpVideoGeneralStats& __obj)
{
    __oput->write(__obj.captureResolution);
    __oput->write(__obj.captureFrameRate);
    __oput->write(__obj.renderFrameRate);
    __oput->write(__obj.rtt);
    __oput->write(__obj.networkStatus);
}

void __read_RtpVideoGeneralStats(const Common::IputStreamPtr& __iput,MpCallSip::RtpVideoGeneralStats& __obj)
{
    __iput->read(__obj.captureResolution);
    __iput->read(__obj.captureFrameRate);
    __iput->read(__obj.renderFrameRate);
    __iput->read(__obj.rtt);
    __iput->read(__obj.networkStatus);
}

void __textWrite_RtpVideoGeneralStats(const Common::OputStreamPtr& __oput,const Common::String& __name,const MpCallSip::RtpVideoGeneralStats& __obj)
{
    __oput->textStart(__name);
    __oput->textWrite("captureResolution",__obj.captureResolution);
    __oput->textWrite("captureFrameRate",__obj.captureFrameRate);
    __oput->textWrite("renderFrameRate",__obj.renderFrameRate);
    __oput->textWrite("rtt",__obj.rtt);
    __oput->textWrite("networkStatus",__obj.networkStatus);
    __oput->textEnd();
}

bool __textRead_RtpVideoGeneralStats(const Common::IputStreamPtr& __iput,const Common::String& __name,MpCallSip::RtpVideoGeneralStats& __obj,int __idx)
{
    if (!__iput->textStart(__name,__idx)) return false;
    __iput->textRead("captureResolution",__obj.captureResolution,0);
    __iput->textRead("captureFrameRate",__obj.captureFrameRate,0);
    __iput->textRead("renderFrameRate",__obj.renderFrameRate,0);
    __iput->textRead("rtt",__obj.rtt,0);
    __iput->textRead("networkStatus",__obj.networkStatus,0);
    __iput->textEnd();
    return true;
}

RtpVideoConfigStatus::RtpVideoConfigStatus() :
    localPort(0),
    remotePort(0),
    payload(0),
    bitrate(0),
    frameRate(0)
{
}

RtpVideoConfigStatus::RtpVideoConfigStatus(const Common::String& x_localIp,int x_localPort,const Common::String& x_remoteIp,int x_remotePort,const Common::String& x_codec,int x_payload,int x_bitrate,int x_frameRate,const Common::String& x_resolution) :
    localIp(x_localIp),localPort(x_localPort),remoteIp(x_remoteIp),remotePort(x_remotePort),codec(x_codec),payload(x_payload),bitrate(x_bitrate),frameRate(x_frameRate),resolution(x_resolution)
{
}

bool RtpVideoConfigStatus::operator<(const RtpVideoConfigStatus&__obj) const
{
    if (this == &__obj) return false;
    if (localIp < __obj.localIp) return true;
    if (__obj.localIp < localIp) return false;
    if (localPort < __obj.localPort) return true;
    if (__obj.localPort < localPort) return false;
    if (remoteIp < __obj.remoteIp) return true;
    if (__obj.remoteIp < remoteIp) return false;
    if (remotePort < __obj.remotePort) return true;
    if (__obj.remotePort < remotePort) return false;
    if (codec < __obj.codec) return true;
    if (__obj.codec < codec) return false;
    if (payload < __obj.payload) return true;
    if (__obj.payload < payload) return false;
    if (bitrate < __obj.bitrate) return true;
    if (__obj.bitrate < bitrate) return false;
    if (frameRate < __obj.frameRate) return true;
    if (__obj.frameRate < frameRate) return false;
    if (resolution < __obj.resolution) return true;
    if (__obj.resolution < resolution) return false;
    return false;
}

bool RtpVideoConfigStatus::operator==(const RtpVideoConfigStatus&__obj) const
{
    if (this == &__obj) return true;
    if (localIp != __obj.localIp) return false;
    if (localPort != __obj.localPort) return false;
    if (remoteIp != __obj.remoteIp) return false;
    if (remotePort != __obj.remotePort) return false;
    if (codec != __obj.codec) return false;
    if (payload != __obj.payload) return false;
    if (bitrate != __obj.bitrate) return false;
    if (frameRate != __obj.frameRate) return false;
    if (resolution != __obj.resolution) return false;
    return true;
}

void RtpVideoConfigStatus::__write(const Common::OputStreamPtr& __oput) const
{
    __write_RtpVideoConfigStatus(__oput,*this);
}

void RtpVideoConfigStatus::__read(const Common::IputStreamPtr& __iput)
{
    __read_RtpVideoConfigStatus(__iput,*this);
}

void RtpVideoConfigStatus::__textWrite(const Common::OputStreamPtr& __oput,const Common::String& __name) const
{
    __textWrite_RtpVideoConfigStatus(__oput,__name,*this);
}

bool RtpVideoConfigStatus::__textRead(const Common::IputStreamPtr& __iput,const Common::String& __name,int __idx)
{
    return __textRead_RtpVideoConfigStatus(__iput,__name,*this,__idx);
}

void __write_RtpVideoConfigStatus(const Common::OputStreamPtr& __oput,const MpCallSip::RtpVideoConfigStatus& __obj)
{
    __oput->write(__obj.localIp);
    __oput->write(__obj.localPort);
    __oput->write(__obj.remoteIp);
    __oput->write(__obj.remotePort);
    __oput->write(__obj.codec);
    __oput->write(__obj.payload);
    __oput->write(__obj.bitrate);
    __oput->write(__obj.frameRate);
    __oput->write(__obj.resolution);
}

void __read_RtpVideoConfigStatus(const Common::IputStreamPtr& __iput,MpCallSip::RtpVideoConfigStatus& __obj)
{
    __iput->read(__obj.localIp);
    __iput->read(__obj.localPort);
    __iput->read(__obj.remoteIp);
    __iput->read(__obj.remotePort);
    __iput->read(__obj.codec);
    __iput->read(__obj.payload);
    __iput->read(__obj.bitrate);
    __iput->read(__obj.frameRate);
    __iput->read(__obj.resolution);
}

void __textWrite_RtpVideoConfigStatus(const Common::OputStreamPtr& __oput,const Common::String& __name,const MpCallSip::RtpVideoConfigStatus& __obj)
{
    __oput->textStart(__name);
    __oput->textWrite("localIp",__obj.localIp);
    __oput->textWrite("localPort",__obj.localPort);
    __oput->textWrite("remoteIp",__obj.remoteIp);
    __oput->textWrite("remotePort",__obj.remotePort);
    __oput->textWrite("codec",__obj.codec);
    __oput->textWrite("payload",__obj.payload);
    __oput->textWrite("bitrate",__obj.bitrate);
    __oput->textWrite("frameRate",__obj.frameRate);
    __oput->textWrite("resolution",__obj.resolution);
    __oput->textEnd();
}

bool __textRead_RtpVideoConfigStatus(const Common::IputStreamPtr& __iput,const Common::String& __name,MpCallSip::RtpVideoConfigStatus& __obj,int __idx)
{
    if (!__iput->textStart(__name,__idx)) return false;
    __iput->textRead("localIp",__obj.localIp,0);
    __iput->textRead("localPort",__obj.localPort,0);
    __iput->textRead("remoteIp",__obj.remoteIp,0);
    __iput->textRead("remotePort",__obj.remotePort,0);
    __iput->textRead("codec",__obj.codec,0);
    __iput->textRead("payload",__obj.payload,0);
    __iput->textRead("bitrate",__obj.bitrate,0);
    __iput->textRead("frameRate",__obj.frameRate,0);
    __iput->textRead("resolution",__obj.resolution,0);
    __iput->textEnd();
    return true;
}

RtpVideoStats::RtpVideoStats()
{
}

RtpVideoStats::RtpVideoStats(const MpCallSip::RtpVideoSendStats& x_send,const MpCallSip::RtpVideoRecvStats& x_recv,const MpCallSip::RtpVideoGeneralStats& x_general,const MpCallSip::RtpVideoConfigStatus& x_config) :
    send(x_send),recv(x_recv),general(x_general),config(x_config)
{
}

bool RtpVideoStats::operator<(const RtpVideoStats&__obj) const
{
    if (this == &__obj) return false;
    if (send < __obj.send) return true;
    if (__obj.send < send) return false;
    if (recv < __obj.recv) return true;
    if (__obj.recv < recv) return false;
    if (general < __obj.general) return true;
    if (__obj.general < general) return false;
    if (config < __obj.config) return true;
    if (__obj.config < config) return false;
    return false;
}

bool RtpVideoStats::operator==(const RtpVideoStats&__obj) const
{
    if (this == &__obj) return true;
    if (send != __obj.send) return false;
    if (recv != __obj.recv) return false;
    if (general != __obj.general) return false;
    if (config != __obj.config) return false;
    return true;
}

void RtpVideoStats::__write(const Common::OputStreamPtr& __oput) const
{
    __write_RtpVideoStats(__oput,*this);
}

void RtpVideoStats::__read(const Common::IputStreamPtr& __iput)
{
    __read_RtpVideoStats(__iput,*this);
}

void RtpVideoStats::__textWrite(const Common::OputStreamPtr& __oput,const Common::String& __name) const
{
    __textWrite_RtpVideoStats(__oput,__name,*this);
}

bool RtpVideoStats::__textRead(const Common::IputStreamPtr& __iput,const Common::String& __name,int __idx)
{
    return __textRead_RtpVideoStats(__iput,__name,*this,__idx);
}

void __write_RtpVideoStats(const Common::OputStreamPtr& __oput,const MpCallSip::RtpVideoStats& __obj)
{
    MpCallSip::__write_RtpVideoSendStats(__oput,__obj.send);
    MpCallSip::__write_RtpVideoRecvStats(__oput,__obj.recv);
    MpCallSip::__write_RtpVideoGeneralStats(__oput,__obj.general);
    MpCallSip::__write_RtpVideoConfigStatus(__oput,__obj.config);
}

void __read_RtpVideoStats(const Common::IputStreamPtr& __iput,MpCallSip::RtpVideoStats& __obj)
{
    MpCallSip::__read_RtpVideoSendStats(__iput,__obj.send);
    MpCallSip::__read_RtpVideoRecvStats(__iput,__obj.recv);
    MpCallSip::__read_RtpVideoGeneralStats(__iput,__obj.general);
    MpCallSip::__read_RtpVideoConfigStatus(__iput,__obj.config);
}

void __textWrite_RtpVideoStats(const Common::OputStreamPtr& __oput,const Common::String& __name,const MpCallSip::RtpVideoStats& __obj)
{
    __oput->textStart(__name);
    MpCallSip::__textWrite_RtpVideoSendStats(__oput,"send",__obj.send);
    MpCallSip::__textWrite_RtpVideoRecvStats(__oput,"recv",__obj.recv);
    MpCallSip::__textWrite_RtpVideoGeneralStats(__oput,"general",__obj.general);
    MpCallSip::__textWrite_RtpVideoConfigStatus(__oput,"config",__obj.config);
    __oput->textEnd();
}

bool __textRead_RtpVideoStats(const Common::IputStreamPtr& __iput,const Common::String& __name,MpCallSip::RtpVideoStats& __obj,int __idx)
{
    if (!__iput->textStart(__name,__idx)) return false;
    MpCallSip::__textRead_RtpVideoSendStats(__iput,"send",__obj.send,0);
    MpCallSip::__textRead_RtpVideoRecvStats(__iput,"recv",__obj.recv,0);
    MpCallSip::__textRead_RtpVideoGeneralStats(__iput,"general",__obj.general,0);
    MpCallSip::__textRead_RtpVideoConfigStatus(__iput,"config",__obj.config,0);
    __iput->textEnd();
    return true;
}

RtpStats::RtpStats()
{
}

RtpStats::RtpStats(const MpCallSip::RtpAudioStats& x_audio,const MpCallSip::RtpVideoStats& x_video) :
    audio(x_audio),video(x_video)
{
}

bool RtpStats::operator<(const RtpStats&__obj) const
{
    if (this == &__obj) return false;
    if (audio < __obj.audio) return true;
    if (__obj.audio < audio) return false;
    if (video < __obj.video) return true;
    if (__obj.video < video) return false;
    return false;
}

bool RtpStats::operator==(const RtpStats&__obj) const
{
    if (this == &__obj) return true;
    if (audio != __obj.audio) return false;
    if (video != __obj.video) return false;
    return true;
}

void RtpStats::__write(const Common::OputStreamPtr& __oput) const
{
    __write_RtpStats(__oput,*this);
}

void RtpStats::__read(const Common::IputStreamPtr& __iput)
{
    __read_RtpStats(__iput,*this);
}

void RtpStats::__textWrite(const Common::OputStreamPtr& __oput,const Common::String& __name) const
{
    __textWrite_RtpStats(__oput,__name,*this);
}

bool RtpStats::__textRead(const Common::IputStreamPtr& __iput,const Common::String& __name,int __idx)
{
    return __textRead_RtpStats(__iput,__name,*this,__idx);
}

void __write_RtpStats(const Common::OputStreamPtr& __oput,const MpCallSip::RtpStats& __obj)
{
    MpCallSip::__write_RtpAudioStats(__oput,__obj.audio);
    MpCallSip::__write_RtpVideoStats(__oput,__obj.video);
}

void __read_RtpStats(const Common::IputStreamPtr& __iput,MpCallSip::RtpStats& __obj)
{
    MpCallSip::__read_RtpAudioStats(__iput,__obj.audio);
    MpCallSip::__read_RtpVideoStats(__iput,__obj.video);
}

void __textWrite_RtpStats(const Common::OputStreamPtr& __oput,const Common::String& __name,const MpCallSip::RtpStats& __obj)
{
    __oput->textStart(__name);
    MpCallSip::__textWrite_RtpAudioStats(__oput,"audio",__obj.audio);
    MpCallSip::__textWrite_RtpVideoStats(__oput,"video",__obj.video);
    __oput->textEnd();
}

bool __textRead_RtpStats(const Common::IputStreamPtr& __iput,const Common::String& __name,MpCallSip::RtpStats& __obj,int __idx)
{
    if (!__iput->textStart(__name,__idx)) return false;
    MpCallSip::__textRead_RtpAudioStats(__iput,"audio",__obj.audio,0);
    MpCallSip::__textRead_RtpVideoStats(__iput,"video",__obj.video,0);
    __iput->textEnd();
    return true;
}

};//namespace: MpCallSip
