﻿//
// *****************************************************************************
// Copyright(c) 2017-2025 Juphoon System Software Co., LTD. All rights reserved.
// *****************************************************************************
//
// Auto generated from: MpCallRtp.def
// Warning: do not edit this file.
//

#ifndef __MpCallSip_MpCallRtpServer_h
#define __MpCallSip_MpCallRtpServer_h

#include "MpCallSip/MpCallRtpPub.h"

namespace MpCallSip
{

class RtpRunnerMonitorServer : virtual public Common::ObjectServer
{
public:
    virtual bool __ex(const Common::ServerCallPtr& __call,const Common::String& __cmd,const Common::IputStreamPtr& __iput);

    virtual void onError(const Common::ServerCallPtr& __call,const Common::String& reason) = 0;
    virtual void onNetworkStatusChanged(const Common::ServerCallPtr& __call,const Common::StrIntMap& networkStatus) = 0;

    static void onError_end(const Common::ServerCallPtr& __call);
    static void onNetworkStatusChanged_end(const Common::ServerCallPtr& __call);

    static inline void onError_end(const Common::ServerCallPtr &__call,const Common::CallError &error)
    {
        __call->setError(error);
        onError_end(__call);
    }
    static inline void onNetworkStatusChanged_end(const Common::ServerCallPtr &__call,const Common::CallError &error)
    {
        __call->setError(error);
        onNetworkStatusChanged_end(__call);
    }

private:
    void __cmd_onError(const Common::ServerCallPtr& __call,const Common::IputStreamPtr& __iput);
    void __cmd_onNetworkStatusChanged(const Common::ServerCallPtr& __call,const Common::IputStreamPtr& __iput);
};

class RtpRunnerServer : virtual public Common::ObjectServer
{
public:
    virtual bool __ex(const Common::ServerCallPtr& __call,const Common::String& __cmd,const Common::IputStreamPtr& __iput);

    virtual void callout_begin(const Common::ServerCallPtr& __call,const Common::String& targetOid,const Common::String& monitorOid,const MpCallSip::RtpRunnerConfig& config,const MpCallSip::SdpParams& sdpParams) = 0;
    virtual void callin_begin(const Common::ServerCallPtr& __call,const Common::String& targetOid,const Common::String& monitorOid,const MpCallSip::RtpRunnerConfig& config,const Common::String& peerSdp) = 0;
    virtual void callinAndOffer_begin(const Common::ServerCallPtr& __call,const Common::String& targetOid,const Common::String& monitorOid,const MpCallSip::RtpRunnerConfig& config,const MpCallSip::SdpParams& sdpParams) = 0;
    virtual void callinAndAnswer_begin(const Common::ServerCallPtr& __call,const Common::String& targetOid,const Common::String& monitorOid,const MpCallSip::RtpRunnerConfig& config,const Common::String& peerSdp,const MpCallSip::SdpParams& sdpParams) = 0;
    virtual bool close(const Common::ServerCallPtr& __call) = 0;
    virtual bool genOffer(const Common::ServerCallPtr& __call,const MpCallSip::SdpParams& sdpParams,Common::String& offerSdp) = 0;
    virtual bool setOffer(const Common::ServerCallPtr& __call,const Common::String& offerSdp) = 0;
    virtual bool setOfferAndAnswer(const Common::ServerCallPtr& __call,const Common::String& offerSdp,const MpCallSip::SdpParams& sdpParams,Common::String& answerSdp) = 0;
    virtual bool genAnswer(const Common::ServerCallPtr& __call,const MpCallSip::SdpParams& sdpParams,Common::String& answerSdp) = 0;
    virtual bool setAnswer(const Common::ServerCallPtr& __call,const Common::String& answerSdp) = 0;
    virtual bool setTalking(const Common::ServerCallPtr& __call,bool talking) = 0;
    virtual bool getRtpStatus(const Common::ServerCallPtr& __call,MpCallSip::RtpStats& stats) = 0;

    static void callout_end(const Common::ServerCallPtr& __call,bool __ret,const Common::String& offerSdp);
    static void callin_end(const Common::ServerCallPtr& __call,bool __ret);
    static void callinAndOffer_end(const Common::ServerCallPtr& __call,bool __ret,const Common::String& localSdp);
    static void callinAndAnswer_end(const Common::ServerCallPtr& __call,bool __ret,const Common::String& localSdp);
    static void close_end(const Common::ServerCallPtr& __call,bool __ret);
    static void genOffer_end(const Common::ServerCallPtr& __call,bool __ret,const Common::String& offerSdp);
    static void setOffer_end(const Common::ServerCallPtr& __call,bool __ret);
    static void setOfferAndAnswer_end(const Common::ServerCallPtr& __call,bool __ret,const Common::String& answerSdp);
    static void genAnswer_end(const Common::ServerCallPtr& __call,bool __ret,const Common::String& answerSdp);
    static void setAnswer_end(const Common::ServerCallPtr& __call,bool __ret);
    static void setTalking_end(const Common::ServerCallPtr& __call,bool __ret);
    static void getRtpStatus_end(const Common::ServerCallPtr& __call,bool __ret,const MpCallSip::RtpStats& stats);

    static inline void callout_end(const Common::ServerCallPtr &__call,const Common::CallError &error)
    {
        __call->setError(error);
        callout_end(__call,false,"");
    }
    static inline void callin_end(const Common::ServerCallPtr &__call,const Common::CallError &error)
    {
        __call->setError(error);
        callin_end(__call,false);
    }
    static inline void callinAndOffer_end(const Common::ServerCallPtr &__call,const Common::CallError &error)
    {
        __call->setError(error);
        callinAndOffer_end(__call,false,"");
    }
    static inline void callinAndAnswer_end(const Common::ServerCallPtr &__call,const Common::CallError &error)
    {
        __call->setError(error);
        callinAndAnswer_end(__call,false,"");
    }
    static inline void close_end(const Common::ServerCallPtr &__call,const Common::CallError &error)
    {
        __call->setError(error);
        close_end(__call,false);
    }
    static inline void genOffer_end(const Common::ServerCallPtr &__call,const Common::CallError &error)
    {
        __call->setError(error);
        genOffer_end(__call,false,"");
    }
    static inline void setOffer_end(const Common::ServerCallPtr &__call,const Common::CallError &error)
    {
        __call->setError(error);
        setOffer_end(__call,false);
    }
    static inline void setOfferAndAnswer_end(const Common::ServerCallPtr &__call,const Common::CallError &error)
    {
        __call->setError(error);
        setOfferAndAnswer_end(__call,false,"");
    }
    static inline void genAnswer_end(const Common::ServerCallPtr &__call,const Common::CallError &error)
    {
        __call->setError(error);
        genAnswer_end(__call,false,"");
    }
    static inline void setAnswer_end(const Common::ServerCallPtr &__call,const Common::CallError &error)
    {
        __call->setError(error);
        setAnswer_end(__call,false);
    }
    static inline void setTalking_end(const Common::ServerCallPtr &__call,const Common::CallError &error)
    {
        __call->setError(error);
        setTalking_end(__call,false);
    }
    static inline void getRtpStatus_end(const Common::ServerCallPtr &__call,const Common::CallError &error)
    {
        __call->setError(error);
        getRtpStatus_end(__call,false,MpCallSip::RtpStats());
    }

private:
    void __cmd_callout(const Common::ServerCallPtr& __call,const Common::IputStreamPtr& __iput);
    void __cmd_callin(const Common::ServerCallPtr& __call,const Common::IputStreamPtr& __iput);
    void __cmd_callinAndOffer(const Common::ServerCallPtr& __call,const Common::IputStreamPtr& __iput);
    void __cmd_callinAndAnswer(const Common::ServerCallPtr& __call,const Common::IputStreamPtr& __iput);
    void __cmd_close(const Common::ServerCallPtr& __call,const Common::IputStreamPtr& __iput);
    void __cmd_genOffer(const Common::ServerCallPtr& __call,const Common::IputStreamPtr& __iput);
    void __cmd_setOffer(const Common::ServerCallPtr& __call,const Common::IputStreamPtr& __iput);
    void __cmd_setOfferAndAnswer(const Common::ServerCallPtr& __call,const Common::IputStreamPtr& __iput);
    void __cmd_genAnswer(const Common::ServerCallPtr& __call,const Common::IputStreamPtr& __iput);
    void __cmd_setAnswer(const Common::ServerCallPtr& __call,const Common::IputStreamPtr& __iput);
    void __cmd_setTalking(const Common::ServerCallPtr& __call,const Common::IputStreamPtr& __iput);
    void __cmd_getRtpStatus(const Common::ServerCallPtr& __call,const Common::IputStreamPtr& __iput);
};

};//namespace: MpCallSip

#endif //__MpCallSip_MpCallRtpServer_h
