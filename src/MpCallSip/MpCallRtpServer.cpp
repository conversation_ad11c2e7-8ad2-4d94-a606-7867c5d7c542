﻿//
// *****************************************************************************
// Copyright(c) 2017-2025 Juphoon System Software Co., LTD. All rights reserved.
// *****************************************************************************
//
// Auto generated from: MpCallRtp.def
// Warning: do not edit this file.
//

#include "MpCallSip/MpCallRtpServer.h"

namespace MpCallSip
{

bool RtpRunnerMonitorServer::__ex(const Common::ServerCallPtr& __call,const Common::String& __cmd,const Common::IputStreamPtr& __iput)
{
    if (__cmd == "onError.RtpRunnerMonitor.MpCallSip") { __cmd_onError(__call,__iput);return true;}
    if (__cmd == "onNetworkStatusChanged.RtpRunnerMonitor.MpCallSip") { __cmd_onNetworkStatusChanged(__call,__iput);return true;}
    return false;
}

void RtpRunnerMonitorServer::onError_end(const Common::ServerCallPtr& __call)
{
    Common::VerListPtr __vers = __call->verList();
    Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
    short __ver = (__vers)?__vers->ver(true):0;
    __call->cmdResult(__ver,__oput);
}

void RtpRunnerMonitorServer::onNetworkStatusChanged_end(const Common::ServerCallPtr& __call)
{
    Common::VerListPtr __vers = __call->verList();
    Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
    short __ver = (__vers)?__vers->ver(true):0;
    __call->cmdResult(__ver,__oput);
}

void RtpRunnerMonitorServer::__cmd_onError(const Common::ServerCallPtr& __call,const Common::IputStreamPtr& __iput)
{
    do
    {
        Common::VerListPtr __vers = __call->verList();
        Common::String reason;
        switch (__vers->ver(false))
        {
        case 0:
            __iput->read(reason);
            break;
        default: goto __ver_err;
        }
        __start(false);
        onError(__call,reason);
        onError_end(__call);
        return;
    } while(0);
__ver_err:
    Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
    __oput->write((short)1);
    __oput->write((short)0);
    __call->cmdResult(VersionException<<16,__oput);
}

void RtpRunnerMonitorServer::__cmd_onNetworkStatusChanged(const Common::ServerCallPtr& __call,const Common::IputStreamPtr& __iput)
{
    do
    {
        Common::VerListPtr __vers = __call->verList();
        Common::StrIntMap networkStatus;
        switch (__vers->ver(false))
        {
        case 0:
            Common::__read_StrIntMap(__iput,networkStatus);
            break;
        default: goto __ver_err;
        }
        __start(false);
        onNetworkStatusChanged(__call,networkStatus);
        onNetworkStatusChanged_end(__call);
        return;
    } while(0);
__ver_err:
    Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
    __oput->write((short)1);
    __oput->write((short)0);
    __call->cmdResult(VersionException<<16,__oput);
}

bool RtpRunnerServer::__ex(const Common::ServerCallPtr& __call,const Common::String& __cmd,const Common::IputStreamPtr& __iput)
{
    if (__cmd == "callout.RtpRunner.MpCallSip") { __cmd_callout(__call,__iput);return true;}
    if (__cmd == "callin.RtpRunner.MpCallSip") { __cmd_callin(__call,__iput);return true;}
    if (__cmd == "callinAndOffer.RtpRunner.MpCallSip") { __cmd_callinAndOffer(__call,__iput);return true;}
    if (__cmd == "callinAndAnswer.RtpRunner.MpCallSip") { __cmd_callinAndAnswer(__call,__iput);return true;}
    if (__cmd == "close.RtpRunner.MpCallSip") { __cmd_close(__call,__iput);return true;}
    if (__cmd == "genOffer.RtpRunner.MpCallSip") { __cmd_genOffer(__call,__iput);return true;}
    if (__cmd == "setOffer.RtpRunner.MpCallSip") { __cmd_setOffer(__call,__iput);return true;}
    if (__cmd == "setOfferAndAnswer.RtpRunner.MpCallSip") { __cmd_setOfferAndAnswer(__call,__iput);return true;}
    if (__cmd == "genAnswer.RtpRunner.MpCallSip") { __cmd_genAnswer(__call,__iput);return true;}
    if (__cmd == "setAnswer.RtpRunner.MpCallSip") { __cmd_setAnswer(__call,__iput);return true;}
    if (__cmd == "setTalking.RtpRunner.MpCallSip") { __cmd_setTalking(__call,__iput);return true;}
    if (__cmd == "getRtpStatus.RtpRunner.MpCallSip") { __cmd_getRtpStatus(__call,__iput);return true;}
    return false;
}

void RtpRunnerServer::callout_end(const Common::ServerCallPtr& __call,bool __ret,const Common::String& offerSdp)
{
    Common::VerListPtr __vers = __call->verList();
    Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
    short __ver = (__vers)?__vers->ver(true):0;
    switch (__ver)
    {
    case 0:
        __oput->write(__ret);
        __oput->write(offerSdp);
        break;
    }
    __call->cmdResult(__ver,__oput);
}

void RtpRunnerServer::callin_end(const Common::ServerCallPtr& __call,bool __ret)
{
    Common::VerListPtr __vers = __call->verList();
    Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
    short __ver = (__vers)?__vers->ver(true):0;
    switch (__ver)
    {
    case 0:
        __oput->write(__ret);
        break;
    }
    __call->cmdResult(__ver,__oput);
}

void RtpRunnerServer::callinAndOffer_end(const Common::ServerCallPtr& __call,bool __ret,const Common::String& localSdp)
{
    Common::VerListPtr __vers = __call->verList();
    Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
    short __ver = (__vers)?__vers->ver(true):0;
    switch (__ver)
    {
    case 0:
        __oput->write(__ret);
        __oput->write(localSdp);
        break;
    }
    __call->cmdResult(__ver,__oput);
}

void RtpRunnerServer::callinAndAnswer_end(const Common::ServerCallPtr& __call,bool __ret,const Common::String& localSdp)
{
    Common::VerListPtr __vers = __call->verList();
    Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
    short __ver = (__vers)?__vers->ver(true):0;
    switch (__ver)
    {
    case 0:
        __oput->write(__ret);
        __oput->write(localSdp);
        break;
    }
    __call->cmdResult(__ver,__oput);
}

void RtpRunnerServer::close_end(const Common::ServerCallPtr& __call,bool __ret)
{
    Common::VerListPtr __vers = __call->verList();
    Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
    short __ver = (__vers)?__vers->ver(true):0;
    switch (__ver)
    {
    case 0:
        __oput->write(__ret);
        break;
    }
    __call->cmdResult(__ver,__oput);
}

void RtpRunnerServer::genOffer_end(const Common::ServerCallPtr& __call,bool __ret,const Common::String& offerSdp)
{
    Common::VerListPtr __vers = __call->verList();
    Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
    short __ver = (__vers)?__vers->ver(true):0;
    switch (__ver)
    {
    case 0:
        __oput->write(__ret);
        __oput->write(offerSdp);
        break;
    }
    __call->cmdResult(__ver,__oput);
}

void RtpRunnerServer::setOffer_end(const Common::ServerCallPtr& __call,bool __ret)
{
    Common::VerListPtr __vers = __call->verList();
    Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
    short __ver = (__vers)?__vers->ver(true):0;
    switch (__ver)
    {
    case 0:
        __oput->write(__ret);
        break;
    }
    __call->cmdResult(__ver,__oput);
}

void RtpRunnerServer::setOfferAndAnswer_end(const Common::ServerCallPtr& __call,bool __ret,const Common::String& answerSdp)
{
    Common::VerListPtr __vers = __call->verList();
    Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
    short __ver = (__vers)?__vers->ver(true):0;
    switch (__ver)
    {
    case 0:
        __oput->write(__ret);
        __oput->write(answerSdp);
        break;
    }
    __call->cmdResult(__ver,__oput);
}

void RtpRunnerServer::genAnswer_end(const Common::ServerCallPtr& __call,bool __ret,const Common::String& answerSdp)
{
    Common::VerListPtr __vers = __call->verList();
    Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
    short __ver = (__vers)?__vers->ver(true):0;
    switch (__ver)
    {
    case 0:
        __oput->write(__ret);
        __oput->write(answerSdp);
        break;
    }
    __call->cmdResult(__ver,__oput);
}

void RtpRunnerServer::setAnswer_end(const Common::ServerCallPtr& __call,bool __ret)
{
    Common::VerListPtr __vers = __call->verList();
    Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
    short __ver = (__vers)?__vers->ver(true):0;
    switch (__ver)
    {
    case 0:
        __oput->write(__ret);
        break;
    }
    __call->cmdResult(__ver,__oput);
}

void RtpRunnerServer::setTalking_end(const Common::ServerCallPtr& __call,bool __ret)
{
    Common::VerListPtr __vers = __call->verList();
    Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
    short __ver = (__vers)?__vers->ver(true):0;
    switch (__ver)
    {
    case 0:
        __oput->write(__ret);
        break;
    }
    __call->cmdResult(__ver,__oput);
}

void RtpRunnerServer::getRtpStatus_end(const Common::ServerCallPtr& __call,bool __ret,const MpCallSip::RtpStats& stats)
{
    Common::VerListPtr __vers = __call->verList();
    Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
    short __ver = (__vers)?__vers->ver(true):0;
    switch (__ver)
    {
    case 0:
        __oput->write(__ret);
        MpCallSip::__write_RtpStats(__oput,stats);
        break;
    }
    __call->cmdResult(__ver,__oput);
}

void RtpRunnerServer::__cmd_callout(const Common::ServerCallPtr& __call,const Common::IputStreamPtr& __iput)
{
    do
    {
        Common::VerListPtr __vers = __call->verList();
        Common::String targetOid;Common::String monitorOid;MpCallSip::RtpRunnerConfig config;MpCallSip::SdpParams sdpParams;
        switch (__vers->ver(false))
        {
        case 0:
            __iput->read(targetOid);
            __iput->read(monitorOid);
            MpCallSip::__read_RtpRunnerConfig(__iput,config);
            MpCallSip::__read_SdpParams(__iput,sdpParams);
            break;
        default: goto __ver_err;
        }
        __start(false);
        callout_begin(__call,targetOid,monitorOid,config,sdpParams);
        return;
    } while(0);
__ver_err:
    Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
    __oput->write((short)1);
    __oput->write((short)0);
    __call->cmdResult(VersionException<<16,__oput);
}

void RtpRunnerServer::__cmd_callin(const Common::ServerCallPtr& __call,const Common::IputStreamPtr& __iput)
{
    do
    {
        Common::VerListPtr __vers = __call->verList();
        Common::String targetOid;Common::String monitorOid;MpCallSip::RtpRunnerConfig config;Common::String peerSdp;
        switch (__vers->ver(false))
        {
        case 0:
            __iput->read(targetOid);
            __iput->read(monitorOid);
            MpCallSip::__read_RtpRunnerConfig(__iput,config);
            __iput->read(peerSdp);
            break;
        default: goto __ver_err;
        }
        __start(false);
        callin_begin(__call,targetOid,monitorOid,config,peerSdp);
        return;
    } while(0);
__ver_err:
    Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
    __oput->write((short)1);
    __oput->write((short)0);
    __call->cmdResult(VersionException<<16,__oput);
}

void RtpRunnerServer::__cmd_callinAndOffer(const Common::ServerCallPtr& __call,const Common::IputStreamPtr& __iput)
{
    do
    {
        Common::VerListPtr __vers = __call->verList();
        Common::String targetOid;Common::String monitorOid;MpCallSip::RtpRunnerConfig config;MpCallSip::SdpParams sdpParams;
        switch (__vers->ver(false))
        {
        case 0:
            __iput->read(targetOid);
            __iput->read(monitorOid);
            MpCallSip::__read_RtpRunnerConfig(__iput,config);
            MpCallSip::__read_SdpParams(__iput,sdpParams);
            break;
        default: goto __ver_err;
        }
        __start(false);
        callinAndOffer_begin(__call,targetOid,monitorOid,config,sdpParams);
        return;
    } while(0);
__ver_err:
    Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
    __oput->write((short)1);
    __oput->write((short)0);
    __call->cmdResult(VersionException<<16,__oput);
}

void RtpRunnerServer::__cmd_callinAndAnswer(const Common::ServerCallPtr& __call,const Common::IputStreamPtr& __iput)
{
    do
    {
        Common::VerListPtr __vers = __call->verList();
        Common::String targetOid;Common::String monitorOid;MpCallSip::RtpRunnerConfig config;Common::String peerSdp;MpCallSip::SdpParams sdpParams;
        switch (__vers->ver(false))
        {
        case 0:
            __iput->read(targetOid);
            __iput->read(monitorOid);
            MpCallSip::__read_RtpRunnerConfig(__iput,config);
            __iput->read(peerSdp);
            MpCallSip::__read_SdpParams(__iput,sdpParams);
            break;
        default: goto __ver_err;
        }
        __start(false);
        callinAndAnswer_begin(__call,targetOid,monitorOid,config,peerSdp,sdpParams);
        return;
    } while(0);
__ver_err:
    Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
    __oput->write((short)1);
    __oput->write((short)0);
    __call->cmdResult(VersionException<<16,__oput);
}

void RtpRunnerServer::__cmd_close(const Common::ServerCallPtr& __call,const Common::IputStreamPtr& __iput)
{
    do
    {
        Common::VerListPtr __vers = __call->verList();
        bool __ret;
        switch (__vers->ver(false))
        {
        case 0:
            break;
        default: goto __ver_err;
        }
        __start(false);
        __ret = close(__call);
        close_end(__call,__ret);
        return;
    } while(0);
__ver_err:
    Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
    __oput->write((short)1);
    __oput->write((short)0);
    __call->cmdResult(VersionException<<16,__oput);
}

void RtpRunnerServer::__cmd_genOffer(const Common::ServerCallPtr& __call,const Common::IputStreamPtr& __iput)
{
    do
    {
        Common::VerListPtr __vers = __call->verList();
        bool __ret;MpCallSip::SdpParams sdpParams;Common::String offerSdp;
        switch (__vers->ver(false))
        {
        case 0:
            MpCallSip::__read_SdpParams(__iput,sdpParams);
            break;
        default: goto __ver_err;
        }
        __start(false);
        __ret = genOffer(__call,sdpParams,offerSdp);
        genOffer_end(__call,__ret,offerSdp);
        return;
    } while(0);
__ver_err:
    Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
    __oput->write((short)1);
    __oput->write((short)0);
    __call->cmdResult(VersionException<<16,__oput);
}

void RtpRunnerServer::__cmd_setOffer(const Common::ServerCallPtr& __call,const Common::IputStreamPtr& __iput)
{
    do
    {
        Common::VerListPtr __vers = __call->verList();
        bool __ret;Common::String offerSdp;
        switch (__vers->ver(false))
        {
        case 0:
            __iput->read(offerSdp);
            break;
        default: goto __ver_err;
        }
        __start(false);
        __ret = setOffer(__call,offerSdp);
        setOffer_end(__call,__ret);
        return;
    } while(0);
__ver_err:
    Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
    __oput->write((short)1);
    __oput->write((short)0);
    __call->cmdResult(VersionException<<16,__oput);
}

void RtpRunnerServer::__cmd_setOfferAndAnswer(const Common::ServerCallPtr& __call,const Common::IputStreamPtr& __iput)
{
    do
    {
        Common::VerListPtr __vers = __call->verList();
        bool __ret;Common::String offerSdp;MpCallSip::SdpParams sdpParams;Common::String answerSdp;
        switch (__vers->ver(false))
        {
        case 0:
            __iput->read(offerSdp);
            MpCallSip::__read_SdpParams(__iput,sdpParams);
            break;
        default: goto __ver_err;
        }
        __start(false);
        __ret = setOfferAndAnswer(__call,offerSdp,sdpParams,answerSdp);
        setOfferAndAnswer_end(__call,__ret,answerSdp);
        return;
    } while(0);
__ver_err:
    Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
    __oput->write((short)1);
    __oput->write((short)0);
    __call->cmdResult(VersionException<<16,__oput);
}

void RtpRunnerServer::__cmd_genAnswer(const Common::ServerCallPtr& __call,const Common::IputStreamPtr& __iput)
{
    do
    {
        Common::VerListPtr __vers = __call->verList();
        bool __ret;MpCallSip::SdpParams sdpParams;Common::String answerSdp;
        switch (__vers->ver(false))
        {
        case 0:
            MpCallSip::__read_SdpParams(__iput,sdpParams);
            break;
        default: goto __ver_err;
        }
        __start(false);
        __ret = genAnswer(__call,sdpParams,answerSdp);
        genAnswer_end(__call,__ret,answerSdp);
        return;
    } while(0);
__ver_err:
    Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
    __oput->write((short)1);
    __oput->write((short)0);
    __call->cmdResult(VersionException<<16,__oput);
}

void RtpRunnerServer::__cmd_setAnswer(const Common::ServerCallPtr& __call,const Common::IputStreamPtr& __iput)
{
    do
    {
        Common::VerListPtr __vers = __call->verList();
        bool __ret;Common::String answerSdp;
        switch (__vers->ver(false))
        {
        case 0:
            __iput->read(answerSdp);
            break;
        default: goto __ver_err;
        }
        __start(false);
        __ret = setAnswer(__call,answerSdp);
        setAnswer_end(__call,__ret);
        return;
    } while(0);
__ver_err:
    Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
    __oput->write((short)1);
    __oput->write((short)0);
    __call->cmdResult(VersionException<<16,__oput);
}

void RtpRunnerServer::__cmd_setTalking(const Common::ServerCallPtr& __call,const Common::IputStreamPtr& __iput)
{
    do
    {
        Common::VerListPtr __vers = __call->verList();
        bool __ret;bool talking=false;
        switch (__vers->ver(false))
        {
        case 0:
            __iput->read(talking);
            break;
        default: goto __ver_err;
        }
        __start(false);
        __ret = setTalking(__call,talking);
        setTalking_end(__call,__ret);
        return;
    } while(0);
__ver_err:
    Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
    __oput->write((short)1);
    __oput->write((short)0);
    __call->cmdResult(VersionException<<16,__oput);
}

void RtpRunnerServer::__cmd_getRtpStatus(const Common::ServerCallPtr& __call,const Common::IputStreamPtr& __iput)
{
    do
    {
        Common::VerListPtr __vers = __call->verList();
        bool __ret;MpCallSip::RtpStats stats;
        switch (__vers->ver(false))
        {
        case 0:
            break;
        default: goto __ver_err;
        }
        __start(false);
        __ret = getRtpStatus(__call,stats);
        getRtpStatus_end(__call,__ret,stats);
        return;
    } while(0);
__ver_err:
    Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
    __oput->write((short)1);
    __oput->write((short)0);
    __call->cmdResult(VersionException<<16,__oput);
}

};//namespace: MpCallSip
