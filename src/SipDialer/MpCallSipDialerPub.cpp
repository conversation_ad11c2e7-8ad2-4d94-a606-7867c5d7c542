﻿//
// *****************************************************************************
// Copyright(c) 2017-2025 Juphoon System Software Co., LTD. All rights reserved.
// *****************************************************************************
//
// Auto generated from: src/MpCallSipGateway/dialer/MpCallSipDialer.def
// Warning: do not edit this file.
//

#include "SipDialer/MpCallSipDialerPub.h"

namespace SipDialer
{

RequestCall::RequestCall() :
    video(false),
    is3pcc(false),
    timeout(0)
{
}

RequestCall::RequestCall(const Common::String& x_caller,const Common::String& x_callee,const Common::String& x_uui,bool x_video,bool x_is3pcc,int x_timeout,const Common::String& x_callback) :
    caller(x_caller),callee(x_callee),uui(x_uui),video(x_video),is3pcc(x_is3pcc),timeout(x_timeout),callback(x_callback)
{
}

bool RequestCall::operator<(const RequestCall&__obj) const
{
    if (this == &__obj) return false;
    if (caller < __obj.caller) return true;
    if (__obj.caller < caller) return false;
    if (callee < __obj.callee) return true;
    if (__obj.callee < callee) return false;
    if (uui < __obj.uui) return true;
    if (__obj.uui < uui) return false;
    if (video < __obj.video) return true;
    if (__obj.video < video) return false;
    if (is3pcc < __obj.is3pcc) return true;
    if (__obj.is3pcc < is3pcc) return false;
    if (timeout < __obj.timeout) return true;
    if (__obj.timeout < timeout) return false;
    if (callback < __obj.callback) return true;
    if (__obj.callback < callback) return false;
    return false;
}

bool RequestCall::operator==(const RequestCall&__obj) const
{
    if (this == &__obj) return true;
    if (caller != __obj.caller) return false;
    if (callee != __obj.callee) return false;
    if (uui != __obj.uui) return false;
    if (video != __obj.video) return false;
    if (is3pcc != __obj.is3pcc) return false;
    if (timeout != __obj.timeout) return false;
    if (callback != __obj.callback) return false;
    return true;
}

void RequestCall::__write(const Common::OputStreamPtr& __oput) const
{
    __write_RequestCall(__oput,*this);
}

void RequestCall::__read(const Common::IputStreamPtr& __iput)
{
    __read_RequestCall(__iput,*this);
}

void RequestCall::__textWrite(const Common::OputStreamPtr& __oput,const Common::String& __name) const
{
    __textWrite_RequestCall(__oput,__name,*this);
}

bool RequestCall::__textRead(const Common::IputStreamPtr& __iput,const Common::String& __name,int __idx)
{
    return __textRead_RequestCall(__iput,__name,*this,__idx);
}

void __write_RequestCall(const Common::OputStreamPtr& __oput,const SipDialer::RequestCall& __obj)
{
    __oput->write(__obj.caller);
    __oput->write(__obj.callee);
    __oput->write(__obj.uui);
    __oput->write(__obj.video);
    __oput->write(__obj.is3pcc);
    __oput->write(__obj.timeout);
    __oput->write(__obj.callback);
}

void __read_RequestCall(const Common::IputStreamPtr& __iput,SipDialer::RequestCall& __obj)
{
    __iput->read(__obj.caller);
    __iput->read(__obj.callee);
    __iput->read(__obj.uui);
    __iput->read(__obj.video);
    __iput->read(__obj.is3pcc);
    __iput->read(__obj.timeout);
    __iput->read(__obj.callback);
}

void __textWrite_RequestCall(const Common::OputStreamPtr& __oput,const Common::String& __name,const SipDialer::RequestCall& __obj)
{
    __oput->textStart(__name);
    __oput->textWrite("caller",__obj.caller);
    __oput->textWrite("callee",__obj.callee);
    __oput->textWrite("uui",__obj.uui);
    __oput->textWrite("video",__obj.video);
    __oput->textWrite("is3pcc",__obj.is3pcc);
    __oput->textWrite("timeout",__obj.timeout);
    __oput->textWrite("callback",__obj.callback);
    __oput->textEnd();
}

bool __textRead_RequestCall(const Common::IputStreamPtr& __iput,const Common::String& __name,SipDialer::RequestCall& __obj,int __idx)
{
    if (!__iput->textStart(__name,__idx)) return false;
    __iput->textRead("caller",__obj.caller,0);
    __iput->textRead("callee",__obj.callee,0);
    __iput->textRead("uui",__obj.uui,0);
    __iput->textRead("video",__obj.video,0);
    __iput->textRead("is3pcc",__obj.is3pcc,0);
    __iput->textRead("timeout",__obj.timeout,0);
    __iput->textRead("callback",__obj.callback,0);
    __iput->textEnd();
    return true;
}

ResponseCall::ResponseCall() :
    sessionId(0)
{
}

ResponseCall::ResponseCall(int x_sessionId,const Common::String& x_callId) :
    sessionId(x_sessionId),callId(x_callId)
{
}

bool ResponseCall::operator<(const ResponseCall&__obj) const
{
    if (this == &__obj) return false;
    if (sessionId < __obj.sessionId) return true;
    if (__obj.sessionId < sessionId) return false;
    if (callId < __obj.callId) return true;
    if (__obj.callId < callId) return false;
    return false;
}

bool ResponseCall::operator==(const ResponseCall&__obj) const
{
    if (this == &__obj) return true;
    if (sessionId != __obj.sessionId) return false;
    if (callId != __obj.callId) return false;
    return true;
}

void ResponseCall::__write(const Common::OputStreamPtr& __oput) const
{
    __write_ResponseCall(__oput,*this);
}

void ResponseCall::__read(const Common::IputStreamPtr& __iput)
{
    __read_ResponseCall(__iput,*this);
}

void ResponseCall::__textWrite(const Common::OputStreamPtr& __oput,const Common::String& __name) const
{
    __textWrite_ResponseCall(__oput,__name,*this);
}

bool ResponseCall::__textRead(const Common::IputStreamPtr& __iput,const Common::String& __name,int __idx)
{
    return __textRead_ResponseCall(__iput,__name,*this,__idx);
}

void __write_ResponseCall(const Common::OputStreamPtr& __oput,const SipDialer::ResponseCall& __obj)
{
    __oput->write(__obj.sessionId);
    __oput->write(__obj.callId);
}

void __read_ResponseCall(const Common::IputStreamPtr& __iput,SipDialer::ResponseCall& __obj)
{
    __iput->read(__obj.sessionId);
    __iput->read(__obj.callId);
}

void __textWrite_ResponseCall(const Common::OputStreamPtr& __oput,const Common::String& __name,const SipDialer::ResponseCall& __obj)
{
    __oput->textStart(__name);
    __oput->textWrite("sessionId",__obj.sessionId);
    __oput->textWrite("callId",__obj.callId);
    __oput->textEnd();
}

bool __textRead_ResponseCall(const Common::IputStreamPtr& __iput,const Common::String& __name,SipDialer::ResponseCall& __obj,int __idx)
{
    if (!__iput->textStart(__name,__idx)) return false;
    __iput->textRead("sessionId",__obj.sessionId,0);
    __iput->textRead("callId",__obj.callId,0);
    __iput->textEnd();
    return true;
}

ResponseTerminated::ResponseTerminated() :
    sessionId(0),
    error(false)
{
}

ResponseTerminated::ResponseTerminated(int x_sessionId,bool x_error,const Common::String& x_reason) :
    sessionId(x_sessionId),error(x_error),reason(x_reason)
{
}

bool ResponseTerminated::operator<(const ResponseTerminated&__obj) const
{
    if (this == &__obj) return false;
    if (sessionId < __obj.sessionId) return true;
    if (__obj.sessionId < sessionId) return false;
    if (error < __obj.error) return true;
    if (__obj.error < error) return false;
    if (reason < __obj.reason) return true;
    if (__obj.reason < reason) return false;
    return false;
}

bool ResponseTerminated::operator==(const ResponseTerminated&__obj) const
{
    if (this == &__obj) return true;
    if (sessionId != __obj.sessionId) return false;
    if (error != __obj.error) return false;
    if (reason != __obj.reason) return false;
    return true;
}

void ResponseTerminated::__write(const Common::OputStreamPtr& __oput) const
{
    __write_ResponseTerminated(__oput,*this);
}

void ResponseTerminated::__read(const Common::IputStreamPtr& __iput)
{
    __read_ResponseTerminated(__iput,*this);
}

void ResponseTerminated::__textWrite(const Common::OputStreamPtr& __oput,const Common::String& __name) const
{
    __textWrite_ResponseTerminated(__oput,__name,*this);
}

bool ResponseTerminated::__textRead(const Common::IputStreamPtr& __iput,const Common::String& __name,int __idx)
{
    return __textRead_ResponseTerminated(__iput,__name,*this,__idx);
}

void __write_ResponseTerminated(const Common::OputStreamPtr& __oput,const SipDialer::ResponseTerminated& __obj)
{
    __oput->write(__obj.sessionId);
    __oput->write(__obj.error);
    __oput->write(__obj.reason);
}

void __read_ResponseTerminated(const Common::IputStreamPtr& __iput,SipDialer::ResponseTerminated& __obj)
{
    __iput->read(__obj.sessionId);
    __iput->read(__obj.error);
    __iput->read(__obj.reason);
}

void __textWrite_ResponseTerminated(const Common::OputStreamPtr& __oput,const Common::String& __name,const SipDialer::ResponseTerminated& __obj)
{
    __oput->textStart(__name);
    __oput->textWrite("sessionId",__obj.sessionId);
    __oput->textWrite("error",__obj.error);
    __oput->textWrite("reason",__obj.reason);
    __oput->textEnd();
}

bool __textRead_ResponseTerminated(const Common::IputStreamPtr& __iput,const Common::String& __name,SipDialer::ResponseTerminated& __obj,int __idx)
{
    if (!__iput->textStart(__name,__idx)) return false;
    __iput->textRead("sessionId",__obj.sessionId,0);
    __iput->textRead("error",__obj.error,0);
    __iput->textRead("reason",__obj.reason,0);
    __iput->textEnd();
    return true;
}

SessionBriefStatus::SessionBriefStatus() :
    sessionId(0),
    duration(0),
    error(false)
{
}

SessionBriefStatus::SessionBriefStatus(int x_sessionId,const Common::String& x_caller,const Common::String& x_callee,const Common::String& x_callId,const Common::String& x_uui,const Common::String& x_direction,const Common::String& x_state,const Common::String& x_startTime,int x_duration,bool x_error,const Common::String& x_reason) :
    sessionId(x_sessionId),caller(x_caller),callee(x_callee),callId(x_callId),uui(x_uui),direction(x_direction),state(x_state),startTime(x_startTime),duration(x_duration),error(x_error),reason(x_reason)
{
}

bool SessionBriefStatus::operator<(const SessionBriefStatus&__obj) const
{
    if (this == &__obj) return false;
    if (sessionId < __obj.sessionId) return true;
    if (__obj.sessionId < sessionId) return false;
    if (caller < __obj.caller) return true;
    if (__obj.caller < caller) return false;
    if (callee < __obj.callee) return true;
    if (__obj.callee < callee) return false;
    if (callId < __obj.callId) return true;
    if (__obj.callId < callId) return false;
    if (uui < __obj.uui) return true;
    if (__obj.uui < uui) return false;
    if (direction < __obj.direction) return true;
    if (__obj.direction < direction) return false;
    if (state < __obj.state) return true;
    if (__obj.state < state) return false;
    if (startTime < __obj.startTime) return true;
    if (__obj.startTime < startTime) return false;
    if (duration < __obj.duration) return true;
    if (__obj.duration < duration) return false;
    if (error < __obj.error) return true;
    if (__obj.error < error) return false;
    if (reason < __obj.reason) return true;
    if (__obj.reason < reason) return false;
    return false;
}

bool SessionBriefStatus::operator==(const SessionBriefStatus&__obj) const
{
    if (this == &__obj) return true;
    if (sessionId != __obj.sessionId) return false;
    if (caller != __obj.caller) return false;
    if (callee != __obj.callee) return false;
    if (callId != __obj.callId) return false;
    if (uui != __obj.uui) return false;
    if (direction != __obj.direction) return false;
    if (state != __obj.state) return false;
    if (startTime != __obj.startTime) return false;
    if (duration != __obj.duration) return false;
    if (error != __obj.error) return false;
    if (reason != __obj.reason) return false;
    return true;
}

void SessionBriefStatus::__write(const Common::OputStreamPtr& __oput) const
{
    __write_SessionBriefStatus(__oput,*this);
}

void SessionBriefStatus::__read(const Common::IputStreamPtr& __iput)
{
    __read_SessionBriefStatus(__iput,*this);
}

void SessionBriefStatus::__textWrite(const Common::OputStreamPtr& __oput,const Common::String& __name) const
{
    __textWrite_SessionBriefStatus(__oput,__name,*this);
}

bool SessionBriefStatus::__textRead(const Common::IputStreamPtr& __iput,const Common::String& __name,int __idx)
{
    return __textRead_SessionBriefStatus(__iput,__name,*this,__idx);
}

void __write_SessionBriefStatus(const Common::OputStreamPtr& __oput,const SipDialer::SessionBriefStatus& __obj)
{
    __oput->write(__obj.sessionId);
    __oput->write(__obj.caller);
    __oput->write(__obj.callee);
    __oput->write(__obj.callId);
    __oput->write(__obj.uui);
    __oput->write(__obj.direction);
    __oput->write(__obj.state);
    __oput->write(__obj.startTime);
    __oput->write(__obj.duration);
    __oput->write(__obj.error);
    __oput->write(__obj.reason);
}

void __read_SessionBriefStatus(const Common::IputStreamPtr& __iput,SipDialer::SessionBriefStatus& __obj)
{
    __iput->read(__obj.sessionId);
    __iput->read(__obj.caller);
    __iput->read(__obj.callee);
    __iput->read(__obj.callId);
    __iput->read(__obj.uui);
    __iput->read(__obj.direction);
    __iput->read(__obj.state);
    __iput->read(__obj.startTime);
    __iput->read(__obj.duration);
    __iput->read(__obj.error);
    __iput->read(__obj.reason);
}

void __textWrite_SessionBriefStatus(const Common::OputStreamPtr& __oput,const Common::String& __name,const SipDialer::SessionBriefStatus& __obj)
{
    __oput->textStart(__name);
    __oput->textWrite("sessionId",__obj.sessionId);
    __oput->textWrite("caller",__obj.caller);
    __oput->textWrite("callee",__obj.callee);
    __oput->textWrite("callId",__obj.callId);
    __oput->textWrite("uui",__obj.uui);
    __oput->textWrite("direction",__obj.direction);
    __oput->textWrite("state",__obj.state);
    __oput->textWrite("startTime",__obj.startTime);
    __oput->textWrite("duration",__obj.duration);
    __oput->textWrite("error",__obj.error);
    __oput->textWrite("reason",__obj.reason);
    __oput->textEnd();
}

bool __textRead_SessionBriefStatus(const Common::IputStreamPtr& __iput,const Common::String& __name,SipDialer::SessionBriefStatus& __obj,int __idx)
{
    if (!__iput->textStart(__name,__idx)) return false;
    __iput->textRead("sessionId",__obj.sessionId,0);
    __iput->textRead("caller",__obj.caller,0);
    __iput->textRead("callee",__obj.callee,0);
    __iput->textRead("callId",__obj.callId,0);
    __iput->textRead("uui",__obj.uui,0);
    __iput->textRead("direction",__obj.direction,0);
    __iput->textRead("state",__obj.state,0);
    __iput->textRead("startTime",__obj.startTime,0);
    __iput->textRead("duration",__obj.duration,0);
    __iput->textRead("error",__obj.error,0);
    __iput->textRead("reason",__obj.reason,0);
    __iput->textEnd();
    return true;
}
void __write_SessionBriefStatusVec(const Common::OputStreamPtr& __oput,const SipDialer::SessionBriefStatusVec& __obj)
{
    __oput->write((int)__obj.size());
    vector<SipDialer::SessionBriefStatus>::const_iterator it1;
    for (it1 = __obj.begin();it1 != __obj.end();it1++)
        SipDialer::__write_SessionBriefStatus(__oput,*it1);
}

void __read_SessionBriefStatusVec(const Common::IputStreamPtr& __iput,SipDialer::SessionBriefStatusVec& __obj)
{
    __obj.clear();
    int size;__iput->read(size);
    for (int i=0;i< size;i++)
    {
        SipDialer::SessionBriefStatus m;
        SipDialer::__read_SessionBriefStatus(__iput,m);
        __obj.push_back(m);
    }
}

void __textWrite_SessionBriefStatusVec(const Common::OputStreamPtr& __oput,const Common::String& __name,const SipDialer::SessionBriefStatusVec& __obj)
{
    __oput->textArray(__name);
    vector<SipDialer::SessionBriefStatus>::const_iterator it1;
    for (it1 = __obj.begin();it1 != __obj.end();it1++)
        SipDialer::__textWrite_SessionBriefStatus(__oput,__name,*it1);
}

bool __textRead_SessionBriefStatusVec(const Common::IputStreamPtr& __iput,const Common::String& __name,SipDialer::SessionBriefStatusVec& __obj)
{
    __obj.clear();
    int size = __iput->textCount(__name);
    for (int i=0;i<size;i++)
    {
        SipDialer::SessionBriefStatus m;
        if (SipDialer::__textRead_SessionBriefStatus(__iput,__name,m,i))
            __obj.push_back(m);
    }
    return true;
}


ResponseAllCallStatus::ResponseAllCallStatus() :
    count(0)
{
}

ResponseAllCallStatus::ResponseAllCallStatus(int x_count,const SipDialer::SessionBriefStatusVec& x_sessions) :
    count(x_count),sessions(x_sessions)
{
}

bool ResponseAllCallStatus::operator<(const ResponseAllCallStatus&__obj) const
{
    if (this == &__obj) return false;
    if (count < __obj.count) return true;
    if (__obj.count < count) return false;
    if (sessions < __obj.sessions) return true;
    if (__obj.sessions < sessions) return false;
    return false;
}

bool ResponseAllCallStatus::operator==(const ResponseAllCallStatus&__obj) const
{
    if (this == &__obj) return true;
    if (count != __obj.count) return false;
    if (sessions != __obj.sessions) return false;
    return true;
}

void ResponseAllCallStatus::__write(const Common::OputStreamPtr& __oput) const
{
    __write_ResponseAllCallStatus(__oput,*this);
}

void ResponseAllCallStatus::__read(const Common::IputStreamPtr& __iput)
{
    __read_ResponseAllCallStatus(__iput,*this);
}

void ResponseAllCallStatus::__textWrite(const Common::OputStreamPtr& __oput,const Common::String& __name) const
{
    __textWrite_ResponseAllCallStatus(__oput,__name,*this);
}

bool ResponseAllCallStatus::__textRead(const Common::IputStreamPtr& __iput,const Common::String& __name,int __idx)
{
    return __textRead_ResponseAllCallStatus(__iput,__name,*this,__idx);
}

void __write_ResponseAllCallStatus(const Common::OputStreamPtr& __oput,const SipDialer::ResponseAllCallStatus& __obj)
{
    __oput->write(__obj.count);
    SipDialer::__write_SessionBriefStatusVec(__oput,__obj.sessions);
}

void __read_ResponseAllCallStatus(const Common::IputStreamPtr& __iput,SipDialer::ResponseAllCallStatus& __obj)
{
    __iput->read(__obj.count);
    SipDialer::__read_SessionBriefStatusVec(__iput,__obj.sessions);
}

void __textWrite_ResponseAllCallStatus(const Common::OputStreamPtr& __oput,const Common::String& __name,const SipDialer::ResponseAllCallStatus& __obj)
{
    __oput->textStart(__name);
    __oput->textWrite("count",__obj.count);
    SipDialer::__textWrite_SessionBriefStatusVec(__oput,"sessions",__obj.sessions);
    __oput->textEnd();
}

bool __textRead_ResponseAllCallStatus(const Common::IputStreamPtr& __iput,const Common::String& __name,SipDialer::ResponseAllCallStatus& __obj,int __idx)
{
    if (!__iput->textStart(__name,__idx)) return false;
    __iput->textRead("count",__obj.count,0);
    SipDialer::__textRead_SessionBriefStatusVec(__iput,"sessions",__obj.sessions);
    __iput->textEnd();
    return true;
}

SipEvent::SipEvent() :
    timestamp(0)
{
}

SipEvent::SipEvent(const Common::String& x_type,const Common::String& x_time,const Common::String& x_title,const Common::String& x_detail,Common::Long x_timestamp) :
    type(x_type),time(x_time),title(x_title),detail(x_detail),timestamp(x_timestamp)
{
}

bool SipEvent::operator<(const SipEvent&__obj) const
{
    if (this == &__obj) return false;
    if (type < __obj.type) return true;
    if (__obj.type < type) return false;
    if (time < __obj.time) return true;
    if (__obj.time < time) return false;
    if (title < __obj.title) return true;
    if (__obj.title < title) return false;
    if (detail < __obj.detail) return true;
    if (__obj.detail < detail) return false;
    if (timestamp < __obj.timestamp) return true;
    if (__obj.timestamp < timestamp) return false;
    return false;
}

bool SipEvent::operator==(const SipEvent&__obj) const
{
    if (this == &__obj) return true;
    if (type != __obj.type) return false;
    if (time != __obj.time) return false;
    if (title != __obj.title) return false;
    if (detail != __obj.detail) return false;
    if (timestamp != __obj.timestamp) return false;
    return true;
}

void SipEvent::__write(const Common::OputStreamPtr& __oput) const
{
    __write_SipEvent(__oput,*this);
}

void SipEvent::__read(const Common::IputStreamPtr& __iput)
{
    __read_SipEvent(__iput,*this);
}

void SipEvent::__textWrite(const Common::OputStreamPtr& __oput,const Common::String& __name) const
{
    __textWrite_SipEvent(__oput,__name,*this);
}

bool SipEvent::__textRead(const Common::IputStreamPtr& __iput,const Common::String& __name,int __idx)
{
    return __textRead_SipEvent(__iput,__name,*this,__idx);
}

void __write_SipEvent(const Common::OputStreamPtr& __oput,const SipDialer::SipEvent& __obj)
{
    __oput->write(__obj.type);
    __oput->write(__obj.time);
    __oput->write(__obj.title);
    __oput->write(__obj.detail);
    __oput->write(__obj.timestamp);
}

void __read_SipEvent(const Common::IputStreamPtr& __iput,SipDialer::SipEvent& __obj)
{
    __iput->read(__obj.type);
    __iput->read(__obj.time);
    __iput->read(__obj.title);
    __iput->read(__obj.detail);
    __iput->read(__obj.timestamp);
}

void __textWrite_SipEvent(const Common::OputStreamPtr& __oput,const Common::String& __name,const SipDialer::SipEvent& __obj)
{
    __oput->textStart(__name);
    __oput->textWrite("type",__obj.type);
    __oput->textWrite("time",__obj.time);
    __oput->textWrite("title",__obj.title);
    __oput->textWrite("detail",__obj.detail);
    __oput->textWrite("timestamp",__obj.timestamp);
    __oput->textEnd();
}

bool __textRead_SipEvent(const Common::IputStreamPtr& __iput,const Common::String& __name,SipDialer::SipEvent& __obj,int __idx)
{
    if (!__iput->textStart(__name,__idx)) return false;
    __iput->textRead("type",__obj.type,0);
    __iput->textRead("time",__obj.time,0);
    __iput->textRead("title",__obj.title,0);
    __iput->textRead("detail",__obj.detail,0);
    __iput->textRead("timestamp",__obj.timestamp,0);
    __iput->textEnd();
    return true;
}
void __write_SipEventVec(const Common::OputStreamPtr& __oput,const SipDialer::SipEventVec& __obj)
{
    __oput->write((int)__obj.size());
    vector<SipDialer::SipEvent>::const_iterator it1;
    for (it1 = __obj.begin();it1 != __obj.end();it1++)
        SipDialer::__write_SipEvent(__oput,*it1);
}

void __read_SipEventVec(const Common::IputStreamPtr& __iput,SipDialer::SipEventVec& __obj)
{
    __obj.clear();
    int size;__iput->read(size);
    for (int i=0;i< size;i++)
    {
        SipDialer::SipEvent m;
        SipDialer::__read_SipEvent(__iput,m);
        __obj.push_back(m);
    }
}

void __textWrite_SipEventVec(const Common::OputStreamPtr& __oput,const Common::String& __name,const SipDialer::SipEventVec& __obj)
{
    __oput->textArray(__name);
    vector<SipDialer::SipEvent>::const_iterator it1;
    for (it1 = __obj.begin();it1 != __obj.end();it1++)
        SipDialer::__textWrite_SipEvent(__oput,__name,*it1);
}

bool __textRead_SipEventVec(const Common::IputStreamPtr& __iput,const Common::String& __name,SipDialer::SipEventVec& __obj)
{
    __obj.clear();
    int size = __iput->textCount(__name);
    for (int i=0;i<size;i++)
    {
        SipDialer::SipEvent m;
        if (SipDialer::__textRead_SipEvent(__iput,__name,m,i))
            __obj.push_back(m);
    }
    return true;
}


SipStats::SipStats()
{
}

SipStats::SipStats(const Common::String& x_local,const Common::String& x_remote,const SipDialer::SipEventVec& x_events) :
    local(x_local),remote(x_remote),events(x_events)
{
}

bool SipStats::operator<(const SipStats&__obj) const
{
    if (this == &__obj) return false;
    if (local < __obj.local) return true;
    if (__obj.local < local) return false;
    if (remote < __obj.remote) return true;
    if (__obj.remote < remote) return false;
    if (events < __obj.events) return true;
    if (__obj.events < events) return false;
    return false;
}

bool SipStats::operator==(const SipStats&__obj) const
{
    if (this == &__obj) return true;
    if (local != __obj.local) return false;
    if (remote != __obj.remote) return false;
    if (events != __obj.events) return false;
    return true;
}

void SipStats::__write(const Common::OputStreamPtr& __oput) const
{
    __write_SipStats(__oput,*this);
}

void SipStats::__read(const Common::IputStreamPtr& __iput)
{
    __read_SipStats(__iput,*this);
}

void SipStats::__textWrite(const Common::OputStreamPtr& __oput,const Common::String& __name) const
{
    __textWrite_SipStats(__oput,__name,*this);
}

bool SipStats::__textRead(const Common::IputStreamPtr& __iput,const Common::String& __name,int __idx)
{
    return __textRead_SipStats(__iput,__name,*this,__idx);
}

void __write_SipStats(const Common::OputStreamPtr& __oput,const SipDialer::SipStats& __obj)
{
    __oput->write(__obj.local);
    __oput->write(__obj.remote);
    SipDialer::__write_SipEventVec(__oput,__obj.events);
}

void __read_SipStats(const Common::IputStreamPtr& __iput,SipDialer::SipStats& __obj)
{
    __iput->read(__obj.local);
    __iput->read(__obj.remote);
    SipDialer::__read_SipEventVec(__iput,__obj.events);
}

void __textWrite_SipStats(const Common::OputStreamPtr& __oput,const Common::String& __name,const SipDialer::SipStats& __obj)
{
    __oput->textStart(__name);
    __oput->textWrite("local",__obj.local);
    __oput->textWrite("remote",__obj.remote);
    SipDialer::__textWrite_SipEventVec(__oput,"events",__obj.events);
    __oput->textEnd();
}

bool __textRead_SipStats(const Common::IputStreamPtr& __iput,const Common::String& __name,SipDialer::SipStats& __obj,int __idx)
{
    if (!__iput->textStart(__name,__idx)) return false;
    __iput->textRead("local",__obj.local,0);
    __iput->textRead("remote",__obj.remote,0);
    SipDialer::__textRead_SipEventVec(__iput,"events",__obj.events);
    __iput->textEnd();
    return true;
}

ResponseStatus::ResponseStatus() :
    sessionId(0),
    duration(0)
{
}

ResponseStatus::ResponseStatus(int x_sessionId,const Common::String& x_caller,const Common::String& x_callee,const Common::String& x_callId,const Common::String& x_direction,const Common::String& x_state,const Common::String& x_startTime,int x_duration,const SipDialer::SipStats& x_sipStats,const SimpleRtpSession::RtpSessionStats& x_rtpStats) :
    sessionId(x_sessionId),caller(x_caller),callee(x_callee),callId(x_callId),direction(x_direction),state(x_state),startTime(x_startTime),duration(x_duration),sipStats(x_sipStats),rtpStats(x_rtpStats)
{
}

bool ResponseStatus::operator<(const ResponseStatus&__obj) const
{
    if (this == &__obj) return false;
    if (sessionId < __obj.sessionId) return true;
    if (__obj.sessionId < sessionId) return false;
    if (caller < __obj.caller) return true;
    if (__obj.caller < caller) return false;
    if (callee < __obj.callee) return true;
    if (__obj.callee < callee) return false;
    if (callId < __obj.callId) return true;
    if (__obj.callId < callId) return false;
    if (direction < __obj.direction) return true;
    if (__obj.direction < direction) return false;
    if (state < __obj.state) return true;
    if (__obj.state < state) return false;
    if (startTime < __obj.startTime) return true;
    if (__obj.startTime < startTime) return false;
    if (duration < __obj.duration) return true;
    if (__obj.duration < duration) return false;
    if (sipStats < __obj.sipStats) return true;
    if (__obj.sipStats < sipStats) return false;
    if (rtpStats < __obj.rtpStats) return true;
    if (__obj.rtpStats < rtpStats) return false;
    return false;
}

bool ResponseStatus::operator==(const ResponseStatus&__obj) const
{
    if (this == &__obj) return true;
    if (sessionId != __obj.sessionId) return false;
    if (caller != __obj.caller) return false;
    if (callee != __obj.callee) return false;
    if (callId != __obj.callId) return false;
    if (direction != __obj.direction) return false;
    if (state != __obj.state) return false;
    if (startTime != __obj.startTime) return false;
    if (duration != __obj.duration) return false;
    if (sipStats != __obj.sipStats) return false;
    if (rtpStats != __obj.rtpStats) return false;
    return true;
}

void ResponseStatus::__write(const Common::OputStreamPtr& __oput) const
{
    __write_ResponseStatus(__oput,*this);
}

void ResponseStatus::__read(const Common::IputStreamPtr& __iput)
{
    __read_ResponseStatus(__iput,*this);
}

void ResponseStatus::__textWrite(const Common::OputStreamPtr& __oput,const Common::String& __name) const
{
    __textWrite_ResponseStatus(__oput,__name,*this);
}

bool ResponseStatus::__textRead(const Common::IputStreamPtr& __iput,const Common::String& __name,int __idx)
{
    return __textRead_ResponseStatus(__iput,__name,*this,__idx);
}

void __write_ResponseStatus(const Common::OputStreamPtr& __oput,const SipDialer::ResponseStatus& __obj)
{
    __oput->write(__obj.sessionId);
    __oput->write(__obj.caller);
    __oput->write(__obj.callee);
    __oput->write(__obj.callId);
    __oput->write(__obj.direction);
    __oput->write(__obj.state);
    __oput->write(__obj.startTime);
    __oput->write(__obj.duration);
    SipDialer::__write_SipStats(__oput,__obj.sipStats);
    SimpleRtpSession::__write_RtpSessionStats(__oput,__obj.rtpStats);
}

void __read_ResponseStatus(const Common::IputStreamPtr& __iput,SipDialer::ResponseStatus& __obj)
{
    __iput->read(__obj.sessionId);
    __iput->read(__obj.caller);
    __iput->read(__obj.callee);
    __iput->read(__obj.callId);
    __iput->read(__obj.direction);
    __iput->read(__obj.state);
    __iput->read(__obj.startTime);
    __iput->read(__obj.duration);
    SipDialer::__read_SipStats(__iput,__obj.sipStats);
    SimpleRtpSession::__read_RtpSessionStats(__iput,__obj.rtpStats);
}

void __textWrite_ResponseStatus(const Common::OputStreamPtr& __oput,const Common::String& __name,const SipDialer::ResponseStatus& __obj)
{
    __oput->textStart(__name);
    __oput->textWrite("sessionId",__obj.sessionId);
    __oput->textWrite("caller",__obj.caller);
    __oput->textWrite("callee",__obj.callee);
    __oput->textWrite("callId",__obj.callId);
    __oput->textWrite("direction",__obj.direction);
    __oput->textWrite("state",__obj.state);
    __oput->textWrite("startTime",__obj.startTime);
    __oput->textWrite("duration",__obj.duration);
    SipDialer::__textWrite_SipStats(__oput,"sipStats",__obj.sipStats);
    SimpleRtpSession::__textWrite_RtpSessionStats(__oput,"rtpStats",__obj.rtpStats);
    __oput->textEnd();
}

bool __textRead_ResponseStatus(const Common::IputStreamPtr& __iput,const Common::String& __name,SipDialer::ResponseStatus& __obj,int __idx)
{
    if (!__iput->textStart(__name,__idx)) return false;
    __iput->textRead("sessionId",__obj.sessionId,0);
    __iput->textRead("caller",__obj.caller,0);
    __iput->textRead("callee",__obj.callee,0);
    __iput->textRead("callId",__obj.callId,0);
    __iput->textRead("direction",__obj.direction,0);
    __iput->textRead("state",__obj.state,0);
    __iput->textRead("startTime",__obj.startTime,0);
    __iput->textRead("duration",__obj.duration,0);
    SipDialer::__textRead_SipStats(__iput,"sipStats",__obj.sipStats,0);
    SimpleRtpSession::__textRead_RtpSessionStats(__iput,"rtpStats",__obj.rtpStats,0);
    __iput->textEnd();
    return true;
}

};//namespace: SipDialer
