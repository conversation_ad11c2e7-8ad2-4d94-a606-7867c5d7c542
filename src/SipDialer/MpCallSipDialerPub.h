﻿//
// *****************************************************************************
// Copyright(c) 2017-2025 Juphoon System Software Co., LTD. All rights reserved.
// *****************************************************************************
//
// Auto generated from: src/MpCallSipGateway/dialer/MpCallSipDialer.def
// Warning: do not edit this file.
//

#ifndef __SipDialer_MpCallSipDialerPub_h
#define __SipDialer_MpCallSipDialerPub_h

#include "Common/Common.h"
#include "SimpleRtpSession/SimpleRtpSessionPub.h"

namespace SipDialer
{

class RequestCall
{
public:
    RequestCall();
    RequestCall(const Common::String&,const Common::String&,const Common::String&,bool,bool,int,const Common::String&);

    bool operator<(const RequestCall&) const;
    bool operator==(const RequestCall&) const;
    bool operator!=(const RequestCall&__obj) const { return !operator==(__obj);}
    void __write(const Common::OputStreamPtr&) const;
    void __read(const Common::IputStreamPtr&);
    void __textWrite(const Common::OputStreamPtr&,const Common::String&) const;
    bool __textRead(const Common::IputStreamPtr&,const Common::String&,int = 0);
public:
    Common::String caller;
    Common::String callee;
    Common::String uui;
    bool video;
    bool is3pcc;
    int timeout;
    Common::String callback;
};
void __write_RequestCall(const Common::OputStreamPtr&,const SipDialer::RequestCall&);
void __read_RequestCall(const Common::IputStreamPtr&,SipDialer::RequestCall&);
void __textWrite_RequestCall(const Common::OputStreamPtr&,const Common::String&,const SipDialer::RequestCall&);
bool __textRead_RequestCall(const Common::IputStreamPtr&,const Common::String&,SipDialer::RequestCall&,int = 0);

class ResponseCall
{
public:
    ResponseCall();
    ResponseCall(int,const Common::String&);

    bool operator<(const ResponseCall&) const;
    bool operator==(const ResponseCall&) const;
    bool operator!=(const ResponseCall&__obj) const { return !operator==(__obj);}
    void __write(const Common::OputStreamPtr&) const;
    void __read(const Common::IputStreamPtr&);
    void __textWrite(const Common::OputStreamPtr&,const Common::String&) const;
    bool __textRead(const Common::IputStreamPtr&,const Common::String&,int = 0);
public:
    int sessionId;
    Common::String callId;
};
void __write_ResponseCall(const Common::OputStreamPtr&,const SipDialer::ResponseCall&);
void __read_ResponseCall(const Common::IputStreamPtr&,SipDialer::ResponseCall&);
void __textWrite_ResponseCall(const Common::OputStreamPtr&,const Common::String&,const SipDialer::ResponseCall&);
bool __textRead_ResponseCall(const Common::IputStreamPtr&,const Common::String&,SipDialer::ResponseCall&,int = 0);

class ResponseTerminated
{
public:
    ResponseTerminated();
    ResponseTerminated(int,bool,const Common::String&);

    bool operator<(const ResponseTerminated&) const;
    bool operator==(const ResponseTerminated&) const;
    bool operator!=(const ResponseTerminated&__obj) const { return !operator==(__obj);}
    void __write(const Common::OputStreamPtr&) const;
    void __read(const Common::IputStreamPtr&);
    void __textWrite(const Common::OputStreamPtr&,const Common::String&) const;
    bool __textRead(const Common::IputStreamPtr&,const Common::String&,int = 0);
public:
    int sessionId;
    bool error;
    Common::String reason;
};
void __write_ResponseTerminated(const Common::OputStreamPtr&,const SipDialer::ResponseTerminated&);
void __read_ResponseTerminated(const Common::IputStreamPtr&,SipDialer::ResponseTerminated&);
void __textWrite_ResponseTerminated(const Common::OputStreamPtr&,const Common::String&,const SipDialer::ResponseTerminated&);
bool __textRead_ResponseTerminated(const Common::IputStreamPtr&,const Common::String&,SipDialer::ResponseTerminated&,int = 0);

class SessionBriefStatus
{
public:
    SessionBriefStatus();
    SessionBriefStatus(int,const Common::String&,const Common::String&,const Common::String&,const Common::String&,const Common::String&,const Common::String&,const Common::String&,int,bool,const Common::String&);

    bool operator<(const SessionBriefStatus&) const;
    bool operator==(const SessionBriefStatus&) const;
    bool operator!=(const SessionBriefStatus&__obj) const { return !operator==(__obj);}
    void __write(const Common::OputStreamPtr&) const;
    void __read(const Common::IputStreamPtr&);
    void __textWrite(const Common::OputStreamPtr&,const Common::String&) const;
    bool __textRead(const Common::IputStreamPtr&,const Common::String&,int = 0);
public:
    int sessionId;
    Common::String caller;
    Common::String callee;
    Common::String callId;
    Common::String uui;
    Common::String direction;
    Common::String state;
    Common::String startTime;
    int duration;
    bool error;
    Common::String reason;
};
void __write_SessionBriefStatus(const Common::OputStreamPtr&,const SipDialer::SessionBriefStatus&);
void __read_SessionBriefStatus(const Common::IputStreamPtr&,SipDialer::SessionBriefStatus&);
void __textWrite_SessionBriefStatus(const Common::OputStreamPtr&,const Common::String&,const SipDialer::SessionBriefStatus&);
bool __textRead_SessionBriefStatus(const Common::IputStreamPtr&,const Common::String&,SipDialer::SessionBriefStatus&,int = 0);

typedef vector<SipDialer::SessionBriefStatus> SessionBriefStatusVec;
void __write_SessionBriefStatusVec(const Common::OputStreamPtr&,const SipDialer::SessionBriefStatusVec&);
void __read_SessionBriefStatusVec(const Common::IputStreamPtr&,SipDialer::SessionBriefStatusVec&);
void __textWrite_SessionBriefStatusVec(const Common::OputStreamPtr&,const Common::String&,const SipDialer::SessionBriefStatusVec&);
bool __textRead_SessionBriefStatusVec(const Common::IputStreamPtr&,const Common::String&,SipDialer::SessionBriefStatusVec&);

class ResponseAllCallStatus
{
public:
    ResponseAllCallStatus();
    ResponseAllCallStatus(int,const SipDialer::SessionBriefStatusVec&);

    bool operator<(const ResponseAllCallStatus&) const;
    bool operator==(const ResponseAllCallStatus&) const;
    bool operator!=(const ResponseAllCallStatus&__obj) const { return !operator==(__obj);}
    void __write(const Common::OputStreamPtr&) const;
    void __read(const Common::IputStreamPtr&);
    void __textWrite(const Common::OputStreamPtr&,const Common::String&) const;
    bool __textRead(const Common::IputStreamPtr&,const Common::String&,int = 0);
public:
    int count;
    SipDialer::SessionBriefStatusVec sessions;
};
void __write_ResponseAllCallStatus(const Common::OputStreamPtr&,const SipDialer::ResponseAllCallStatus&);
void __read_ResponseAllCallStatus(const Common::IputStreamPtr&,SipDialer::ResponseAllCallStatus&);
void __textWrite_ResponseAllCallStatus(const Common::OputStreamPtr&,const Common::String&,const SipDialer::ResponseAllCallStatus&);
bool __textRead_ResponseAllCallStatus(const Common::IputStreamPtr&,const Common::String&,SipDialer::ResponseAllCallStatus&,int = 0);

class SipEvent
{
public:
    SipEvent();
    SipEvent(const Common::String&,const Common::String&,const Common::String&,const Common::String&,Common::Long);

    bool operator<(const SipEvent&) const;
    bool operator==(const SipEvent&) const;
    bool operator!=(const SipEvent&__obj) const { return !operator==(__obj);}
    void __write(const Common::OputStreamPtr&) const;
    void __read(const Common::IputStreamPtr&);
    void __textWrite(const Common::OputStreamPtr&,const Common::String&) const;
    bool __textRead(const Common::IputStreamPtr&,const Common::String&,int = 0);
public:
    Common::String type;
    Common::String time;
    Common::String title;
    Common::String detail;
    Common::Long timestamp;
};
void __write_SipEvent(const Common::OputStreamPtr&,const SipDialer::SipEvent&);
void __read_SipEvent(const Common::IputStreamPtr&,SipDialer::SipEvent&);
void __textWrite_SipEvent(const Common::OputStreamPtr&,const Common::String&,const SipDialer::SipEvent&);
bool __textRead_SipEvent(const Common::IputStreamPtr&,const Common::String&,SipDialer::SipEvent&,int = 0);

typedef vector<SipDialer::SipEvent> SipEventVec;
void __write_SipEventVec(const Common::OputStreamPtr&,const SipDialer::SipEventVec&);
void __read_SipEventVec(const Common::IputStreamPtr&,SipDialer::SipEventVec&);
void __textWrite_SipEventVec(const Common::OputStreamPtr&,const Common::String&,const SipDialer::SipEventVec&);
bool __textRead_SipEventVec(const Common::IputStreamPtr&,const Common::String&,SipDialer::SipEventVec&);

class SipStats
{
public:
    SipStats();
    SipStats(const Common::String&,const Common::String&,const SipDialer::SipEventVec&);

    bool operator<(const SipStats&) const;
    bool operator==(const SipStats&) const;
    bool operator!=(const SipStats&__obj) const { return !operator==(__obj);}
    void __write(const Common::OputStreamPtr&) const;
    void __read(const Common::IputStreamPtr&);
    void __textWrite(const Common::OputStreamPtr&,const Common::String&) const;
    bool __textRead(const Common::IputStreamPtr&,const Common::String&,int = 0);
public:
    Common::String local;
    Common::String remote;
    SipDialer::SipEventVec events;
};
void __write_SipStats(const Common::OputStreamPtr&,const SipDialer::SipStats&);
void __read_SipStats(const Common::IputStreamPtr&,SipDialer::SipStats&);
void __textWrite_SipStats(const Common::OputStreamPtr&,const Common::String&,const SipDialer::SipStats&);
bool __textRead_SipStats(const Common::IputStreamPtr&,const Common::String&,SipDialer::SipStats&,int = 0);

class ResponseStatus
{
public:
    ResponseStatus();
    ResponseStatus(int,const Common::String&,const Common::String&,const Common::String&,const Common::String&,const Common::String&,const Common::String&,int,const SipDialer::SipStats&,const SimpleRtpSession::RtpSessionStats&);

    bool operator<(const ResponseStatus&) const;
    bool operator==(const ResponseStatus&) const;
    bool operator!=(const ResponseStatus&__obj) const { return !operator==(__obj);}
    void __write(const Common::OputStreamPtr&) const;
    void __read(const Common::IputStreamPtr&);
    void __textWrite(const Common::OputStreamPtr&,const Common::String&) const;
    bool __textRead(const Common::IputStreamPtr&,const Common::String&,int = 0);
public:
    int sessionId;
    Common::String caller;
    Common::String callee;
    Common::String callId;
    Common::String direction;
    Common::String state;
    Common::String startTime;
    int duration;
    SipDialer::SipStats sipStats;
    SimpleRtpSession::RtpSessionStats rtpStats;
};
void __write_ResponseStatus(const Common::OputStreamPtr&,const SipDialer::ResponseStatus&);
void __read_ResponseStatus(const Common::IputStreamPtr&,SipDialer::ResponseStatus&);
void __textWrite_ResponseStatus(const Common::OputStreamPtr&,const Common::String&,const SipDialer::ResponseStatus&);
bool __textRead_ResponseStatus(const Common::IputStreamPtr&,const Common::String&,SipDialer::ResponseStatus&,int = 0);

};//namespace: SipDialer

#endif //__SipDialer_MpCallSipDialerPub_h
