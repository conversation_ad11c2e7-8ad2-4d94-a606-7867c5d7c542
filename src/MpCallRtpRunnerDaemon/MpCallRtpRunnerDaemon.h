//
// *****************************************************************************
// Copyright (c) 2023 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//
// Created on 2023/6/1 by <PERSON>
//

#pragma once

#include "Common/CommonEx.h"
#include "Service/ServiceI.h"
#include "ServiceRunner/RunnerDaemon.h"
#include "Util/PortManager.h"

namespace SipMpCall
{

class MpCallRtpRunnerItem : virtual public Common::Shared
{
public:
    PortsPtr audioPorts;
    PortsPtr videoPorts;
};

class MpCallRtpRunnerDaemon : public Service::ServiceManagerI, public ServiceRunner::RunnerDaemonListener
{
public:
    explicit MpCallRtpRunnerDaemon(const Common::ApplicationExPtr &application)
        : Service::ServiceManagerI(application, "Daemon", true)
    {
        if (application->getAppConfig("Daemon.Endpoints").empty())
            application->setConfig("global.Daemon.Endpoints", "sudp -h 127.0.0.1;");
    }

    bool __ex(const Common::ServerCallPtr &call, const Common::String &cmd, const Common::IputStreamPtr &iput) { return false; }

    bool onActivate() override;
    void onDeactivate() override;
    void onShutdown() override;
    void onSchd() override;
    void onUpdateConfigs() override;

    bool getMainService(Common::ObjectServerPtr &service, Common::String &name, bool &famous) override;
    bool isMainServiceReady(Common::String &reason) override;
    bool isSafeClose(Common::String &unsafeReason) override { return true; }

    // implement ServiceRunner::RunnerDaemonListener
    ServiceRunner::RunnerDaemonItemListenerPtr createItem(const Common::String &name, Common::String &execName, Common::StrVec &args, Common::StrStrMap &configs) override;

private:
    bool initRunnerExe();
    bool getRunnerExePath(Common::String &info);
    bool getRunnerExeByPwd(Common::String &info);
    bool getRunnerInfo(Common::String &info);

private:
    ServiceRunner::RunnerDaemonPtr _daemon;
    Common::StrStrMap _rtpRunnerConfigs;
    Common::String _runnerExe;

    PortManagerPtr _audioPm;
    PortManagerPtr _videoPm;
};

} // namespace SipMpCall
