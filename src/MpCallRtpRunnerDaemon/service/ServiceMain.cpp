//
// *****************************************************************************
// Copyright (c) 2023 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//
// Created on 2023/6/1 by <PERSON>
//

#include "Service/ServiceI.h"
#include "MpCallRtpRunnerDaemon/MpCallRtpRunnerDaemon.h"

#define MpCallRtpRunnerDaemon_MAJOR_VER "2"
#define MpCallRtpRunnerDaemon_MINOR_VER "4"
#define MpCallRtpRunnerDaemon_PATCH "4"
#define MpCallRtpRunnerDaemon_STATUS "0"

DECLARE_SERVICE_CLASS_MODULE0(SipMpCall::MpCallRtpRunnerDaemon, MpCallRtpRunnerDaemon);
