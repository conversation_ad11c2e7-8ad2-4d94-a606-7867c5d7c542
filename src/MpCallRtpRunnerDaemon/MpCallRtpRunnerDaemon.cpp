//
// *****************************************************************************
// Copyright (c) 2023 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//
// Created on 2023/6/1 by <PERSON>
//

#include "MpCallRtpRunnerDaemon.h"
#include "MpCallRtpRunner/MpCallRtpRunnerType.h"
#include <libgen.h>
#include <linux/limits.h>
#include <unistd.h>

namespace SipMpCall
{

bool MpCallRtpRunnerDaemon::onActivate()
{
    if (!initRunnerExe())
        return false;

    int audioLowerBound, audioUpperBound;
    if (!_application->getAppConfigAsInt("RtpRunner.AudioPorts.UpperBound", audioUpperBound)
        || !_application->getAppConfigAsInt("RtpRunner.AudioPorts.LowerBound", audioLowerBound))
    {
        audioLowerBound = 21000;
        audioUpperBound = 21099;
    }
    else
    {
        if (audioLowerBound % 2 != 0)
            audioLowerBound++;
        if (audioUpperBound % 2 == 0)
            audioUpperBound--;
    }
    _audioPm = PortManager::create(audioLowerBound, audioUpperBound);
    if (!_audioPm)
        return false;

    _application->setStatistics("RtpRunner.AudioPorts", Common::String::formatString("%d-%d", audioLowerBound, audioUpperBound));

    int videoLowerBound, videoUpperBound;
    if (!_application->getAppConfigAsInt("RtpRunner.VideoPorts.UpperBound", videoUpperBound)
        || !_application->getAppConfigAsInt("RtpRunner.VideoPorts.LowerBound", videoLowerBound))
    {
        videoLowerBound = 21100;
        videoUpperBound = 21199;
    }
    else
    {
        if (videoLowerBound % 2 != 0)
            videoLowerBound++;
        if (videoUpperBound % 2 == 0)
            videoUpperBound--;
    }
    _videoPm = PortManager::create(videoLowerBound, videoUpperBound);
    if (!_videoPm)
        return false;

    _application->setStatistics("RtpRunner.VideoPorts", Common::String::formatString("%d-%d", videoLowerBound, videoUpperBound));

    return Service::ServiceManagerI::onActivate();
}

void MpCallRtpRunnerDaemon::onDeactivate()
{
    if (_daemon)
    {
        _daemon->close();
        _daemon = 0;
    }

    Service::ServiceManagerI::onDeactivate();
}

void MpCallRtpRunnerDaemon::onShutdown()
{
    if (_daemon)
    {
        _daemon->close();
        _daemon = 0;
    }

    Service::ServiceManagerI::onShutdown();
}

void MpCallRtpRunnerDaemon::onSchd()
{
    if (_daemon)
        _daemon->schd();

    Service::ServiceManagerI::onSchd();
}

void MpCallRtpRunnerDaemon::onUpdateConfigs()
{
    Service::ServiceManagerI::onUpdateConfigs();

    if (_daemon)
        _daemon->updateConfigs();

    Common::StrStrMap configs;
    _application->getAppConfigs("RtpRunner.", configs);
    _rtpRunnerConfigs.swap(configs);
}

bool MpCallRtpRunnerDaemon::getMainService(Common::ObjectServerPtr &service, Common::String &name, bool &famous)
{
    if (!_daemon)
    {
        _daemon = ServiceRunner::RunnerDaemon::create(this, _application, RtpRunnerType(_application.get()), _mainAdapter);
        if (!_daemon)
        {
            UTIL_LOG_ERR("MpCallRtpRunnerDaemon", "content:create daemon failed.");
            return false;
        }
    }

    return false;
}

bool MpCallRtpRunnerDaemon::isMainServiceReady(Common::String &reason)
{
    if (!_daemon)
    {
        reason = "CreateDaemonFailed";
        return false;
    }

    return _daemon->isServiceReady(reason);
}

ServiceRunner::RunnerDaemonItemListenerPtr MpCallRtpRunnerDaemon::createItem(const Common::String &name, Common::String &execName, Common::StrVec &args, Common::StrStrMap &configs)
{
    execName = _runnerExe;
    configs = _rtpRunnerConfigs;

    Common::Handle<MpCallRtpRunnerItem> item = new MpCallRtpRunnerItem();
    item->audioPorts = _audioPm->allocatePort();
    if (!item->audioPorts)
    {
        UTIL_LOG_WRN("MpCallRtpRunnerDaemon", "content:allocate audio port failed");
        return 0;
    }
    configs["RtpRunner.AudioPorts.LowerBound"] = Common::String((*item->audioPorts)[0]);
    configs["RtpRunner.AudioPorts.UpperBound"] = Common::String((*item->audioPorts)[1]);

    item->videoPorts = _videoPm->allocatePort();
    if (!item->videoPorts)
    {
        UTIL_LOG_WRN("MpCallRtpRunnerDaemon", "content:allocate video port failed");
        return 0;
    }
    configs["RtpRunner.VideoPorts.LowerBound"] = Common::String((*item->videoPorts)[0]);
    configs["RtpRunner.VideoPorts.UpperBound"] = Common::String((*item->videoPorts)[1]);

    Common::String coreNetId;
    if (_application->getAppConfig("SipCall.CoreNetId", coreNetId))
    {
        configs["SipCall.CoreNetId"] = coreNetId;
    }

    return item;
}

bool MpCallRtpRunnerDaemon::initRunnerExe()
{
    Common::String info;
    if (!getRunnerExePath(info) && !getRunnerExeByPwd(info))
    {
        UTIL_LOG_ERR("MpCallRtpRunnerDaemon", "content:get runner executable file failed.");
        return false;
    }

    _application->setStatistics("MpCallRtpRunner.Exe", _runnerExe);
    _application->setStatistics("MpCallRtpRunner.Info", info);

    UTIL_LOG_IFO("MpCallRtpRunnerDaemon", "content:runner executable file path:" + _runnerExe);
    return true;
}

bool MpCallRtpRunnerDaemon::getRunnerExePath(Common::String &info)
{
    char result[PATH_MAX];
    ssize_t count = readlink("/proc/self/exe", result, PATH_MAX);
    if (count == -1)
    {
        UTIL_LOG_WRN("MpCallRtpRunnerDaemon", "content:get executable file path failed.");
        return false;
    }

    const char *path = dirname(result);
    if (!path)
    {
        UTIL_LOG_WRN("MpCallRtpRunnerDaemon", "content:get dirname <" + Common::String(result) + ">of executable file failed.");
        return false;
    }

    _runnerExe = path;
    if (_runnerExe[_runnerExe.size() - 1] != '/')
        _runnerExe += "/";
    _runnerExe += RtpRunnerType(_application.get());

    Common::Long size, mtime;
    if (!Common::fileInfo(_runnerExe, size, mtime))
    {
        UTIL_LOG_WRN("MpCallRtpRunnerDaemon", "content:runner executable file <" + _runnerExe + "> is not exists.");
        return false;
    }

    return getRunnerInfo(info);
}

bool MpCallRtpRunnerDaemon::getRunnerExeByPwd(Common::String &info)
{
    char path[2048];
    if (!getcwd(path, 2048))
    {
        UTIL_LOG_ERR("MpCallRtpRunnerDaemon", "content:get current working path failed.");
        return false;
    }

    _runnerExe = Common::String(path) + "/" + RtpRunnerType(_application.get());

    Common::Long size, mtime;
    if (!Common::fileInfo(_runnerExe, size, mtime))
    {
        UTIL_LOG_WRN("MpCallRtpRunnerDaemon", "content:runner executable file <" + _runnerExe + "> is not exists.");

        _runnerExe = Common::String(path) + "/" + _application->getAppName() + "/" + RtpRunnerType(_application.get());
        if (!Common::fileInfo(_runnerExe, size, mtime))
        {
            UTIL_LOG_WRN("MpCallRtpRunnerDaemon", "content:runner executable file <" + _runnerExe + "> is not exists.");
            return false;
        }
    }

    return getRunnerInfo(info);
}

bool MpCallRtpRunnerDaemon::getRunnerInfo(Common::String &info)
{
    FILE *pipe = popen((_runnerExe + " -v").c_str(), "r");
    if (!pipe)
    {
        UTIL_LOG_WRN("MpCallRtpRunnerDaemon", "content:read runner executable <" + _runnerExe + "> version failed.");
        return false;
    }
    Common::String output;
    char buff[1024] = {0};
    while (!feof(pipe))
    {
        if (fgets(buff, 1024, pipe))
            output += buff;
    }
    pclose(pipe);

    Common::StrVec lines;
    output.split(lines, "\r\n");
    info = Common::String::join(lines);
    return true;
}

} // namespace SipMpCall