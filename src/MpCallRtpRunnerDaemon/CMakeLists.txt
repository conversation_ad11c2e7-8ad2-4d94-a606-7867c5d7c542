aux_source_directory(. MPCALL_RTPRUNNER_DAEMON_SRC)
add_library(MpCallRtpRunnerDaemonImpl
    ${MPCALL_RTPRUNNER_DAEMON_SRC}
)

target_include_directories(MpCallRtpRunnerDaemonImpl PRIVATE
    ${DEFAULT_INCLUDE_DIRECTORIES}
    ${PROJECT_SOURCE_DIR}/3rd
)
target_compile_definitions(MpCallRtpRunnerDaemonImpl PRIVATE
    ${DEFAULT_COMPILE_DEFINITIONS}
)

aux_source_directory(service MPCALL_RTPRUNNER_DAEMON_SERVICE_SRC)
add_executable(MpCallRtpRunnerDaemon
    ${WARP_GLIBC_SRC}
    ${MPCALL_RTPRUNNER_DAEMON_SERVICE_SRC}
    ${WARP_GLIBC_SRC}
)

target_include_directories(MpCallRtpRunnerDaemon PRIVATE
    ${DEFAULT_INCLUDE_DIRECTORIES}
    ${PROJECT_SOURCE_DIR}/3rd
)
target_compile_definitions(MpCallRtpRunnerDaemon PRIVATE
    ${DEFAULT_COMPILE_DEFINITIONS}
)

target_link_libraries(MpCallRtpRunnerDaemon MpCallRtpRunnerDaemonImpl ServiceRunnerDaemon ServiceRunnerRpc Util ServiceI NmsFoundation ServiceMain
    ${DEFAULT_SERVER_LIBRARIES}
    ${DEFAULT_SYS_LIBRARIES}
)
target_link_options(MpCallRtpRunnerDaemon PRIVATE
    ${DEFAULT_LINK_OPTIONS}
)
