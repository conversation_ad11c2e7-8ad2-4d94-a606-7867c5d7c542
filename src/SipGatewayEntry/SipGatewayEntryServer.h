﻿//
// *****************************************************************************
// Copyright(c) 2017-2025 Juphoon System Software Co., LTD. All rights reserved.
// *****************************************************************************
//
// Auto generated from: SipGatewayEntry.def
// Warning: do not edit this file.
//

#ifndef __SipGatewayEntry_SipGatewayEntryServer_h
#define __SipGatewayEntry_SipGatewayEntryServer_h

#include "SipGatewayEntry/SipGatewayEntryPub.h"

namespace SipGatewayEntry
{

class SipGatewayEntryServer : virtual public Common::ObjectServer
{
public:
    virtual bool __ex(const Common::ServerCallPtr& __call,const Common::String& __cmd,const Common::IputStreamPtr& __iput);

    virtual void onInvited_begin(const Common::ServerCallPtr& __call,const Common::String& conferenceId,const Common::String& callerAccount,const Common::String& calleeNumber,const Common::String& domainId,const Common::String& appId,const Common::String& deviceIp,const Common::StrStrMap& params) = 0;

    static void onInvited_end(const Common::ServerCallPtr& __call,bool __ret);

    static inline void onInvited_end(const Common::ServerCallPtr &__call,const Common::CallError &error)
    {
        __call->setError(error);
        onInvited_end(__call,false);
    }

private:
    void __cmd_onInvited(const Common::ServerCallPtr& __call,const Common::IputStreamPtr& __iput);
};

};//namespace: SipGatewayEntry

#endif //__SipGatewayEntry_SipGatewayEntryServer_h
