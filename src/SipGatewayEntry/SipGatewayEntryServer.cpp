﻿//
// *****************************************************************************
// Copyright(c) 2017-2025 Juphoon System Software Co., LTD. All rights reserved.
// *****************************************************************************
//
// Auto generated from: SipGatewayEntry.def
// Warning: do not edit this file.
//

#include "SipGatewayEntry/SipGatewayEntryServer.h"

namespace SipGatewayEntry
{

bool SipGatewayEntryServer::__ex(const Common::ServerCallPtr& __call,const Common::String& __cmd,const Common::IputStreamPtr& __iput)
{
    if (__cmd == "onInvited.SipGatewayEntry.SipGatewayEntry") { __cmd_onInvited(__call,__iput);return true;}
    return false;
}

void SipGatewayEntryServer::onInvited_end(const Common::ServerCallPtr& __call,bool __ret)
{
    Common::VerListPtr __vers = __call->verList();
    Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
    short __ver = (__vers)?__vers->ver(true):0;
    switch (__ver)
    {
    case 0:
        __oput->write(__ret);
        break;
    }
    __call->cmdResult(__ver,__oput);
}

void SipGatewayEntryServer::__cmd_onInvited(const Common::ServerCallPtr& __call,const Common::IputStreamPtr& __iput)
{
    do
    {
        Common::VerListPtr __vers = __call->verList();
        Common::String conferenceId;Common::String callerAccount;Common::String calleeNumber;Common::String domainId;Common::String appId;Common::String deviceIp;Common::StrStrMap params;
        switch (__vers->ver(false))
        {
        case 0:
            __iput->read(conferenceId);
            __iput->read(callerAccount);
            __iput->read(calleeNumber);
            __iput->read(domainId);
            __iput->read(appId);
            __iput->read(deviceIp);
            Common::__read_StrStrMap(__iput,params);
            break;
        default: goto __ver_err;
        }
        __start(false);
        onInvited_begin(__call,conferenceId,callerAccount,calleeNumber,domainId,appId,deviceIp,params);
        return;
    } while(0);
__ver_err:
    Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
    __oput->write((short)1);
    __oput->write((short)0);
    __call->cmdResult(VersionException<<16,__oput);
}

};//namespace: SipGatewayEntry
