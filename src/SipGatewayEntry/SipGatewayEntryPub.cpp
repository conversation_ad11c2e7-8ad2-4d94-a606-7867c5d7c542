﻿//
// *****************************************************************************
// Copyright(c) 2017-2025 Juphoon System Software Co., LTD. All rights reserved.
// *****************************************************************************
//
// Auto generated from: SipGatewayEntry.def
// Warning: do not edit this file.
//

#include "SipGatewayEntry/SipGatewayEntryPub.h"

namespace SipGatewayEntry
{

SipConfMessage::SipConfMessage() :
    Video(false)
{
}

SipConfMessage::SipConfMessage(const Common::String& x_Type,const Common::String& x_ConfUri,const Common::String& x_From,const Common::String& x_UserToUserInfo,bool x_Video) :
    Type(x_Type),ConfUri(x_ConfUri),From(x_From),UserToUserInfo(x_UserToUserInfo),Video(x_Video)
{
}

bool SipConfMessage::operator<(const SipConfMessage&__obj) const
{
    if (this == &__obj) return false;
    if (Type < __obj.Type) return true;
    if (__obj.Type < Type) return false;
    if (ConfUri < __obj.ConfUri) return true;
    if (__obj.ConfUri < ConfUri) return false;
    if (From < __obj.From) return true;
    if (__obj.From < From) return false;
    if (UserToUserInfo < __obj.UserToUserInfo) return true;
    if (__obj.UserToUserInfo < UserToUserInfo) return false;
    if (Video < __obj.Video) return true;
    if (__obj.Video < Video) return false;
    return false;
}

bool SipConfMessage::operator==(const SipConfMessage&__obj) const
{
    if (this == &__obj) return true;
    if (Type != __obj.Type) return false;
    if (ConfUri != __obj.ConfUri) return false;
    if (From != __obj.From) return false;
    if (UserToUserInfo != __obj.UserToUserInfo) return false;
    if (Video != __obj.Video) return false;
    return true;
}

void SipConfMessage::__write(const Common::OputStreamPtr& __oput) const
{
    __write_SipConfMessage(__oput,*this);
}

void SipConfMessage::__read(const Common::IputStreamPtr& __iput)
{
    __read_SipConfMessage(__iput,*this);
}

void SipConfMessage::__textWrite(const Common::OputStreamPtr& __oput,const Common::String& __name) const
{
    __textWrite_SipConfMessage(__oput,__name,*this);
}

bool SipConfMessage::__textRead(const Common::IputStreamPtr& __iput,const Common::String& __name,int __idx)
{
    return __textRead_SipConfMessage(__iput,__name,*this,__idx);
}

void __write_SipConfMessage(const Common::OputStreamPtr& __oput,const SipGatewayEntry::SipConfMessage& __obj)
{
    __oput->write(__obj.Type);
    __oput->write(__obj.ConfUri);
    __oput->write(__obj.From);
    __oput->write(__obj.UserToUserInfo);
    __oput->write(__obj.Video);
}

void __read_SipConfMessage(const Common::IputStreamPtr& __iput,SipGatewayEntry::SipConfMessage& __obj)
{
    __iput->read(__obj.Type);
    __iput->read(__obj.ConfUri);
    __iput->read(__obj.From);
    __iput->read(__obj.UserToUserInfo);
    __iput->read(__obj.Video);
}

void __textWrite_SipConfMessage(const Common::OputStreamPtr& __oput,const Common::String& __name,const SipGatewayEntry::SipConfMessage& __obj)
{
    __oput->textStart(__name);
    __oput->textWrite("Type",__obj.Type);
    __oput->textWrite("ConfUri",__obj.ConfUri);
    __oput->textWrite("From",__obj.From);
    __oput->textWrite("UserToUserInfo",__obj.UserToUserInfo);
    __oput->textWrite("Video",__obj.Video);
    __oput->textEnd();
}

bool __textRead_SipConfMessage(const Common::IputStreamPtr& __iput,const Common::String& __name,SipGatewayEntry::SipConfMessage& __obj,int __idx)
{
    if (!__iput->textStart(__name,__idx)) return false;
    __iput->textRead("Type",__obj.Type,0);
    __iput->textRead("ConfUri",__obj.ConfUri,0);
    __iput->textRead("From",__obj.From,0);
    __iput->textRead("UserToUserInfo",__obj.UserToUserInfo,0);
    __iput->textRead("Video",__obj.Video,0);
    __iput->textEnd();
    return true;
}

};//namespace: SipGatewayEntry
