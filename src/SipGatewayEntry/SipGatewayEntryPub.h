﻿//
// *****************************************************************************
// Copyright(c) 2017-2025 Juphoon System Software Co., LTD. All rights reserved.
// *****************************************************************************
//
// Auto generated from: SipGatewayEntry.def
// Warning: do not edit this file.
//

#ifndef __SipGatewayEntry_SipGatewayEntryPub_h
#define __SipGatewayEntry_SipGatewayEntryPub_h

#include "Common/Common.h"
#include "Common/TypesPub.h"

namespace SipGatewayEntry
{

class SipConfMessage
{
public:
    SipConfMessage();
    SipConfMessage(const Common::String&,const Common::String&,const Common::String&,const Common::String&,bool);

    bool operator<(const SipConfMessage&) const;
    bool operator==(const SipConfMessage&) const;
    bool operator!=(const SipConfMessage&__obj) const { return !operator==(__obj);}
    void __write(const Common::OputStreamPtr&) const;
    void __read(const Common::IputStreamPtr&);
    void __textWrite(const Common::OputStreamPtr&,const Common::String&) const;
    bool __textRead(const Common::IputStreamPtr&,const Common::String&,int = 0);
public:
    Common::String Type;
    Common::String ConfUri;
    Common::String From;
    Common::String UserToUserInfo;
    bool Video;
};
void __write_SipConfMessage(const Common::OputStreamPtr&,const SipGatewayEntry::SipConfMessage&);
void __read_SipConfMessage(const Common::IputStreamPtr&,SipGatewayEntry::SipConfMessage&);
void __textWrite_SipConfMessage(const Common::OputStreamPtr&,const Common::String&,const SipGatewayEntry::SipConfMessage&);
bool __textRead_SipConfMessage(const Common::IputStreamPtr&,const Common::String&,SipGatewayEntry::SipConfMessage&,int = 0);

};//namespace: SipGatewayEntry

#endif //__SipGatewayEntry_SipGatewayEntryPub_h
