﻿//
// *****************************************************************************
// Copyright(c) 2017-2025 Juphoon System Software Co., LTD. All rights reserved.
// *****************************************************************************
//
// Auto generated from: SipGatewayEntry.def
// Warning: do not edit this file.
//

#ifndef __SipGatewayEntry_SipGatewayEntryAgent_h
#define __SipGatewayEntry_SipGatewayEntryAgent_h

#include "SipGatewayEntry/SipGatewayEntryPub.h"

namespace SipGatewayEntry
{

class SipGatewayEntryAgent : public Common::Agent
{
public:
    SipGatewayEntryAgent(int zero = 0) : Common::Agent(zero) {}
    SipGatewayEntryAgent(const Common::Agent& agent) : Common::Agent(agent) {}
    SipGatewayEntryAgent(const Common::ObjectAgentPtr& agent) : Common::Agent(agent) {}

    bool onInvited(const Common::String& conferenceId,const Common::String& callerAccount,const Common::String& calleeNumber,const Common::String& domainId,const Common::String& appId,const Common::String& deviceIp,const Common::StrStrMap& params,const Common::CallParamsPtr& __params = 0) const throw();
    void onInvited_begin(const Common::AgentAsyncPtr& __async,const Common::String& conferenceId,const Common::String& callerAccount,const Common::String& calleeNumber,const Common::String& domainId,const Common::String& appId,const Common::String& deviceIp,const Common::StrStrMap& params,const Common::CallParamsPtr& __params = 0,const Common::ObjectPtr& __userdata = 0) const throw();
    static bool onInvited_end(int __rslt,const Common::IputStreamPtr& __iput) throw();
};

};//namespace: SipGatewayEntry

#endif //__SipGatewayEntry_SipGatewayEntryAgent_h
