﻿//
// *****************************************************************************
// Copyright(c) 2017-2025 Juphoon System Software Co., LTD. All rights reserved.
// *****************************************************************************
//
// Auto generated from: SipGatewayEntry.def
// Warning: do not edit this file.
//

#include "SipGatewayEntry/SipGatewayEntryAgent.h"

namespace SipGatewayEntry
{

bool SipGatewayEntryAgent::onInvited(const Common::String& conferenceId,const Common::String& callerAccount,const Common::String& calleeNumber,const Common::String& domainId,const Common::String& appId,const Common::String& deviceIp,const Common::StrStrMap& params,const Common::CallParamsPtr& __params) const throw()
{
    try
    {
        int __loop = 0;
        while (1)
        {
            Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
            Common::VerListPtr __vers = __agent->verList("onInvited.SipGatewayEntry.SipGatewayEntry");
            short __ver = 0;
            if (__vers)
            {
                __ver = __vers->ver(true);
                if (__ver > 0) __ver = 0;
            }
            switch (__ver)
            {
            case 0:
                __oput->write((short)1);
                __oput->write((short)__ver);
                __oput->write(conferenceId);
                __oput->write(callerAccount);
                __oput->write(calleeNumber);
                __oput->write(domainId);
                __oput->write(appId);
                __oput->write(deviceIp);
                Common::__write_StrStrMap(__oput,params);
                break;
            default:
                throw Common::CallException(Common::ObjectAgent::versionError("onInvited.SipGatewayEntry.SipGatewayEntry"));
            }
            Common::IputStreamPtr __iput;
            Common::CallError __error;
            int __rslt = __agent->ex_sync("onInvited.SipGatewayEntry.SipGatewayEntry",__oput,__iput,__params,__error);
            if (__rslt == -1)
                throw Common::CallException(__error);
            if (__rslt>>16)
            {
                Assert((__rslt>>16) == VersionException);
                __loop ++;
                if (__loop >= 3)
                    throw Common::CallException(Common::ObjectAgent::versionError("onInvited.SipGatewayEntry.SipGatewayEntry"));
                continue;
            }
            bool __ret;
            switch (__rslt)
            {
            case 0:
                __iput->read(__ret);
                break;
            default:
                throw Common::CallException(Common::ObjectAgent::versionError("onInvited.SipGatewayEntry.SipGatewayEntry"));
            }
            Common::ObjectAgent::processFinal(__iput);
            return __ret;
        }
    }
    catch (const Common::CallException& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
    catch (const Common::Exception& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
}

void SipGatewayEntryAgent::onInvited_begin(const Common::AgentAsyncPtr& __async,const Common::String& conferenceId,const Common::String& callerAccount,const Common::String& calleeNumber,const Common::String& domainId,const Common::String& appId,const Common::String& deviceIp,const Common::StrStrMap& params,const Common::CallParamsPtr& __params,const Common::ObjectPtr& __userdata) const throw()
{
    class __AgentAsync : public Common::AgentAsync
    {
    public:
        __AgentAsync(const Common::ObjectAgentPtr& __agent,const Common::AgentAsyncPtr& __async,const Common::String& conferenceId,const Common::String& callerAccount,const Common::String& calleeNumber,const Common::String& domainId,const Common::String& appId,const Common::String& deviceIp,const Common::StrStrMap& params,const Common::CallParamsPtr& __params,const Common::ObjectPtr& __userdata)
            : x__agent(__agent),x__async(__async),x_conferenceId(conferenceId),x_callerAccount(callerAccount),x_calleeNumber(calleeNumber),x_domainId(domainId),x_appId(appId),x_deviceIp(deviceIp),x_params(params),x__params(__params),x__userdata(__userdata),x__loop(0) {}

        void start()
        {
            Common::AgentAsyncPtr pthis = this;
            try
            {
                Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
                Common::VerListPtr __vers = x__agent->verList("onInvited.SipGatewayEntry.SipGatewayEntry");
                short __ver = 0;
                if (__vers)
                {
                    __ver = __vers->ver(true);
                    if (__ver > 0) __ver = 0;
                }
                switch (__ver)
                {
                case 0:
                    __oput->write((short)1);
                    __oput->write((short)__ver);
                    __oput->write(x_conferenceId);
                    __oput->write(x_callerAccount);
                    __oput->write(x_calleeNumber);
                    __oput->write(x_domainId);
                    __oput->write(x_appId);
                    __oput->write(x_deviceIp);
                    Common::__write_StrStrMap(__oput,x_params);
                    break;
                default:
                    throw Common::CallException(Common::ObjectAgent::versionError("onInvited.SipGatewayEntry.SipGatewayEntry"));
                }
                x__agent->ex_async(this,"onInvited.SipGatewayEntry.SipGatewayEntry",__oput,x__params,0);
            }
            catch (const Common::CallException& ex)
            {
                Common::ObjectAgent::throwException(x__async,ex,x__userdata);
            }
            catch (const Common::Exception& ex)
            {
                Common::ObjectAgent::throwException(x__async,ex,x__userdata);
            }
        }
        void cmdResult(int __rslt,const Common::IputStreamPtr& __iput,const Common::ObjectPtr& __userdata)
        {
            if ((__rslt>>16)!=VersionException)
            {
                if (x__async) x__async->cmdResult(__rslt,__iput,x__userdata);
                return;
            }
            x__loop ++;
            if (x__loop >= 3)
            {
                if (x__async)
                    Common::ObjectAgent::throwException(x__async, Common::CallException(Common::ObjectAgent::versionError("onInvited.SipGatewayEntry.SipGatewayEntry")), x__userdata);
                return;
            }
            start();
        }
    private:
        Common::ObjectAgentPtr x__agent;
        Common::AgentAsyncPtr x__async;
        Common::String x_conferenceId;
        Common::String x_callerAccount;
        Common::String x_calleeNumber;
        Common::String x_domainId;
        Common::String x_appId;
        Common::String x_deviceIp;
        Common::StrStrMap x_params;
        Common::CallParamsPtr x__params;
        Common::ObjectPtr x__userdata;
        int x__loop;
    };
    (new __AgentAsync(__agent,__async,conferenceId,callerAccount,calleeNumber,domainId,appId,deviceIp,params,__params,__userdata))->start();
}

bool SipGatewayEntryAgent::onInvited_end(int __rslt,const Common::IputStreamPtr& __iput) throw()
{
    try
    {
        Common::CallError __error;
        if (Common::ObjectAgent::processException(__rslt,__iput,__error))
            throw Common::CallException(__error);
        Assert((__rslt>>16) == 0);
        bool __ret;
        switch (__rslt)
        {
        case 0:
            __iput->read(__ret);
            break;
        default:
            throw Common::CallException(Common::ObjectAgent::versionError("onInvited.SipGatewayEntry.SipGatewayEntry"));
        }
        Common::ObjectAgent::processFinal(__iput);
        return __ret;
    }
    catch (const Common::CallException& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
    catch (const Common::Exception& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
}

};//namespace: SipGatewayEntry
