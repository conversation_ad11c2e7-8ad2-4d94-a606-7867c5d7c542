//
// *****************************************************************************
// Copyright (c) 2023 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//
// Created on 2023/11/27 by <PERSON>
//

#pragma once

#include "Common/CommonEx.h"
#include "Common/Util.h"
#include "AgentCallNumber.h"
#include "MpCallEb/MpCallEbPub.h"
#include "MpCallEbService/ManagerData.h"

namespace EbService
{

class AgentCallNumber : public Data, public MpCallEb::AgentCallNumber
{
public:
    AgentCallNumber() {}
    AgentCallNumber(const Common::String &account, const Common::String &number);

    virtual Common::String to_string() override;
    virtual bool from_string(const Common::String &str) override;

    virtual const DataKeys &keys() override;

private:
    DataKeys _keys;
};

typedef Common::Handle<AgentCallNumber> AgentCallNumberPtr;

class AgentCallNumberManagerI : public AgentCallNumberManager, public DataCategoryListener
{
public:
    AgentCallNumberManagerI(const Common::ApplicationExPtr &app, const DataDbPtr &db);

    virtual bool onActivate(Common::String &failReason) override;
    virtual void onDeactivate() override;

    virtual bool bind(const Common::String &account, Common::String &number) override;
    virtual void unbind(const Common::String &account) override;
    virtual bool alive(const Common::String &account) override;

    virtual Common::String getBindedNumber(const Common::String &account) override;
    virtual Common::String getBindedAccount(const Common::String &number) override;

    virtual DataPtr createData(const Common::String &str) override;

    static Common::String accountKey(const Common::String &account);
    static Common::String numberKey(const Common::String &number);

private:
    Common::String getNextNumber();

private:
    Common::ApplicationExPtr _app;
    DataDbPtr _dataDb;
    DataCategoryPtr _db;

    int _startNumberId;
    int _endNumberId;
    int _numberSize;

    int _nextNumberIdOffset;
};

} // namespace EbService