//
// *****************************************************************************
// Copyright (c) 2023 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//
// Created on 2023/6/14 by <PERSON>
//

#pragma once

#include "Common/TypesPub.h"
#include "Error.h"
#include "MpCallEbService/TokenChecker.h"
#include "Service/ServiceI.h"
#include "MpCall/MpCallSipServer.h"
#include "MpCallEb/MpCallEbServer.h"
#include "AcdGateway/MpCallEbServer.h"
#include "CcSvr/CallCenterSvrServer.h"

#include "ManagerData.h"
#include "AgentCallNumber.h"
#include "CallRecord.h"
#include "OpenApiServer.h"
#include "MessageNotifier.h"

namespace MpCall
{

class EbServer;
typedef Common::Handle<EbServer> EbServerPtr;

class EbServer : public Service::ServiceManagerI, public MpCall::SipGatewayControllerServer, public MpCallEb::OpenApiServer
{
private:
    class Entry : public AcdGateway::AcdGatewayServer, public CcSvr::CcSvrServer
    {
    public:
        explicit Entry(const EbServerPtr &service);
        virtual bool __ex(const Common::ServerCallPtr &__call, const Common::String &__cmd, const Common::IputStreamPtr &__iput) override;

        // implement AcdGateway::AcdGatewayServer for app
        virtual void getWaitSize_begin(const Common::ServerCallPtr &__call, const Common::String &httpAddress, const Common::StrStrMap &inParams) override;
        virtual void getQueueLength_begin(const Common::ServerCallPtr &__call, int vcid, int skill, const Common::StrStrMap &inParams) override;

        // implement AcdGateway::AcdGatewayServer for agent
        virtual void keepalive_begin(const Common::ServerCallPtr &__call, const Common::StrStrMap &params) override;
        virtual void checkin_begin(const Common::ServerCallPtr &__call) override;
        virtual void checkout_begin(const Common::ServerCallPtr &__call) override;

    private:
        EbServerPtr _service;
    };

public:
    struct Interfaces
    {
        EbService::MessageNotifierPtr msgNotifier;
        EbService::DataDbPtr db;
        EbService::AgentCallNumberManagerPtr agentCallNumberMgr;
        EbService::CallRecordManagerPtr callRecordMgr;
        EbService::OpenApiManagerPtr openApiMgr;
        EbService::TokenCheckerPtr tokenChecker;
    };

    explicit EbServer(const Common::ApplicationExPtr &application, const Interfaces &interfaces = Interfaces());

    virtual bool __ex(const Common::ServerCallPtr &__call, const Common::String &__cmd, const Common::IputStreamPtr &__iput) override;

    // implement Service::ServiceManagerI
    bool onActivate() override;
    void onDeactivate() override;
    void onShutdown() override;
    void onSchd() override;
    void onUpdateConfigs() override;
    bool getMainService(Common::ObjectServerPtr &service, Common::String &name, bool &famous) override;
    bool isMainServiceReady(Common::String &reason) override;

    // implement MpCall::SipGatewayControllerServer for MpCallSipGateway
    virtual void onCallout_begin(const Common::ServerCallPtr &__call, const Common::String &sessId, const Common::String &caller, const Common::String &callee, const MpCall::InviteConfig &inviteConfig) override;
    virtual void onCallin_begin(const Common::ServerCallPtr &__call, const Common::String &sessId, const Common::String &caller, const Common::String &callee, const MpCall::InviteConfig &inviteConfig) override;
    virtual void onHolded_begin(const Common::ServerCallPtr &__call, const Common::String &sessId, bool hold) override;
    virtual void onTermed_begin(const Common::ServerCallPtr &__call, const Common::String &sessId) override;

    // implement MpCallEb::OpenApiServer for IVR
    virtual void bindSession_begin(const Common::ServerCallPtr &__call, const Common::String &serialNumber, const Common::String &callId) override;
    virtual void notifyAnswer_begin(const Common::ServerCallPtr &__call, const Common::String &serialNumber, const Common::String &calleeNumber) override;
    virtual void notifyFilePlay_begin(const Common::ServerCallPtr &__call, const Common::String &serialNumber, const Common::String &calleeNumber, bool start, const Common::String &filename) override;

private:
    Common::String getCallId(const Common::String &sessId);
    bool getCallIdAccount(const Common::String &serialNumber, const Common::String &calleeNumber, Common::String &callId, Common::String &account);
    bool getCallIdAccount(const Common::String &serialNumber, const Common::String &calleeNumber, MpCallEb::CallRecord &record, Common::String &account);
    bool sendMsg(const Common::AgentAsyncPtr async, const Common::String &account, const Common::String &type, Common::StrStrMap &params);
    bool sendMsgAnswer(const Common::AgentAsyncPtr async, const Common::String &account);
    bool sendMsgHold(const Common::AgentAsyncPtr async, const Common::String &account);
    bool sendMsgUnhold(const Common::AgentAsyncPtr async, const Common::String &account);
    bool sendMsgStartPlay(const Common::AgentAsyncPtr async, const Common::String &account, const Common::String &filename);
    bool sendMsgStopPlay(const Common::AgentAsyncPtr async, const Common::String &account, const Common::String &filename);

private:
    Common::String _failReason;
    EbService::MessageNotifierPtr _msgNotifier;
    EbService::DataDbPtr _db;
    EbService::AgentCallNumberManagerPtr _agentCallNumberMgr;
    EbService::CallRecordManagerPtr _callRecordMgr;
    EbService::OpenApiManagerPtr _openApiMgr;
    EbService::TokenCheckerPtr _tokenChecker;

    Common::String _lineCallNumber;
    Common::String _serverOid;
};

} // namespace MpCall