//
// *****************************************************************************
// Copyright (c) 2023 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//
// Created on 2023/5/27 by <PERSON>
//

#include "MpCallEbService.h"
#include "Account/AccountPub2Agent.h"
#include "Common/Common.h"
#include "Common/Error.h"
#include "Common/Property.h"
#include "Common/TypesPub.h"
#include "Common/Util.h"
#include "MpCall/MpCallPub.h"
#include "MpCall/MpCallSipServer.h"
#include "MpCallEb/MpCallEbPub.h"
#include "MpCallEbService/AgentCallNumber.h"
#include "MpCallEbService/CallRecord.h"
#include "MpCallEbService/Error.h"
#include "MpCallEbService/MessageNotifier.h"
#include "MpCallEbService/TokenChecker.h"

namespace MpCall
{

class Uui
{
public:
    explicit Uui(const Common::String &str)
    {
        Common::StrVec strs;
        str.split(strs, ";");
        for (auto &s : strs)
        {
            int pos = s.find("=");
            if (pos > 0)
                _fields.insert(make_pair(s.substr(0, pos), s.substr(pos + 1)));
            else
                _fields.insert(make_pair(s.substr(0, pos), ""));
        }
    }

    Common::String get(const Common::String &key)
    {
        auto it = _fields.find(key);
        if (it == _fields.end())
            return "";
        return it->second;
    }

private:
    std::map<Common::String, Common::String> _fields;
};

class UuiFormat
{
public:
    Common::String uui()
    {
        return _content;
    }

    template<class ...T>
    Common::String uui(const Common::String &key, const Common::String &value, T... args)
    {
        if (value.empty())
            _content += key + ";";
        else
            _content += key + "=" + value + ";";
        return uui(args...);
    }

private:
    Common::String _content;
};

#define UUI_KEY_TOKEN "token"
#define UUI_KEY_PTI "pti"
#define UUI_KEY_CSN "csn"

EbServer::Entry::Entry(const EbServerPtr &service)
    : _service(service)
{
}

bool EbServer::Entry::__ex(const Common::ServerCallPtr &__call, const Common::String &__cmd, const Common::IputStreamPtr &__iput)
{
    if (AcdGateway::AcdGatewayServer::__ex(__call, __cmd, __iput))
        return true;
    if (CcSvr::CcSvrServer::__ex(__call, __cmd, __iput))
        return true;
    UTIL_LOG_WRN("EbServerEntry", "content:invalid cmd:" + __cmd);
    return false;
}

void EbServer::Entry::getWaitSize_begin(const Common::ServerCallPtr &__call, const Common::String &httpAddress, const Common::StrStrMap &inParams)
{
    EbService::CallRecordManagerPtr callRecordMgr = _service->_callRecordMgr;
    EbService::OpenApiManagerPtr openApiMgr = _service->_openApiMgr;
    if (!openApiMgr || !callRecordMgr)
    {
        UTIL_LOG_WRN("EbServer", "content:getWaitSize server deactivated.");
        __call->setError(EbServiceError::ServerShutdown(ELOC));
        AcdGateway::AcdGatewayServer::getWaitSize_end(__call, false, Common::StrStrMap());
        return;
    }

    Common::String domain = __call->getParam(CALL_PARAMS_DOMAIN);
    Common::String app = __call->getParam(CALL_PARAMS_APP);

    auto it = inParams.find("vcid");
    if (it == inParams.end())
    {
        UTIL_LOG_WRN("EbServer", "content:getWaitSize param vcid not found");
        __call->setError(EbServiceError::InvalidParam(EARGS("param", "vcid")));
        AcdGateway::AcdGatewayServer::getWaitSize_end(__call, false, Common::StrStrMap());
        return;
    }

    Common::String vcid = it->second;

    it = inParams.find("callflowno");
    if (it == inParams.end())
    {
        UTIL_LOG_WRN("EbServer", "content:getWaitSize param callflowno not found");
        __call->setError(EbServiceError::InvalidParam(EARGS("param", "callflowno")));
        AcdGateway::AcdGatewayServer::getWaitSize_end(__call, false, Common::StrStrMap());
        return;
    }

    Common::String callId = it->second + domain + "_" + app;
    Common::String callFlowNo;
    if (!callRecordMgr->getSerialNumber(callId, callFlowNo))
    {
        UTIL_LOG_WRN("EbServer", "content:getWaitSize callflowno:" + it->second + " serialnumber not found.");
        __call->setError(EbServiceError::SerialNumberNotFound(EARGS("callflowno", it->second, "callId", callId)));
        AcdGateway::AcdGatewayServer::getWaitSize_end(__call, false, Common::StrStrMap());
        return;
    }

    Common::String queueOrder;
    if (!openApiMgr->queryWaitSize(httpAddress, vcid.toInt(0), callFlowNo, queueOrder))
    {
        __call->setError(Common::getLastError());
        AcdGateway::AcdGatewayServer::getWaitSize_end(__call, false, Common::StrStrMap());
        return;
    }

    Common::StrStrMap params;
    params["size"] = queueOrder.empty() ? "-2" : queueOrder;

    UTIL_LOG_IFO("EbServer", "content:getWaitSize queueOrder:" + queueOrder);
    AcdGateway::AcdGatewayServer::getWaitSize_end(__call, true, params);
}

void EbServer::Entry::getQueueLength_begin(const Common::ServerCallPtr &__call, int vcid, int skill, const Common::StrStrMap &inParams)
{
    EbService::OpenApiManagerPtr openApiMgr = _service->_openApiMgr;
    if (!openApiMgr)
    {
        UTIL_LOG_WRN("EbServer", "content:getQueueLength server deactivated.");
        __call->setError(EbServiceError::ServerShutdown(ELOC));
        AcdGateway::AcdGatewayServer::getQueueLength_end(__call, false, Common::StrStrMap());
        return;
    }

    Common::String httpAddr;
    auto it = inParams.find("HttpReqAddr");
    if (it != inParams.end())
        httpAddr = it->second;

    Common::String queueCnt, forecast;
    if (!openApiMgr->queryQueueLen(httpAddr, vcid, skill, queueCnt, forecast))
    {
        __call->setError(Common::getLastError());
        AcdGateway::AcdGatewayServer::getWaitSize_end(__call, false, Common::StrStrMap());
        return;
    }

    Common::StrStrMap params;
    params["queuecnt"] = queueCnt;
    params["forecast"] = forecast;

    UTIL_LOG_IFO("EbServer", "content:getQueueLength queuecnt:" + queueCnt + " forecast:" + forecast);
    AcdGateway::AcdGatewayServer::getQueueLength_end(__call, true, params);
}

void EbServer::Entry::keepalive_begin(const Common::ServerCallPtr &__call, const Common::StrStrMap &params)
{
    EbService::AgentCallNumberManagerPtr agentCallNumberMgr = _service->_agentCallNumberMgr;
    if (!agentCallNumberMgr)
    {
        UTIL_LOG_WRN("EbServer", "content:keepalive server deactivated.");
        CcSvr::CcSvrServer::keepalive_end(__call, EbServiceError::ServerShutdown(ELOC));
        return;
    }

    Common::String accountId = __call->getParam(CALL_PARAMS_ACCOUNT);
    if (!agentCallNumberMgr->alive(accountId))
    {
        UTIL_LOG_WRN("EbServer", "content:keepalive account:" + accountId + " failed.");
        CcSvr::CcSvrServer::keepalive_end(__call, EbServiceError::AccountNotFound(ELOC));
        return;
    }

    UTIL_LOG_IFO("EbServer", "content:keepalive account:" + accountId);
    CcSvr::CcSvrServer::keepalive_end(__call, true);
}

void EbServer::Entry::checkin_begin(const Common::ServerCallPtr &__call)
{
    EbService::AgentCallNumberManagerPtr agentCallNumberMgr = _service->_agentCallNumberMgr;
    if (!agentCallNumberMgr)
    {
        UTIL_LOG_WRN("EbServer", "content:checkin server deactivated.");
        CcSvr::CcSvrServer::checkin_end(__call, EbServiceError::ServerShutdown(ELOC));
        return;
    }

    Common::String accountId = __call->getParam(CALL_PARAMS_ACCOUNT);
    Common::String number;
    if (!agentCallNumberMgr->bind(accountId, number))
    {
        UTIL_LOG_WRN("EbServer", "content:checkin account:" + accountId + " failed.");
        CcSvr::CcSvrServer::checkin_end(__call, EbServiceError::BindAccountNumberFailed(ELOC));
        return;
    }

    UTIL_LOG_IFO("EbServer", "content:checkin account:" + accountId + " number:" + number);
    CcSvr::CcSvrServer::checkin_end(__call, true, number);
}

void EbServer::Entry::checkout_begin(const Common::ServerCallPtr &__call)
{
    EbService::AgentCallNumberManagerPtr agentCallNumberMgr = _service->_agentCallNumberMgr;
    if (!agentCallNumberMgr)
    {
        UTIL_LOG_WRN("EbServer", "content:checkout server deactivated.");
        CcSvr::CcSvrServer::checkout_end(__call, EbServiceError::ServerShutdown(ELOC));
        return;
    }

    Common::String accountId = __call->getParam(CALL_PARAMS_ACCOUNT);
    agentCallNumberMgr->unbind(accountId);

    UTIL_LOG_IFO("EbServer", "content:checkout account:" + accountId);
    CcSvr::CcSvrServer::checkout_end(__call, true);
}

EbServer::EbServer(const Common::ApplicationExPtr &application, const Interfaces &interfaces)
    : Service::ServiceManagerI(application)
    , _msgNotifier(interfaces.msgNotifier)
    , _db(interfaces.db)
    , _agentCallNumberMgr(interfaces.agentCallNumberMgr)
    , _callRecordMgr(interfaces.callRecordMgr)
    , _openApiMgr(interfaces.openApiMgr)
    , _tokenChecker(interfaces.tokenChecker)
{
    Common::String value;
    if (!application->getAppConfig("EventManager.MaxProcessors", value))
        application->setConfig("global.EventManager.MaxProcessors", "10");
}

bool EbServer::__ex(const Common::ServerCallPtr &__call, const Common::String &__cmd, const Common::IputStreamPtr &__iput)
{
    if (MpCall::SipGatewayControllerServer::__ex(__call, __cmd, __iput))
        return true;
    if (MpCallEb::OpenApiServer::__ex(__call, __cmd, __iput))
        return true;
    UTIL_LOG_WRN("EbServer", "content:invalid cmd:" + __cmd);
    return false;
}

bool EbServer::onActivate()
{
    if (!Service::ServiceManagerI::onActivate())
    {
        UTIL_LOG_ERR("EbServer", "content:service manager activate failed.");
        return false;
    }

    _mainAdapter->localOptimize(true);

#define __MODULE_INIT_AND_CHECK__(__mod__, __name__)                         \
    do                                                                       \
    {                                                                        \
        if (!__mod__ || !__mod__->onActivate(_failReason))                   \
        {                                                                    \
            if (_failReason.empty())                                         \
                _failReason = "CreateModuleFailed:" __name__;                \
            UTIL_LOG_ERR("EbServer", "content:create " __name__ " failed."); \
            return true;                                                     \
        }                                                                    \
    } while (0)

    if (!_msgNotifier)
        _msgNotifier = EbService::MessageNotifier::create(_application);
    __MODULE_INIT_AND_CHECK__(_msgNotifier, "MessageNotifier");

    if (!_tokenChecker)
        _tokenChecker = EbService::TokenChecker::create(_application.get());
    __MODULE_INIT_AND_CHECK__(_tokenChecker, "TokenChecker");

    if (!_db)
        _db = EbService::DataDb::create(_application.get());
    __MODULE_INIT_AND_CHECK__(_db, "DataDb");

    if (!_agentCallNumberMgr)
        _agentCallNumberMgr = EbService::AgentCallNumberManager::create(_application, _db);
    __MODULE_INIT_AND_CHECK__(_agentCallNumberMgr, "ManagerAccount");

    if (!_callRecordMgr)
        _callRecordMgr = EbService::CallRecordManager::create(_application, _db);
    __MODULE_INIT_AND_CHECK__(_callRecordMgr, "CallRecord");

    if (!_openApiMgr)
        _openApiMgr = EbService::OpenApiManager::create(_application);
    __MODULE_INIT_AND_CHECK__(_openApiMgr, "ApiServer");

    Common::Handle<Entry> entry = new Entry(this);
    if (!entry)
    {
        UTIL_LOG_ERR("EbServer", "content:create entry server failed.");
        _failReason = "EbServer.CreateEntryFailed";
        return true;
    }

    _mainAdapter->addServer("#AcdGateway", entry, true);
    _mainAdapter->addServer("#CcSvr", entry, true);

    _lineCallNumber = _application->getAppConfig("Eb.LineCallNumber");

    UTIL_LOG_IFO("EbServer", "content:onActivate succeed.");
    return true;
}

void EbServer::onDeactivate()
{
    UTIL_LOG_IFO("EbServer", "content:onDeactivate.");

    _mainAdapter->removeServer("#AcdGateway");
    _mainAdapter->removeServer("#CcSvr");

    if (_openApiMgr)
    {
        _openApiMgr->onDeactivate();
        _openApiMgr = 0;
    }

    if (_callRecordMgr)
    {
        _callRecordMgr->onDeactivate();
        _callRecordMgr = 0;
    }

    if (_agentCallNumberMgr)
    {
        _agentCallNumberMgr->onDeactivate();
        _agentCallNumberMgr = 0;
    }

    if (_db)
    {
        _db->onDeactivate();
        _db = 0;
    }

    if (_tokenChecker)
    {
        _tokenChecker->onDeactivate();
        _tokenChecker = 0;
    }

    if (_msgNotifier)
    {
        _msgNotifier->onDeactivate();
        _msgNotifier = 0;
    }

    Service::ServiceManagerI::onDeactivate();
}

void EbServer::onShutdown()
{
    UTIL_LOG_IFO("EbServer", "content:onShutdown.");
    Service::ServiceManagerI::onShutdown();
}

void EbServer::onSchd()
{
    Service::ServiceManagerI::onSchd();
}

void EbServer::onUpdateConfigs()
{
    Service::ServiceManagerI::onUpdateConfigs();

    if (!_failReason.empty())
        return ;

    _openApiMgr->onUpdateConfigs();
    _callRecordMgr->onUpdateConfigs();
    _agentCallNumberMgr->onUpdateConfigs();
    _db->onUpdateConfigs();
    _tokenChecker->onUpdateConfigs();
    _msgNotifier->onUpdateConfigs();

    if (_serverOid.empty())
    {
        _serverOid = _mainAdapter->getObjectId("EbService");
        _application->setStatistics("Eb.ServerOid", _serverOid);
        UTIL_LOG_IFO("EbServer", "content:service oid:" + _serverOid);
    }

    Common::String value;
    if (_lineCallNumber.empty() && _application->getAppConfig("Eb.LineCallNumber", value))
    {
        _lineCallNumber = value;
        _application->setStatistics("Eb.LineCallNumber", _lineCallNumber);
        UTIL_LOG_IFO("EbServer", "content:config Eb.LineCallNumber:" + _lineCallNumber);
    }
}

bool EbServer::getMainService(Common::ObjectServerPtr &service, Common::String &name, bool &famous)
{
    service = this;
    name = "EbService";
    famous = true;

    UTIL_LOG_IFO("EbServer", "content:getMainService name:" + name + (famous ? " famous" : ""));
    return true;
}

bool EbServer::isMainServiceReady(Common::String &reason)
{
    reason = _failReason;
    if (!reason.empty())
        return false;

    if (_lineCallNumber.empty())
    {
        UTIL_LOG_WRN("EbServer", "content:no config Eb.LineCallNumber.");
        reason = "EbServer.InvalidConfig:Eb.LineCallNumber";
        return false;
    }

    return true;
}

void EbServer::onCallout_begin(const Common::ServerCallPtr &__call, const Common::String &sessId, const Common::String &caller, const Common::String &callee, const MpCall::InviteConfig &inviteConfig)
{
    EbService::CallRecordManagerPtr callRecordMgr = _callRecordMgr;
    EbService::OpenApiManagerPtr openApiMgr = _openApiMgr;
    EbService::TokenCheckerPtr tokenChecker = _tokenChecker;
    if (!openApiMgr || !callRecordMgr || !tokenChecker)
    {
        UTIL_LOG_WRN("EbServer", "content:onCallout sessId:" + sessId + " callee:" + callee + " deactivated.");
        MpCall::SipGatewayControllerServer::onCallout_end(__call, EbServiceError::ServerShutdown(ELOC));
        return;
    }

    Uui uui = Uui(inviteConfig.uui);
    Common::String token = uui.get(UUI_KEY_TOKEN);
    Common::String tokenValue;
    if (!token.empty() && !tokenChecker->checkValid(token, tokenValue))
    {
        UTIL_LOG_WRN("EbServer", "content:onCallout sessId:" + sessId + " callee:" + callee + " token:" + token + " check failed.");
        MpCall::SipGatewayControllerServer::onCallout_end(__call, EbServiceError::TokenCheckFailed(ELOC));
        return;
    }

    Common::String callId = getCallId(sessId);
    if (!callRecordMgr->add(callId, uui.get(UUI_KEY_PTI), tokenValue, MpCallEb::SessionInfo(sessId, caller)))
    {
        UTIL_LOG_WRN("EbSErver", "content:onCallout sessId:" + sessId + " callee:" + callee + " add call record failed.");
        MpCall::SipGatewayControllerServer::onCallout_end(__call, EbServiceError::AddCallRecordFailed(ELOC));
        return;
    }

    MpCall::InviteConfig outInviteConfig = inviteConfig;
    outInviteConfig.uui = "uuid=" + callId + ";reason=" + callee + ";" + openApiMgr->getUuiInfo();
    UTIL_LOG_IFO("EbServer", "content:onCallout sessId:" + sessId + " callee:" + callee + "->" + _lineCallNumber + " uui:" + inviteConfig.uui + "->" + outInviteConfig.uui);
    MpCall::SipGatewayControllerServer::onCallout_end(__call, true, _lineCallNumber, outInviteConfig);
}

void EbServer::onCallin_begin(const Common::ServerCallPtr &__call, const Common::String &sessId, const Common::String &caller, const Common::String &callee, const MpCall::InviteConfig &inviteConfig)
{
    EbService::CallRecordManagerPtr callRecordMgr = _callRecordMgr;
    if (!callRecordMgr)
    {
        UTIL_LOG_WRN("EbServer", "content:onCallin sessId:" + sessId + " callee:" + callee + " deactivated.");
        MpCall::SipGatewayControllerServer::onCallout_end(__call, EbServiceError::ServerShutdown(ELOC));
        return;
    }

    Common::String serialNumber, account;
    int pos = inviteConfig.uui.find(";");
    if (pos <= 0)
    {
        UTIL_LOG_WRN("EbServer", "content:onCallin invalid uui:" + inviteConfig.uui);
        MpCall::SipGatewayControllerServer::onCallin_end(__call, EbServiceError::GetSerialNumberFailed(ELOC));
        return;
    }
    serialNumber = inviteConfig.uui.substr(0, pos);

    MpCallEb::CallRecord record;
    if (!getCallIdAccount(serialNumber, callee, record, account))
    {
        MpCall::SipGatewayControllerServer::onCallin_end(__call, Common::getLastError());
        return;
    }

    Common::String newSessId = caller + "/" + record.callId;
    if (!callRecordMgr->setSession(record.callId, MpCallEb::SessionInfo(newSessId, account)))
    {
        UTIL_LOG_WRN("EbServer", "content:onCallin callId:" + record.callId + " callee:" + callee + "->" + account + " set session failed.");
        MpCall::SipGatewayControllerServer::onCallin_end(__call, EbServiceError::GetSerialNumberFailed(ELOC));
        return;
    }

    MpCall::InviteConfig outInviteConfig = inviteConfig;
    outInviteConfig.uui = UuiFormat().uui(UUI_KEY_CSN, serialNumber, UUI_KEY_PTI, record.passthroughInfo, UUI_KEY_TOKEN, record.token);
    outInviteConfig.requireAlert = true;

    UTIL_LOG_IFO("EbServer", "content:onCallin callId:" + sessId + "->" + record.callId + " callee:" + callee + "->" + account + " uui:" + inviteConfig.uui + "->" + outInviteConfig.uui);
    MpCall::SipGatewayControllerServer::onCallin_end(__call, true, record.callId, account, outInviteConfig);
}

void EbServer::onHolded_begin(const Common::ServerCallPtr &__call, const Common::String &sessId, bool hold)
{
    EbService::CallRecordManagerPtr callRecordMgr = _callRecordMgr;
    if (!callRecordMgr)
    {
        UTIL_LOG_WRN("EbServer", "content:onHolded sessId:" + sessId + " deactivated.");
        MpCall::SipGatewayControllerServer::onHolded_end(__call, EbServiceError::ServerShutdown(ELOC));
        return;
    }

    Common::String callId = getCallId(sessId);
    MpCallEb::SessionInfoMap sessions = callRecordMgr->getAllSessions(callId);
    if (sessions.empty())
    {
        UTIL_LOG_WRN("EbServer", "content:onHolded invalid sessId:" + sessId);
        MpCall::SipGatewayControllerServer::onHolded_end(__call, EbServiceError::SessionNotFound(ELOC));
        return;
    }

    for (auto kv : sessions)
    {
        bool ret;
        if (hold)
            ret = sendMsgHold(0, kv.second.peerAccount);
        else
            ret = sendMsgUnhold(0, kv.second.peerAccount);
        if (!ret)
        {
            UTIL_LOG_WRN("EbServer", "content:onHolded sessId:" + sessId + " account:" + kv.second.peerAccount + " send " + (hold ? "hold" : "unhold") + " failed:" + Common::ObjectAgent::getLogInfo(2));
            MpCall::SipGatewayControllerServer::onHolded_end(__call, EbServiceError::SendNotifyFailed(ELOC));
            return;
        }

        UTIL_LOG_IFO("EbServer", "content:onHolded sessId:" + sessId + " account:" + kv.second.peerAccount + (hold ? " hold " : " unhold "));
    }

    MpCall::SipGatewayControllerServer::onHolded_end(__call, true);
}

void EbServer::onTermed_begin(const Common::ServerCallPtr &__call, const Common::String &sessId)
{
    EbService::CallRecordManagerPtr callRecordMgr = _callRecordMgr;
    if (!callRecordMgr)
    {
        UTIL_LOG_WRN("EbServer", "content:onTermed sessId:" + sessId + " deactivated.");
        MpCall::SipGatewayControllerServer::onTermed_end(__call, EbServiceError::ServerShutdown(ELOC));
        return;
    }

    UTIL_LOG_IFO("EbServer", "content:onTermed sessId:" + sessId);
    callRecordMgr->setSessionTermed(getCallId(sessId), sessId);
    MpCall::SipGatewayControllerServer::onTermed_end(__call, true);
}

void EbServer::bindSession_begin(const Common::ServerCallPtr &__call, const Common::String &serialNumber, const Common::String &callId)
{
    UTIL_LOG_IFO("EbServer", "content:bind session serial:" + serialNumber + " callId:" + callId);

    EbService::CallRecordManagerPtr callRecordMgr = _callRecordMgr;
    if (!callRecordMgr)
    {
        UTIL_LOG_WRN("EbServer", "content:bind session server deactivated.");
        MpCallEb::OpenApiServer::bindSession_end(__call, EbServiceError::ServerShutdown(ELOC));
        return;
    }

    if (!callRecordMgr->setSerialNumber(callId, serialNumber))
    {
        UTIL_LOG_WRN("EbServer", "content:bind session failed.");
        MpCallEb::OpenApiServer::bindSession_end(__call, EbServiceError::BindSessionFailed(__ERROR_LOC__));
        return;
    }

    MpCallEb::OpenApiServer::bindSession_end(__call, true);
}

void EbServer::notifyAnswer_begin(const Common::ServerCallPtr &__call, const Common::String &serialNumber, const Common::String &calleeNumber)
{
    Common::String account, callId;
    if (!getCallIdAccount(serialNumber, calleeNumber, callId, account))
    {
        MpCallEb::OpenApiServer::notifyAnswer_end(__call, Common::getLastError());
        return;
    }

    if (account.empty())
    {
        UTIL_LOG_IFO("EbServer", "content:notify answer serial:" + serialNumber + " number:" + calleeNumber + " callId:" + callId + " account not found");
        MpCallEb::OpenApiServer::notifyAnswer_end(__call, EbServiceError::AccountNotFound(EARGS("serial", serialNumber, "number", calleeNumber)));
        return;
    }

    UTIL_LOG_IFO("EbServer", "content:notify answer serial:" + serialNumber + " number:" + calleeNumber + " callId:" + callId + " account:" + account);
    if (!sendMsgAnswer(0, account))
    {
        UTIL_LOG_WRN("EbServer", "content:notify answer failed:" + Common::ObjectAgent::getLogInfo(2));
        MpCallEb::OpenApiServer::notifyAnswer_end(__call, EbServiceError::SendNotifyFailed(EWRAP));
        return;
    }

    MpCallEb::OpenApiServer::notifyAnswer_end(__call, true);
}

void EbServer::notifyFilePlay_begin(const Common::ServerCallPtr &__call, const Common::String &serialNumber, const Common::String &calleeNumber, bool start, const Common::String &filename)
{
    Common::String account, callId;
    if (!getCallIdAccount(serialNumber, calleeNumber, callId, account))
    {
        UTIL_LOG_WRN("EbServer", "content:notify serial:" + serialNumber + " number:" + calleeNumber + (start ? " start" : " stop") + " play " + filename + "get session account failed.");
        MpCallEb::OpenApiServer::notifyFilePlay_end(__call, Common::getLastError());
        return;
    }

    if (account.empty())
        account = calleeNumber;

    bool ret;
    if (start)
        ret = sendMsgStartPlay(0, account, filename);
    else
        ret = sendMsgStopPlay(0, account, filename);
    if (!ret)
    {
        UTIL_LOG_WRN("EbServer", "content:notify serial:" + serialNumber + " number:" + calleeNumber + " callId:" + callId + " account:" + account + (start ? " start" : " stop") + " play " + filename + " failed:" + Common::ObjectAgent::getLogInfo(2));
        MpCallEb::OpenApiServer::notifyFilePlay_end(__call, EbServiceError::SendNotifyFailed(ELOC));
        return;
    }

    UTIL_LOG_IFO("EbServer", "content:notify serial:" + serialNumber + " number:" + calleeNumber + " callId:" + callId + " account:" + account + (start ? " start" : " stop") + " play " + filename);
    MpCallEb::OpenApiServer::notifyFilePlay_end(__call, true);
}

Common::String EbServer::getCallId(const Common::String &sessId)
{
    int pos = sessId.rfind("/");
    if (pos < 0)
        return sessId;

    return sessId.substr(pos + 1);
}

bool EbServer::getCallIdAccount(const Common::String &serialNumber, const Common::String &calleeNumber, Common::String &callId, Common::String &account)
{
    EbService::AgentCallNumberManagerPtr agentCallNumberMgr = _agentCallNumberMgr;
    EbService::CallRecordManagerPtr callRecordMgr = _callRecordMgr;
    if (!agentCallNumberMgr || !callRecordMgr)
    {
        UTIL_LOG_WRN("EbServer", "content:getCallIdAccount server deactivated.");
        Common::setError(EbServiceError::ServerShutdown(__ERROR_LOC__));
        return false;
    }

    callId = callRecordMgr->getCallId(serialNumber);
    if (callId.empty())
    {
        UTIL_LOG_WRN("EbServer", "content:getCallIdAccount serial:" + serialNumber + " session not found");
        Common::setError(EbServiceError::SessionNotFound(__ERROR_LOC__));
        return false;
    }

    account = agentCallNumberMgr->getBindedAccount(calleeNumber);
    UTIL_LOG_IFO("EbServer", "content:getCallIdAccount serial:" + serialNumber + " callId:" + callId + " number:" + calleeNumber + " account:" + account);
    return true;
}

bool EbServer::getCallIdAccount(const Common::String &serialNumber, const Common::String &calleeNumber, MpCallEb::CallRecord &record, Common::String &account)
{
    EbService::AgentCallNumberManagerPtr agentCallNumberMgr = _agentCallNumberMgr;
    EbService::CallRecordManagerPtr callRecordMgr = _callRecordMgr;
    if (!agentCallNumberMgr || !callRecordMgr)
    {
        UTIL_LOG_WRN("EbServer", "content:getCallIdAccount server deactivated.");
        Common::setError(EbServiceError::ServerShutdown(__ERROR_LOC__));
        return false;
    }

    if (!callRecordMgr->getCallRecord(serialNumber, record))
    {
        UTIL_LOG_WRN("EbServer", "content:getCallIdAccount serial:" + serialNumber + " session not found");
        Common::setError(EbServiceError::SessionNotFound(__ERROR_LOC__));
        return false;
    }

    account = agentCallNumberMgr->getBindedAccount(calleeNumber);
    if (account.empty())
    {
        UTIL_LOG_WRN("EbServer", "content:getCallIdAccount serial:" + serialNumber + " callee:" + calleeNumber + " account not found.");
        Common::setError(EbServiceError::AccountNotFound(__ERROR_LOC__));
        return false;
    }

    UTIL_LOG_IFO("EbServer", "content:getCallIdAccount serial:" + serialNumber + " callId:" + record.callId + " number:" + calleeNumber + " account:" + account);
    return true;
}

bool EbServer::sendMsg(const Common::AgentAsyncPtr async, const Common::String &account, const Common::String &type, Common::StrStrMap &params)
{
    EbService::MessageNotifierPtr notifier = _msgNotifier;
    if (!notifier)
    {
        UTIL_LOG_WRN("EbServer", "content:sendmsg server deactivated.");
        return false;
    }

    return notifier->sendMsg(async, account, type, params);
}

bool EbServer::sendMsgAnswer(const Common::AgentAsyncPtr async, const Common::String &account)
{
    UTIL_LOG_IFO("EbServer", "content:send message answer to account:" + account);

    Common::StrStrMap params;
    params["Account"] = account;
    return sendMsg(async, account, "AnswerResult", params);
}

bool EbServer::sendMsgHold(const Common::AgentAsyncPtr async, const Common::String &account)
{
    UTIL_LOG_IFO("EbServer", "content:send message hold to account:" + account);

    Common::StrStrMap params;
    params["KeepNotifyResult"] = "";
    return sendMsg(async, account, "NotifyResult", params);
}

bool EbServer::sendMsgUnhold(const Common::AgentAsyncPtr async, const Common::String &account)
{
    UTIL_LOG_IFO("EbServer", "content:send message unhold to account:" + account);

    Common::StrStrMap params;
    params["GetBackNotifyResult"] = "";
    return sendMsg(async, account, "NotifyResult", params);
}

bool EbServer::sendMsgStartPlay(const Common::AgentAsyncPtr async, const Common::String &account, const Common::String &filename)
{
    UTIL_LOG_IFO("EbServer", "content:send message start play to account:" + account + " filename:" + filename);

    Common::StrStrMap params;
    params["StartPlayMusicResult"] = filename;
    return sendMsg(async, account, "NotifyResult", params);
}

bool EbServer::sendMsgStopPlay(const Common::AgentAsyncPtr async, const Common::String &account, const Common::String &filename)
{
    UTIL_LOG_IFO("EbServer", "content:send message stop play to account:" + account + " filename:" + filename);

    Common::StrStrMap params;
    params["StopPlayMusicResult"] = filename;
    return sendMsg(async, account, "NotifyResult", params);
}

} // namespace MpCall
