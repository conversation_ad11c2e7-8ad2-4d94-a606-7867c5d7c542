//
// *****************************************************************************
// Copyright (c) 2023 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//
// Created on 2023/12/4 by <PERSON>
//

#include "MessageNotifierI.h"
#include "Account/AccountPub2Agent.h"
#include "MpCallEbService/MessageNotifier.h"

namespace EbService
{

MessageNotifierI::MessageNotifierI(const Common::ApplicationExPtr &app)
    : _app(app)
{
}

bool MessageNotifierI::onActivate(Common::String &failReason)
{
    if (!_app->getAppConfig("Eb.DomainId", _domainId))
    {
        UTIL_LOG_ERR("MessageNotifier", "content:invalid config Eb.DomainId");
        failReason = "MessageNotifier.InvalidConfig:Eb.DomainId";
        return false;
    }

    UTIL_LOG_IFO("MessageNotifier", "content:onActivate config Eb.DomainId:" + _domainId);
    _app->setStatistics("Eb.DomainId", _domainId);
    return true;
}

bool MessageNotifierI::sendMsg(const Common::AgentAsyncPtr async, const Common::String &account, const Common::String &type, Common::StrStrMap &params)
{
    params["notify"] = type;
    Account::AccountAgent agent = _app->createAgent("Account/" + getAccountId(account));
    if (async)
    {
        agent.sendOnlineMessage_begin(async, type, params, Common::Stream());
        return true;
    }

    return agent.sendOnlineMessage(type, params, Common::Stream());
}

Common::String MessageNotifierI::getAccountId(const Common::String &account)
{
    if (account.find("[") == 0)
        return account;

    return "[username:" + account + "@" + _domainId + ".cloud.justalk.com]";
}

MessageNotifierPtr MessageNotifier::create(const Common::ApplicationExPtr &app)
{
    MessageNotifierPtr mgr;

    try
    {
        mgr = new MessageNotifierI(app);
    }
    catch (const std::exception &e)
    {
        UTIL_LOG_ERR("DataDb", "content:create message notifier error:" + Common::String(e.what()));
        return nullptr;
    }

    return mgr;
}

} // namespace EbService