//
// *****************************************************************************
// Copyright (c) 2023 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//
// Created on 2023/11/27 by <PERSON>
//

#include "Common/CommonEx.h"
#include "Common/Util.h"
#include "AgentCallNumberI.h"
#include "DbConn/RedisClient.h"
#include "ServiceUtil/Struct2Json.h"
#include "MpCallEb/MpCallEbPub.h"
#include "MpCallEbService/ManagerData.h"

namespace EbService
{

AgentCallNumber::AgentCallNumber(const Common::String &account, const Common::String &number)
    : MpCallEb::AgentCallNumber(account, number)
{
    _keys.insert(AgentCallNumberManagerI::accountKey(account));
    _keys.insert(AgentCallNumberManagerI::numberKey(number));
}

Common::String AgentCallNumber::to_string()
{
    return ServiceUtil::to_json<MpCallEb::AgentCallNumber>(*this);
}

bool AgentCallNumber::from_string(const Common::String &str)
{
    if (!ServiceUtil::from_json<MpCallEb::AgentCallNumber>(*this, str))
    {
        UTIL_LOG_WRN("ManagerAccount", "content:from invalid json:" + str);
        return false;
    }

    _keys.clear();
    _keys.insert(AgentCallNumberManagerI::accountKey(account));
    _keys.insert(AgentCallNumberManagerI::numberKey(number));
    return true;
}

const DataKeys &AgentCallNumber::keys()
{
    return _keys;
}

AgentCallNumberManagerI::AgentCallNumberManagerI(const Common::ApplicationExPtr &app, const DataDbPtr &db)
    : _app(app)
    , _dataDb(db)
    , _startNumberId(-1)
    , _numberSize(-1)
    , _nextNumberIdOffset(0)
{
}

bool AgentCallNumberManagerI::onActivate(Common::String &failReason)
{
    Common::String config = _app->getAppConfig("Eb.Agent.CallNumberRange");
    int pos = config.find("-");
    if (pos < 1)
    {
        UTIL_LOG_ERR("ManagerAccount", "content:invalid agent call number range:" + config);
        failReason = "ManagerAccount.InvalidConfig:Eb.Agent.CallNumberRange";
        return false;
    }

    _startNumberId = config.substr(0, pos).toInt(-1);
    if (_startNumberId <= 0)
    {
        UTIL_LOG_ERR("ManagerAccount", "content:invalid agent call number range:" + config);
        failReason = "ManagerAccount.InvalidConfig:Eb.Agent.CallNumberRange";
        return false;
    }

    _endNumberId = config.substr(pos + 1).toInt(-1);
    _numberSize = _endNumberId - _startNumberId + 1;
    if (_endNumberId < _startNumberId || _numberSize <= 0)
    {
        UTIL_LOG_ERR("ManagerAccount", "content:invalid agent call number range:" + config + " too few call number count.");
        failReason = "ManagerAccount.TooFewCallNumberCount";
        return false;
    }
    _app->setStatistics("Eb.Agent.CallNumberRange", Common::String(_startNumberId) + "-" + Common::String(_endNumberId));

    _db = _dataDb->addCategory("acn", this);
    if (!_db)
    {
        UTIL_LOG_WRN("ManagerAccount", "content:create db category failed.");
        failReason = "ManagerAccount.CreateDbCategoryFailed";
        return false;
    }

    UTIL_LOG_IFO("ManagerAccount", "content:activate start:" + Common::String(_startNumberId) + " size:" + Common::String(_numberSize));
    return true;
}

void AgentCallNumberManagerI::onDeactivate()
{
    _app = 0;
    _db = 0;
    _dataDb = 0;
}

bool AgentCallNumberManagerI::bind(const Common::String &account, Common::String &number)
{
    DataCategoryPtr db = _db;
    if (!db)
    {
        UTIL_LOG_WRN("ManagerAccount", "content:bind account:" + account + " number:" + number + " deactivated.");
        return false;
    }

    if (!db->lock())
    {
        UTIL_LOG_WRN("ManagerAccount", "content:bind account:" + account + " number:" + number + " lock failed.");
        return false;
    }
    DataCategoryDeferUnlock deferUnlock(db);

    DataPtr data = db->get(accountKey(account));
    if (data)
    {
        number = AgentCallNumberPtr::dynamicCast(data)->number;
        UTIL_LOG_IFO("ManagerAccount", "content:bind account:" + account + " number:" + number + " exists");
        return true;
    }

    number = getNextNumber();
    if (number.empty())
    {
        UTIL_LOG_ERR("ManagerAccount", "content:bind account:" + account + " no free number size:" + Common::String(_numberSize));
        return false;
    }

    AgentCallNumberPtr callNumber = new AgentCallNumber(account, number);
    if (!db->set(callNumber, 30))
    {
        UTIL_LOG_WRN("ManagerAccount", "content:bind account:" + account + " number:" + number + " set db failed.");
        return false;
    }

    UTIL_LOG_IFO("ManagerAccount", "content:bind account:" + account + " number:" + number);
    return true;
}

void AgentCallNumberManagerI::unbind(const Common::String &account)
{
    DataCategoryPtr db = _db;
    if (!db)
    {
        UTIL_LOG_WRN("ManagerAccount", "content:unbind account:" + account + " deactivated.");
        return;
    }

    if (!db->lock())
    {
        UTIL_LOG_WRN("CallRecordManager", "content:unbind account:" + account + " lock failed.");
        return;
    }
    bool ret = db->update(accountKey(account), 0);
    db->unlock();

    if (!ret)
        UTIL_LOG_WRN("ManagerAccount", "content:unbind account:" + account);
    else
        UTIL_LOG_IFO("ManagerAccount", "content:unbind account:" + account);
}

bool AgentCallNumberManagerI::alive(const Common::String &account)
{
    DataCategoryPtr db = _db;
    if (!db)
    {
        UTIL_LOG_WRN("ManagerAccount", "content:alive account:" + account + " deactivated.");
        return false;
    }

    if (!db->lock())
    {
        UTIL_LOG_WRN("CallRecordManager", "content:alive account:" + account + " lock failed.");
        return false;
    }
    bool ret = db->update(accountKey(account), 30);
    db->unlock();

    if (!ret)
    {
        UTIL_LOG_WRN("ManagerAccount", "content:alive account:" + account + " failed.");
        return false;
    }

    UTIL_LOG_DBG("ManagerAccount", "content:alive account:" + account);
    return true;
}

Common::String AgentCallNumberManagerI::getBindedNumber(const Common::String &account)
{
    DataCategoryPtr db = _db;
    if (!db)
    {
        UTIL_LOG_WRN("ManagerAccount", "content:get binded number account:" + account + " deactivated.");
        return "";
    }

    if (!db->lock())
    {
        UTIL_LOG_WRN("CallRecordManager", "content:get binded number account:" + account + " lock failed.");
        return "";
    }
    DataPtr data = db->get(accountKey(account));
    db->unlock();

    if (!data)
    {
        UTIL_LOG_WRN("ManagerAccount", "content:get binded number account:" + account + " failed.");
        return "";
    }

    UTIL_LOG_DBG("ManagerAccount", "content:get binded number account:" + account + " number:" + AgentCallNumberPtr::dynamicCast(data)->number);
    return AgentCallNumberPtr::dynamicCast(data)->number;
}

Common::String AgentCallNumberManagerI::getBindedAccount(const Common::String &number)
{
    DataCategoryPtr db = _db;
    if (!db)
    {
        UTIL_LOG_WRN("ManagerAccount", "content:get binded account number:" + number + " deactivated.");
        return "";
    }

    if (!db->lock())
    {
        UTIL_LOG_WRN("CallRecordManager", "content:get binded account number:" + number + " lock failed.");
        return "";
    }
    DataPtr data = db->get(numberKey(number));
    db->unlock();

    if (!data)
    {
        UTIL_LOG_WRN("ManagerAccount", "content:get binded account number:" + number + " failed.");
        return "";
    }

    UTIL_LOG_DBG("ManagerAccount", "content:get binded account number:" + number + " account:" + AgentCallNumberPtr::dynamicCast(data)->account);
    return AgentCallNumberPtr::dynamicCast(data)->account;
}

DataPtr AgentCallNumberManagerI::createData(const Common::String &str)
{
    AgentCallNumberPtr data = new AgentCallNumber();

    if (!data->from_string(str))
    {
        UTIL_LOG_WRN("ManagerAccount", "conten:createData invalid str:" + str);
        return nullptr;
    }

    UTIL_LOG_VBS("ManagerAccount", "conten:createData str:" + str);
    return data.get();
}

Common::String AgentCallNumberManagerI::accountKey(const Common::String &account)
{
    return "AcnAcc:" + account;
}

Common::String AgentCallNumberManagerI::numberKey(const Common::String &number)
{
    return "AcnNum:" + number;
}

Common::String AgentCallNumberManagerI::getNextNumber()
{
    static Common::String lua_script = R"(
local start_id = tonumber(ARGV[1])
local end_id = tonumber(ARGV[2])
local max_attempts = end_id - start_id + 1

-- 设置初始值
redis.call('SETNX', KEYS[1], start_id)

-- 获取当前值
local current_id = tonumber(redis.call('GET', KEYS[1]))

-- 如果超过范围，则重置 current_id
if current_id > end_id then
    redis.call('SET', KEYS[1], start_id)
    current_id = start_id
end

-- 查找下一个未使用的 ID
local attempts = 0
while redis.call('EXISTS', ARGV[3] .. 'AcnNum:' .. current_id) == 1 do
    current_id = tonumber(redis.call('INCR', KEYS[1]))
    if current_id > end_id then
        redis.call('SET', KEYS[1], start_id)
        current_id = start_id
    end

    attempts = attempts + 1
    if attempts >= max_attempts then
        return ''
    end
end

redis.call('INCR', KEYS[1])

-- 返回未使用的 ID
return tostring(current_id)
)";

    Redis::CliPtr redis = _db->getRedis();
    if (!redis)
    {
        UTIL_LOG_WRN("ManagerAccount", "content:getNextNumber no redis client.");
        return Common::String();
    }

    Common::StrVec argv;
    argv.push_back(Common::String(_startNumberId));
    argv.push_back(Common::String(_endNumberId));
    argv.push_back(Redis::Key(""));

    Redis::RplPtr rpl;
    Common::String reason;
    if (!redis->evalSha(lua_script, Redis::Key("AcnNumCurrentId"), argv, rpl, reason))
    {
        UTIL_LOG_WRN("ManagerAccount", "content:getNextNumber failed:" + reason);
        return Common::String();
    }

    if (rpl->isErr())
    {
        UTIL_LOG_WRN("ManagerAccount", "content:getNextNumber exec error:" + rpl->getErr());
        return Common::String();
    }

    if (!rpl->isStr())
    {
        UTIL_LOG_WRN("ManagerAccount", "content:getNextNumber invalid type");
        return Common::String();
    }

    return rpl->getStr();
}

AgentCallNumberManagerPtr AgentCallNumberManager::create(const Common::ApplicationExPtr &app, const DataDbPtr &db)
{
    AgentCallNumberManagerPtr mgr;

    try
    {
        mgr = new AgentCallNumberManagerI(app, db);
    }
    catch (const std::exception &e)
    {
        UTIL_LOG_ERR("DataDb", "content:create agent call number manager error:" + Common::String(e.what()));
        return nullptr;
    }

    return mgr;
}

} // namespace EbService
