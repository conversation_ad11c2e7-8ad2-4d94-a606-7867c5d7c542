//
// *****************************************************************************
// Copyright (c) 2023 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//
// Created on 2023/12/4 by <PERSON>
//

#pragma once

#include "Common/CommonEx.h"
#include "Common/TypesPub.h"
#include "Util/ServiceModule.h"

namespace EbService
{

class MessageNotifier;
typedef Common::Handle<MessageNotifier> MessageNotifierPtr;

class MessageNotifier : public Service::ServiceModule
{
public:
    static MessageNotifierPtr create(const Common::ApplicationExPtr &app);

    virtual bool sendMsg(const Common::AgentAsyncPtr async, const Common::String &account, const Common::String &type, Common::StrStrMap &params) = 0;
};

} // namespace EbService