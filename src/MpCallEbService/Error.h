//
// *****************************************************************************
// Copyright (c) 2023 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//
// Created on 2023/6/14 by <PERSON>
//

#pragma once

#include "Common/Common.h"
#include "Common/Error.h"

namespace MpCall
{

namespace EbServiceError
{
    static const char *const DomainCode = "M30";

    ERROR_DECLARE_F(18000, ServerShutdown);
    ERROR_DECLARE_F(18001, AccountNotFound);
    ERROR_DECLARE_F(18002, BindAccountNumberFailed);
    ERROR_DECLARE_F(18003, SessionNotFound);
    ERROR_DECLARE_F(18004, SendNotifyFailed);
    ERROR_DECLARE_F(18005, BindSessionFailed);
    ERROR_DECLARE_F(18006, GetSerialNumberFailed);
    ERROR_DECLARE_F(18007, AddCallRecordFailed);
    ERROR_DECLARE_F(18008, InvalidParam);
    ERROR_DECLARE_F(18009, QueryHttpFailed);
    ERROR_DECLARE_F(18010, TokenCheckFailed);
    ERROR_DECLARE_F(18011, SerialNumberNotFound);
} // namespace EbServiceError

} // namespace MpCall