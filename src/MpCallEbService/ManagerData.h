//
// *****************************************************************************
// Copyright (c) 2023 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//
// Created on 2023/11/27 by <PERSON>
//

#pragma  once

#include "Common/CommonEx.h"
#include "Common/TypesPub.h"
#include "Common/Util.h"
#include "Util/ServiceModule.h"
#include "DbConn/RedisClient.h"

namespace EbService
{

typedef Common::StrSet DataKeys;

class Data : virtual public Common::Shared
{
public:
    virtual Common::String to_string() { return ""; }
    virtual bool from_string(const Common::String &str) { return false; }

    virtual const DataKeys &keys() = 0;

    Common::String did;
};

typedef Common::Handle<Data> DataPtr;

class DataCategoryListener : virtual public Common::Shared
{
public:
    virtual DataPtr createData(const Common::String &str) = 0;
};

typedef Common::Handle<DataCategoryListener> DataCategoryListenerPtr;

class DataCategory : virtual public Common::Shared
{
public:
    virtual bool lock(int timeout = -1) = 0;
    virtual bool unlock() = 0;

    virtual bool set(const DataPtr &data, int expires) = 0;
    virtual DataPtr get(const Common::String &key) = 0;
    virtual bool update(const Common::String &key, int expires) = 0;

    virtual Redis::CliPtr getRedis() { return nullptr; }
};

typedef Common::Handle<DataCategory> DataCategoryPtr;

class DataCategoryDeferUnlock
{
public:
    explicit DataCategoryDeferUnlock(const DataCategoryPtr &category)
        : _category(category)
    {
    }

    ~DataCategoryDeferUnlock()
    {
        _category->unlock();
    }

private:
    DataCategoryPtr _category;
};

class DataDb;
typedef Common::Handle<DataDb> DataDbPtr;

class DataDb : public Service::ServiceModule
{
public:
    static DataDbPtr create(const Common::ApplicationPtr &app);

    virtual DataCategoryPtr addCategory(const Common::String &name, const DataCategoryListenerPtr &listener) = 0;
};

} // namespace EbService