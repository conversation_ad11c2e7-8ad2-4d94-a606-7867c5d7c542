//
// *****************************************************************************
// Copyright (c) 2023 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//
// Created on 2023/12/2 by <PERSON>
//

#include "Mocks/MockApp.h"
#include "MpCallEb/MpCallEbServerMock.h"

#include "gtest/gtest.h"
#include <functional>
#include "Common/Common.h"
#include "Common/Util.h"
#include "MpCallEb/MpCallEbServer.h"
#include "MpCallEbService/OpenApiServer.h"
#include "Common/CommonEx.h"
#include "Common/TypesPub.h"
#include "cpp-httplib/httplib.h"
#include "TestHttpServer.h"

using ::testing::_;
using ::testing::Eq;

class OpenApiServerTestServer : public MpCallEb::OpenApiServer
{
public:
    virtual void bindSession_begin(const Common::ServerCallPtr &__call, const Common::String &serialNumber, const Common::String &callId) = 0;
    virtual void notifyAnswer_begin(const Common::ServerCallPtr &__call, const Common::String &serialNumber, const Common::String &calleeNumber) = 0;
    virtual void notifyFilePlay_begin(const Common::ServerCallPtr &__call, const Common::String &serialNumber, const Common::String &calleeNumber, bool start, const Common::String &filename) = 0;
};

class OpenApiServerTest : public ModuleTest::MockTest
{
public:
    void SetUp() override
    {
        ModuleTest::MockTest::SetUp();
        openApiServer = AddServer<ModuleTest::OpenApiServerMockBase>("EbService");

        httpSvr = new TestHttpServer();
    }

    void TearDown() override
    {
        httpSvr->close();
        ModuleTest::MockTest::TearDown();
    }

    Common::StrStrMap appConfigs() override
    {
        Common::StrStrMap configs;
        configs["global.Eb.Http.ListenHost"] = "127.0.0.1";
        configs["global.Eb.Http.ListenPort"] = "12080";
        return configs;
    }

    Common::Handle<TestHttpServer> httpSvr;
    ModuleTest::OpenApiServerMockBasePtr openApiServer;
};

TEST_F(OpenApiServerTest, Http2Rpc)
{
    EbService::OpenApiManagerPtr mgr = EbService::OpenApiManager::create(_app);
    ASSERT_TRUE(mgr != nullptr);
    Common::String failReason;
    ASSERT_TRUE(mgr->onActivate(failReason));

    httplib::Client client = httplib::Client("http://127.0.0.1:12080");

    EXPECT_CALL(*(openApiServer.get()), bindSession_begin(_, Eq("123"), Eq("hello"))).WillOnce([](const Common::ServerCallPtr &__call, const Common::String &serialNumber, const Common::String &callId) {
        MpCallEb::OpenApiServer::bindSession_end(__call, true);
    });
    auto ret = client.Post("/", R"({"head":{"IDNO":"123", "ReqName":"InService", "Terminal":"terminal", "InvokeId":"xxxx"}, "body":{"ObjectId":"hello"}})", "application/json");
    ASSERT_TRUE(ret);

    EXPECT_CALL(*(openApiServer.get()), notifyAnswer_begin(_, Eq("123"), Eq("terminal"))).WillOnce([](const Common::ServerCallPtr &__call, const Common::String &serialNumber, const Common::String &calleeNumber) {
        MpCallEb::OpenApiServer::notifyAnswer_end(__call, true);
    });
    ret = client.Post("/", R"({"head":{"IDNO":"123", "ReqName":"AnswerReq", "Terminal":"terminal", "InvokeId":"xxxx"}, "body":{}})", "application/json");
    ASSERT_TRUE(ret);

    EXPECT_CALL(*(openApiServer.get()), notifyFilePlay_begin(_, Eq("123"), Eq("terminal"), true, Eq("hold.wav"))).WillOnce([](const Common::ServerCallPtr &__call, const Common::String &serialNumber, const Common::String &calleeNumber, bool start, const Common::String &filename) {
        MpCallEb::OpenApiServer::notifyFilePlay_end(__call, true);
    });
    ret = client.Post("/", R"({"head":{"IDNO":"123", "ReqName":"StartPlay", "Terminal":"terminal", "InvokeId":"xxxx"}, "body":{"FileName":"hold.wav"}})", "application/json");
    ASSERT_TRUE(ret);

    EXPECT_CALL(*(openApiServer.get()), notifyFilePlay_begin(_, Eq("123"), Eq("terminal"), false, Eq(""))).WillOnce([](const Common::ServerCallPtr &__call, const Common::String &serialNumber, const Common::String &calleeNumber, bool start, const Common::String &filename) {
        MpCallEb::OpenApiServer::notifyFilePlay_end(__call, true);
    });
    ret = client.Post("/", R"({"head":{"IDNO":"123", "ReqName":"StopPlay", "Terminal":"terminal", "InvokeId":"xxxx"}, "body":{}})", "application/json");
    ASSERT_TRUE(ret);

    Common::sleep(1000);
}

TEST_F(OpenApiServerTest, Rpc2Http)
{
    httpSvr->open("127.0.0.1", 11080, "/mr/engine/execsql.action", [](const httplib::Request &req, httplib::Response &rsp) {
        std::string sqlId = req.get_param_value("sqlId");
        if (sqlId == "ebaseCloud.ngplgetqueueorder")
        {
            rsp.status = 200;
            rsp.body = R"({"isSucc":true,"result":{"queueorder":"3"}})";
        }
        else if (sqlId == "ebaseCloud.ngplqueuecntandforecast")
        {
            rsp.status = 200;
            rsp.body = R"({"isSucc":true,"result":{"queuecnt":"3","forecast":"1"}})";
        }
        else
        {
            rsp.status = 400;
        }
    });

    EbService::OpenApiManagerPtr mgr = EbService::OpenApiManager::create(_app);
    ASSERT_TRUE(mgr != nullptr);
    Common::String failReason;
    ASSERT_TRUE(mgr->onActivate(failReason));

    ASSERT_TRUE(mgr->getUuiInfo() == "jfip=127.0.0.1;jfport=12080;");

    Common::sleep(100);
    Common::String queueOrder;
    ASSERT_TRUE(mgr->queryWaitSize("http://127.0.0.1:11080", 1111, "testflowno", queueOrder));
    ASSERT_TRUE(queueOrder == "3");

    Common::String queueCnt, forecast;
    ASSERT_TRUE(mgr->queryQueueLen("http://127.0.0.1:11080", 1111, 6000, queueCnt, forecast));
    ASSERT_TRUE(queueCnt == "3");
    ASSERT_TRUE(forecast == "1");
}