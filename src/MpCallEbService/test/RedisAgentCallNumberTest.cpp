//
// *****************************************************************************
// Copyright (c) 2023 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//
// Created on 2023/12/12 by <PERSON>
//

#include "gtest/gtest.h"

#include "Common/Common.h"
#include "Common/TypesPub.h"
#include "Common/Util.h"
#include "MpCallEbService/AgentCallNumber.h"
#include "MpCallEbService/ManagerData.h"

class RedisAgentCallNumberTest : public ::testing::Test
{
public:
    void SetUp()
    {
        system("../tools/redis-server-start.sh");
        Common::sleep(1000);

        Common::StrStrMap configs;
        configs["global.Log.Level"] = "4";
        configs["global.Log.Print"] = "1";
        configs["global.RedisUri"] = "redis://127.0.0.1:6379/?pwd=qbEYCMQtB16tfBcOIyHW1Db5BjX0Mqdf";
        configs["global.Redis.Namespace"] = "jf";
        configs["global.Eb.Agent.CallNumberRange"] = "1000-1999";
        _app = Common::ApplicationEx::create("Test2-0", "", 0, configs);
        ASSERT_TRUE(_app->activate());

        Common::String failReason;

        _db = EbService::DataDb::create(_app);
        ASSERT_TRUE(_db->onActivate(failReason));

        _mgr = EbService::AgentCallNumberManager::create(_app, _db);
        ASSERT_TRUE(_mgr->onActivate(failReason));
    }

    void TearDown()
    {
        _mgr->onDeactivate();
        _db->onDeactivate();
        _app->indShutdown();
        while (!_app->isShutdown())
            Common::sleep(100);
    }

    Common::ApplicationExPtr _app;
    EbService::DataDbPtr _db;
    EbService::AgentCallNumberManagerPtr _mgr;
};

TEST_F(RedisAgentCallNumberTest, Normal)
{
    Common::String number;
    ASSERT_TRUE(_mgr->bind(Common::String("xxxx"), number));
    ASSERT_TRUE(_mgr->getBindedNumber("xxxx") == number);
    ASSERT_TRUE(_mgr->getBindedAccount(number) == "xxxx");

    for (int i = 0; i < 10; i++)
    {
        Common::sleep(3000);
        _db->onUpdateConfigs();
        _mgr->alive("xxxx");
    }

    ASSERT_TRUE(_mgr->getBindedNumber("xxxx") == number);
    ASSERT_TRUE(_mgr->getBindedAccount(number) == "xxxx");

    for (int i = 0; i < 10; i++)
    {
        Common::sleep(3000);
        _db->onUpdateConfigs();
    }

    ASSERT_TRUE(_mgr->getBindedNumber("xxxx") == "");
    ASSERT_TRUE(_mgr->getBindedAccount(number) == "");
}