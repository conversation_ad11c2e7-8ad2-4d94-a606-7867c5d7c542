//
// *****************************************************************************
// Copyright (c) 2023 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//
// Created on 2023/12/2 by <PERSON>
//

#include "gtest/gtest.h"
#include "Common/CommonEx.h"
#include "MpCallEb/MpCallEbPub.h"
#include "MpCallEbService/CallRecord.h"

class CallRecordTest : public ::testing::Test
{
public:
    void SetUp(const Common::String &appname, Common::StrStrMap &configs)
    {
        system("../tools/redis-server-start.sh");
        configs["global.RedisUri"] = "redis://127.0.0.1:6379/?pwd=juphoon419708";

        app = Common::ApplicationEx::create(appname, "", 0, configs);
        ASSERT_TRUE(app->activate());
        db = EbService::DataDb::create(app.get());
        Common::String failReason;
        db->onActivate(failReason);
    }

    void TearDown()
    {
        db->onDeactivate();
        app->indShutdown();
        while (!app->isShutdown())
            Common::sleep(10);
    }

    EbService::DataDbPtr db;
    Common::ApplicationExPtr app;
};

TEST_F(CallRecordTest, Normal)
{
    Common::StrStrMap configs;
    configs["global.Log.Level"] = "3";
    configs["global.Log.Print"] = "1";
    SetUp("Test2-0", configs);

    EbService::CallRecordManagerPtr mgr = EbService::CallRecordManager::create(app, db);

    ASSERT_TRUE(mgr != nullptr);
    Common::String failReason;
    ASSERT_TRUE(mgr->onActivate(failReason));

    Common::String callId = "1234567";
    Common::String serial = "8888888";
    Common::String pti = "hello world";
    ASSERT_TRUE(mgr->add(callId, pti, "", MpCallEb::SessionInfo("session1", "account1")));
    ASSERT_TRUE(mgr->setSerialNumber(callId, serial));
    ASSERT_TRUE(mgr->setSession(callId, MpCallEb::SessionInfo("session2", "account2")));

    ASSERT_TRUE(mgr->getCallId(serial) == callId);
    ASSERT_TRUE(mgr->getCallId("invalid") == "");

    MpCallEb::CallRecord record;
    ASSERT_TRUE(mgr->getCallRecord(serial, record));
    ASSERT_TRUE(record.callId == callId);
    ASSERT_TRUE(record.passthroughInfo == pti);

    MpCallEb::SessionInfo info = mgr->getSession(callId, "session1");
    ASSERT_TRUE(info.peerAccount == "account1");
    info = mgr->getSession(callId, "session2");
    ASSERT_TRUE(info.peerAccount == "account2");
    info = mgr->getSession(callId, "sessionX");
    ASSERT_TRUE(info.sessionId == "");
    ASSERT_TRUE(info.peerAccount == "");

    MpCallEb::SessionInfoMap sessions = mgr->getAllSessions(callId);
    ASSERT_EQ(sessions.size(), 2);
    auto it = sessions.find("session1");
    ASSERT_TRUE(it != sessions.end());
    ASSERT_TRUE(it->second.sessionId == "session1");
    ASSERT_TRUE(it->second.peerAccount == "account1");
    it = sessions.find("session2");
    ASSERT_TRUE(it != sessions.end());
    ASSERT_TRUE(it->second.sessionId == "session2");
    ASSERT_TRUE(it->second.peerAccount == "account2");
    it = sessions.find("sessionX");
    ASSERT_TRUE(it == sessions.end());

    ASSERT_FALSE(mgr->setSessionTermed(callId, "sessionX"));
    ASSERT_TRUE(mgr->setSessionTermed(callId, "session1"));
    info = mgr->getSession(callId, "session1");
    ASSERT_TRUE(info.sessionId == "");
    ASSERT_TRUE(info.peerAccount == "");
    info = mgr->getSession(callId, "session2");
    ASSERT_TRUE(info.peerAccount == "account2");

    ASSERT_TRUE(mgr->setSessionTermed(callId, "session2"));
    info = mgr->getSession(callId, "session2");
    ASSERT_TRUE(info.sessionId == "");
    ASSERT_TRUE(info.peerAccount == "");

    ASSERT_FALSE(mgr->setSerialNumber(callId, serial));
    ASSERT_FALSE(mgr->setSession(callId, MpCallEb::SessionInfo("session3", "account3")));
}