//
// *****************************************************************************
// Copyright (c) 2023 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//
// Created on 2023/12/1 by <PERSON>
//

#pragma once

#include "gmock/gmock.h"
#include "Common/Util.h"
#include "MpCallEbService/MessageNotifier.h"

namespace EbService
{

class MessageNotifierMock : public MessageNotifier
{
public:
    MOCK_METHOD(bool, onActivate, (Common::String &failReason), (override));
    MOCK_METHOD(void, onDeactivate, (), (override));
    MOCK_METHOD(void, onUpdateConfigs, (), (override));
    MOCK_METHOD(bool, sendMsg, (const Common::AgentAsyncPtr async, const Common::String &account, const Common::String &type, Common::StrStrMap &params), (override));
};

typedef Common::Handle<MessageNotifierMock> MessageNotifierMockPtr;

} // namespace EbService
