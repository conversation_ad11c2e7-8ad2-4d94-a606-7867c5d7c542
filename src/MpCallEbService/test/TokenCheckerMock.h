//
// *****************************************************************************
// Copyright (c) 2023 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//
// Created on 2023/12/1 by <PERSON>
//

#pragma once

#include "gmock/gmock.h"
#include "Common/Util.h"
#include "MpCallEbService/TokenChecker.h"

namespace EbService
{

class TokenCheckerMock : public TokenChecker
{
public:
    MOCK_METHOD(bool, onActivate, (Common::String &failReason), (override));
    MOCK_METHOD(void, onDeactivate, (), (override));
    MOCK_METHOD(void, onUpdateConfigs, (), (override));
    MOCK_METHOD(bool, checkValid, (const Common::String &token, Common::String &value), (override));
};

typedef Common::Handle<TokenCheckerMock> TokenCheckerMockPtr;

} // namespace EbService
