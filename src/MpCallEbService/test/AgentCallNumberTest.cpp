//
// *****************************************************************************
// Copyright (c) 2023 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//
// Created on 2023/12/1 by <PERSON>
//

#include "gtest/gtest.h"
#include "Common/CommonEx.h"
#include "Common/Util.h"
#include "MpCallEbService/AgentCallNumber.h"

class AgentCallNumberTest : public ::testing::Test
{
public:
    void SetUp(const Common::String &appname, Common::StrStrMap &configs)
    {
        system("../tools/redis-server-start.sh");
        configs["global.RedisUri"] = "redis://127.0.0.1:6379/?pwd=juphoon419708";

        app = Common::ApplicationEx::create(appname, "", 0, configs);
        ASSERT_TRUE(app->activate());
        db = EbService::DataDb::create(app.get());
        Common::String faileReason;
        db->onActivate(faileReason);
    }

    void TearDown()
    {
        db->onDeactivate();
        app->indShutdown();
        while (!app->isShutdown())
            Common::sleep(10);
    }

    EbService::DataDbPtr db;
    Common::ApplicationExPtr app;
};

TEST_F(AgentCallNumberTest, Bind)
{
    Common::StrStrMap configs;
    configs["global.Eb.Agent.CallNumberRange"] = "1000-1999";
    configs["global.Log.Level"] = "3";
    configs["global.Log.Print"] = "1";

    SetUp("Test2-1", configs);

    EbService::AgentCallNumberManagerPtr mgr = EbService::AgentCallNumberManager::create(app, db);
    ASSERT_TRUE(mgr != nullptr);
    Common::String faileReason;
    ASSERT_TRUE(mgr->onActivate(faileReason));

    Common::String number;
    for (int i = 0; i < 1000; i++)
        ASSERT_TRUE(mgr->bind(Common::String(i), number));

    ASSERT_FALSE(mgr->bind(Common::String("xxxx"), number));
}

TEST_F(AgentCallNumberTest, Timeout)
{
    Common::StrStrMap configs;
    configs["global.Eb.Agent.CallNumberRange"] = "1000-1999";
    configs["global.Log.Level"] = "3";
    configs["global.Log.Print"] = "1";

    SetUp("Test2-0", configs);

    EbService::AgentCallNumberManagerPtr mgr = EbService::AgentCallNumberManager::create(app, db);
    ASSERT_TRUE(mgr != nullptr);
    Common::String faileReason;
    ASSERT_TRUE(mgr->onActivate(faileReason));

    Common::String number;
    ASSERT_TRUE(mgr->bind(Common::String("xxxx"), number));
    ASSERT_TRUE(mgr->getBindedNumber("xxxx") == number);
    ASSERT_TRUE(mgr->getBindedAccount(number) == "xxxx");

    for (int i = 0; i < 10; i++)
    {
        Common::sleep(3000);
        db->onUpdateConfigs();
        mgr->alive("xxxx");
    }

    ASSERT_TRUE(mgr->getBindedNumber("xxxx") == number);
    ASSERT_TRUE(mgr->getBindedAccount(number) == "xxxx");

    for (int i = 0; i < 10; i++)
    {
        Common::sleep(3000);
        db->onUpdateConfigs();
    }

    ASSERT_TRUE(mgr->getBindedNumber("xxxx") == "");
    ASSERT_TRUE(mgr->getBindedAccount(number) == "");
}