//
// *****************************************************************************
// Copyright (c) 2023 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//
// Created on 2023/12/12 by <PERSON>
//

#include "gtest/gtest.h"

#include "Common/Common.h"
#include "Common/TypesPub.h"
#include "Common/Util.h"
#include "MpCallEbService/ManagerData.h"

class RedisDataDbTestSchd : public Common::AppScheduler
{
public:
    explicit RedisDataDbTestSchd(EbService::DataDbPtr db) : _db(db) {}

    virtual bool onActivate() { return true; }
    virtual void onDeactivate() { _db = 0; }
    virtual void onShutdown() {}
    virtual void onSchd() {}
    virtual void onUpdateConfigs() { _db->onUpdateConfigs(); }

    EbService::DataDbPtr _db;
};

class RedisDataDbTest : public ::testing::Test
{
public:
    void SetUp()
    {
        system("../tools/redis-server-start.sh");

        Common::StrStrMap configs;
        configs["global.Log.Level"] = "4";
        configs["global.Log.Print"] = "1";
        configs["global.RedisUri"] = "redis://127.0.0.1:6379/?pwd=qbEYCMQtB16tfBcOIyHW1Db5BjX0Mqdf";
        configs["global.Redis.Namespace"] = "jf";
        _app = Common::Application::create("Test1-0", "", 0, configs);
        ASSERT_TRUE(_app->activate());

        Common::String failReason;
        _db = EbService::DataDb::create(_app);
        ASSERT_TRUE(_db->onActivate(failReason));
        _app->addScheduler(new RedisDataDbTestSchd(_db));
    }

    void TearDown()
    {
        _db->onDeactivate();
        _app->indShutdown();
        while (!_app->isShutdown())
            Common::sleep(100);
    }

    Common::ApplicationPtr _app;
    EbService::DataDbPtr _db;
};

class RedisDataDbTestData : public EbService::Data
{
public:
    RedisDataDbTestData() {}

    RedisDataDbTestData(const Common::String &key1, const Common::String &key2)
    {
        _keys.insert(key1);
        _keys.insert(key2);
    }

    virtual Common::String to_string() override
    {
        return "hello";
    }

    virtual bool from_string(const Common::String &str) override
    {
        if (str != "hello")
            return false;

        _keys.insert("foo");
        _keys.insert("bar");
        return true;
    }

    virtual const EbService::DataKeys &keys() override
    {
        return _keys;
    }

    EbService::DataKeys _keys;
};

class RedisDataDbTestCategory : public EbService::DataCategoryListener
{
public:
    virtual EbService::DataPtr createData(const Common::String &str) override
    {
        Common::Handle<RedisDataDbTestData> data = new RedisDataDbTestData();
        if (!data->from_string(str))
            return nullptr;
        return data.get();
    }
};

TEST_F(RedisDataDbTest, Normal)
{
    EbService::DataCategoryPtr category = _db->addCategory("test", new RedisDataDbTestCategory());
    ASSERT_TRUE(category != nullptr);

    std::cout << "----------------lock" << std::endl;
    unsigned int startTicks = Common::getCurTicks();
    ASSERT_TRUE(category->lock());
    ASSERT_LT(Common::getCurTicks() - startTicks, 100);

    std::cout << "----------------check reenter lock" << std::endl;
    startTicks = Common::getCurTicks();
    ASSERT_FALSE(category->lock(3000));
    ASSERT_GT(Common::getCurTicks() - startTicks, 1000);
    ASSERT_LT(Common::getCurTicks() - startTicks, 5000);

    std::cout << "----------------check enter lock after unlock" << std::endl;
    ASSERT_TRUE(category->unlock());
    startTicks = Common::getCurTicks();
    ASSERT_TRUE(category->lock());
    ASSERT_LT(Common::getCurTicks() - startTicks, 100);
    ASSERT_TRUE(category->unlock());

    std::cout << "----------------set data" << std::endl;
    ASSERT_TRUE(category->set(new RedisDataDbTestData("foo", "bar"), 30));
    EbService::DataPtr data = category->get("foo");
    ASSERT_TRUE(data != nullptr);
    data = category->get("bar");
    ASSERT_TRUE(data != nullptr);

    std::cout << "----------------chceck data timeout" << std::endl;
    Common::sleep(30500);
    data = category->get("foo");
    ASSERT_TRUE(data == nullptr);
    data = category->get("bar");
    ASSERT_TRUE(data == nullptr);

    std::cout << "----------------set data again" << std::endl;
    ASSERT_TRUE(category->set(new RedisDataDbTestData("foo", "bar"), 30));
    data = category->get("foo");
    ASSERT_TRUE(data != nullptr);
    data = category->get("bar");
    ASSERT_TRUE(data != nullptr);

    std::cout << "----------------update data by key1" << std::endl;
    Common::sleep(15000);
    ASSERT_TRUE(category->update("foo", 30));
    data = category->get("foo");
    ASSERT_TRUE(data != nullptr);
    data = category->get("bar");
    ASSERT_TRUE(data != nullptr);

    std::cout << "----------------update data by key2" << std::endl;
    Common::sleep(15000);
    ASSERT_TRUE(category->update("bar", 30));
    data = category->get("foo");
    ASSERT_TRUE(data != nullptr);
    data = category->get("bar");
    ASSERT_TRUE(data != nullptr);

    std::cout << "----------------update expired" << std::endl;
    Common::sleep(15500);
    ASSERT_TRUE(category->update("foo", 0));
    data = category->get("foo");
    ASSERT_TRUE(data == nullptr);
    data = category->get("bar");
    ASSERT_TRUE(data == nullptr);
}