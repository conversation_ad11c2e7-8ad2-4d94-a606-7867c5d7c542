//
// *****************************************************************************
// Copyright (c) 2023 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//
// Created on 2023/12/1 by <PERSON>
//

#include "Common/Property.h"
#include "MessageNotifierMock.h"
#include "TokenCheckerMock.h"

#include "gmock/gmock-actions.h"
#include "gmock/gmock-matchers.h"
#include "gmock/gmock-spec-builders.h"
#include "gtest/gtest.h"
#include "Common/Common.h"
#include "Common/CommonEx.h"
#include "Common/TypesPub.h"
#include "Common/Util.h"
#include "Common/Property_.h"
#include "MpCall/MpCallPub.h"
#include "MpCall/MpCallSipServer.h"
#include "MpCallEbService/MpCallEbService.h"

#include "MpCall/MpCallSipAgent.h"
#include "MpCallEb/MpCallEbAgent.h"
#include "AcdGateway/MpCallEbAgent.h"
#include "CcSvr/CallCenterSvrAgent.h"

#include "Service/ServiceI.h"
#include "Mocks/Service.h"
#include "cpp-httplib/httplib.h"
#include "TestHttpServer.h"

static MpCall::EbServerPtr __ebSvr;

const char *__getManagerConfigPath() { return ""; }
const char *__getServerConfigPath() { return ""; }
Common::AppSchedulerPtr __createService(const Common::ApplicationExPtr &app) { return __ebSvr; }

using ::testing::_;
using ::testing::Eq;
using ::testing::Key;
using ::testing::Return;
using ::testing::Contains;

class EbServiceTest : public ::testing::Test
{
public:
    void SetUp(const Common::String &appname, Common::StrStrMap &configs)
    {
        system("../tools/redis-server-start.sh");
        configs["global.RedisUri"] = "redis://127.0.0.1:6379/?pwd=juphoon419708";

        notifier = new EbService::MessageNotifierMock();
        EXPECT_CALL(*(notifier.get()), onActivate(_)).WillOnce(Return(true));
        EXPECT_CALL(*(notifier.get()), onUpdateConfigs()).WillRepeatedly(Return());
        EXPECT_CALL(*(notifier.get()), onDeactivate());

        checker = new EbService::TokenCheckerMock();
        EXPECT_CALL(*(checker.get()), onActivate(_)).WillOnce(Return(true));
        EXPECT_CALL(*(checker.get()), onUpdateConfigs()).WillRepeatedly(Return());
        EXPECT_CALL(*(checker.get()), onDeactivate());

        configs["global.Log.Level"] = "3";
        configs["global.Log.Print"] = "1";
        configs["global.Log.Verbose.AgentCall"] = "5";
        configs["global.Log.Verbose.ServerCall"] = "5";
        configs["global.Main.Endpoints"] = "sudp -h 127.0.0.1;";
        app = Common::ApplicationEx::create(appname, "", 0, configs);
        ASSERT_TRUE(app != nullptr);

        MpCall::EbServer::Interfaces itfs;
        itfs.msgNotifier = notifier;
        itfs.tokenChecker = checker;

        __ebSvr = new MpCall::EbServer(app, itfs);
        ASSERT_TRUE(__ebSvr != nullptr);

        service = new ModuleTest::Service(appname);
        ASSERT_EQ(service->start(configs, app), 0);

        ASSERT_TRUE(app->activate());

        httpSvr = new TestHttpServer();
        httpSvr->open("127.0.0.1", 11080, "/mr/engine/execsql.action", [](const httplib::Request &req, httplib::Response &rsp) {
            std::string sqlId = req.get_param_value("sqlId");
            if (sqlId == "ebaseCloud.ngplgetqueueorder")
            {
                rsp.status = 200;
                rsp.body = R"({"isSucc":true,"result":{"queueorder":"3"}})";
            }
            else if (sqlId == "ebaseCloud.ngplqueuecntandforecast")
            {
                rsp.status = 200;
                rsp.body = R"({"isSucc":true,"result":{"queuecnt":"3","forecast":"1"}})";
            }
            else
            {
                rsp.status = 400;
            }
        });

        Common::sleep(6000);
    }

    void TearDown() override
    {
        httpSvr->close();
        httpSvr = 0;
        __ebSvr = 0;
        service->stop();
        app = 0;
        notifier = 0;
    }

    EbService::MessageNotifierMockPtr notifier;
    EbService::TokenCheckerMockPtr checker;
    Common::ApplicationExPtr app;
    Common::Handle<ModuleTest::Service> service;
    Common::Handle<TestHttpServer> httpSvr;
};

TEST_F(EbServiceTest, Normal)
{
    Common::String LineNumber = "666000";
    Common::String SkillGroupNumber = "5600";
    Common::String SessId = "session100645_0";
    Common::String CallOutSessId = "5600/session100645_0";

    Common::StrStrMap configs;
    configs["global.Eb.LineCallNumber"] = LineNumber;
    configs["global.Eb.Agent.CallNumberRange"] = "1000-1999";
    configs["global.Eb.Http.ListenHost"] = "127.0.0.1";
    configs["global.Eb.Http.ListenPort"] = "12080";
    SetUp("App2-0", configs);

    CcSvr::CcSvrAgent ccSvr = app->createAgent("#CcSvr");
    Common::String agentNumber;
    ASSERT_TRUE(ccSvr.checkin(agentNumber, Common::CallParams::create(CALL_PARAMS_ACCOUNT, "[username:<EMAIL>]")));
    ASSERT_FALSE(agentNumber.empty());

    EXPECT_CALL(*(checker.get()), checkValid(Eq("000000"), _)).WillOnce([](const Common::String &token, Common::String &value) {
        value = "token123456";
        return true;
    });
    MpCall::SipGatewayControllerAgent ctrl = app->createAgent("EbService");
    Common::String outCallee;
    MpCall::InviteConfig outInviteConfig;
    outInviteConfig.uui = "token=000000;pti=hello;";
    ASSERT_TRUE(ctrl.onCallout(CallOutSessId, "guest", SkillGroupNumber, outInviteConfig, outCallee, outInviteConfig));
    ASSERT_TRUE(outCallee == LineNumber);
    ASSERT_TRUE(outInviteConfig.uui.find(SkillGroupNumber) > 0);

    httplib::Client httpCli = httplib::Client("http://127.0.0.1:12080");
    auto ret = httpCli.Post("/", R"({"head":{"IDNO":"**********", "ReqName":"InService", "Terminal":"1000", "InvokeId":"01"}, "body":{"ObjectId":"session100645_0"}})", "application/json");
    std::cout << ret->status << " " << ret->body << std::endl;
    ASSERT_TRUE(ret);

    AcdGateway::AcdGatewayAgent acdGw = app->createAgent("#AcdGateway");
    Common::StrStrMap params;
    params["vcid"] = "1";
    params["callflowno"] = "session";
    Common::CallParamsPtr callParams = Common::CallParams::create(CALL_PARAMS_DOMAIN, "100645");
    callParams->setParam(CALL_PARAMS_APP, "0");
    ASSERT_TRUE(acdGw.getWaitSize("http://127.0.0.1:11080", params, params, callParams));
    ASSERT_TRUE(params["size"] == "3");

    params.clear();
    params["HttpReqAddr"] = "http://127.0.0.1:11080";
    ASSERT_TRUE(acdGw.getQueueLength(1, 5600, params, params));
    ASSERT_TRUE(params["queuecnt"] == "3");
    ASSERT_TRUE(params["forecast"] == "1");

    MpCall::InviteConfig inviteConfig;
    inviteConfig.uui = "**********;codec=hex";
    Common::String outSessId;
    ASSERT_TRUE(ctrl.onCallin("", "guest", agentNumber, inviteConfig, outSessId, outCallee, outInviteConfig));
    ASSERT_TRUE(outSessId == "session100645_0");
    ASSERT_TRUE(outCallee == "[username:<EMAIL>]");
    ASSERT_TRUE(outInviteConfig.uui.find("csn=**********;") >= 0);
    ASSERT_TRUE(outInviteConfig.uui.find("pti=hello;") >= 0);
    ASSERT_TRUE(outInviteConfig.uui.find("token=token123456;") >= 0);
    ASSERT_TRUE(outInviteConfig.requireAlert);

    EXPECT_CALL(*(notifier.get()), sendMsg(_, Eq("[username:<EMAIL>]"), Eq("AnswerResult"), _)).WillOnce(Return(true));
    ret = httpCli.Post("/", R"({"head":{"IDNO":"**********", "ReqName":"AnswerReq", "Terminal":"1000", "InvokeId":"02"}, "body":{}})", "application/json");
    std::cout << ret->status << " " << ret->body << std::endl;
    ASSERT_TRUE(ret);

    EXPECT_CALL(*(notifier.get()), sendMsg(_, Eq("[username:<EMAIL>]"), Eq("NotifyResult"), Contains(Key(Eq("KeepNotifyResult"))))).WillOnce(Return(true));
    EXPECT_CALL(*(notifier.get()), sendMsg(_, Eq("guest"), Eq("NotifyResult"), Contains(Key(Eq("KeepNotifyResult"))))).WillOnce(Return(true));
    ASSERT_TRUE(ctrl.onHolded(CallOutSessId, true));

    EXPECT_CALL(*(notifier.get()), sendMsg(_, Eq("guest"), Eq("NotifyResult"), Contains(Key(Eq("StartPlayMusicResult"))))).WillOnce(Return(true));
    ret = httpCli.Post("/", R"({"head":{"IDNO":"**********", "ReqName":"StartPlay", "Terminal":"guest", "InvokeId":"03"}, "body":{}})", "application/json");
    std::cout << ret->status << " " << ret->body << std::endl;
    ASSERT_TRUE(ret);

    EXPECT_CALL(*(notifier.get()), sendMsg(_, Eq("[username:<EMAIL>]"), Eq("NotifyResult"), Contains(Key(Eq("GetBackNotifyResult"))))).WillOnce(Return(true));
    EXPECT_CALL(*(notifier.get()), sendMsg(_, Eq("guest"), Eq("NotifyResult"), Contains(Key(Eq("GetBackNotifyResult"))))).WillOnce(Return(true));
    ASSERT_TRUE(ctrl.onHolded(CallOutSessId, false));

    EXPECT_CALL(*(notifier.get()), sendMsg(_, Eq("guest"), Eq("NotifyResult"), Contains(Key(Eq("StopPlayMusicResult"))))).WillOnce(Return(true));
    ret = httpCli.Post("/", R"({"head":{"IDNO":"**********", "ReqName":"StopPlay", "Terminal":"guest", "InvokeId":"04"}, "body":{}})", "application/json");
    std::cout << ret->status << " " << ret->body << std::endl;
    ASSERT_TRUE(ret);

    ASSERT_TRUE(ctrl.onTermed(CallOutSessId));
    ASSERT_TRUE(ctrl.onTermed("[username:<EMAIL>]/" + SessId));

    ASSERT_TRUE(ccSvr.checkout(Common::CallParams::create(CALL_PARAMS_ACCOUNT, "[username:<EMAIL>]")));
}