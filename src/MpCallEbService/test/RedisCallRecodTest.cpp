//
// *****************************************************************************
// Copyright (c) 2023 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//
// Created on 2023/12/12 by <PERSON>
//

#include "gtest/gtest.h"

#include "Common/Common.h"
#include "Common/TypesPub.h"
#include "Common/Util.h"
#include "MpCallEbService/CallRecord.h"
#include "MpCallEbService/ManagerData.h"

class RedisCallRecordTest : public ::testing::Test
{
public:
    void SetUp()
    {
        system("../tools/redis-server-start.sh");

        Common::StrStrMap configs;
        configs["global.Log.Level"] = "4";
        configs["global.Log.Print"] = "1";
        configs["global.RedisUri"] = "redis://127.0.0.1:6379/?pwd=qbEYCMQtB16tfBcOIyHW1Db5BjX0Mqdf";
        configs["global.Redis.Namespace"] = "jf";
        configs["global.Eb.Agent.CallNumberRange"] = "1000-1999";
        _app = Common::ApplicationEx::create("Test2-0", "", 0, configs);
        ASSERT_TRUE(_app->activate());

        Common::String failReason;

        _db = EbService::DataDb::create(_app);
        ASSERT_TRUE(_db->onActivate(failReason));

        _mgr = EbService::CallRecordManager::create(_app, _db);
        ASSERT_TRUE(_mgr->onActivate(failReason));
    }

    void TearDown()
    {
        _mgr->onDeactivate();
        _db->onDeactivate();
        _app->indShutdown();
        while (!_app->isShutdown())
            Common::sleep(100);
    }

    Common::ApplicationExPtr _app;
    EbService::DataDbPtr _db;
    EbService::CallRecordManagerPtr _mgr;
};

TEST_F(RedisCallRecordTest, Normal)
{
    Common::String callId = "1234567";
    Common::String serial = "8888888";
    Common::String pti = "hello world";
    ASSERT_TRUE(_mgr->add(callId, pti, "", MpCallEb::SessionInfo("session1", "account1")));
    ASSERT_TRUE(_mgr->setSerialNumber(callId, serial));
    ASSERT_TRUE(_mgr->setSession(callId, MpCallEb::SessionInfo("session2", "account2")));

    ASSERT_TRUE(_mgr->getCallId(serial) == callId);
    ASSERT_TRUE(_mgr->getCallId("invalid") == "");

    MpCallEb::CallRecord record;
    ASSERT_TRUE(_mgr->getCallRecord(serial, record));
    ASSERT_TRUE(record.callId == callId);
    ASSERT_TRUE(record.passthroughInfo == pti);

    MpCallEb::SessionInfo info = _mgr->getSession(callId, "session1");
    ASSERT_TRUE(info.peerAccount == "account1");
    info = _mgr->getSession(callId, "session2");
    ASSERT_TRUE(info.peerAccount == "account2");
    info = _mgr->getSession(callId, "sessionX");
    ASSERT_TRUE(info.sessionId == "");
    ASSERT_TRUE(info.peerAccount == "");

    MpCallEb::SessionInfoMap sessions = _mgr->getAllSessions(callId);
    ASSERT_EQ(sessions.size(), 2);
    auto it = sessions.find("session1");
    ASSERT_TRUE(it != sessions.end());
    ASSERT_TRUE(it->second.sessionId == "session1");
    ASSERT_TRUE(it->second.peerAccount == "account1");
    it = sessions.find("session2");
    ASSERT_TRUE(it != sessions.end());
    ASSERT_TRUE(it->second.sessionId == "session2");
    ASSERT_TRUE(it->second.peerAccount == "account2");
    it = sessions.find("sessionX");
    ASSERT_TRUE(it == sessions.end());

    ASSERT_FALSE(_mgr->setSessionTermed(callId, "sessionX"));
    ASSERT_TRUE(_mgr->setSessionTermed(callId, "session1"));
    info = _mgr->getSession(callId, "session1");
    ASSERT_TRUE(info.sessionId == "");
    ASSERT_TRUE(info.peerAccount == "");
    info = _mgr->getSession(callId, "session2");
    ASSERT_TRUE(info.peerAccount == "account2");

    ASSERT_TRUE(_mgr->setSessionTermed(callId, "session2"));
    info = _mgr->getSession(callId, "session2");
    ASSERT_TRUE(info.sessionId == "");
    ASSERT_TRUE(info.peerAccount == "");

    ASSERT_FALSE(_mgr->setSerialNumber(callId, serial));
    ASSERT_FALSE(_mgr->setSession(callId, MpCallEb::SessionInfo("session3", "account3")));
}