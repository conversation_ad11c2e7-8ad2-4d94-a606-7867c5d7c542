//
// *****************************************************************************
// Copyright (c) 2023 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//
// Created on 2023/12/4 by <PERSON>
//

#pragma once

#include "cpp-httplib/httplib.h"
#include "Common/Util.h"

class TestHttpServer : public Common::Thread
{
public:
    void open(const std::string &host, int port, const std::string &path, std::function<void(const httplib::Request &req, httplib::Response &rsp)> func)
    {
        _host = host;
        _port = port;
        server.Get(path, func);
        startRun(0);
    }

    void close()
    {
        server.stop();
    }

    void onRun()
    {
        server.listen(_host, _port);
    }

    httplib::Server server;
    std::string _host;
    int _port;
};