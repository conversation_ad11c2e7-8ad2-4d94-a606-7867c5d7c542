//
// *****************************************************************************
// Copyright (c) 2023 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//
// Created on 2023/11/27 by <PERSON>
//

#pragma once

#include "Common/CommonEx.h"
#include "ManagerData.h"
#include "MpCallEb/MpCallEbPub.h"
#include "Util/ServiceModule.h"

namespace EbService
{

class CallRecordManager;
typedef Common::Handle<CallRecordManager> CallRecordManagerPtr;

class CallRecordManager : public Service::ServiceModule
{
public:
    static CallRecordManagerPtr create(const Common::ApplicationExPtr &app, const DataDbPtr &db);

    virtual bool add(const Common::String &callId, const Common::String &passthroughInfo, const Common::String &token, const MpCallEb::SessionInfo &info) = 0;
    virtual bool setSerialNumber(const Common::String &callId, const Common::String &serialNumber) = 0;
    virtual bool getSerialNumber(const Common::String &callId, Common::String &serialNumber) = 0;
    virtual bool setSession(const Common::String &callId, const MpCallEb::SessionInfo &info) = 0;
    virtual bool setSessionTermed(const Common::String &callId, const Common::String &sessionId) = 0;
    virtual Common::String getCallId(const Common::String &serialNumber) = 0;
    virtual bool getCallRecord(const Common::String &serialNumber, MpCallEb::CallRecord &record) = 0;
    virtual MpCallEb::SessionInfo getSession(const Common::String &callId, const Common::String &sessionId) = 0;
    virtual MpCallEb::SessionInfoMap getAllSessions(const Common::String &callId) = 0;
};

} // namespace EbService
