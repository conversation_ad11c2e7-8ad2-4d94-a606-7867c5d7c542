# implement
aux_source_directory(. MPCALL_EBSERVICE_SRC)
add_library(MpCallEbServiceImpl
    ${MPCALL_EBSERVICE_SRC}
)

target_include_directories(MpCallEbServiceImpl PRIVATE
    ${DEFAULT_INCLUDE_DIRECTORIES}
    ${PROJECT_SOURCE_DIR}/3rd
)
target_compile_definitions(MpCallEbServiceImpl PRIVATE
    ${DEFAULT_COMPILE_DEFINITIONS}
)

# service
aux_source_directory(service MPCALL_EBSERVICE_SERVICE_SRC)
add_executable(MpCallEbService
    ${WARP_GLIBC_SRC}
    ${MPCALL_EBSERVICE_SERVICE_SRC}
)

target_include_directories(MpCallEbService PRIVATE
    ${DEFAULT_INCLUDE_DIRECTORIES}
    ${PROJECT_SOURCE_DIR}/3rd
)
target_compile_definitions(MpCallEbService PRIVATE
    ${DEFAULT_COMPILE_DEFINITIONS}
)

target_link_libraries(MpCallEbService MpCallEbServiceImpl AccountPub2 AcdGateway CcSvr MpCallEb MpCallRpc DbConn ServiceI NmsFoundation ServiceMain
    ${DEFAULT_SERVER_LIBRARIES}
    ${DEFAULT_SYS_LIBRARIES}
)
target_link_options(MpCallEbService PRIVATE
    ${DEFAULT_LINK_OPTIONS}
)

# unit test
aux_source_directory(test MPCALL_EBSERVICE_TEST_SRC)
add_executable(MpCallEbServiceUnitTest
    ${MPCALL_EBSERVICE_TEST_SRC}
)

target_include_directories(MpCallEbServiceUnitTest PRIVATE
    ${DEFAULT_INCLUDE_DIRECTORIES}
    ${PROJECT_SOURCE_DIR}/3rd
)
target_compile_definitions(MpCallEbServiceUnitTest PRIVATE
    ${DEFAULT_COMPILE_DEFINITIONS}
)

target_link_libraries(MpCallEbServiceUnitTest MpCallEbServiceImpl AccountPub2 AcdGateway CcSvr MpCallEb DbConn MpCallRpc ServiceI NmsFoundation
    ${DEFAULT_SERVER_LIBRARIES}
    ${DEFAULT_SYS_LIBRARIES}
    $ENV{JssSharedPath}/lib/${DEFAULT_LIB_PATH}/libgmock.a
    $ENV{JssSharedPath}/lib/${DEFAULT_LIB_PATH}/libgtest.a
    $ENV{JssSharedPath}/lib/${DEFAULT_LIB_PATH}/libgtest_main.a
)

include(GoogleTest)
gtest_discover_tests(MpCallEbServiceUnitTest)

# dialer
aux_source_directory(dialer MPCALL_EBSERVICE_DIALER_SRC)
add_executable(MpCallEbDialer
    ${WARP_GLIBC_SRC}
    ${MPCALL_EBSERVICE_DIALER_SRC}
)

target_include_directories(MpCallEbDialer PRIVATE
    ${DEFAULT_INCLUDE_DIRECTORIES}
    ${PROJECT_SOURCE_DIR}/3rd
)
target_compile_definitions(MpCallEbDialer PRIVATE
    ${DEFAULT_COMPILE_DEFINITIONS}
)

target_link_libraries(MpCallEbDialer SipLine SimpleSipSession SipAdapter Util DbConn ServiceI NmsFoundation JsmLog ServiceMain
    ${DEFAULT_SERVER_LIBRARIES}
    ${DEFAULT_SYS_LIBRARIES}
)
target_link_options(MpCallEbDialer PRIVATE
    ${DEFAULT_LINK_OPTIONS}
)