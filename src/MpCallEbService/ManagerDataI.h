//
// *****************************************************************************
// Copyright (c) 2023 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//
// Created on 2023/11/27 by <PERSON>
//

#pragma once

#include "Common/Common.h"
#include "Common/CommonEx.h"
#include "Common/TypesPub.h"
#include "Common/Util.h"
#include "ManagerData.h"
#include "DbConn/RedisClient.h"

namespace EbService
{

class MemDataDb : public DataDb
{
private:
    class MemData : virtual public Common::Shared
    {
    public:
        MemData(Common::Ulong _expiredTicks, const DataPtr &_data)
            : expiredTicks(_expiredTicks)
            , data(_data)
        {
        }

        Common::Ulong expiredTicks;
        DataPtr data;
    };

    typedef Common::Handle<MemData> MemDataPtr;

    class Category : public DataCategory
    {
    public:
        Category(const Common::String &name)
            : _name(name)
        {
        }

        virtual bool lock(int timeout = -1) override;
        virtual bool unlock() override;

        virtual bool set(const DataPtr &data, int expires) override;
        virtual DataPtr get(const Common::String &key) override;
        virtual bool update(const Common::String &key, int expires) override;

        void schd();

    private:
        const Common::String _name;
        Common::RecMutex _mutex;
        std::map<Common::String, MemDataPtr> _datas;
    };

    typedef Common::Handle<Category> CategoryPtr;

public:
    virtual bool onActivate(Common::String &failReason) override { return true; }
    virtual void onDeactivate() override { _categorys.clear(); }
    virtual void onUpdateConfigs() override;

    virtual DataCategoryPtr addCategory(const Common::String &name, const DataCategoryListenerPtr &listener) override;

private:
    std::map<Common::String, CategoryPtr> _categorys;
};

class RedisDataDbCategoryListener : public DataCategoryListener
{
public:
    virtual Common::String genDid() = 0;
};

typedef Common::Handle<RedisDataDbCategoryListener> RedisDataDbCategoryListenerPtr;

class RedisDataDb : public DataDb
{
private:
    class Lock : virtual public Common::Shared
    {
    public:
        Lock(const Redis::CliPtr &redis, const Common::String &key, const Common::String &uniqueId)
            : _redis(redis)
            , _key(key)
            , _value(uniqueId)
        {
        }

        ~Lock()
        {
            unlock();
        }

        bool schd();

        bool lock(int timeout = -1);
        bool unlock();

    private:
        Redis::CliPtr _redis;
        Common::String _key;
        Common::String _value;
    };

    class Category : public DataCategory
    {
    public:
        Category(const Common::String &name, const RedisDataDbCategoryListenerPtr &listener, const Redis::CliPtr &redis, const Common::String &appName)
            : _name(name)
            , _listener(listener)
            , _redis(redis)
            , _appName(appName)
        {
        }

        void schd();

        virtual bool lock(int timeout = -1) override;
        virtual bool unlock() override;

        virtual bool set(const DataPtr &data, int expires) override;
        virtual DataPtr get(const Common::String &key) override;
        virtual bool update(const Common::String &key, int expires) override;

        virtual Redis::CliPtr getRedis() override { return _redis; }

    private:
        const Common::String _name;
        const Common::String _appName;
        RedisDataDbCategoryListenerPtr _listener;
        Common::RecMutex _mutex;
        Redis::CliPtr _redis;
        Common::Handle<Lock> _lock;
    };

    typedef Common::Handle<Category> CategoryPtr;

    class CategoryListener : public RedisDataDbCategoryListener
    {
    public:
        CategoryListener(RedisDataDb *db, const Common::String &name, const DataCategoryListenerPtr &listener)
            : _db(db)
            , _name(name)
            , _listener(listener)
        {
        }

        virtual Common::String genDid() override;
        virtual DataPtr createData(const Common::String &str) override;

    private:
        Common::Handle<RedisDataDb> _db;
        Common::String _name;
        DataCategoryListenerPtr _listener;
    };

public:
    explicit RedisDataDb(const Common::ApplicationPtr &app, const Common::String &uri)
        : _app(app)
        , _uri(uri)
        , _lastDidMs(Common::getCurTimeMs() - 1)
    {
    }

    virtual bool onActivate(Common::String &failReason) override;
    virtual void onDeactivate() override;
    virtual void onUpdateConfigs() override;

    virtual DataCategoryPtr addCategory(const Common::String &name, const DataCategoryListenerPtr &listener) override;

    Common::String genDid();

private:
    Common::ApplicationPtr _app;
    Common::String _uri;
    Common::String _appId;
    Common::Long _lastDidMs;
    Redis::CliPtr _redis;
    std::map<Common::String, CategoryPtr> _categorys;
};

} // namespace EbService
