//
// *****************************************************************************
// Copyright (c) 2023 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//
// Created on 2023/11/29 by <PERSON>
//

#pragma once

#include "Common/CommonEx.h"
#include "Common/Util.h"
#include "MpCallEb/MpCallEbServer.h"
#include "OpenApiServer.h"
#include "cpp-httplib/httplib.h"
#include "MpCallEb/MpCallEbAgent.h"

namespace EbService
{

class OpenApiManagerI : public OpenApiManager, public Common::Thread
{
private:
    enum HttpServerState
    {
        HttpServerIdle,
        HttpServerListened,
        HttpServerDone,
        HttpServerError
    };

public:
    explicit OpenApiManagerI(const Common::ApplicationExPtr &app);

    virtual void onRun() override;

    virtual bool onActivate(Common::String &failReason) override;
    virtual void onDeactivate() override;
    virtual void onUpdateConfigs() override;

    virtual Common::String getUuiInfo() override;
    virtual bool queryWaitSize(const Common::String &httpAddress, int vcid, const Common::String &callFlowNo, Common::String &queueOrder) override;
    virtual bool queryQueueLen(const Common::String &httpAddress, int vcid, int skill, Common::String &queueCnt, Common::String &forecast) override;

private:
    int processInService(const MpCallEb::OpenApiRequest &req, MpCallEb::OpenApiResponse &rsp);
    int processAnswerReq(const MpCallEb::OpenApiRequest &req, MpCallEb::OpenApiResponse &rsp);
    int processFilePlay(const MpCallEb::OpenApiRequest &req, MpCallEb::OpenApiResponse &rsp);

private:
    Common::ApplicationExPtr _app;
    Common::String _httpQueryAddr;

    Common::String _httpHost;
    int _httpPort;
    Common::String _uuiInfo;
    int _httpServerState;
    httplib::Server _httpServer;
};

} // namespace EbService
