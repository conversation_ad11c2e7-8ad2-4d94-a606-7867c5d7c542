//
// *****************************************************************************
// Copyright (c) 2023 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//
// Created on 2023/12/4 by <PERSON>
//

#pragma once

#include "MessageNotifier.h"
#include "Common/CommonEx.h"

namespace EbService
{

class MessageNotifierI : public MessageNotifier
{
public:
    explicit MessageNotifierI(const Common::ApplicationExPtr &app);

    virtual bool onActivate(Common::String &failReason) override;

    virtual bool sendMsg(const Common::AgentAsyncPtr async, const Common::String &account, const Common::String &type, Common::StrStrMap &params) override;

private:
    Common::String getAccountId(const Common::String &account);

private:
    Common::ApplicationExPtr _app;
    Common::String _domainId;
};

} // namespace EbService