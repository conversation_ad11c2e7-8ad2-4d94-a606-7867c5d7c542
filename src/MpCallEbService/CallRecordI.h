//
// *****************************************************************************
// Copyright (c) 2023 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//
// Created on 2023/11/27 by <PERSON>
//

#pragma once

#include "Common/CommonEx.h"
#include "CallRecord.h"
#include "MpCallEb/MpCallEbPub.h"
#include "MpCallEbService/ManagerData.h"

namespace EbService
{

class CallRecord : public Data, public MpCallEb::CallRecord
{
public:
    CallRecord() {}
    CallRecord(const Common::String &sessionId, const Common::String &passthroughInfo, const Common::String &token);

    virtual Common::String to_string() override;
    virtual bool from_string(const Common::String &str) override;

    virtual const DataKeys &keys() override;

private:
    DataKeys _keys;

    friend class CallRecordManagerI;
};

typedef Common::Handle<CallRecord> CallRecordPtr;

class CallRecordManagerI : public CallRecordManager, public DataCategoryListener
{
public:
    CallRecordManagerI(const Common::ApplicationExPtr &app, const DataDbPtr &db);

    virtual bool onActivate(Common::String &failReason) override;
    virtual void onDeactivate() override;

    virtual bool add(const Common::String &callId, const Common::String &passthroughInfo, const Common::String &token, const MpCallEb::SessionInfo &info) override;
    virtual bool setSerialNumber(const Common::String &callId, const Common::String &serialNumber) override;
    virtual bool getSerialNumber(const Common::String &callId, Common::String &serialNumber) override;
    virtual bool setSession(const Common::String &callId, const MpCallEb::SessionInfo &info) override;
    virtual bool setSessionTermed(const Common::String &callId, const Common::String &sessionId) override;
    virtual Common::String getCallId(const Common::String &serialNumber) override;
    virtual bool getCallRecord(const Common::String &serialNumber, MpCallEb::CallRecord &record) override;
    virtual MpCallEb::SessionInfo getSession(const Common::String &callId, const Common::String &sessionId) override;
    virtual MpCallEb::SessionInfoMap getAllSessions(const Common::String &callId) override;

    virtual DataPtr createData(const Common::String &str) override;

    static Common::String callIdKey(const Common::String &callId);
    static Common::String serialNumberKey(const Common::String &serialNumber);

private:
    Common::ApplicationExPtr _app;
    DataDbPtr _dataDb;
    DataCategoryPtr _db;
};

} // namespace EbService
