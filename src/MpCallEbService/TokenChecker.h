//
// *****************************************************************************
// Copyright (c) 2023 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//
// Created on 2023/12/13 by <PERSON>
//

#pragma once

#include "Common/CommonEx.h"
#include "Util/ServiceModule.h"

namespace EbService
{

class TokenChecker;
typedef Common::Handle<TokenChecker> TokenCheckerPtr;

class TokenChecker : public Service::ServiceModule
{
public:
    static TokenCheckerPtr create(const Common::ApplicationExPtr &app);

    virtual bool checkValid(const Common::String &token, Common::String &value) = 0;
};

} // namespace EbService