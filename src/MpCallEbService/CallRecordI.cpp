//
// *****************************************************************************
// Copyright (c) 2023 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//
// Created on 2023/11/27 by <PERSON>
//

#include "Common/CommonEx.h"
#include "Common/Util.h"
#include "CallRecordI.h"
#include "ServiceUtil/Struct2Json.h"
#include "MpCallEb/MpCallEbPub.h"
#include "MpCallEbService/CallRecord.h"
#include "MpCallEbService/ManagerData.h"

namespace EbService
{

CallRecord::CallRecord(const Common::String &callId, const Common::String &passthroughInfo, const Common::String &token)
    : MpCallEb::CallRecord(callId, "", passthroughInfo, token, MpCallEb::SessionInfoMap())
{
    _keys.insert(CallRecordManagerI::callIdKey(callId));
}

Common::String CallRecord::to_string()
{
    return ServiceUtil::to_json<MpCallEb::CallRecord>(*this);
}

bool CallRecord::from_string(const Common::String &str)
{
    if (!ServiceUtil::from_json<MpCallEb::CallRecord>(*this, str))
    {
        UTIL_LOG_WRN("ManagerAccount", "content:from invalid json:" + str);
        return false;
    }

    _keys.clear();
    _keys.insert(CallRecordManagerI::callIdKey(callId));
    if (!serialNumber.empty())
        _keys.insert(CallRecordManagerI::serialNumberKey(serialNumber));

    return true;
}

const DataKeys &CallRecord::keys()
{
    return _keys;
}

CallRecordManagerI::CallRecordManagerI(const Common::ApplicationExPtr &app, const DataDbPtr &db)
    : _app(app)
    , _dataDb(db)
{
}

bool CallRecordManagerI::onActivate(Common::String &failReason)
{
    _db = _dataDb->addCategory("crm", this);
    if (!_db)
    {
        UTIL_LOG_WRN("CallRecordManager", "content:create db category failed.");
        failReason = "CallRecordManager.CreateDbCategoryFailed";
        return false;
    }

    UTIL_LOG_IFO("CallRecordManager", "content:onActivate ok.");
    return true;
}

void CallRecordManagerI::onDeactivate()
{
    _app = 0;
    _db = 0;
    _dataDb = 0;
}

bool CallRecordManagerI::add(const Common::String &callId, const Common::String &passthroughInfo, const Common::String &token, const MpCallEb::SessionInfo &info)
{
    DataCategoryPtr db = _db;
    if (!db)
    {
        UTIL_LOG_WRN("CallRecordManager", "content:add callId:" + callId + " deactivated.");
        return false;
    }

    CallRecordPtr data = new CallRecord(callId, passthroughInfo, token);
    data->sessions.insert(make_pair(info.sessionId, info));

    if (!db->lock())
    {
        UTIL_LOG_WRN("CallRecordManager", "content:add callId:" + callId + " lock failed.");
        return false;
    }
    bool ret = db->set(data, 3600 * 24);
    db->unlock();

    if (!ret)
    {
        UTIL_LOG_WRN("CallRecordManager", "content:add callId:" + callId + " set db failed..");
        return false;
    }

    UTIL_LOG_IFO("CallRecordManager", "content:add callId:" + callId);
    return true;
}

bool CallRecordManagerI::setSerialNumber(const Common::String &callId, const Common::String &serialNumber)
{
    DataCategoryPtr db = _db;
    if (!db)
    {
        UTIL_LOG_WRN("CallRecordManager", "content:set serial number callId:" + callId + " deactivated.");
        return false;
    }

    if (!db->lock())
    {
        UTIL_LOG_WRN("CallRecordManager", "content:set serial number callId:" + callId + " lock failed.");
        return false;
    }
    DataCategoryDeferUnlock deferUnlock(db);

    DataPtr data = db->get(callIdKey(callId));
    if (!data)
    {
        UTIL_LOG_WRN("CallRecordManager", "content:set serial number callId:" + callId + " not found.");
        return false;
    }

    CallRecordPtr record = CallRecordPtr::dynamicCast(data);
    if (!record)
    {
        UTIL_LOG_WRN("CallRecordManager", "content:set serial number callId:" + callId + " invalid.");
        return false;
    }

    record->serialNumber = serialNumber;
    record->_keys.insert(serialNumberKey(serialNumber));
    if (!db->set(record, 3600))
    {
        UTIL_LOG_WRN("CallRecordManager", "content:set serial number callId:" + callId + " set db failed..");
        return false;
    }

    UTIL_LOG_IFO("CallRecordManager", "content:set serial number callId:" + callId + " serial:" + serialNumber);
    return true;
}

bool CallRecordManagerI::getSerialNumber(const Common::String &callId, Common::String &serialNumber)
{
    DataCategoryPtr db = _db;
    if (!db)
    {
        UTIL_LOG_WRN("CallRecordManager", "content:get serial number callId:" + callId + " deactivated.");
        return false;
    }

    if (!db->lock())
    {
        UTIL_LOG_WRN("CallRecordManager", "content:get serial number callId:" + callId + " lock failed.");
        return false;
    }
    DataCategoryDeferUnlock deferUnlock(db);

    DataPtr data = db->get(callIdKey(callId));
    if (!data)
    {
        UTIL_LOG_WRN("CallRecordManager", "content:get serial number callId:" + callId + " not found.");
        return false;
    }

    CallRecordPtr record = CallRecordPtr::dynamicCast(data);
    if (!record)
    {
        UTIL_LOG_WRN("CallRecordManager", "content:get serial number callId:" + callId + " invalid.");
        return false;
    }

    if (record->serialNumber.empty())
    {
        UTIL_LOG_WRN("CallRecordManager", "content:get serial number callId:" + callId + " serial number empty.");
        return false;
    }

    serialNumber = record->serialNumber;
    UTIL_LOG_IFO("CallRecordManager", "content:get serial number callId:" + callId + " serial:" + serialNumber);
    return true;
}

bool CallRecordManagerI::setSession(const Common::String &callId, const MpCallEb::SessionInfo &info)
{
    DataCategoryPtr db = _db;
    if (!db)
    {
        UTIL_LOG_WRN("CallRecordManager", "content:set session callId:" + callId + " deactivated.");
        return false;
    }

    if (!db->lock())
    {
        UTIL_LOG_WRN("CallRecordManager", "content:set session callId:" + callId + " lock failed.");
        return false;
    }
    DataCategoryDeferUnlock deferUnlock(db);

    DataPtr data = db->get(callIdKey(callId));
    if (!data)
    {
        UTIL_LOG_WRN("CallRecordManager", "content:set session callId:" + callId + " not found.");
        return false;
    }

    CallRecordPtr record = CallRecordPtr::dynamicCast(data);
    if (!record)
    {
        UTIL_LOG_WRN("CallRecordManager", "content:set session callId:" + callId + " invalid.");
        return false;
    }

    record->sessions[info.sessionId] = info;

    if (!db->set(record, 3600))
    {
        UTIL_LOG_WRN("CallRecordManager", "content:set session callId:" + callId + " sessId:" + info.sessionId + " set db failed..");
        return false;
    }

    UTIL_LOG_IFO("CallRecordManager", "content:set session callId:" + callId + " sessId:" + info.sessionId);
    return true;
}

bool CallRecordManagerI::setSessionTermed(const Common::String &callId, const Common::String &sessionId)
{
    DataCategoryPtr db = _db;
    if (!db)
    {
        UTIL_LOG_WRN("CallRecordManager", "content:set session termed callId:" + callId + " sessId:" + sessionId + " deactivated.");
        return false;
    }

    if (!db->lock())
    {
        UTIL_LOG_WRN("CallRecordManager", "content:set session termed callId:" + callId + " lock failed.");
        return false;
    }
    DataCategoryDeferUnlock deferUnlock(db);

    DataPtr data = db->get(callIdKey(callId));
    if (!data)
    {
        UTIL_LOG_WRN("CallRecordManager", "content:set session termed callId:" + callId + " not found.");
        return false;
    }

    CallRecordPtr record = CallRecordPtr::dynamicCast(data);
    if (!record)
    {
        UTIL_LOG_WRN("CallRecordManager", "content:set session termed callId:" + callId + " invalid.");
        return false;
    }

    auto it = record->sessions.find(sessionId);
    if (it == record->sessions.end())
    {
        UTIL_LOG_WRN("CallRecordManager", "content:set session termed callId:" + callId + " sessId:" + sessionId + " not found.");
        return false;
    }

    record->sessions.erase(it);
    if (record->sessions.empty())
    {
        if (!db->update(callIdKey(callId), 0))
        {
            UTIL_LOG_WRN("CallRecordManager", "content:set session termed callId:" + callId + " sessId:" + sessionId + " terminated, update db failed.");
            return false;
        }

        UTIL_LOG_IFO("CallRecordManager", "content:set session termed callId:" + callId + " sessId:" + sessionId + " terminated.");
    }
    else
    {
        if (!db->set(record, 3600))
        {
            UTIL_LOG_WRN("CallRecordManager", "content:set session termed callId:" + callId + " sessId:" + sessionId + " set db failed..");
            return false;
        }

        UTIL_LOG_IFO("CallRecordManager", "content:set session termed callId:" + callId + " sessId:" + sessionId);
    }

    return true;
}

Common::String CallRecordManagerI::getCallId(const Common::String &serialNumber)
{
    DataCategoryPtr db = _db;
    if (!db)
    {
        UTIL_LOG_WRN("CallRecordManager", "content:get callid serial:" + serialNumber + " deactivated.");
        return "";
    }

    if (!db->lock())
    {
        UTIL_LOG_WRN("CallRecordManager", "content:get callid serial:" + serialNumber + " lock failed.");
        return "";
    }
    DataPtr data = db->get(serialNumberKey(serialNumber));
    db->unlock();

    if (!data)
    {
        UTIL_LOG_WRN("CallRecordManager", "content:get callid serial:" + serialNumber + " not found.");
        return "";
    }

    CallRecordPtr record = CallRecordPtr::dynamicCast(data);
    if (!record)
    {
        UTIL_LOG_WRN("CallRecordManager", "content:get callid serial:" + serialNumber + " invalid.");
        return "";
    }

    UTIL_LOG_DBG("CallRecordManager", "content:get callid serial:" + serialNumber + " callId:" + record->callId);
    return record->callId;
}

bool CallRecordManagerI::getCallRecord(const Common::String &serialNumber, MpCallEb::CallRecord &record)
{
    DataCategoryPtr db = _db;
    if (!db)
    {
        UTIL_LOG_WRN("CallRecordManager", "content:get call record serial:" + serialNumber + " deactivated.");
        return false;
    }

    if (!db->lock())
    {
        UTIL_LOG_WRN("CallRecordManager", "content:get call record serial:" + serialNumber + " lock failed.");
        return false;
    }
    DataPtr data = db->get(serialNumberKey(serialNumber));
    db->unlock();

    if (!data)
    {
        UTIL_LOG_WRN("CallRecordManager", "content:get call record serial:" + serialNumber + " not found.");
        return false;
    }

    CallRecordPtr recordData = CallRecordPtr::dynamicCast(data);
    if (!recordData)
    {
        UTIL_LOG_WRN("CallRecordManager", "content:get call record serial:" + serialNumber + " invalid.");
        return false;
    }

    record = *(recordData.get());
    UTIL_LOG_DBG("CallRecordManager", "content:get call record serial:" + serialNumber + " callId:" + record.callId);
    return true;
}

MpCallEb::SessionInfo CallRecordManagerI::getSession(const Common::String &callId, const Common::String &sessionId)
{
    DataCategoryPtr db = _db;
    if (!db)
    {
        UTIL_LOG_WRN("CallRecordManager", "content:get session callId:" + callId + " deactivated.");
        return MpCallEb::SessionInfo();
    }

    if (!db->lock())
    {
        UTIL_LOG_WRN("CallRecordManager", "content:get session callId:" + callId + " lock failed.");
        return MpCallEb::SessionInfo();
    }
    DataPtr data = db->get(callIdKey(callId));
    db->unlock();

    if (!data)
    {
        UTIL_LOG_WRN("CallRecordManager", "content:get session callId:" + callId + " not found.");
        return MpCallEb::SessionInfo();
    }

    CallRecordPtr record = CallRecordPtr::dynamicCast(data);
    if (!record)
    {
        UTIL_LOG_WRN("CallRecordManager", "content:get session callId:" + callId + " invalid.");
        return MpCallEb::SessionInfo();
    }

    auto it = record->sessions.find(sessionId);
    if (it == record->sessions.end())
    {
        UTIL_LOG_WRN("CallRecordManager", "content:get session callId:" + callId + " sessId:" + sessionId + " not found.");
        return MpCallEb::SessionInfo();
    }

    UTIL_LOG_DBG("CallRecordManager", "content:get session callId:" + callId + " sessId:" + sessionId);
    return it->second;
}

MpCallEb::SessionInfoMap CallRecordManagerI::getAllSessions(const Common::String &callId)
{
    DataCategoryPtr db = _db;
    if (!db)
    {
        UTIL_LOG_WRN("CallRecordManager", "content:get all session callId:" + callId + " deactivated.");
        return MpCallEb::SessionInfoMap();
    }

    if (!db->lock())
    {
        UTIL_LOG_WRN("CallRecordManager", "content:get all session callId:" + callId + " lock failed.");
        return MpCallEb::SessionInfoMap();
    }
    DataPtr data = _db->get(callIdKey(callId));
    db->unlock();

    if (!data)
    {
        UTIL_LOG_WRN("CallRecordManager", "content:get all session callId:" + callId + " not found.");
        return MpCallEb::SessionInfoMap();
    }

    CallRecordPtr record = CallRecordPtr::dynamicCast(data);
    if (!record)
    {
        UTIL_LOG_WRN("CallRecordManager", "content:get all session callId:" + callId + " invalid.");
        return MpCallEb::SessionInfoMap();
    }

    UTIL_LOG_DBG("CallRecordManager", "content:get all session callId:" + callId);
    return record->sessions;
}

DataPtr CallRecordManagerI::createData(const Common::String &str)
{
    CallRecordPtr data = new CallRecord();

    if (!data->from_string(str))
    {
        UTIL_LOG_IFO("CallRecordManager", "conten:createData invalid str:" + str);
        return nullptr;
    }

    UTIL_LOG_VBS("CallRecordManager", "conten:createData str:" + str);
    return data.get();
}

Common::String CallRecordManagerI::callIdKey(const Common::String &callId)
{
    return "CrmCallId:" + callId;
}

Common::String CallRecordManagerI::serialNumberKey(const Common::String &serialNumber)
{
    return "CrmSerial:" + serialNumber;
}

CallRecordManagerPtr CallRecordManager::create(const Common::ApplicationExPtr &app, const DataDbPtr &db)
{
    CallRecordManagerPtr mgr;

    try
    {
        mgr = new CallRecordManagerI(app, db);
    }
    catch (const std::exception &e)
    {
        UTIL_LOG_ERR("DataDb", "content:create call record manager error:" + Common::String(e.what()));
        return nullptr;
    }

    return mgr;
}

} // namespace EbService
