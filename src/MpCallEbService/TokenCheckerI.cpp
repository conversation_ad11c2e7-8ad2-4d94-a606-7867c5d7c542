//
// *****************************************************************************
// Copyright (c) 2023 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//
// Created on 2023/12/13 by <PERSON>
//

#include "TokenCheckerI.h"
#include "DbConn/RedisClient.h"
#include "MpCallEbService/TokenChecker.h"

namespace EbService
{

bool TokenCheckerI::onActivate(Common::String &failReason)
{
    if (_uri.empty())
    {
        UTIL_LOG_ERR("TokenChecker", "content:create redis no uri");
        failReason = "ApiServer.InvalidConfig:RedisUri";
        return false;
    }

    _redis = Redis::Cli::create(_app, _uri);
    if (!_redis)
    {
        UTIL_LOG_ERR("TokenChecker", "content:create redis client failed, uri:" + _uri);
        failReason = "ApiServer.CreateRedisClientFailed:" + _uri;
        return false;
    }

    UTIL_LOG_IFO("TokenChecker", "content:create redis client ok, uri:" + _uri);
    return true;
}

void TokenCheckerI::onDeactivate()
{
    UTIL_LOG_IFO("TokenChecker", "content:onDeactivate");
    if (_redis)
    {
        _redis->onDeactivate();
        _redis = 0;
    }
}

void TokenCheckerI::onUpdateConfigs()
{
    if (_redis)
        _redis->onUpdateConfigs();
}

bool TokenCheckerI::checkValid(const Common::String &token, Common::String &value)
{
    Redis::RplPtr reply;
    Common::String reason;
    if (!_redis->exec(Redis::Cmd::cmd("GET")->key(token), reply, reason))
    {
        UTIL_LOG_WRN("TokenChecker", "content:checkValid token:" + token + " exec failed:" + reason);
        return false;
    }

    if (reply->isErr())
    {
        UTIL_LOG_WRN("TokenChecker", "content:checkValid token:" + token + " reply error:" + reply->getErr());
        return false;
    }

    if (!reply->isStr())
    {
        UTIL_LOG_WRN("TokenChecker", "content:checkValid token:" + token + (reply->isNil() ? " not found" : " invalid type."));
        return false;
    }

    value = reply->getStr();
    UTIL_LOG_IFO("TokenChecker", "content:checkValid token:" + token + " OK, value:" + value);

    if (!_redis->exec(Redis::Cmd::cmd("DEL")->key(token), reply, reason))
        UTIL_LOG_WRN("TokenChecker", "content:checkValid token:" + token + " del failed:" + reason);
    else if (reply->isErr())
        UTIL_LOG_WRN("TokenChecker", "content:checkValid token:" + token + " del reply error:" + reply->getErr());

    return true;
}

TokenCheckerPtr TokenChecker::create(const Common::ApplicationExPtr &app)
{
    TokenCheckerI *db;

    try
    {
        db = new TokenCheckerI(app, app->getAppConfig("RedisUri"));
    }
    catch (const std::exception &e)
    {
        UTIL_LOG_ERR("TokenChecker", "content:create error:" + Common::String(e.what()));
        return nullptr;
    }

    return db;
}

} // namespace EbService
