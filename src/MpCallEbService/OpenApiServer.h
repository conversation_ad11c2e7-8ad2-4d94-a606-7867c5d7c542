//
// *****************************************************************************
// Copyright (c) 2023 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//
// Created on 2023/11/29 by <PERSON>
//

#pragma once

#include "Common/CommonEx.h"
#include "Util/ServiceModule.h"
#include <string>

namespace EbService
{

class OpenApiManager;
typedef Common::Handle<OpenApiManager> OpenApiManagerPtr;

class OpenApiManager : public Service::ServiceModule
{
public:
    static OpenApiManagerPtr create(const Common::ApplicationExPtr &app);

    virtual Common::String getUuiInfo() = 0;
    virtual bool queryWaitSize(const Common::String &httpAddress, int vcid, const Common::String &callFlowNo, Common::String &queueOrder) = 0;
    virtual bool queryQueueLen(const Common::String &httpAddress, int vcid, int skill, Common::String &queueCnt, Common::String &forecast) = 0;
};

} // namespace EbService
