//
// *****************************************************************************
// Copyright (c) 2023 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//
// Created on 2023/11/29 by <PERSON>
//

#include "OpenApiServerI.h"
#include "Common/Common.h"
#include "ServiceUtil/Struct2Json.h"
#include "MpCallEb/MpCallEbPub.h"
#include "MpCallEbService/Error.h"
#include "MpCallEbService/OpenApiServer.h"
#include "Error.h"

static Common::String getValue(const std::string &body, const std::string &key)
{
    int pos = body.find("\"" + key + "\"");
    if (pos == std::string::npos)
        return "";
    pos = body.find("\"", pos + key.size() + 2);
    if (pos == std::string::npos)
        return "";
    int epos = body.find("\"", pos + 1);
    if (epos == std::string::npos)
        return "";
    return body.substr(pos + 1, epos - pos - 1).c_str();
}

namespace EbService
{

OpenApiManagerI::OpenApiManagerI(const Common::ApplicationExPtr &app)
    : _app(app)
    , _httpServerState(HttpServerIdle)
    , _httpPort(80)
{
}

bool OpenApiManagerI::onActivate(Common::String &failReason)
{
    _httpHost = _app->getAppConfig("Eb.Http.ListenHost");
    _app->getAppConfigAsInt("Eb.Http.ListenPort", _httpPort);
    _app->setStatistics("Eb.Http.ListenHost", _httpHost);
    _app->setStatisticsLong("Eb.Http.ListenPort", _httpPort);

    if (_httpHost.empty())
    {
        UTIL_LOG_ERR("ApiServer", "content:activate no config Eb.Http.ListenHost");
        failReason = "ApiServer.InvaidConfig:Eb.Http.ListenHost";
        return false;
    }

    if (_httpPort <= 0 || _httpPort >= 65535)
    {
        UTIL_LOG_ERR("ApiServer", "content:activate invalid config Eb.Http.ListenPort");
        failReason = "ApiServer.InvaidConfig:Eb.Http.ListenPort";
        return false;
    }

    if (!_httpServer.bind_to_port(_httpHost.c_str(), _httpPort))
    {
        UTIL_LOG_ERR("ApiServer", "content:activate listen:" + _httpHost + ":" + Common::String(_httpPort) + " failed.");
        failReason = "ApiServer.BindPortFailed:" + _httpHost + ":" + Common::String(_httpPort);
        return false;
    }

    if (!startRun(0, "ApiServer"))
    {
        UTIL_LOG_ERR("ApiServer", "content:activate start listen:" + _httpHost + ":" + Common::String(_httpPort) + " failed.");
        failReason = "ApiServer.StartListenFailed:" + _httpHost + ":" + Common::String(_httpPort);
        return false;
    }

    for (int i = 0; i < 5; i++)
    {
        if (_httpServerState == HttpServerError)
        {
            UTIL_LOG_ERR("ApiServer", "content:activate start listen:" + _httpHost + ":" + Common::String(_httpPort) + " failed.");
            failReason = "ApiServer.ListenFailed:" + _httpHost + ":" + Common::String(_httpPort);
            return false;
        }
    }

    _uuiInfo = "jfip=" + _httpHost + ";jfport=" + Common::String(_httpPort) + ";";

    UTIL_LOG_IFO("ApiServer", "content:activate start listen:" + _httpHost + ":" + Common::String(_httpPort));
    return true;
}

void OpenApiManagerI::onRun()
{
    _httpServer.Post(".*", [&](const httplib::Request &req, httplib::Response &res) {

        MpCallEb::OpenApiRequest apiReq;
        MpCallEb::OpenApiResponse apiRsp;
        if (!ServiceUtil::from_json<MpCallEb::OpenApiRequest>(apiReq, req.body.c_str()))
        {
            UTIL_LOG_WRN("ApiServer", "content:invalid http request:" + Common::String(req.body.c_str()));
            res.status = 400;
            apiRsp.body.detail = "InvalidRequest";
            res.set_content(ServiceUtil::to_json(apiRsp).c_str(), "application/json");
            return;
        }

        apiRsp.head.InvokeId = apiReq.head.InvokeId;
        apiRsp.head.Terminal = apiReq.head.Terminal;
        apiRsp.head.ReqName = apiReq.head.ReqName;

        if (apiReq.head.ReqName == "InService")
            res.status = processInService(apiReq, apiRsp);
        else if (apiReq.head.ReqName == "AnswerReq")
            res.status = processAnswerReq(apiReq, apiRsp);
        else if (apiReq.head.ReqName == "StartPlay" || apiReq.head.ReqName == "StopPlay")
            res.status = processFilePlay(apiReq, apiRsp);
        else
        {
            res.status = 501;
            apiRsp.body.detail = "NotSupport:" + apiReq.head.ReqName;
        }

        apiRsp.head.Result = Common::String(res.status);
        res.set_content(ServiceUtil::to_json(apiRsp).c_str(), "application/json");
    });

    _httpServerState = HttpServerListened;

    if (!_httpServer.listen_after_bind())
    {
        UTIL_LOG_ERR("ApiServer", "content:http server thread listen:" + _httpHost + ":" + Common::String(_httpPort) + " failed.");
        _httpServerState = HttpServerError;
        return;
    }

    UTIL_LOG_IFO("ApiServer", "content:http server thread exit");
    _httpServerState = HttpServerDone;
}

void OpenApiManagerI::onDeactivate()
{
    _httpServer.stop();
}

void OpenApiManagerI::onUpdateConfigs()
{
    Common::String value;
    if (_app->getAppConfig("Eb.Http.QueryAddr", value))
    {
        _httpQueryAddr = value;
        _app->setStatistics("Eb.Http.QueryAddr", _httpQueryAddr);
    }
}

Common::String OpenApiManagerI::getUuiInfo()
{
    return _uuiInfo;
}

bool OpenApiManagerI::queryWaitSize(const Common::String &httpAddress, int vcid, const Common::String &callFlowNo, Common::String &queueOrder)
{
    Common::String queryStr = Common::String::formatString("/mr/engine/execsql.action?sqlId=ebaseCloud.ngplgetqueueorder&variables={\"vcid\":%d,\"callflowno\":\"%s\"}", vcid, callFlowNo.c_str());
    Common::String httpAddr = httpAddress;
    if (!_httpQueryAddr.empty())
        httpAddr = _httpQueryAddr;

    auto ret = httplib::Client(httpAddr.c_str()).Get(queryStr.c_str());
    if (!ret)
    {
        UTIL_LOG_WRN("EbServer", "content:queryWaitSize query:" + httpAddr + "/" + queryStr + " http failed:" + httplib::to_string(ret.error()).c_str());
        Common::setError(MpCall::EbServiceError::QueryHttpFailed(__ERROR_LOC__));
        return false;
    }

    queueOrder = getValue(ret->body, "queueorder");
    UTIL_LOG_IFO("EbServer", "content:queryWaitSize query:" + httpAddr + "/" + queryStr + " queueorder:" + queueOrder);
    return true;
}

bool OpenApiManagerI::queryQueueLen(const Common::String &httpAddress, int vcid, int skill, Common::String &queueCnt, Common::String &forecast)
{
    Common::String queryStr = Common::String::formatString("/mr/engine/execsql.action?sqlId=ebaseCloud.ngplqueuecntandforecast&variables={\"vcid\":%d,\"skill\":%d}", vcid, skill);
    Common::String httpAddr = httpAddress;
    if (!_httpQueryAddr.empty())
        httpAddr = _httpQueryAddr;

    auto ret = httplib::Client(httpAddr.c_str()).Get(queryStr.c_str());
    if (!ret)
    {
        UTIL_LOG_WRN("EbServer", "content:queryQueueLen query:" + httpAddr + "/" + queryStr + " http failed:" + httplib::to_string(ret.error()).c_str());
        Common::setError(MpCall::EbServiceError::QueryHttpFailed(__ERROR_LOC__));
        return false;
    }

    queueCnt = getValue(ret->body, "queuecnt");
    forecast = getValue(ret->body, "forecast");

    UTIL_LOG_WRN("EbServer", "content:queryQueueLen query:" + httpAddr + "/" + queryStr + " queueCnt:" + queueCnt + " forecast:" + forecast);
    return true;
}

int OpenApiManagerI::processInService(const MpCallEb::OpenApiRequest &req, MpCallEb::OpenApiResponse &rsp)
{
    MpCallEb::OpenApiAgent apiAgent = _app->createAgent("EbService");
    if (!apiAgent.bindSession(req.head.IDNO, req.body.ObjectId))
    {
        UTIL_LOG_WRN("ApiServer", "content:processInService serial:" + req.head.IDNO + " callId:" + req.body.ObjectId + " failed:" + Common::ObjectAgent::getLogInfo(2));
        rsp.body.detail = Common::ObjectAgent::getLogInfo(2);
        return 500;
    }

    UTIL_LOG_IFO("ApiServer", "content:processInService serial:" + req.head.IDNO + " callId:" + req.body.ObjectId);
    return 200;
}

int OpenApiManagerI::processAnswerReq(const MpCallEb::OpenApiRequest &req, MpCallEb::OpenApiResponse &rsp)
{
    MpCallEb::OpenApiAgent apiAgent = _app->createAgent("EbService");
    if (!apiAgent.notifyAnswer(req.head.IDNO, req.head.Terminal))
    {
        UTIL_LOG_WRN("ApiServer", "content:processAnswerReq serial:" + req.head.IDNO + " number:" + req.head.Terminal + " failed:" + Common::ObjectAgent::getLogInfo(2));
        rsp.body.detail = Common::ObjectAgent::getLogInfo(2);
        return 500;
    }

    UTIL_LOG_IFO("ApiServer", "content:processAnswerReq serial:" + req.head.IDNO + " number:" + req.head.Terminal);
    return 200;
}

int OpenApiManagerI::processFilePlay(const MpCallEb::OpenApiRequest &req, MpCallEb::OpenApiResponse &rsp)
{
    MpCallEb::OpenApiAgent apiAgent = _app->createAgent("EbService");
    if (!apiAgent.notifyFilePlay(req.head.IDNO, req.head.Terminal, req.head.ReqName == "StartPlay", req.body.FileName))
    {
        UTIL_LOG_WRN("ApiServer", "content:processFilePlay serial:" + req.head.IDNO + " number:" + req.head.Terminal + " " + req.head.ReqName + " filename:" + req.body.FileName + " failed:" + Common::ObjectAgent::getLogInfo(2));
        rsp.body.detail = Common::ObjectAgent::getLogInfo(2);
        return 500;
    }

    UTIL_LOG_IFO("ApiServer", "content:processFilePlay serial:" + req.head.IDNO + " number:" + req.head.Terminal + " " + req.head.ReqName + " filename:" + req.body.FileName);
    return 200;
}

OpenApiManagerPtr OpenApiManager::create(const Common::ApplicationExPtr &app)
{
    OpenApiManagerPtr mgr;

    try
    {
        mgr = new OpenApiManagerI(app);
    }
    catch (const std::exception &e)
    {
        UTIL_LOG_ERR("DataDb", "content:create agent call number manager error:" + Common::String(e.what()));
        return nullptr;
    }

    return mgr;
}

} // namespace EbService
