//
// *****************************************************************************
// Copyright (c) 2023 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//
// Created on 2023/12/6 by <PERSON>
//

#include "MpCallEbDialer.h"
#include "Common/Util.h"
#include "DbConn/RedisClient.h"
#include "Service/ServiceI.h"
#include "SimpleSipSession/SipMessage.h"
#include "SipAdapter/SipCallInterface.h"
#include "cpp-httplib/httplib.h"
#include <exception>

namespace EbService
{

void DialerSipSession::close()
{
    if (_termed)
        return;

    if (_sipSess)
        _sipSess->SipTerm();
    _termed = true;
    _dialerSess = 0;
}

void DialerSipSession::onCallIncoming(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg)
{
    if (_dialerSess)
        _dialerSess->onCallIncoming(this, pcSdp, pcSipMsg);
}

void DialerSipSession::onCallAlerted(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg)
{
    if (_dialerSess)
        _dialerSess->onCallAlerted(this);
}

void DialerSipSession::onCallAnswered(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg)
{
    if (_dialerSess)
        _dialerSess->onCallAnswered(this, pcSdp);
}

void DialerSipSession::onCallUpdate(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg)
{
    // todo
}

void DialerSipSession::onCallRequestUpdate(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg)
{
    // todo
}

void DialerSipSession::onCallResponseUdate(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg)
{
}

void DialerSipSession::onCallConnected(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg)
{
    if (_dialerSess)
        _dialerSess->onCallConnected(this, pcSdp);
}

void DialerSipSession::onCallRequestModify(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg)
{
    // todo
}

void DialerSipSession::onCallResponseModify(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg)
{
    if (_dialerSess)
        _dialerSess->onCallResponseModify(this, pcSdp);
}

void DialerSipSession::onCallTerminated(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg)
{
    _termed = true;
    if (_dialerSess)
        _dialerSess->onCallTerminated(this);
}

SipMpCall::SipLineSessionListenerPtr DialerSession::open(const SipMpCall::SipLineSessionPtr &session)
{
    Common::RecLock lock(_mutex);

    _callinSess = new DialerSipSession(this, session);
    return _callinSess;
}

void DialerSession::schd()
{
    updateExpire();

    Common::RecLock lock(_mutex);

    // check answer expired
    if (_state == StateCalling && Common::getCurTicks() - _lastStateTicks > 30000)
    {
        UTIL_LOG_IFO("EbDialer", "content:answer timeout, callee:" + _callee);
        if (_calloutSess)
        {
            _calloutSess->_sipSess->SipTerm();
            Common::sleep(1000);
        }
        close();
        return;
    }

    // switch hold and unload
    // if (_state == StateTalking && Common::getCurTicks() - _lastStateTicks > 30000)
    // {
    //     std::string sdp = _lastSdp;
    //     for (;;)
    //     {
    //         size_t pos = sdp.find("a=sendrecv\r\n");
    //         if (pos == std::string::npos)
    //             break;

    //         sdp = sdp.substr(0, pos) + sdp.substr(pos + 12);
    //     }

    //     size_t pos = 0;
    //     for (;;)
    //     {
    //         pos = sdp.find("m=", pos);
    //         if (pos == std::string::npos)
    //             break;
    //         pos = sdp.find("\r\n", pos);
    //         sdp = sdp.substr(0, pos + 2) + "a=sendonly\r\n" + sdp.substr(pos + 2);
    //     }

    //     _callinSess->_sipSess->SipUpdate(sdp);
    //     setState(StateHolding);

    //     std::string body = R"({"head":{"IDNO":")" + std::string(_callee.c_str()) + R"(", "ReqName":"StartPlay", "Terminal":")" + _guestName + R"(", "InvokeId":"001"}, "body":{"FileName":"hold.wav"}})";
    //     auto ret = httplib::Client(_httpAddr).Post("/", body, "application/json");
    //     if (!ret)
    //         UTIL_LOG_WRN("EbDialer", "content:send start play failed, name:" + Common::String(_guestName.c_str()));
    // }
    // else if (_state == StateHolding && Common::getCurTicks() - _lastStateTicks > 30000)
    // {
    //     _callinSess->_sipSess->SipUpdate("");
    //     setState(StateTalking);

    //     std::string body = R"({"head":{"IDNO":")" + std::string(_callee.c_str()) + R"(", "ReqName":"StopPlay", "Terminal":")" + _guestName + R"(", "InvokeId":"001"}, "body":{"FileName":"hold.wav"}})";
    //     auto ret = httplib::Client(_httpAddr).Post("/", body, "application/json");
    //     if (!ret)
    //         UTIL_LOG_WRN("EbDialer", "content:send stop play failed, name:" + Common::String(_guestName.c_str()));
    // }
}

void DialerSession::close()
{
    Common::RecLock lock(_mutex);

    setState(StateTermed);

    if (_callinSess)
        _callinSess->close();

    if (_calloutSess)
        _calloutSess->close();
}

void DialerSession::onCallIncoming(const DialerSipSessionPtr &sess, const std::string &sdp, const std::string &sip)
{
    Common::RecLock lock(_mutex);

    if (sess != _callinSess)
        return;

    setState(StateCalling);

    std::string name, uri;
    _callinSess->_sipSess->GetPeerUri(name, uri);
    _guestName = SipMpCall::SipLine::getNumber(uri);

    SimpleSipSession::SipMessagePtr msg = SimpleSipSession::SipMessage::create(sip);
    std::string uui = msg->getHeader("User-to-User");
    int pos = uui.find("uuid=");
    int epos = uui.find(";", pos);
    std::string sessId = uui.substr(pos + 5, epos - (pos + 5));
    pos = uui.find("jfip=");
    epos = uui.find(";", pos);
    std::string ip = uui.substr(pos + 5, epos - (pos + 5));
    pos = uui.find("jfport=");
    epos = uui.find(";", pos);
    std::string port = uui.substr(pos + 7, epos - (pos + 7));
    _httpAddr = "http://" + ip + ":" + port;

    std::string body = R"({"head":{"IDNO":")" + std::string(_callee.c_str()) + R"(", "ReqName":"InService", "Terminal":"terminal", "InvokeId":"001"}, "body":{"ObjectId":")" + sessId + R"("}})";
    auto ret = httplib::Client(_httpAddr).Post("/", body, "application/json");
    if (!ret || ret->status != 200)
    {
        UTIL_LOG_WRN("EbDialer", "content:notify InService to server:" + Common::String(_httpAddr.c_str()) + "failed.");
        setState(StateTermed);
        _callinSess->_sipSess->SipTerm();
        return;
    }
    UTIL_LOG_IFO("EbDialer", "content:notify InService to server:" + Common::String(_httpAddr.c_str()));

    std::map<std::string, std::string> exts;
    exts["User-to-User"] = (_callee + ";codec=hex").c_str();

    _calloutSess = new DialerSipSession(this, nullptr);
    _calloutSess->_sipSess = _sipLine->SipCall(_calloutSess.get(), ("Sess" + Common::randString()).c_str(), "dialer", _callee.c_str(), sdp, exts);
}

void DialerSession::onCallAlerted(const DialerSipSessionPtr &sess)
{
    do
    {
        Common::RecLock lock(_mutex);

        if (sess != _calloutSess)
            return;

        _callinSess->_sipSess->SipAlert("");
    } while (0);


    if (!_autoAnswer)
    {
        UTIL_LOG_IFO("EbDialer", "content:call alerted no auto answer, callee:" + _callee);
        return;
    }

    Common::sleep(1000);

    std::string body = R"({"head":{"IDNO":")" + std::string(_callee.c_str()) + R"(", "ReqName":"AnswerReq", "Terminal":")" + std::string(_callee.c_str()) + R"(", "InvokeId":"002"}, "body":{}})";
    auto ret = httplib::Client(_httpAddr.c_str()).Post("/", body, "application/json");
    if (!ret || ret->status != 200)
    {
        UTIL_LOG_WRN("EbDialer", "content:notify AnswerReq to server:" + Common::String(_httpAddr.c_str()) + " failed.");
        close();
        return;
    }
    UTIL_LOG_IFO("EbDialer", "content:notify AnswerReq to server:" + Common::String(_httpAddr.c_str()));
}

void DialerSession::onCallAnswered(const DialerSipSessionPtr &sess, const std::string &sdp)
{
    Common::RecLock lock(_mutex);

    if (sess != _calloutSess)
        return;

    _calloutSess->_sipSess->SipAck("");
    _lastSdp = sdp;
    _callinSess->_sipSess->SipAnswer(sdp, std::map<std::string, std::string>());
}

void DialerSession::onCallConnected(const DialerSipSessionPtr &sess, const std::string &sdp)
{
    Common::RecLock lock(_mutex);

    if (_state == StateCalling)
        setState(StateTalking);
    else if (_state == StateHolding)
        setState(StateTalking);
}

void DialerSession::onCallTerminated(const DialerSipSessionPtr &sess)
{
    Common::RecLock lock(_mutex);

    setState(StateTermed);
    if (_callinSess == sess)
        _calloutSess->_sipSess->SipTerm();
    else
        _callinSess->_sipSess->SipTerm();
}

void DialerSession::onCallResponseModify(const DialerSipSessionPtr &sess, const std::string &sdp)
{
    Common::RecLock lock(_mutex);

    _lastSdp = sdp;
    if (_state == StateHolding)
    {
        UTIL_LOG_IFO("EbDialer", "content:onCallResponseModify holding send ack");
        sess->_sipSess->SipAck("");
    }
    else
    {
        if (sess == _callinSess)
        {
            UTIL_LOG_IFO("EbDialer", "content:onCallResponseModify talking send callout update " + Common::String(sdp.empty() ? " no sdp" : " with sdp"));
            _calloutSess->_sipSess->SipUpdate(sdp);
        }
        else
        {
            UTIL_LOG_IFO("EbDialer", "content:onCallResponseModify talking send callin ack " + Common::String(sdp.empty() ? " no sdp" : " with sdp"));
            _callinSess->_sipSess->SipAck(sdp);
            _calloutSess->_sipSess->SipAck("");
        }
    }
}

void DialerSession::setState(enum State state)
{
    static Common::String __state[] = {"Idle", "Calling", "Talking", "Holding", "Termed"};
    if (_state == state)
        return;

    UTIL_LOG_IFO("EbDialer", "content:update callee:" + _callee + " state:" + __state[_state] + "->" + __state[state]);
    _state = state;
    _lastStateTicks = Common::getCurTicks();
}

void DialerSession::updateExpire()
{
    if (Common::getCurTicks() - _lastUpdateTicks < 10000)
        return;
    _lastUpdateTicks = Common::getCurTicks();

    static Common::String lua_script = R"(
redis.call('EXPIRE', KEYS[1], 30)
)";

    Redis::CliPtr redis = _redis;
    if (!redis)
    {
        UTIL_LOG_WRN("ManagerAccount", "content:updateExpire no redis client.");
        return;
    }

    Common::StrVec argv;
    Redis::RplPtr rpl;
    Common::String reason;
    if (!redis->evalSha(lua_script, Redis::Key("DialerAcnNum:" + _callee), argv, rpl, reason))
    {
        UTIL_LOG_WRN("ManagerAccount", "content:updateExpire callee:" + _callee + " failed:" + reason);
        return;
    }

    if (rpl->isErr())
    {
        UTIL_LOG_WRN("ManagerAccount", "content:updateExpire callee:" + _callee + " exec error:" + rpl->getErr());
        return;
    }

    UTIL_LOG_IFO("ManagerAccount", "content:updateExpire callee:" + _callee);
}

bool TokenDialer::open(Common::String &failReason)
{
    Common::String httpHost;
    int httpPort;
    if (!_app->getAppConfig("EbDialer.Http.ListenHost", httpHost) || !_app->getAppConfigAsInt("EbDialer.Http.ListenPort", httpPort))
        return false;

    if (httpHost.empty())
    {
        UTIL_LOG_ERR("TokenDialer", "content:activate no config EbDialer.Http.ListenHost");
        failReason = "TokenDialer.InvaidConfig:EbDialer.Http.ListenHost";
        return false;
    }

    if (httpPort <= 0 || httpPort >= 65535)
    {
        UTIL_LOG_ERR("TokenDialer", "content:activate invalid config EbDialer.Http.ListenPort");
        failReason = "TokenDialer.InvaidConfig:EbDialer.Http.ListenPort";
        return false;
    }

    if (!_httpServer.bind_to_port(httpHost.c_str(), httpPort))
    {
        UTIL_LOG_ERR("TokenDialer", "content:activate listen:" + httpHost + ":" + Common::String(httpPort) + " failed.");
        failReason = "TokenDialer.BindPortFailed:" + httpHost + ":" + Common::String(httpPort);
        return false;
    }

    if (!startRun(0, "TokenDialer"))
    {
        UTIL_LOG_ERR("TokenDialer", "content:activate start listen:" + httpHost + ":" + Common::String(httpPort) + " failed.");
        failReason = "TokenDialer.StartListenFailed:" + httpHost + ":" + Common::String(httpPort);
        return false;
    }

    for (int i = 0; i < 5; i++)
    {
        if (_httpServerState == HttpServerError)
        {
            UTIL_LOG_ERR("TokenDialer", "content:activate start listen:" + httpHost + ":" + Common::String(httpPort) + " failed.");
            failReason = "TokenDialer.ListenFailed:" + httpHost + ":" + Common::String(httpPort);
            return false;
        }
        Common::sleep(10);
    }

    _app->setStatistics("EbDialer.Http.ListenHost", httpHost);
    _app->setStatisticsLong("EbDialer.Http.ListenPort", httpPort);
    UTIL_LOG_IFO("TokenDialer", "content:activate start listen:" + httpHost + ":" + Common::String(httpPort));
    return true;
}

void TokenDialer::close()
{
    _httpServer.stop();
}

void TokenDialer::onRun()
{
    _httpServer.Post("/token", [&](const httplib::Request &req, httplib::Response &res) {
        Common::String token;
        if (addToken(token))
        {
            res.status = 200;
            res.set_content(token.c_str(), "text/plain");
        }
        else
        {
            res.status = 400;
            res.set_content("InvalidToken", "text/plain");
        }
    });

    _httpServer.Get("/token/.*", [&](const httplib::Request &req, httplib::Response &res) {
        std::string token = req.path.substr(7);
        if (checkValid(token.c_str()))
            res.status = 200;
        else
            res.status = 400;
    });

    _httpServerState = HttpServerListened;

    if (!_httpServer.listen_after_bind())
    {
        UTIL_LOG_ERR("TokenDialer", "content:http server thread listen failed.");
        _httpServerState = HttpServerError;
        return;
    }

    UTIL_LOG_IFO("TokenDialer", "content:http server thread exit");
    _httpServerState = HttpServerDone;
}

static const char *const TokenKeyPrefix = "EbDialerTestToken";

bool TokenDialer::addToken(Common::String &token)
{
    static Common::String lua_script = R"(
redis.call('SET', KEYS[1], ARGV[1])
redis.call('EXPIRE', KEYS[1], 120)
)";

    Common::String value = Common::randString();
    token = TokenKeyPrefix + value;

    Redis::CliPtr redis = _redis;
    if (!redis)
    {
        UTIL_LOG_WRN("TokenDialer", "content:set token no redis client.");
        return false;
    }

    Common::StrVec argv = {value};
    Redis::RplPtr rpl;
    Common::String reason;
    if (!redis->evalSha(lua_script, Redis::Key(token), argv, rpl, reason))
    {
        UTIL_LOG_WRN("TokenDialer", "content:set token:" + token + " failed:" + reason);
        return false;
    }

    if (rpl->isErr())
    {
        UTIL_LOG_WRN("TokenDialer", "content:set token:" + token + " exec error:" + rpl->getErr());
        return false;
    }

    UTIL_LOG_IFO("TokenDialer", "content:set token:" + token);
    return true;
}

bool TokenDialer::checkValid(const Common::String &token)
{
    static Common::String lua_script = R"(
return redis.call('GET', KEYS[1])
)";

    Redis::CliPtr redis = _redis;
    if (!redis)
    {
        UTIL_LOG_WRN("TokenDialer", "content:check token no redis client.");
        return false;
    }

    Common::StrVec argv;
    Redis::RplPtr rpl;
    Common::String reason;
    if (!redis->evalSha(lua_script, Redis::Key(token), argv, rpl, reason))
    {
        UTIL_LOG_WRN("TokenDialer", "content:check token:" + token + " failed:" + reason);
        return false;
    }

    if (rpl->isErr())
    {
        UTIL_LOG_WRN("TokenDialer", "content:check token:" + token + " exec error:" + rpl->getErr());
        return false;
    }

    if (!rpl->isStr())
    {
        UTIL_LOG_IFO("TokenDialer", "content:check token:" + token + (rpl->isNil() ? " nil" : " invalid."));
        return false;
    }

    if (TokenKeyPrefix + rpl->getStr() != token)
    {
        UTIL_LOG_IFO("TokenDialer", "content:check token:" + token + " mismatch " + rpl->getStr());
        return false;
    }

    UTIL_LOG_IFO("TokenDialer", "content:check token:" + token + " OK.");
    return true;
}

MpCallEbDialer::MpCallEbDialer(const Common::ApplicationExPtr &application, const SipMpCall::SipLinePtr &sipLine)
    : Service::ServiceManagerI(application)
    , _sipLine(sipLine)
    , _calleeNumberStart(0)
    , _calleeNumberCount(0)
    , _calleeNumberNext(0)
{
    Common::String value;
    if (!application->getAppConfig("EventManager.MaxProcessors", value))
        application->setConfig("global.EventManager.MaxProcessors", "10");
}

// implement Service::ServiceManagerI
bool MpCallEbDialer::onActivate()
{
    if (!Service::ServiceManagerI::onActivate())
    {
        UTIL_LOG_ERR("EbDialer", "content:service manager activate failed.");
        return false;
    }

    if (!_sipLine)
    {
        _sipLine = SipMpCall::SipLine::create(this, _application.get());
        if (!_sipLine)
        {
            UTIL_LOG_ERR("EbDialer", "content:create sip line failed.");
            return false;
        }
    }

    Common::String config = _application->getAppConfig("EbDialer.CalleeNumberRange");
    int pos = config.find("-");
    if (pos < 1)
    {
        UTIL_LOG_ERR("EbDialer", "content:invalid EbDialer.CalleeNumberStart:" + config);
        return false;
    }

    _calleeNumberStart = config.substr(0, pos).toInt(-1);
    if (_calleeNumberStart <= 0)
    {
        UTIL_LOG_ERR("EbDialer", "content:invalid EbDialer.CalleeNumberStart:" + config);
        return false;
    }

    int endId = config.substr(pos + 1).toInt(-1);
    if (endId < _calleeNumberStart)
    {
        UTIL_LOG_ERR("EbDialer", "content:invalid EbDialer.CalleeNumberStart:" + config + " too few call number count.");
        return false;
    }

    _application->setStatistics("EbDialer.CalleeNumberRange", config);

    _calleeNumberCount = endId - _calleeNumberStart + 1;
    _calleeNumberNext = _calleeNumberStart;

    _application->getAppConfig("EbDialer.HttpUri", _httpAddr);

    return true;
}

void MpCallEbDialer::onDeactivate()
{
    if (_tokenDialer)
    {
        _tokenDialer->close();
        _tokenDialer = 0;
    }

    if (_redis)
    {
        _redis->onDeactivate();
        _redis = 0;
    }

    for (auto kv : _sessions)
        kv.second->close();
    _sessions.clear();

    Service::ServiceManagerI::onDeactivate();
}

void MpCallEbDialer::onShutdown()
{
    Service::ServiceManagerI::onShutdown();
}

void MpCallEbDialer::onSchd()
{
    Service::ServiceManagerI::onSchd();

    if (_sipLine)
        _sipLine->schd();
}

void MpCallEbDialer::onUpdateConfigs()
{
    Service::ServiceManagerI::onUpdateConfigs();

    if (_sipLine)
        _sipLine->updateConfigs();

    for (auto it = _sessions.begin(); it != _sessions.end();)
    {
        if (it->second->isTermed())
            _sessions.erase(it++);
        else
        {
            it->second->schd();
            ++it;
        }
    }

    if (_redis)
    {
        _redis->onUpdateConfigs();
    }
    else if (_application->getAppConfig("RedisUri", _redisUri))
    {
        _redis = Redis::Cli::create(_application.get(), _redisUri);
        if (!_redis)
            UTIL_LOG_WRN("EbDialer", "content:invalid redis uri:" + _redisUri);
    }

    _application->getAppConfig("EbDialer.HttpUri", _httpAddr);
    _application->setStatistics("EbDialer.HttpUri", _httpAddr);
}

bool MpCallEbDialer::getMainService(Common::ObjectServerPtr &service, Common::String &name, bool &famous)
{
    return false;
}

bool MpCallEbDialer::isMainServiceReady(Common::String &reason)
{
    if (!_sipLine)
    {
        reason = "InvalidSipLine";
        return false;
    }

    if (!_sipLine->isReady(reason))
    {
        return false;
    }

    if (_httpAddr.empty())
    {
        reason = "NoHttpUri";
        return false;
    }

    if (!_redis)
    {
        reason = "NoRedis";
        return false;
    }

    if (!_tokenDialer)
    {
        _tokenDialer = new TokenDialer(_application, _redis);
        if (!_tokenDialer->open(reason))
        {
            if (!reason.empty())
                UTIL_LOG_WRN("EbDialer", "content:open token dialer failed:" + reason);
            _tokenDialer = 0;
        }
    }

    return true;
}

Common::String MpCallEbDialer::getNextNumber(int startNumberId, int endNumberId)
{
    static Common::String lua_script = R"(
local start_id = tonumber(ARGV[1])
local end_id = tonumber(ARGV[2])
local max_attempts = end_id - start_id + 1

-- 设置初始值
redis.call('SETNX', KEYS[1], start_id)
redis.call('EXPIRE', KEYS[1], 60)

-- 获取当前值
local current_id = tonumber(redis.call('GET', KEYS[1]))

-- 如果超过范围，则重置 current_id
if current_id > end_id then
    redis.call('SET', KEYS[1], start_id)
    current_id = start_id
end

-- 查找下一个已经使用的 ID
local attempts = 0
while redis.call('EXISTS', ARGV[3] .. 'AcnNum:' .. current_id) == 0 or redis.call('EXISTS', ARGV[3] .. 'DialerAcnNum:' .. current_id) == 1 do
    current_id = tonumber(redis.call('INCR', KEYS[1]))
    if current_id > end_id then
        redis.call('SET', KEYS[1], start_id)
        current_id = start_id
    end

    attempts = attempts + 1
    if attempts >= max_attempts then
        return ''
    end
end

redis.call('INCR', KEYS[1])
redis.call('SET', ARGV[3] .. 'DialerAcnNum:' .. current_id, '1', 'EX', 30)

-- 返回未使用的 ID
return tostring(current_id)
)";

    Redis::CliPtr redis = _redis;
    if (!redis)
    {
        UTIL_LOG_WRN("ManagerAccount", "content:getNextNumber no redis client.");
        return Common::String();
    }

    Common::StrVec argv;
    argv.push_back(Common::String(startNumberId));
    argv.push_back(Common::String(endNumberId));
    argv.push_back(Redis::Key(""));

    Redis::RplPtr rpl;
    Common::String reason;
    if (!redis->evalSha(lua_script, Redis::Key("DialerAcnNumCurrentId"), argv, rpl, reason))
    {
        UTIL_LOG_WRN("ManagerAccount", "content:getNextNumber failed:" + reason);
        return Common::String();
    }

    if (rpl->isErr())
    {
        UTIL_LOG_WRN("ManagerAccount", "content:getNextNumber exec error:" + rpl->getErr());
        return Common::String();
    }

    if (!rpl->isStr())
    {
        UTIL_LOG_WRN("ManagerAccount", "content:getNextNumber invalid type");
        return Common::String();
    }

    UTIL_LOG_IFO("ManagerAccount", "content:getNextNumber " + rpl->getStat());
    return rpl->getStr();
}

SipMpCall::SipLineSessionListenerPtr MpCallEbDialer::onCreateSession(const SipMpCall::SipLineSessionPtr &session)
{
    Common::String callee = getNextNumber(_calleeNumberStart, _calleeNumberStart + _calleeNumberCount - 1);
    if (callee.empty())
    {
        UTIL_LOG_WRN("EbDialer", "content:no avaliable callee");
        return nullptr;
    }

    bool autoAnswer = true;
    Common::String value;
    if (_application->getAppConfig("EbDialer.AutoAnswer", value))
        autoAnswer = value.toInt(1) != 0;

    DialerSessionPtr dialerSess = new DialerSession(callee, _httpAddr, _sipLine, _redis, autoAnswer);
    auto ret = dialerSess->open(session);
    if (ret)
        _sessions[callee] = dialerSess;
    return ret;
}

} // namespace EbService