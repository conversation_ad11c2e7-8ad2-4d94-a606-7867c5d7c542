//
// *****************************************************************************
// Copyright (c) 2023 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//
// Created on 2023/12/6 by <PERSON>
//

#pragma once

#include "Common/CommonEx.h"
#include "Common/Net.h"
#include "Common/Util.h"
#include "DbConn/RedisClient.h"
#include "SipLine/SipLine.h"
#include "Service/ServiceI.h"
#include "SipAdapter/SipCallInterface.h"
#include "cpp-httplib/httplib.h"

namespace EbService
{

class DialerSession;
class DialerRtpStream;
class DialerRtpSession;
class DialerSipSession;

typedef Common::Handle<DialerSession> DialerSessionPtr;
typedef Common::Handle<DialerRtpStream> DialerRtpStreamPtr;
typedef Common::Handle<DialerRtpSession> DialerRtpSessionPtr;
typedef Common::Handle<DialerSipSession> DialerSipSessionPtr;


class DialerRtpStream : public Common::NetReceiver
{
public:
    bool open(const Common::String &host, int port, Common::String &localHost, int &localPort);
    void close();

    virtual void recv(const unsigned char *data, int dataLen);
    virtual Common::NetReceiverPtr recvConnection(const Common::NetSenderPtr &sender);

private:
    Common::NetSenderPtr _listener;
    Common::NetSenderPtr _sender;
};

class DialerRtpSession : virtual public Common::Shared
{
public:
    bool openAudio(int port);
    bool openVideo(int port);
    void close();

    Common::String getSdp(const Common::String &peerSdp);
    Common::String setSdp(const Common::String &peerSdp);

private:
    DialerRtpStreamPtr _audioStream;
    DialerRtpStreamPtr _videoStream;
};

class DialerSipSession : public SipMpCall::SipLineSessionListener
{
public:
    DialerSipSession(const DialerSessionPtr &dialerSess, const SipMpCall::SipLineSessionPtr &sipSess)
        : _termed(false)
        , _dialerSess(dialerSess)
        , _sipSess(sipSess)
    {
    }

    bool isTermed() { return _termed || !_sipSess; }
    void close();

    virtual void onCallIncoming(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) override;
    virtual void onCallAlerted(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) override;
    virtual void onCallAnswered(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) override;
    virtual void onCallUpdate(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) override;
    virtual void onCallRequestUpdate(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) override;
    virtual void onCallResponseUdate(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) override;
    virtual void onCallConnected(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) override;
    virtual void onCallRequestModify(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) override;
    virtual void onCallResponseModify(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) override;
    virtual void onCallTerminated(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) override;

private:
    bool _termed;
    DialerSessionPtr _dialerSess;
    SipMpCall::SipLineSessionPtr _sipSess;

    friend class DialerSession;
};

class DialerSession : virtual public Common::Shared
{
public:
    enum State
    {
        StateIdle,
        StateCalling,
        StateTalking,
        StateHolding,
        StateTermed
    };

public:
    DialerSession(const Common::String &callee, const Common::String &httpAddr, const SipMpCall::SipLinePtr &sipLine, const Redis::CliPtr &redis, bool autoAnswer)
        : _callee(callee)
        , _httpAddr(httpAddr.c_str())
        , _sipLine(sipLine)
        , _state(StateIdle)
        , _lastStateTicks(0)
        , _redis(redis)
        , _lastUpdateTicks(Common::getCurTicks())
        , _autoAnswer(autoAnswer)
    {
    }

    SipMpCall::SipLineSessionListenerPtr open(const SipMpCall::SipLineSessionPtr &session);
    void schd();
    void close();
    bool isTermed() { return (!_callinSess || _callinSess->isTermed()) && (!_calloutSess || _calloutSess->isTermed()); }

    void onCallIncoming(const DialerSipSessionPtr &sess, const std::string &sdp, const std::string &sip);
    void onCallAlerted(const DialerSipSessionPtr &sess);
    void onCallAnswered(const DialerSipSessionPtr &sess, const std::string &sdp);
    void onCallConnected(const DialerSipSessionPtr &sess, const std::string &sdp);
    void onCallTerminated(const DialerSipSessionPtr &sess);
    void onCallResponseModify(const DialerSipSessionPtr &sess, const std::string &sdp);

private:
    void setState(enum State state);
    void updateExpire();

private:
    Common::RecMutex _mutex;
    std::string _guestName;
    Common::String _callee;
    std::string _httpAddr;
    SipMpCall::SipLinePtr _sipLine;
    DialerSipSessionPtr _callinSess;
    DialerSipSessionPtr _calloutSess;
    Redis::CliPtr _redis;
    unsigned int _lastUpdateTicks;
    bool _autoAnswer;

    std::string _lastSdp;
    unsigned int _lastStateTicks;
    enum State _state;
};

class TokenDialer : public Common::Thread
{
    enum HttpServerState
    {
        HttpServerIdle,
        HttpServerListened,
        HttpServerDone,
        HttpServerError
    };

public:
    TokenDialer(const Common::ApplicationExPtr application, const Redis::CliPtr &redis)
        : _app(application)
        , _redis(redis)
        , _httpServerState(HttpServerIdle)
    {
    }

    bool open(Common::String &failReason);
    void close();
    void onRun() override;

    bool addToken(Common::String &token);
    bool checkValid(const Common::String &token);

private:
    Common::ApplicationExPtr _app;
    Redis::CliPtr _redis;
    httplib::Server _httpServer;
    int _httpServerState;
};

class MpCallEbDialer : public Service::ServiceManagerI, public SipMpCall::SipLineListner
{
public:
    explicit MpCallEbDialer(const Common::ApplicationExPtr &application, const SipMpCall::SipLinePtr &sipLine = 0);

    bool __ex(const Common::ServerCallPtr &call, const Common::String &cmd, const Common::IputStreamPtr &iput) override { return false; }

    // implement Service::ServiceManagerI
    bool onActivate() override;
    void onDeactivate() override;
    void onShutdown() override;
    void onSchd() override;
    void onUpdateConfigs() override;
    bool getMainService(Common::ObjectServerPtr &service, Common::String &name, bool &famous) override;
    bool isMainServiceReady(Common::String &reason) override;

    // implement SipLineListener
    SipMpCall::SipLineSessionListenerPtr onCreateSession(const SipMpCall::SipLineSessionPtr &session) override;

private:
    Common::String getNextNumber(int startNumberId, int endNumberId);

private:
    Common::String _httpAddr;
    int _calleeNumberStart;
    int _calleeNumberCount;
    int _calleeNumberNext;
    SipMpCall::SipLinePtr _sipLine;
    std::map<Common::String, DialerSessionPtr> _sessions;
    Redis::CliPtr _redis;
    Common::String _redisUri;
    Common::Handle<TokenDialer> _tokenDialer;
};

} // namespace EbService