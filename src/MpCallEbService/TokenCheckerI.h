//
// *****************************************************************************
// Copyright (c) 2023 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//
// Created on 2023/12/13 by <PERSON>
//

#pragma once

#include "TokenChecker.h"
#include "DbConn/RedisClient.h"

namespace EbService
{

class TokenCheckerI : public TokenChecker
{
public:
    TokenCheckerI(const Common::ApplicationPtr &app, const Common::String &uri)
        : _app(app)
        , _uri(uri)
    {
    }

    virtual bool onActivate(Common::String &failReason) override;
    virtual void onDeactivate() override;
    virtual void onUpdateConfigs() override;

    virtual bool checkValid(const Common::String &token, Common::String &value) override;

private:
    Common::ApplicationPtr _app;
    Common::String _uri;
    Redis::CliPtr _redis;
};

} // namespace EbService