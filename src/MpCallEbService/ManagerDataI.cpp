//
// *****************************************************************************
// Copyright (c) 2023 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//
// Created on 2023/11/27 by <PERSON>
//

#include "ManagerDataI.h"
#include "Common/CommonEx.h"
#include "Common/TypesPub.h"
#include "Common/Util.h"
#include "DbConn/RedisClient.h"
#include "MpCallEbService/ManagerData.h"
#include <exception>

namespace EbService
{

bool MemDataDb::Category::lock(int timeout)
{
    _mutex.lock();
    return true;
}

bool MemDataDb::Category::unlock()
{
    _mutex.unlock();
    return true;
}

bool MemDataDb::Category::set(const DataPtr &data, int expires)
{
    if (expires <= 0)
        return false;

    MemDataPtr memData = new MemData(Common::getMonoTicks() + expires * 1000, data);

    for (auto key : data->keys())
        _datas[key] = memData;

    return true;
}

DataPtr MemDataDb::Category::get(const Common::String &key)
{
    auto it = _datas.find(key);
    if (it == _datas.end())
        return nullptr;

    return it->second->data;
}

bool MemDataDb::Category::update(const Common::String &key, int expires)
{
    auto it = _datas.find(key);
    if (it == _datas.end())
        return expires <= 0 ? true : false;

    if (expires <= 0)
    {
        DataKeys keys = it->second->data->keys();
        for (auto key : keys)
            _datas.erase(key);
        return true;
    }

    it->second->expiredTicks = Common::getMonoTicks() + expires * 1000;
    return true;
}

void MemDataDb::Category::schd()
{
    Common::RecLock lock(_mutex);

    Common::Ulong now = Common::getMonoTicks();

    for (auto it = _datas.begin(); it != _datas.end();)
    {
        if (now >= it->second->expiredTicks)
        {
            UTIL_LOG_IFO("MemDataDb", "content:expired key:" + it->first);
            _datas.erase(it++);
        }
        else
            ++it;
    }
}

void MemDataDb::onUpdateConfigs()
{
    for (auto kv : _categorys)
    {
        kv.second->schd();
    }
}

DataCategoryPtr MemDataDb::addCategory(const Common::String &name, const DataCategoryListenerPtr &listener)
{
    if (_categorys.find(name) != _categorys.end())
    {
        UTIL_LOG_WRN("MemDataDb", "content:duplicated category:" + name);
        return nullptr;
    }

    CategoryPtr category = new Category(name);
    if (!category)
    {
        UTIL_LOG_WRN("MemDataDb", "content:create category:" + name + " failed.");
        return nullptr;
    }

    _categorys.insert(make_pair(name, category));
    UTIL_LOG_IFO("MemDataDb", "content:add category:" + name + " obj:" + Common::String((Common::Long)category.get()));
    return category.get();
}

bool RedisDataDb::Lock::schd()
{
    static Common::String lua_script = R"(
if redis.call('EXISTS', KEYS[1]) == 1 and redis.call('GET', KEYS[1]) == ARGV[1] then
   return redis.call('EXPIRE', KEYS[1], 10)
else
   return 0
end
)";

    Common::StrVec argv;
    argv.push_back(_value);

    Redis::RplPtr rpl;
    Common::String reason;
    if (!_redis->evalSha(lua_script, Redis::Key(_key), argv, rpl, reason))
    {
        UTIL_LOG_WRN("RedisDataDb", "content:update key:" + _key + " failed:" + reason);
        return false;
    }

    if (rpl->isErr())
    {
        UTIL_LOG_WRN("RedisDataDb", "content:update key:" + _key + " exec error:" + rpl->getErr());
        return false;
    }

    if (!rpl->isInt())
    {
        UTIL_LOG_WRN("RedisDataDb", "content:update key:" + _key + " return type invalid");
        return false;
    }

    if (rpl->getInt() != 1)
    {
        UTIL_LOG_WRN("RedisDataDb", "content:update key:" + _key + " not locked");
        return false;
    }

    UTIL_LOG_DBG("RedisDataDb", "content:update key:" + _key + " value:" + _value);
    return true;
}

bool RedisDataDb::Lock::lock(int timeout)
{
        static Common::String lua_script = R"(
if redis.call('SETNX', KEYS[1], ARGV[1]) == 1 then
   return redis.call('EXPIRE', KEYS[1], 10)
else
   return 0
end
)";

    int waitMs = 1 + (int)Common::getRand(100);
    unsigned int startTicks = Common::getCurTicks();
    for (;;)
    {
        Common::StrVec argv;
        argv.push_back(_value);

        Redis::RplPtr rpl;
        Common::String reason;
        if (!_redis->evalSha(lua_script, Redis::Key(_key), argv, rpl, reason))
        {
            UTIL_LOG_WRN("RedisDataDb", "content:lock key:" + _key + " failed:" + reason);
            return false;
        }

        if (rpl->isErr())
        {
            UTIL_LOG_WRN("RedisDataDb", "content:lock key:" + _key + " exec error:" + rpl->getErr());
            return false;
        }

        if (!rpl->isInt())
        {
            UTIL_LOG_WRN("RedisDataDb", "content:lock key:" + _key + " return type invalid");
            return false;
        }

        if (rpl->getInt() == 1)
        {
            UTIL_LOG_DBG("RedisDataDb", "content:lock key:" + _key + " value:" + _value);
            return true;
        }

        int duration = Common::getCurTicks() - startTicks;
        if (duration > 6000)
        {
            UTIL_LOG_WRN("RedisDataDb", "content:lock key:" + _key + " wait:" + Common::String(duration));
            return false;
        }

        if (timeout > 0 && duration > timeout)
        {
            UTIL_LOG_WRN("RedisDataDb", "content:lock key:" + _key + " timeout:" + Common::String(timeout));
            return false;
        }

        Common::sleep(waitMs);
        waitMs = 50 + (int)Common::getRand(100);
    }
}

bool RedisDataDb::Lock::unlock()
{
    static Common::String lua_script = R"(
if redis.call('EXISTS', KEYS[1]) == 1 and redis.call('GET', KEYS[1]) == ARGV[1] then
   return redis.call('DEL', KEYS[1])
else
   return 0
end
)";

    Common::StrVec argv;
    argv.push_back(_value);

    Redis::RplPtr rpl;
    Common::String reason;
    if (!_redis->evalSha(lua_script, Redis::Key(_key), argv, rpl, reason))
    {
        UTIL_LOG_WRN("RedisDataDb", "content:unlock key:" + _key + " failed:" + reason);
        return false;
    }

    if (rpl->isErr())
    {
        UTIL_LOG_WRN("RedisDataDb", "content:unlock key:" + _key + " exec error:" + rpl->getErr());
        return false;
    }

    if (!rpl->isInt())
    {
        UTIL_LOG_WRN("RedisDataDb", "content:unlock key:" + _key + " return type invalid");
        return false;
    }

    if (rpl->getInt() != 1)
    {
        UTIL_LOG_WRN("RedisDataDb", "content:unlock key:" + _key);
        return false;
    }

    UTIL_LOG_DBG("RedisDataDb", "content:unlock key:" + _key + " value:" + _value);
    return true;
}

void RedisDataDb::Category::schd()
{
    Common::RecLock lock(_mutex);
    if (_lock && !_lock->schd())
        _lock = 0;
}

bool RedisDataDb::Category::lock(int timeout)
{
    static Common::Long __id = 0;
    Common::Handle<Lock> lock = new Lock(_redis, _name + ".lock", _appName + ":" + Common::String(__id++));
    if (!lock->lock(timeout))
        return false;

    Common::RecLock __lock(_mutex);
    _lock = lock;
    return true;
}

bool RedisDataDb::Category::unlock()
{
    Common::RecLock __lock(_mutex);
    _lock = 0;
    return true;
}

bool RedisDataDb::Category::set(const DataPtr &data, int expires)
{
    if (data->did.empty())
        data->did = _listener->genDid();

    vector<Redis::CmdPtr> cmds;

    cmds.push_back(Redis::Cmd::cmd("SET")->key(Redis::Key(data->did))->p(data->to_string())->p("EX")->p(expires));
    for (auto &key : data->keys())
        cmds.push_back(Redis::Cmd::cmd("SET")->key(Redis::Key(key))->p(data->did)->p("EX")->p(expires));

    vector<Redis::RplPtr> replys;
    Common::String reason;
    if (!_redis->exec(cmds, replys, reason))
    {
        UTIL_LOG_WRN("RedisDataDb", "content:set did:" + data->did + " failed:" + reason);
        return false;
    }

    UTIL_LOG_IFO("RedisDataDb", "content:set did:" + data->did);
    return true;
}

DataPtr RedisDataDb::Category::get(const Common::String &key)
{
    static Common::String lua_script = R"(
if redis.call('EXISTS', KEYS[1]) == 0 then
   return 'KeyNotFound'
end

local did = redis.call('GET', KEYS[1])
local did_key = ARGV[1] .. did
if redis.call('EXISTS', did_key) == 0 then
   redis.call('DEL', KEYS[1])
   return 'DidNotFound:' .. did_key
end

local content = redis.call('GET', did_key)
return {did, content}
)";

    Common::StrVec argv;
    argv.push_back(Redis::Key(""));

    Redis::RplPtr rpl;
    Common::String reason;
    if (!_redis->evalSha(lua_script, Redis::Key(key), argv, rpl, reason))
    {
        UTIL_LOG_WRN("RedisDataDb", "content:get key:" + key + " failed:" + reason);
        return false;
    }

    if (rpl->isErr())
    {
        UTIL_LOG_WRN("RedisDataDb", "content:get key:" + key + " exec error:" + rpl->getErr());
        return false;
    }

    if (rpl->isStr())
    {
        UTIL_LOG_WRN("RedisDataDb", "content:get key:" + key + " exec failed:" + rpl->getStr());
        return false;
    }

    if (!rpl->isArray() && rpl->arraySize() != 2)
    {
        UTIL_LOG_WRN("RedisDataDb", "content:get key:" + key + " return type invalid");
        return false;
    }

    Common::String did = rpl->getArray(0)->getStr();
    Common::String content = rpl->getArray(1)->getStr();
    if (did.empty() || content.empty())
    {
        UTIL_LOG_WRN("RedisDataDb", "content:get key:" + key + " empty data.");
        return false;
    }

    DataPtr data = _listener->createData(content);
    if (!data)
    {
        UTIL_LOG_WRN("RedisDataDb", "content:get key:" + key + " create failed.");
        return false;
    }

    data->did = did;
    UTIL_LOG_DBG("RedisDataDb", "content:get key:" + key + " did:" + did);
    return data.get();
}

bool RedisDataDb::Category::update(const Common::String &key, int expires)
{
    DataPtr data = get(key);
    if (!data)
    {
        UTIL_LOG_WRN("RedisDataDb", "content:update get key:" + key + " failed.");
        return false;
    }

    vector<Redis::CmdPtr> cmds;

    for (auto &__key : data->keys())
    {
        if (expires > 0)
            cmds.push_back(Redis::Cmd::cmd("EXPIRE")->key(Redis::Key(__key))->p(expires));
        else
            cmds.push_back(Redis::Cmd::cmd("DEL")->key(Redis::Key(__key)));
    }

    if (expires > 0)
        cmds.push_back(Redis::Cmd::cmd("EXPIRE")->key(Redis::Key(data->did))->p(expires));
    else
        cmds.push_back(Redis::Cmd::cmd("DEL")->key(Redis::Key(data->did)));

    vector<Redis::RplPtr> replys;
    Common::String reason;
    if (!_redis->exec(cmds, replys, reason))
    {
        UTIL_LOG_WRN("RedisDataDb", "content:update key:" + key + " failed:" + reason);
        return false;
    }

    for (auto &reply : replys)
    {
        if (reply->isErr())
        {
            UTIL_LOG_WRN("RedisDataDb", "content:update key:" + key + " exec error:" + reply->getErr());
            return false;
        }
    }

    if (expires <= 0)
        UTIL_LOG_IFO("RedisDataDb", "content:update key:" + key + " delete");
    else
        UTIL_LOG_DBG("RedisDataDb", "content:update key:" + key + " expires:" + Common::String(expires));
    return true;
}

Common::String RedisDataDb::CategoryListener::genDid()
{
    return _name + _db->genDid();
}

DataPtr RedisDataDb::CategoryListener::createData(const Common::String &str)
{
    return _listener->createData(str);
}

bool RedisDataDb::onActivate(Common::String &failReason)
{
    _redis = Redis::Cli::create(_app, _uri);
    if (!_redis)
    {
        UTIL_LOG_ERR("RedisDataDb", "content:create redis client failed, uri:" + _uri);
        failReason = "RedisDataDb.CreateRedisFailed:" + _uri;
        return false;
    }

    _appId = _app->getAppName().substr(_app->getPrefixName().size());

    UTIL_LOG_IFO("RedisDataDb", "content:create redis client ok, uri:" + _uri);
    return true;
}

void RedisDataDb::onDeactivate()
{
    UTIL_LOG_IFO("RedisDataDb", "content:onDeactivate");
    if (_redis)
    {
        _redis->onDeactivate();
        _redis = 0;
    }
    _categorys.clear();
}

void RedisDataDb::onUpdateConfigs()
{
    if (_redis)
        _redis->onUpdateConfigs();

    for (auto kv : _categorys)
        kv.second->schd();
}

DataCategoryPtr RedisDataDb::addCategory(const Common::String &name, const DataCategoryListenerPtr &listener)
{
    if (_categorys.find(name) != _categorys.end())
    {
        UTIL_LOG_WRN("RedisDataDb", "content:duplicated category:" + name);
        return nullptr;
    }

    UTIL_LOG_IFO("RedisDataDb", "content:add category:" + name);
    CategoryPtr category = new Category(name, new CategoryListener(this, name, listener), _redis, _app->getAppName());
    _categorys.insert(make_pair(name, category));
    return category.get();
}

Common::String RedisDataDb::genDid()
{
    Common::Long now = Common::getCurTimeMs();
    if (now <= _lastDidMs)
        _lastDidMs++;
    else
        _lastDidMs = now;
    return _appId + "#" + Common::String(_lastDidMs);
}

DataDbPtr DataDb::create(const Common::ApplicationPtr &app)
{
    DataDbPtr db;

    Common::String uri = app->getAppConfig("RedisUri");
    try
    {
        if (uri.empty())
        {
            UTIL_LOG_IFO("DataDb", "content:create mem data db.");
            db = new MemDataDb();
        }
        else
        {
            UTIL_LOG_IFO("DataDb", "content:create redis data db, uri:" + uri);
            db = new RedisDataDb(app, uri);
        }
    }
    catch (const std::exception &e)
    {
        UTIL_LOG_ERR("DataDb", "content:create db for uri:" + uri + " error:" + e.what());
        return nullptr;
    }

    return db;
}

} // namespace EbService