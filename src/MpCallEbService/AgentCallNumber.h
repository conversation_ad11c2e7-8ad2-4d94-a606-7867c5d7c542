//
// *****************************************************************************
// Copyright (c) 2023 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//
// Created on 2023/11/27 by <PERSON>
//

#pragma once

#include "Common/CommonEx.h"
#include "Common/Util.h"
#include "ManagerData.h"
#include "Util/ServiceModule.h"

namespace EbService
{

class AgentCallNumberManager;
typedef Common::Handle<AgentCallNumberManager> AgentCallNumberManagerPtr;

class AgentCallNumberManager : public Service::ServiceModule
{
public:
    static AgentCallNumberManagerPtr create(const Common::ApplicationExPtr &app, const DataDbPtr &db);

    virtual bool bind(const Common::String &account, Common::String &number) = 0;
    virtual void unbind(const Common::String &account) = 0;
    virtual bool alive(const Common::String &account) = 0;

    virtual Common::String getBindedNumber(const Common::String &account) = 0;
    virtual Common::String getBindedAccount(const Common::String &number) = 0;
};

} // namespace EbService