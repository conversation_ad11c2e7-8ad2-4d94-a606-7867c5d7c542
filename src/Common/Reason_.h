#include "Common/Reason.h"

#ifndef __Reason__h__
#define __Reason__h__

#define REASON_PERMISSION_DENIED_NAME_RESERVED REASON_PERMISSION_DENIED ":name-reserved"
#define REASON_LICENCE_ERROR "licence-error"                    /* licence error */
#define REASONABLE_ERROR                "reasonable-error"      /* reasonable error */

#define REASON_APPKEY_ERROR "appkey-error"                      /* appkey error */
#define REASON_DOMAIN_ERROR                    "domain-error"          /* domain error */
#define REASON_DOMAIN_ERROR_INVALID_ACCOUNTID REASON_DOMAIN_ERROR ":invalid accountId"
#define REASON_DOMAIN_ERROR_INVALID_USERID REASON_DOMAIN_ERROR ":invalid userId"
#define REASON_DOMAIN_ERROR_FORBIDDEN_DOMAIN REASON_DOMAIN_ERROR ":domain banned"

#define RANDOM_ERROR                    "random-error"          /* random error, maybe random expired, or connection closed */
#define REA<PERSON>N_SESSION_ERROR                   "session-error"         /* session error */
#define REASON_SESSION_ERROR_NO_SESSIONS REASON_SESSION_ERROR ":no-sessions"
#define REASON_SESSION_ERROR_NO_CLIENT_SESSION REASON_SESSION_ERROR ":null-client-session"
#define REASON_SESSION_ERROR_ALL_SESSIONS_FAILED REASON_SESSION_ERROR ":all_session_failed"
#define REASON_SESSION_ERROR_SESSIONID_NOT_FOUND REASON_SESSION_ERROR ":sessionid_not_found"

#define REASON_TOKEN_ERROR                     "token-error"           /* token error, maybe prev valid connection released */
#define REASON_TOKEN_ERROR_TOKEN_MISMATCH REASON_TOKEN_ERROR ":token-mismatch"
#define REASON_PASSWORD_ERROR                       "pwd-error"             /* password error */
#define REASON_PASSWORD_ERROR_TOKEN_SIZE_INVALID REASON_PASSWORD_ERROR ":token_size_invalid"
#define REASON_PASSWORD_ERROR_APPSECRET_NOT_FOUND REASON_PASSWORD_ERROR ":appscret_not_found"
#define REASON_PASSWORD_ERROR_INVALID_PAYLOAD REASON_PASSWORD_ERROR ":payload_invalid"
#define REASON_PASSWORD_ERROR_VERIFY_ERROR REASON_PASSWORD_ERROR ":ver_sig_appkey_error"
#define REASON_PASSWORD_ERROR_ACCOUNTID_MISMATCH REASON_PASSWORD_ERROR ":accountid_mismatch"
#define REASON_PASSWORD_ERROR_TOKEN_EXPIRED REASON_PASSWORD_ERROR ":token_expired"

#define SERVER_BUSY                     "server-busy"           /* server-busy:xxx-error, client retry after 10min when server busy */
#define CONNECTION_ERROR                "connection-error"      /* connection error, inner error */
#define REASON_TIMEOUT_ERROR "timeout-error"                 /* wait timeout */
#define REASON_TERMINATED_ERROR "terminated-error"              /* call terminated when application shutdown */
#define REASON_TERMINATED_ERROR_LOGOUTED REASON_TERMINATED_ERROR ":logout"
#define REASON_TERMINATED_ERROR_TOO_MANY_CALLS REASON_TERMINATED_ERROR ":too-many-calls"
#define REASON_TERMINATED_ERROR_CLOSED REASON_TERMINATED_ERROR ":closed"

#define REASON_THIRD_AUTH_ERROR                "third-auth-error"      /* 3rd auth server return error */
#define KX_VERSION_ERROR                "kx-version-error"      /* key exchange version error */
#define KX_CERT_ERROR                   "kx-cert-error"         /* key exchange certificate error */
#define DOMAIN_BLOCKED_ERROR            "domain-blocked-error"  /* domain blocked error */

#define REASON_ANOTHER_DEVICE_LOGINED "another-device-logined" /* kick off by server when another device logined */
#define SAME_DEVICE_LOGIN               "same-device-login"
#define SERVER_KICK_OFF                 "server-kick-off"
#define CLIENT_LOGOUT                   "client-logout"

#define REASON_UNKNOWN_ERROR "unknown-error"
#define REASON_ACCOUNT_EXPIRED "account-expire"

#define REASON_AUTH_ERROR_NO_APPKEY "auth_error:no_appkey"
#define REASON_AUTH_ERROR_INVALID_APPKEY "auth_error:invalid_appkey"
#define REASON_AUTH_ERROR_INVALID_DOMAIN_CONFIG "auth_error:invalid_domain_config"
#define REASON_SERVER_ERROR_INVALID_UID REASON_SERVER_ERROR ":invalid_uid"

#define REASON_OBJECT_ERROR_INVALID_CATEGORY_SIZE REASON_OBJECT_ERROR ":invalid-categorys-size"
#define REASON_OBJECT_ERROR_CATEGORY_NOT_FOUND REASON_OBJECT_ERROR ":category-not-found"

#define REASON_ACCOUNT_NOT_EXIST "account_not_exist"
#define REASON_PUSH_ERROR "push_error"
#define REASON_NO_PUSH_TEMPLETE "no_push_templete"
#define REASON_LOGIN_SESSION_REPLACE "login_session_replace"

// AccountDb相关
#define REASON_DB_ERROR            "db-error"              /* access database error */
#define REASON_REDIS_EXEC_ERROR     "redis_exec_error"
#define REASON_LOCATE_ERROR        "locate_error"
#define REASON_REDIS_SET_ACCOUNT_ID_ERROR "redis_set_accountId_error"
#define REASON_REDIS_SET_DEVICE_ID_ERROR "redis_set_deviceId_error"
#define REASON_REDIS_SET_USER_ID_ERROR "redis_set_userId_error"
#define REASON_EMPTY_DC_FOR_ACCOUNTID "empty_dc_for_accountId"
#define REASON_INCORRECT_USER_CREATE "incorrect_user_create"
#define REASON_DB_LOCATE_ERROR "db_locate_error"
#define REASON_PARAMS_ERROR_MSG_TO_BIG "params_error_msg_to_big"
#define REASON_DESERIALIZE_ERROR "deserialize_error"
#define REASON_COND_PROPS_ERROR "cond-props-error"           /* conditions error, inner error */

// User相关
#define REASON_AUTHORIZED_RELATION_NOT_FOUND "authorized_relation_not_found"
#define REASON_CLIENT_AUTH_PWD_ERR "client_auth_pwd_error"

// UserDb相关
#define REASON_CONN_ERROR "conn_error"
#define REASON_EXECUTE_ERROR "execute_error"
#define REASON_FETCH_ERROR "fetch_error"
#define REASON_NOT_FOUND_IN_TABLE "not_found_in_table"
#define REASON_USER_ID_NOT_FOUND "user_id_not_found"
#define REASON_INVALID_DOMAIN_ID "invalid_domain_id"
#define REASON_DOMAIN_ID_NOT_FOUND "domain_id_not_found"
#define REASON_GET_CURTIME_EXE_ERRROR "getCurTime_execute_error"
#define REASON_PERSIST_RELATION_EXIST "persist_relation_exist"
#define REASON_ACCOUNT_BANNED_ERROR "account_banned_error"
#define REASON_ACCOUNT_DELETED_ERROR  "account_delete_error"
#define REASON_APP_ID_NOT_FOUND "app_id_not_found"
#define REASON_DOMAIN_NAME_NOT_FOUND "domain_name_not_found"
#define REASON_NO_SUCH_TABLE_ERROR "no_such_table_error"
#define REASON_USER_ID_INVALID  "user_id_invalid"
#define REASON_NOT_ADMIN_USER   "not_admin_user"
#define REASON_USER_ID_NOT_FOUND "user_id_not_found"
#define REASON_DOMAIN_ID_INVALID "domain_id_invalid"
#define REASON_RSA_VERIFY_ERROR "rsa_verify_error"
#define REASON_AUTH_PARAM_ERROR "auth_param_error"
#define REASON_AUTH_PARAM_ERROR_DOMAIN_ID_NOT_FOUND REASON_AUTH_PARAM_ERROR "_domain_id_not_found"
#define REASON_AUTH_PARAM_ERROR_AUTH_EXPIRE_SEC_NOT_FOUND REASON_AUTH_PARAM_ERROR "_auth_expire_sec_not_found"
#define REASON_AUTH_PARAM_ERROR_AUTH_EXPIRE_SEC_INVALID REASON_AUTH_PARAM_ERROR "_auth_expire_sec_invalid"
#define REASON_AUTH_PARAM_ERROR_AUTH_SMS_AVAILABLE_NOT_FOUND REASON_AUTH_PARAM_ERROR "_auth_sms_available_not_found"
#define REASON_AUTH_PARAM_ERROR_AUTH_SMS_INVALID REASON_AUTH_PARAM_ERROR "_auth_sms_invalid"
#define REASON_OPEN_FILE_ERROR "open_file_error"

#endif
