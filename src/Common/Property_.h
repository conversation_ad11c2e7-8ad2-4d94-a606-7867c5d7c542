﻿#include "Common/Property.h"

#ifndef __Property__h__
#define __Property__h__

#define APPCFG_DOMAIN_NAME                  "DomainName"
#define APPCFG_DOMAIN_ID                    "DomainId"
#define APPCFG_APP_ID                       "AppId"
#define APPCFG_APP_KEY                      "AppKey"

#define AUTH_SERVER_USERNAME                "UserName"

// 以下5项未用
#define APPCFG_USER_ENTRY                   "UserEntry"
#define APPCFG_STATISTICS_SERVICE           "StatisticsService"
#define APPCFG_LOG_SERVICE                  "AchivedLogService"
#define APPCFG_MDM_SERVICE                  "MdmService"
#define APPCFG_VERSION_SERVICE              "VersionCheckService"

#define NOTIFY_PARAMS_PREFIX                "Notify."
#define DEVICE_INFO_PREFIX                  "DeviceInfo."
#define APPCFG_CLIENT_PREFIX                "Client."
#define APPCFG_CLIENT_CATEGORY_PREFIX       "Category."

#define CALL_PARAMS_MAIN_ACCOUNT            "mainAccount"
#define CALL_PARAMS_WAIT_TIMEOUT            "IncomingCallWaitTimeout"

#define CLIENT_PROP_DEVICE_ID               "DeviceId"
#define CLIENT_PROP_DEVICE_MODEL            "DeviceModel"
#define CLIENT_PROP_FORCE_FLAG              "ForceFlag"


// absolete /
#define DOMAIN_PROP_CLOUD_ID                "CloudId"
#define DOMAIN_PROP_USER_DB                 DOMAIN_PROP_SERVERS_PREFIX "UserDb"
////////////

#define DOMAIN_PROP_SERVERS_PREFIX          "Servers."
#define DOMAIN_PROP_AUTH_SERVER             "Servers.AuthServer"
#define DOMAIN_PROP_AUTH_SERVER_EXPIRE_SEC  "Servers.AuthServer.ExpireSec"
#define DOMAIN_PROP_AUTH_SERVER_ID          "Servers.AuthServer.Id"
#define DOMAIN_PROP_AUTH_SERVER_PWD         "Servers.AuthServer.Pwd"
#define DOMAIN_PROP_CALL_NOTIFY_SERVER      "Servers.CallNotifyServer"
#define DOMAIN_PROP_CALL_NOTIFY_SERVER_ID   "Servers.CallNotifyServer.Id"
#define DOMAIN_PROP_CALL_NOTIFY_SERVER_PWD  "Servers.CallNotifyServer.Pwd"

#define DOMAIN_PROP_RESTFUL_PREFIX          "RESTfulServer."
#define DOMAIN_PROP_RESTFUL_ID              "RESTfulServer.Id"
#define DOMAIN_PROP_RESTFUL_PWD             "RESTfulServer.Pwd"

#define DOMAIN_PROP_AUTH_PREFIX             "Auth."
#define DOMAIN_PROP_AUTH_EXPIRE_S           DOMAIN_PROP_AUTH_PREFIX "ExpireSecs"
#define DOMAIN_PROP_AUTH_MAX_ERR_CNT        DOMAIN_PROP_AUTH_PREFIX "MaxErrCnt"
#define DOMAIN_PROP_AUTH_SMS_AVAILABLE      DOMAIN_PROP_AUTH_PREFIX "SmsAvailable"
#define DOMAIN_PROP_AUTH_WHITELIST          DOMAIN_PROP_AUTH_PREFIX "Whitelist"
#define DOMAIN_PROP_AUTH_RSA_PUBKEY         DOMAIN_PROP_AUTH_PREFIX "RSAPubKey"
#define DOMAIN_PROP_AUTH_MODE               APPCFG_CLIENT_PREFIX "AuthMode" // directAuth/rsaAuth, however, key breaks the law.

#define DOMAIN_PROP_STAT_PREFIX             "Statistics."
#define DOMAIN_PROP_STAT_MYSQL              DOMAIN_PROP_STAT_PREFIX "Mysql"
#define DOMAIN_PROP_STAT_ALIYUN             DOMAIN_PROP_STAT_PREFIX "Aliyun"

#define DOMAIN_PROP_MSG_PREFIX              "Msg."
#define DOMAIN_PROP_MSG_P2P_EXPIRATION_SEC  DOMAIN_PROP_MSG_PREFIX "P2PExpirationSec"
#define DOMAIN_PROP_MSG_ORG_EXPIRATION_SEC  DOMAIN_PROP_MSG_PREFIX "OrgExpirationSec"

#define DOMAIN_PROP_PREFER_DC               "PreferDc"
#define APPCFG_APPSTATUS                    "AppStatus"                     // 0/1
#define APPCFG_APPSECRET                    "AppSecrets"

// index key
#define USER_PROP_BASIC_PREFIX              "Basic."
#define USER_PROP_REAL_NAME                 USER_PROP_BASIC_PREFIX "RealName"
#define USER_PROP_NICK_NAME                 USER_PROP_BASIC_PREFIX "NickName"
#define USER_PROP_BIRTHDAY                  USER_PROP_BASIC_PREFIX "Birthday"
#define USER_PROP_BIRTH_COUNTRY             USER_PROP_BASIC_PREFIX "BirthCountry"
#define USER_PROP_BIRTH_CITY                USER_PROP_BASIC_PREFIX "BirthCity"
#define USER_PROP_COUNTRY                   USER_PROP_BASIC_PREFIX "Country"
#define USER_PROP_CITY                      USER_PROP_BASIC_PREFIX "City"
#define USER_PROP_COMPANY                   USER_PROP_BASIC_PREFIX "Company"
#define USER_PROP_SCHOOL                    USER_PROP_BASIC_PREFIX "School"
#define USER_PROP_LANG                      USER_PROP_BASIC_PREFIX "Lang"
// unindex key
#define USER_PROP_GENDER                    USER_PROP_BASIC_PREFIX "Gender"
#define USER_PROP_SEXUAL_ORIENTATION        USER_PROP_BASIC_PREFIX "SexualOrientation"
#define USER_PROP_RELATIONSHIP_STATUS       USER_PROP_BASIC_PREFIX "RelationshipStatus"
#define USER_PROP_BLOOD_TYPE                USER_PROP_BASIC_PREFIX "BloodType"
#define USER_PROP_CONSTELLATION             USER_PROP_BASIC_PREFIX "Constellation"
#define USER_PROP_ADDR                      USER_PROP_BASIC_PREFIX "Addr"
#define USER_PROP_OCCUPATION                USER_PROP_BASIC_PREFIX "Occupation"
#define USER_PROP_STATE_MSG                 USER_PROP_BASIC_PREFIX "StateMsg"

#define USER_PROP_PORTRAIT                  "Portrait"

#define USER_PROP_SYSSETTING_PREFIX         "SysSetting."
#define USER_PROP_BAN_END_TIME              USER_PROP_SYSSETTING_PREFIX "BanEndTime"
#define USER_PROP_DEL_EFFECTED_TIME         USER_PROP_SYSSETTING_PREFIX "DelEffectedTime"

#define SERVER_PUBLIC_PATH                  "CubePath"

#endif 
