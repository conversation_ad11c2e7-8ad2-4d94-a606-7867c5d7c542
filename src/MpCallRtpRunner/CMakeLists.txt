aux_source_directory(. MPCALL_RTPRUNNER)
add_executable(MpCallRtpRunner
    ${WARP_GLIBC_SRC}
    ${MPCALL_RTPRUNNER}
    ${WARP_GLIBC_SRC}
)

target_include_directories(MpCallRtpRunner PRIVATE
    ${DEFAULT_INCLUDE_DIRECTORIES}
    ${PROJECT_SOURCE_DIR}/3rd
)
target_compile_definitions(MpCallRtpRunner PRIVATE
    ${DEFAULT_COMPILE_DEFINITIONS}
)

target_link_libraries(MpCallRtpRunner EventCollector EventTessar DataCollection MpCallSipRpc MpCallRpc ServiceRunner ServiceRunnerRpc Util JsmClient ServiceUtil JsmInterface JsmAdapter SipAdapter JsmError JsmLog
    ${DEFAULT_JSM_LIBRARIES}
    ${DEFAULT_SYS_LIBRARIES}
)
target_link_options(MpCallRtpRunner PRIVATE
    ${DEFAULT_LINK_OPTIONS}
)
