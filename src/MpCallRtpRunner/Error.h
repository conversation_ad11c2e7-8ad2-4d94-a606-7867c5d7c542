//
// *****************************************************************************
// Copyright (c) 2023 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//
// Created on 2023/5/29 by <PERSON>
//

#pragma once

#include "Common/Error.h"

namespace SipMpCall
{

namespace Error
{
    static const char *const DomainCode = "M32";

    ERROR_DECLARE_F(7320, GenOfferFailed);
    ERROR_DECLARE_F(7321, CreateClientFailed);
    ERROR_DECLARE_F(7322, JoinRoomFailed);
    ERROR_DECLARE_F(7323, NoOfferSdp);
    ERROR_DECLARE_F(7324, <PERSON>AnswerFailed);
    ERROR_DECLARE_F(7325, NegotiateFailed);
    ERROR_DECLARE_F(7326, StartMediaBrigdeFailed);
    ERROR_DECLARE_F(7327, NoValidRoomConfig);
    ERROR_DECLARE_F(7328, InvalidState);
} // namespace Error

} // namespace SipMpCall