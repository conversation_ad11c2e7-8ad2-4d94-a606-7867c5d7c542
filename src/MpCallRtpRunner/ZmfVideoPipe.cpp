//
// *****************************************************************************
// Copyright (c) 2023 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//
// Created on 2023/5/31 by <PERSON>
//

#include "ZmfVideoPipe.h"

namespace SipMpCall
{

static int to_int(const std::string &str, int dflt = 0)
{
    if (str.empty())
        return dflt;

    size_t pos = 0;
    int value;
    int base = 10;
    if (str.size() > 2 && str.substr(0, 2) == "0x")
        base = 16;

    try
    {
        value = std::stoi(str, &pos, base);
        if (pos == str.size())
            return value;
    }
    catch (...)
    {
    }

    return dflt;
}

int ZmfVideoPipe::RenderCallback(void *pUser, const char *renderId, int sourceType, int iAngle, int iMirror, int *iWidth, int *iHeight, unsigned char *buf, unsigned long timeStamp)
{
    if (!renderId || !iWidth || !iHeight || !buf)
        return 0;

    ZmfVideoPipe *pipe = static_cast<ZmfVideoPipe *>(pUser);
    if (pipe)
        pipe->pipe(renderId, sourceType, iAngle, iMirror, iWidth, iHeight, buf, timeStamp);
    return 0;
}

bool ZmfVideoPipe::start()
{
    if (_started)
        return true;

    if (_zmf->VideoRenderAddCallback(this, (ZmfVideoRenderCallback)ZmfVideoPipe::RenderCallback))
    {
        UTIL_LOG_WRN("ZmfVideoPipe", "content:add render callback failed, renderId:" + _renderId + " captureId:" + _captureId);
        return false;
    }

    UTIL_LOG_IFO("ZmfVideoPipe", "content:add render callback, renderId:" + _renderId + " captureId:" + _captureId);
    __incRefCnt();
    _started = true;
    return true;
}

void ZmfVideoPipe::stop()
{
    if (!_started)
        return;
    _zmf->VideoRenderRemoveCallback(this);
    __decRefCnt();
    _started = false;
    _lastWidth = 0;
    _lastHeight = 0;
}

void ZmfVideoPipe::pipe(const char *renderId, int sourceType, int iAngle, int iMirror, int *iWidth, int *iHeight, unsigned char *buf, unsigned long timeStamp)
{
    if (!_started || _renderId != renderId)
        return;

    if (iAngle != 0)
    {
        UTIL_LOG_ERR("ZmfVideoPipe", "content:pipe ignore rotate render:" + _renderId + " " + Common::String(*iWidth) + "x" + Common::String(*iHeight) + " " + Common::String(iAngle) + " " + Common::String(iMirror));
        return;
    }

    bool log = false;
    if ((_lastWidth != (*iWidth)) || (_lastHeight != (*iHeight)) || (_lastAngle != iAngle) || (_lastMirror != iMirror))
    {
        _lastWidth = *iWidth;
        _lastHeight = *iHeight;
        _lastAngle = iAngle;
        _lastMirror = iMirror;
        log = true;
    }

    if (log)
        UTIL_LOG_IFO("ZmfVideoPipe", "content:pipe render:" + _renderId + " " + Common::String(_lastWidth) + "x" + Common::String(_lastHeight) + " " + Common::String(_lastAngle) + " " + Common::String(_lastMirror));
    _zmf->OnVideoCapture(_captureId.c_str(), 0, iAngle, 0, iWidth, iHeight, buf, nullptr);
}

void ZmfBridgeVideoPipe::update(std::set<JsmClient::MemberPtr> members)
{
    for (auto it = members.begin(); it != members.end();)
    {
        if ((((*it)->State() & 0x05) == 0x04) && (((*it)->Role() & 0x02) == 0x02))
            ++it;
        else
        {
            UTIL_LOG_IFO("ZmfVideoPipe", "content:skip member:" + Common::String((*it)->Name().c_str()) + " state:" + Common::String((*it)->State()) + " role:" + Common::String((*it)->Role()));
            members.erase(it++);
        }
    }

    if (members.empty())
    {
        UTIL_LOG_DBG("ZmfVideoPipe", "content:pipe update render:" + _renderId + " no suitable member");
        return;
    }

    std::string newRenderId;
    for (auto match : _prioritys)
    {
        for (auto &member : members)
        {
            if ((member->Role() & match.mask) == match.value)
            {
                UTIL_LOG_IFO("ZmfVideoPipe", "content:pipe update select render:" + _renderId + " select " + member->Name().c_str() + " by role:" + Common::String(member->Role()) + " " + Common::String(match.value) + "/" + Common::String(match.mask));
                newRenderId = member->Name();
                break;
            }
        }
        if (!newRenderId.empty())
            break;
    }
    if (newRenderId.empty())
    {
        UTIL_LOG_IFO("ZmfVideoPipe", "content:pipe update render:" + _renderId + " select default " + (*members.begin())->Name().c_str());
        newRenderId = (*members.begin())->Name();
    }

    update(newRenderId);
}

void ZmfBridgeVideoPipe::update(const std::string &renderId)
{
    _mutex.lock();
    if (_currentRenderId != renderId)
    {
        UTIL_LOG_IFO("ZmfVideoPipe", "content:pipe update render:" + _renderId + " change " + _currentRenderId.c_str() + "->" + renderId.c_str());
        _currentRenderId = renderId;
        _lastWidth = 0;
        _lastHeight = 0;
        _lastAngle = 0;
        _lastMirror = 0;
    }
    _mutex.unlock();
}

void ZmfBridgeVideoPipe::pipe(const char *renderId, int sourceType, int iAngle, int iMirror, int *iWidth, int *iHeight, unsigned char *buf, unsigned long timeStamp)
{
    if (!_started)
        return;

    _mutex.lock();
    if (_currentRenderId != renderId)
    {
        _mutex.unlock();
        return;
    }
    std::string currentRenderId = _currentRenderId;

    bool log = false;
    if ((_lastWidth != (*iWidth)) || (_lastHeight != (*iHeight)) || (_lastAngle != iAngle) || (_lastMirror != iMirror))
    {
        _lastWidth = *iWidth;
        _lastHeight = *iHeight;
        _lastAngle = iAngle;
        _lastMirror = iMirror;
        log = true;
    }
    _mutex.unlock();

    if (iAngle == 0)
    {
        if (log)
            UTIL_LOG_IFO("ZmfVideoPipe", "content:pipe render:" + _renderId + " " + currentRenderId.c_str() + " " + Common::String(_lastWidth) + "x" + Common::String(_lastHeight) + " " + Common::String(_lastAngle) + " " + Common::String(_lastMirror));
        _zmf->OnVideoCapture(_captureId.c_str(), 0, iAngle, 0, iWidth, iHeight, buf, nullptr);
        return;
    }

    int bufsize = (*iWidth) * (*iHeight) * 3 / 2;
    if (bufsize > _rotateBufSize)
    {
        if (_rotateBuf)
            free(_rotateBuf);
        _rotateBuf = malloc(bufsize);
        _rotateBufSize = bufsize;
    }

    int dstWidth = *iWidth, dstHeight = *iHeight;
    if (_zmf->ConvertToI420(_rotateBuf, 1, buf, bufsize, *iWidth, *iHeight, 0, 0, &dstWidth, &dstHeight, iAngle) == 0)
    {
        if (log)
            UTIL_LOG_IFO("ZmfVideoPipe", "content:pipe render:" + _renderId + " " + currentRenderId.c_str() + " " + Common::String(_lastWidth) + "x" + Common::String(_lastHeight) + " " + Common::String(_lastAngle) + " " + Common::String(_lastMirror) + " -> " + Common::String(dstWidth) + "x" + Common::String(dstHeight));
        _zmf->OnVideoCapture(_captureId.c_str(), 0, 0, 0, &dstWidth, &dstHeight, (unsigned char *)_rotateBuf, nullptr);
    }
}

void ZmfBridgeVideoPipe::parsePriorityConfig(const Common::String &priorityConfig)
{
    vector<Common::String> configs;
    priorityConfig.split(configs, ",");

    Common::String info;
    for (auto match : configs)
    {
        ZmfVideoPipeMatchRule rule;
        if (parseMatch(match, rule))
        {
            _prioritys.push_back(rule);
            info += Common::String(rule.value) + "/" + Common::String(rule.mask) + " ";
        }
    }
    UTIL_LOG_IFO("ZmfVideoPipe", "content:parse priority config:" + priorityConfig + " " + info);
}

bool ZmfBridgeVideoPipe::parseMatch(Common::String match, ZmfVideoPipeMatchRule &rule)
{
    int pos = match.find('/');
    if (pos < 0)
    {
        rule.value = to_int(match.trim().c_str(), -1);
        rule.mask = 0x7fffffff;
    }
    else if (pos > 0 && pos < match.size() - 1)
    {
        rule.value = to_int(match.substr(0, pos).trim().c_str(), -1);
        rule.mask = to_int(match.substr(pos + 1).trim().c_str(), -1);
    }
    else
        return false;

    if (rule.value < 0 || rule.mask < 0)
        return false;

    return true;
}

} // namespace SipMpCall