//
// *****************************************************************************
// Copyright (c) 2023 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//
// Created on 2023/5/31 by <PERSON>
//

#pragma once

#include "Common/Util.h"
#include "JsmAdapter/ZmfInterface.h"
#include "JsmClient/JsmClient.h"
#include <mutex>

namespace SipMpCall
{

class ZmfVideoPipe : virtual public Common::Shared
{
public:
    ZmfVideoPipe(const char *renderId, const char *captureId, JsmClient::ZmfInterfacePtr &zmf)
        : _renderId(renderId)
        , _captureId(captureId)
        , _zmf(zmf)
        , _started(false)
        , _lastWidth(0)
        , _lastHeight(0)
        , _lastAngle(0)
        , _lastMirror(-1)
    {
    }

    virtual ~ZmfVideoPipe()
    {
    }

    static int RenderCallback(void *pUser, const char *renderId, int sourceType, int iAngle, int iMirror, int *iWidth, int *iHeight, unsigned char *buf, unsigned long timeStamp);

    Common::String renderId() const { return _renderId; }
    Common::String captureId() const { return _captureId; }

    virtual bool start();
    virtual void stop();
    virtual void update(std::set<JsmClient::MemberPtr> members) {}
    virtual void update(const std::string &renderId) {}
    virtual void pipe(const char *renderId, int sourceType, int iAngle, int iMirror, int *iWidth, int *iHeight, unsigned char *buf, unsigned long timeStamp);

protected:
    JsmClient::ZmfInterfacePtr _zmf;
    Common::String _renderId;
    Common::String _captureId;
    bool _started;
    int _lastWidth;
    int _lastHeight;
    int _lastAngle;
    int _lastMirror;
};

typedef Common::Handle<ZmfVideoPipe> ZmfVideoPipePtr;

struct ZmfVideoPipeMatchRule
{
    ZmfVideoPipeMatchRule()
        : mask(0)
        , value(0)
    {
    }

    int mask;
    int value;
};

class ZmfBridgeVideoPipe : public ZmfVideoPipe
{
public:
    ZmfBridgeVideoPipe(const char *renderId, const char *captureId, JsmClient::ZmfInterfacePtr &zmf, const Common::String &priorityConfig)
        : ZmfVideoPipe(renderId, captureId, zmf)
        , _rotateBuf(nullptr)
        , _rotateBufSize(0)
    {
        parsePriorityConfig(priorityConfig);
    }

    ~ZmfBridgeVideoPipe()
    {
        if (_rotateBuf)
        {
            free(_rotateBuf);
            _rotateBuf = nullptr;
        }
    }

    virtual void update(std::set<JsmClient::MemberPtr> members) override;
    virtual void update(const std::string &renderId) override;
    virtual void pipe(const char *renderId, int sourceType, int iAngle, int iMirror, int *iWidth, int *iHeight, unsigned char *buf, unsigned long timeStamp) override;

private:
    void parsePriorityConfig(const Common::String &priorityConfig);
    bool parseMatch(Common::String match, ZmfVideoPipeMatchRule &rule);

protected:
    void *_rotateBuf;
    int _rotateBufSize;
    vector<ZmfVideoPipeMatchRule> _prioritys;

    std::mutex _mutex;
    std::string _currentRenderId;
};

typedef Common::Handle<ZmfBridgeVideoPipe> ZmfBridgeVideoPipePtr;

} // namespace SipMpCall