//
// *****************************************************************************
// Copyright (c) 2023 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//
// Created on 2023/5/30 by <PERSON>
//

#pragma once

#include <fstream>
#include "EventCollector/EventCollector.h"
#include "EventCollector/EventTessar/EventTessar.h"
#include "MpCall/MpCallSessionAgent.h"
#include "MpCallSip/MpCallRtpPub.h"
#include "SipAdapter/SipRtpInterface.h"
#include "JsmAdapter/ZmfInterface.h"
#include "JsmAdapter/JsmInterface.h"
#include "ServiceRunner/Runner.h"
#include "ServiceRunner/RunnerMain.h"
#include "ServiceRunner/ServiceRunnerServer.h"
#include "MpCallSip/MpCallRtpAgent.h"
#include "MpCallSip/MpCallRtpServer.h"
#include "ZmfVideoPipe.h"
#include "JsmClient/JsmClient.h"
#include "Util/PortHolder.h"

namespace SipMpCall
{

struct RtpRunnerConfig
{
    Common::ApplicationPtr app;
    ServiceRunner::RunnerPtr runner;
    JsmClient::ClientPtr client;
    SipClient::SipRtpInterfacePtr rtpClient;
    JsmClient::JsmInterfacePtr jsm;
    JsmClient::ZmfInterfacePtr zmf;
    JsmClient::EventCollectorPtr _collector;
};

class MpCallRtpRunner : public Common::AppScheduler, public JsmClient::RoomListener, public SipClient::SipRtpListener, public ServiceRunner::RunnerServer, public ServiceRunner::RunnerListener, public MpCallSip::RtpRunnerServer
{
public:
    explicit MpCallRtpRunner(const Common::ApplicationPtr &application);
    explicit MpCallRtpRunner(const RtpRunnerConfig &config);

    bool __ex(const Common::ServerCallPtr &__call, const Common::String &__cmd, const Common::IputStreamPtr &__iput) override
    {
        if (ServiceRunner::RunnerServer::__ex(__call, __cmd, __iput))
            return true;
        if (MpCallSip::RtpRunnerServer::__ex(__call, __cmd, __iput))
            return true;
        return false;
    }

    // implement Common::AppScheduler
    bool onActivate() override;
    void onDeactivate() override;
    void onShutdown() override;
    void onSchd() override;
    void onUpdateConfigs() override;

    // implement JsmClient::RoomListener
    void onError(const JsmAdapter::EventError &error) override;
    void onLeaved(bool kicked) override;
    void onScreenSharingChanged(bool sharing, const std::string &uri, const JsmClient::MemberPtr &member) override;
    void onDataChanged() override {}
    void onMemberJoined(const JsmClient::MemberPtr &member) override;
    void onMemberLeaved(const JsmClient::MemberPtr &member) override;
    void onMemberChanged(const JsmClient::MemberPtr &member) override;
    void onDtmf(const JsmClient::MemberPtr &from, enum JsmClient::DtmfType dtmfType) override;
    void onNetworkStatusChanged(const JsmClient::MemberPtr &member, int level) override;
    void onMessageReceived(const std::string &from, const std::string &message) override {}
    bool onCaptured(void *cookie, const std::string &filename, bool ended) override { return false; }
    void onRecorded(void *cookie, const std::string &filename, bool ended) override {}

    // implement SipClient::SipRtpListener
    void onDtmfEvent(SipClient::DtmfEvent event) override;

    // implement ServiceRunner::RunnerServer
    bool check(const Common::ServerCallPtr &__call, const Common::String &managerOid, const Common::String &action, Common::String &status, int &freePercent) override;

    // implement ServiceRunner::RunnerListener
    void onRunnerReady() override;
    void onRunnerTermed(const Common::String &reason) override;

    // implement MpCallSip::RtpRunnerServer
    void callout_begin(const Common::ServerCallPtr &__call, const Common::String &targetOid, const Common::String &monitorOid, const MpCallSip::RtpRunnerConfig &config, const MpCallSip::SdpParams &sdpParams) override;
    void callin_begin(const Common::ServerCallPtr &__call, const Common::String &targetOid, const Common::String &monitorOid, const MpCallSip::RtpRunnerConfig &config, const Common::String &offerSdp) override;
    void callinAndOffer_begin(const Common::ServerCallPtr &__call, const Common::String &targetOid, const Common::String &monitorOid, const MpCallSip::RtpRunnerConfig &config, const MpCallSip::SdpParams &sdpParams) override;
    void callinAndAnswer_begin(const Common::ServerCallPtr &__call, const Common::String &targetOid, const Common::String &monitorOid, const MpCallSip::RtpRunnerConfig &config, const Common::String &offerSdp, const MpCallSip::SdpParams &sdpParams) override;
    bool close(const Common::ServerCallPtr &__call) override;
    bool genOffer(const Common::ServerCallPtr &__call, const MpCallSip::SdpParams &sdpParams, Common::String &offerSdp) override;
    bool setOffer(const Common::ServerCallPtr &__call, const Common::String &offerSdp) override;
    bool setOfferAndAnswer(const Common::ServerCallPtr &__call, const Common::String &offerSdp, const MpCallSip::SdpParams &sdpParams, Common::String &answerSdp) override;
    bool genAnswer(const Common::ServerCallPtr &__call, const MpCallSip::SdpParams &sdpParams, Common::String &answerSdp) override;
    bool setAnswer(const Common::ServerCallPtr &__call, const Common::String &answerSdp) override;
    bool setTalking(const Common::ServerCallPtr &__call, bool talking) override;
    bool getRtpStatus(const Common::ServerCallPtr &__call, MpCallSip::RtpStats &stats) override;

private:
    void setWorking();
    void setError(const Common::String &reason, const char *file, int line);

    static void initRtpConig(SipClient::SipRtpConfig &config);
    void readBandwidthConfig(SipClient::SipRtpConfig::BandwidthConfig &bandwidthConfig, const char *key);
    void readRtpConfig(SipClient::SipRtpConfig &config);
    static void saveRtpConfig(std::ofstream &ofs, const SipClient::SipRtpConfig &config);

    static void initRoomConfig(JsmClient::Config &config);
    void readRoomConfig(JsmClient::Config &config);
    static void saveRoomConfig(std::ofstream &ofs, const JsmClient::Config &config);
    void updateBackupBasename(const MpCallSip::RtpRunnerConfig &config);

    void dumpConfig();
    bool checkConfig(const MpCallSip::RtpRunnerConfig &config);
    bool joinRoom(const MpCallSip::RtpRunnerConfig &config);
    void collectJoinRoomFailed(const MpCallSip::RtpRunnerConfig &config, int duration);
    void collectJoinRommSucceed(const MpCallSip::RtpRunnerConfig &config, const std::string &roomId, int duration);
    void collectLeaveRoom();

    bool startAudioBridge();
    void stopAudioBridge();
    bool startVideoBridge();
    void stopVideoBridge();
    bool updateVideoPump();

    void filterAudio(const JsmClient::MemberPtr &member);
    void filterVideo(const JsmClient::MemberPtr &member);
    static bool matchRole(int mmeberRole, int matchMask, int matchRole);

    bool isBindingMode() const { return !_bindingUser.empty(); }
    bool isBindingUser(const JsmClient::MemberPtr &member) const { return member->Name().c_str() == _bindingUser; }

    void notifyNetStatusChanged();

private:
    Common::RecMutex _mutex;
    Common::ApplicationPtr _app;
    Common::AdapterPtr _adapter;
    Common::String _objectId;
    Common::String _serverName;
    ServiceRunner::RunnerPtr _runner;
    PortHolderPtr _audioPortHolder;
    PortHolderPtr _videoPortHolder;

    bool _error;
    bool _closed;
    Common::String _offerSdp;

    JsmClient::ClientPtr _client;
    JsmClient::RoomPtr _room;
    SipClient::SipRtpInterfacePtr _rtpClient;
    JsmClient::JsmInterfacePtr _jsm;
    JsmClient::ZmfInterfacePtr _zmf;

    MpCallSip::RtpRunnerMonitorAgent _monitorAgent;
    MpCall::SessionEndpointAgent _mpcallAgent;
    SipClient::SipRtpConfig _rtpConfig;
    JsmClient::Config _roomConfig;

    int _audioExcludeRoleMask;
    int _audioExcludeRole;
    bool _audioMute;

    int _videoExcludeRoleMask;
    int _videoExcludeRole;
    bool _videoMute;

    Common::String _bindingUser;

    unsigned int _lastGetNetStatusTicks;
    Common::String _rtpNetworkStatus;
    bool _onCallAnswered;
    Common::StrIntMap _jsmNetworkStatus;

    ZmfVideoPipePtr _jsm2RtpVideoPipe;

    JsmClient::EventCollectorPtr _collector;
    JsmClient::TessarEventPtr _tessarEvent;
};

} // namespace SipMpCall