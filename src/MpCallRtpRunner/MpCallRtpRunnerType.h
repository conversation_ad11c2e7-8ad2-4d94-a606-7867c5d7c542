//
// *****************************************************************************
// Copyright (c) 2023 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//
// Created on 2023/10/13 by <PERSON>
//

#pragma once

#include "Common/Common.h"

namespace SipMpCall
{

inline Common::String RtpRunnerType(const Common::ApplicationPtr &app)
{
    if (app->getPrefixName().find("Acd") >= 0)
        return "AcdRtpRunner";

    return "MpCallRtpRunner";
}

} // namespace SipMpCall
