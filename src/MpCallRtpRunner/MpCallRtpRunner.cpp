//
// *****************************************************************************
// Copyright (c) 2023 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//
// Created on 2023/5/30 by <PERSON>
//

#include <cstdlib>
#include <string>
#include <thread>
#include <exception>
#include <fstream>
#include "Common/Common.h"
#include "Common/Util.h"
#include "EventCollector/EventCollector.h"
#include "MpCall/MpCallAgent.h"
#include "MpCall/MpCallEventPub.h"
#include "MpCall/MpCallPub.h"
#include "ServiceUtil/Struct2Json.h"
#include "MpCallRtpRunner.h"
#include "Error.h"
#include "MpCallRtpRunnerType.h"
#include "ZmfVideoPipe.h"
#include "JsmAdapter/ZmfInterfaceI.h"
#include "JsmAdapter/JsmInterfaceI.h"
#include "SipAdapter/StatsDecorder.h"

namespace SipMpCall
{

static const char *MpCallRtpRunnerRenderId = "MpCallRtpRunnerRenderPump";
static const char *MpCallRtpRunnerBridgeRenderId = "MpCallRtpRunnerBridgeRenderPump";
static const char *MpCallRtpRunnerVersion = "2.4.4";

static const int RolePstn = 0x400;
static const int RoleAgent = 0x40;

#ifndef GIT_HASH
#define GIT_HASH "local"
#endif

RUNNER_REGISTER_MAIN_SERVICE("MpCallRtpRunner", MpCallRtpRunnerVersion, GIT_HASH, __DATE__ "/" __TIME__, SipMpCall::MpCallRtpRunner);

static void log_callback(const char *file, int file_size, const char *func, int func_size, int line,
                         int level, const char *mod, const char *info)
{
    Common::log(file, file_size, func, func_size, line, level, mod, info);
}

static bool __hasVideo(const Common::String &sdp)
{
    int pos = sdp.find("\r\nm=video ");
    if (pos < 0)
        return false;

    int pos2 = sdp.find(' ', pos + 10);
    if (pos2 < 0)
        return false;

    try
    {
        int port = sdp.substr(pos + 10, pos2 - (pos + 10)).toInt(0);
        return port > 0;
    }
    catch (...)
    {
    }

    return false;
}

static int __stoi(const Common::String &str, int lower, int upper)
{
    int __i = str.toInt(-1);
    if (__i < lower)
        __i = lower;
    if (__i > upper)
        __i = upper;
    return __i;
}

static long long __stoll(const std::string &str, long long def)
{
    try
    {
        return std::stoll(str);
    }
    catch (...)
    {
        return def;
    }
}

static int __stoi(const std::string &str, int def)
{
    try
    {
        return std::stoi(str);
    }
    catch (...)
    {
        return def;
    }
}

static int __stoi(const std::string &str, int def, int scale)
{
    try
    {
        return std::stof(str) * scale;
    }
    catch (...)
    {
        return def;
    }
}

static float __stof(const std::string &str, float def)
{
    try
    {
        return std::stof(str);
    }
    catch (...)
    {
        return def;
    }
}

static int __getFps(const std::string &value)
{
    int pos = value.find('/');
    if (pos < 0)
        return 0;
    return __stoi(value.substr(0, pos), 0);
}

static int convertNetworkStatusToInt(const Common::String& status)
{
    if (status == "Unknown")
        return 0;
    else if (status == "Very Low")
        return 1;
    else if (status == "Low")
        return 2;
    else if (status == "Normal")
        return 3;
    else if (status == "Good")
        return 4;
    else if (status == "Very Good")
        return 5;
    return 0;
}

class MpCallRtpRunnerTesarInterface : public JsmClient::TessarInterface
{
public:
    explicit MpCallRtpRunnerTesarInterface(const SipClient::SipRtpInterfacePtr &rtpClient) : _rtpClient(rtpClient) {}
    virtual int TessarReg(int iExtUsrId, bool bVideo) override { return _rtpClient->TessarReg(iExtUsrId, bVideo); }
    virtual int TessarUnreg(int iExtUsrId, bool bVideo) override { return _rtpClient->TessarUnreg(iExtUsrId, bVideo); }
    virtual std::string GetTessarData(int iUserId, bool bVideo, int iLevel, int iType) override { return _rtpClient->GetTessarData(iUserId, bVideo, iLevel, iType); }

private:
    SipClient::SipRtpInterfacePtr _rtpClient;
};

MpCallRtpRunner::MpCallRtpRunner(const Common::ApplicationPtr &application)
    : _app(application)
    , _serverName(RtpRunnerType(application))
    , _error(false)
    , _closed(false)
    , _roomConfig("100645", "0", "rtprunner")
    , _audioExcludeRoleMask(0)
    , _audioExcludeRole(0)
    , _audioMute(false)
    , _videoExcludeRoleMask(0)
    , _videoExcludeRole(0)
    , _videoMute(false)
    , _lastGetNetStatusTicks(0)
    , _onCallAnswered(false)
{
    _roomConfig.Sender = true;
}

MpCallRtpRunner::MpCallRtpRunner(const RtpRunnerConfig &config)
    : _app(config.app)
    , _serverName(RtpRunnerType(config.app))
    , _error(false)
    , _closed(false)
    , _runner(config.runner)
    , _client(config.client)
    , _rtpClient(config.rtpClient)
    , _jsm(config.jsm)
    , _zmf(config.zmf)
    , _collector(config._collector)
    , _roomConfig("100645", "0", "rtprunner")
    , _audioExcludeRoleMask(0)
    , _audioExcludeRole(0)
    , _audioMute(false)
    , _videoExcludeRoleMask(0)
    , _videoExcludeRole(0)
    , _videoMute(false)
    , _lastGetNetStatusTicks(0)
    , _onCallAnswered(false)
{
    _roomConfig.Sender = true;
}

bool MpCallRtpRunner::onActivate()
{
    JsmClient::setLogCallback(log_callback);
    JsmClient::setLogLevel(JsmClient::LogDebug);
    _app->setConfig("global.Log.Verbose.AgentCall", "10");
    _app->setConfig("global.Log.Verbose.ServerCall", "10");

    readRtpConfig(_rtpConfig);
    readRoomConfig(_roomConfig);
    dumpConfig();

    _audioPortHolder = PortHolder::create(_app->getDriver(), _rtpConfig.audioPorts.lowerBound, _rtpConfig.audioPorts.upperBound - _rtpConfig.audioPorts.lowerBound + 1);
    if (!_audioPortHolder)
    {
        UTIL_LOG_ERR("RtpRunner", "content:create audio port holder failed.");
        return false;
    }

    _videoPortHolder = PortHolder::create(_app->getDriver(), _rtpConfig.videoPorts.lowerBound, _rtpConfig.videoPorts.upperBound - _rtpConfig.videoPorts.lowerBound + 1);
    if (!_videoPortHolder)
    {
        UTIL_LOG_ERR("RtpRunner", "content:create video port holder failed.");
        return false;
    }

    Common::String locatorOid = "MpCallLocator";
    if (_app->getPrefixName().find("Acd") >= 0)
        locatorOid = "AcdCallLocator";

    Common::String value;
    if (!_app->getAppConfig("Locators.TpSipAgent", value))
        _app->setConfig("global.Locators.TpSipAgent", locatorOid);
    if (!_app->getAppConfig("Locators.MpCall", value))
        _app->setConfig("global.Locators.MpCall", locatorOid);

    if (!_app->getAppConfig("Main.Endpoints", value))
        _app->setConfig("global.Main.Endpoints", "svarc;");
    _adapter = _app->createAdapter("Main", true);
    if (!_adapter)
    {
        UTIL_LOG_ERR("RtpRunner", "content:create adapter failed.");
        return false;
    }
    _adapter->activate();
    _adapter->addServer(_serverName, this, false);

    if (!_runner)
    {
        Common::String serverType, coreNetId;

        //add CoreNetId
        serverType = _serverName;
        if(_app->getAppConfig("SipCall.CoreNetId", coreNetId))
        {
            serverType += coreNetId;
             _app->setStatistics("SipCall.CoreNetId", coreNetId);
        }

        _runner = ServiceRunner::Runner::create(this, _app, serverType, _adapter);
        if (!_runner)
        {
            UTIL_LOG_ERR("RtpRunner", "content:create service runner failed.");
            return false;
        }
    }

    if (!_zmf)
    {
        _zmf = new JsmClient::ZmfInterfaceI();
        if (!_zmf)
        {
            UTIL_LOG_ERR("RtpRunner", "content:create zmf interface failed.");
            return false;
        }
    }

    if (!_jsm)
    {
        _jsm = new JsmClient::JsmInterfaceI();
        if (!_jsm)
        {
            UTIL_LOG_ERR("RtpRunner", "content:create jsm interface failed.");
            return false;
        }
    }

    if (!_rtpClient)
    {
        _rtpClient = SipClient::SipRtpInterface::create(_rtpConfig, this);
        if (!_rtpClient)
        {
            UTIL_LOG_ERR("RtpRunner", "content:create rtp client failed.");
            return false;
        }
    }

    if (!_collector)
    {
        _collector = JsmClient::EventCollector::create(_app);
        if (!_collector)
        {
            UTIL_LOG_ERR("RtpRunner", "content:create event collector failed.");
            return false;
        }
    }

    return true;
}

void MpCallRtpRunner::onDeactivate()
{
    _audioPortHolder = nullptr;
    _videoPortHolder = nullptr;

    if (_runner)
    {
        _runner->close();
        _runner = 0;
    }

    if (_jsm2RtpVideoPipe)
    {
        stopAudioBridge();
        stopVideoBridge();
        _jsm2RtpVideoPipe = 0;
    }

    if (_room)
    {
        collectLeaveRoom();
        _room->close();
        _room = 0;
    }

    if (_rtpClient)
    {
        _rtpClient->SdkRelease();
        _rtpClient = 0;
    }

    if (_client)
    {
        _client->close();
        _client = 0;
    }

    if (_adapter)
    {
        _adapter->removeServer(_serverName);
        _adapter = 0;
    }
}

void MpCallRtpRunner::onShutdown()
{
    _audioPortHolder = nullptr;
    _videoPortHolder = nullptr;

    if (_runner)
    {
        _runner->close();
        _runner = 0;
    }

    if (_room)
    {
        collectLeaveRoom();
        _room->close();
        _room = 0;
    }

    if (_rtpClient)
    {
        _rtpClient->SdkRelease();
        _rtpClient = 0;
    }

    if (_client)
    {
        _client->close();
        _client = 0;
    }

    if (_adapter)
    {
        _adapter->removeServer(_serverName);
        _adapter = 0;
    }
}

void MpCallRtpRunner::onSchd()
{
    if (_error && !_closed)
    {
        _closed = true;
        _onCallAnswered = false;

        JsmClient::RoomPtr room = _room;
        std::thread td([=]() {
            if (room)
                room->close();
            if (_app)
                _app->indShutdown();
        });
        td.detach();
        _room = 0;
    }

    if (_collector)
        _collector->onSchd();

    notifyNetStatusChanged();
}

void MpCallRtpRunner::onUpdateConfigs()
{
    if (_objectId.empty())
    {
        _objectId = _adapter->getObjectId(_serverName);
        if (!_objectId.empty() && !_runner->init(_objectId, Common::StrStrMap()))
            _app->indShutdown();
    }

    if (_runner)
        _runner->updateConfigs();
}

void MpCallRtpRunner::onError(const JsmAdapter::EventError &error)
{
    MpCallSip::RtpRunnerMonitorAgent monitorAgent = _monitorAgent;
    if (monitorAgent)
        monitorAgent.onError_begin(0, "RoomError:" + error.JsmReason);
    setError("JsmError:" + error.JsmReason, __FILE__, __LINE__);
    collectLeaveRoom();
}

void MpCallRtpRunner::onLeaved(bool kicked)
{
    MpCallSip::RtpRunnerMonitorAgent monitorAgent = _monitorAgent;
    if (kicked && monitorAgent)
        monitorAgent.onError_begin(0, "RoomKicked");
    setError("JsmLeaved:" + Common::String(kicked), __FILE__, __LINE__);
    collectLeaveRoom();
}

void MpCallRtpRunner::onScreenSharingChanged(bool sharing, const std::string &uri, const JsmClient::MemberPtr &member)
{
    if (sharing)
        _room->requestScreenSharing(3);
    updateVideoPump();
}

void MpCallRtpRunner::onMemberJoined(const JsmClient::MemberPtr &member)
{
    filterAudio(member);
    filterVideo(member);
    updateVideoPump();
}

void MpCallRtpRunner::onMemberLeaved(const JsmClient::MemberPtr &member)
{
    if (isBindingMode() && isBindingUser(member))
    {
        UTIL_LOG_IFO("RtpRunner", "content:member " + Common::String(member->Name().c_str()) + " is binding user, skip leave");
        setError("BindingUserLeave:" + Common::String(member->Name().c_str()), __FILE__, __LINE__);
        return;
    }

    updateVideoPump();
}

void MpCallRtpRunner::onMemberChanged(const JsmClient::MemberPtr &member)
{
    updateVideoPump();
}

void MpCallRtpRunner::onDtmf(const JsmClient::MemberPtr &from, enum JsmClient::DtmfType dtmfType)
{
    UTIL_LOG_IFO("RtpRunner", "content:dtmf event:" + Common::String((int)dtmfType) + " from " + (from ? from->Name().c_str() : "unkonwn"));
    SipClient::SipRtpInterfacePtr rtpClient;

    do
    {
        Common::RecLock lock(_mutex);

        if (!_rtpClient)
        {
            UTIL_LOG_WRN("RtpRunner", "content:dtmf event:" + Common::String((int)dtmfType) + " from jsm, no rtp client.");
            return;
        }

        rtpClient = _rtpClient;
    } while (0);

    if (!rtpClient->SendDtmf(dtmfType))
        UTIL_LOG_WRN("RtpRunner", "content:send dtmf event:" + Common::String((int)dtmfType) + " to rtp failed");
}

void MpCallRtpRunner::onNetworkStatusChanged(const JsmClient::MemberPtr &member, int level)
{
    Common::RecLock lock(_mutex);
    if (level == 0)
        _jsmNetworkStatus[member->Name().c_str()] = level;
}

void MpCallRtpRunner::onDtmfEvent(SipClient::DtmfEvent event)
{
    class Async : public Common::AgentAsync
    {
    public:
        virtual void cmdResult(int rslt, const Common::IputStreamPtr &iput, const Common::ObjectPtr &userdata)
        {
            Common::UserdataIntPtr event = Common::UserdataIntPtr::dynamicCast(userdata);

            if (!MpCall::SessionEndpointAgent::event_end(rslt, iput))
                UTIL_LOG_WRN("RtpRunner", "content:dtmf evnet:" + Common::String(event->_userdata) + " failed:" + Common::ObjectAgent::getLogInfo(2));
        }
    };

    UTIL_LOG_IFO("RtpRunner", "content:dtmf event:" + Common::String((int)event) + " from rtp");
    Common::RecLock lock(_mutex);

    if (_mpcallAgent)
        _mpcallAgent.event_begin(new Async(), MpCall::Event_toString(MpCall::EventDtmf), ServiceUtil::to_json(MpCall::EventDtmfDetail((MpCall::DtmfEvent)event)), 0, new Common::UserdataInt(event));

    if (_room && !_room->sendDtmf((enum JsmClient::DtmfType)event, false))
        UTIL_LOG_WRN("RtpRunner", "content:send dtmf event:" + Common::String((int)event) + " to jsm failed");
}

bool MpCallRtpRunner::check(const Common::ServerCallPtr &__call, const Common::String &managerOid, const Common::String &action, Common::String &status, int &freePercent)
{
    if (!_runner)
    {
        UTIL_LOG_WRN("RtpRunner", "content:no service runner availible.");
        return false;
    }

    return _runner->check(__call, managerOid, action, status, freePercent);
}

void MpCallRtpRunner::onRunnerReady()
{
}

void MpCallRtpRunner::onRunnerTermed(const Common::String &reason)
{
    MpCallSip::RtpRunnerMonitorAgent monitorAgent = _monitorAgent;
    if (monitorAgent)
        monitorAgent.onError_begin(0, "RunnerError:" + reason);
    setError("RunnerTearmed:" + reason, __FILE__, __LINE__);
}

void MpCallRtpRunner::callout_begin(const Common::ServerCallPtr &__call, const Common::String &targetOid, const Common::String &monitorOid, const MpCallSip::RtpRunnerConfig &config, const MpCallSip::SdpParams &sdpParams)
{
    Common::RecLock lock(_mutex);

    setWorking();

    if (!checkConfig(config))
    {
        _error = true;
        UTIL_LOG_WRN("RtpRunner", "content:start callout no valid room config.");
        MpCallSip::RtpRunnerServer::callout_end(__call, Error::NoValidRoomConfig(ELOC));
        return;
    }

    Common::CallParamsPtr callParams;
    if (!__call->getTraceContext().traceId.empty())
        callParams = Common::CallParams::create()->enableTrace(__call->getTraceContext().traceId);

    if (!targetOid.empty())
    {
        _mpcallAgent = _app->createAgent(targetOid);
        _mpcallAgent->setParams(callParams);
    }
    if (!monitorOid.empty())
    {
        _monitorAgent = _app->createAgent(monitorOid);
        _monitorAgent->setParams(callParams);
    }

    Common::String offerSdp = _rtpClient->GenOfferSdp(sdpParams.videoDirection, sdpParams.precondition).c_str();
    if (offerSdp.empty())
    {
        _error = true;
        UTIL_LOG_WRN("RtpRunner", "content:start callout generate offer failed.");
        MpCallSip::RtpRunnerServer::callout_end(__call, Error::GenOfferFailed(ELOC));
        return;
    }

    UTIL_LOG_IFO("RtpRunner", "content:start callout ok, offer:\n" + offerSdp);
    MpCallSip::RtpRunnerServer::callout_end(__call, true, offerSdp);

    _roomConfig.AudioOnly = !__hasVideo(offerSdp);
    if (!joinRoom(config))
        UTIL_LOG_WRN("RtpRunner", "content:start callout join room failed");
    else
        UTIL_LOG_IFO("RtpRunner", "content:start callout join room ok");
}

void MpCallRtpRunner::callin_begin(const Common::ServerCallPtr &__call, const Common::String &targetOid, const Common::String &monitorOid, const MpCallSip::RtpRunnerConfig &config, const Common::String &offerSdp)
{
    Common::RecLock lock(_mutex);

    setWorking();

    if (!checkConfig(config))
    {
        _error = true;
        UTIL_LOG_WRN("RtpRunner", "content:start callin no valid room config.");
        MpCallSip::RtpRunnerServer::callin_end(__call, Error::NoValidRoomConfig(ELOC));
        return;
    }

    Common::CallParamsPtr callParams;
    if (!__call->getTraceContext().traceId.empty())
        callParams = Common::CallParams::create()->enableTrace(__call->getTraceContext().traceId);

    if (!targetOid.empty())
    {
        _mpcallAgent = _app->createAgent(targetOid);
        _mpcallAgent->setParams(callParams);
    }
    if (!monitorOid.empty())
    {
        _monitorAgent = _app->createAgent(monitorOid);
        _monitorAgent->setParams(callParams);
    }

    _offerSdp = offerSdp;
    if (_offerSdp.empty())
        UTIL_LOG_IFO("RtpRunner", "content:start callin with no offer");
    else
        UTIL_LOG_IFO("RtpRunner", "content:start callin, offer:\n" + _offerSdp);
    MpCallSip::RtpRunnerServer::callin_end(__call, true);

    _roomConfig.AudioOnly = !__hasVideo(offerSdp);
    if (!joinRoom(config))
        UTIL_LOG_WRN("RtpRunner", "content:start callin join room failed");
    else
        UTIL_LOG_IFO("RtpRunner", "content:start callin join room ok");
}

void MpCallRtpRunner::callinAndOffer_begin(const Common::ServerCallPtr &__call, const Common::String &targetOid, const Common::String &monitorOid, const MpCallSip::RtpRunnerConfig &config, const MpCallSip::SdpParams &sdpParams)
{
    Common::RecLock lock(_mutex);

    setWorking();

    if (!checkConfig(config))
    {
        _error = true;
        UTIL_LOG_WRN("RtpRunner", "content:start callin generate offer no valid room config.");
        MpCallSip::RtpRunnerServer::callinAndOffer_end(__call, Error::NoValidRoomConfig(ELOC));
        return;
    }

    if (!targetOid.empty())
        _mpcallAgent = _app->createAgent(targetOid);
    if (!monitorOid.empty())
        _monitorAgent = _app->createAgent(monitorOid);

    Common::String offerSdp = _rtpClient->GenOfferSdp(sdpParams.videoDirection, sdpParams.precondition).c_str();
    if (offerSdp.empty())
    {
        _error = true;
        UTIL_LOG_WRN("RtpRunner", "content:start callin generate offer failed");
        MpCallSip::RtpRunnerServer::callinAndOffer_end(__call, Error::GenOfferFailed(ELOC));
        return;
    }

    UTIL_LOG_IFO("RtpRunner", "content:start callin, generate offer:\n" + offerSdp);
    MpCallSip::RtpRunnerServer::callinAndOffer_end(__call, true, offerSdp);

    _roomConfig.AudioOnly = !(__hasVideo(offerSdp) && (sdpParams.videoDirection != MpCallSip::MediaInactive));
    if (!joinRoom(config))
        UTIL_LOG_WRN("RtpRunner", "content:start callin generate offer join room failed");
    else
        UTIL_LOG_IFO("RtpRunner", "content:start callin generate offer join room ok");
}

void MpCallRtpRunner::callinAndAnswer_begin(const Common::ServerCallPtr &__call, const Common::String &targetOid, const Common::String &monitorOid, const MpCallSip::RtpRunnerConfig &config, const Common::String &offerSdp, const MpCallSip::SdpParams &sdpParams)
{
    Common::RecLock lock(_mutex);

    setWorking();

    if (!checkConfig(config))
    {
        _error = true;
        UTIL_LOG_WRN("RtpRunner", "content:start callin no valid room config.");
        MpCallSip::RtpRunnerServer::callinAndAnswer_end(__call, Error::NoValidRoomConfig(ELOC));
        return;
    }

    Common::CallParamsPtr callParams;
    if (!__call->getTraceContext().traceId.empty())
        callParams = Common::CallParams::create()->enableTrace(__call->getTraceContext().traceId);

    if (!targetOid.empty())
    {
        _mpcallAgent = _app->createAgent(targetOid);
        _mpcallAgent->setParams(callParams);
    }
    if (!monitorOid.empty())
    {
        _monitorAgent = _app->createAgent(monitorOid);
        _monitorAgent->setParams(callParams);
    }

    Common::String answerSdp = _rtpClient->GenAnswerSdp(offerSdp.c_str(), sdpParams.videoDirection, sdpParams.precondition).c_str();
    if (answerSdp.empty())
    {
        _error = true;
        UTIL_LOG_WRN("RtpRunner", "content:start callin generate answer failed, offer:\n" + offerSdp);
        MpCallSip::RtpRunnerServer::callinAndAnswer_end(__call, Error::GenAnswerFailed(ELOC));
        return;
    }

    UTIL_LOG_IFO("RtpRunner", "content:start callin, offer:\n" + offerSdp + "\nanswer:\n" + answerSdp);
    MpCallSip::RtpRunnerServer::callinAndAnswer_end(__call, true, answerSdp);
    _onCallAnswered = true;

    _roomConfig.AudioOnly = !(__hasVideo(offerSdp) && (sdpParams.videoDirection != MpCallSip::MediaInactive));
    if (!joinRoom(config))
    {
        UTIL_LOG_WRN("RtpRunner", "content:start callin and asnwer join room failed");
        return;
    }
    UTIL_LOG_IFO("RtpRunner", "content:start callin and asnwer join room ok");

    _tessarEvent->startRtp();

    if (!startAudioBridge() || !startVideoBridge())
    {
        _error = true;
        UTIL_LOG_WRN("RtpRunner", "content:start callin start media bridge failed.");
        _monitorAgent.onError_begin(0, "MediaBridgeFailed");
    }
}

bool MpCallRtpRunner::close(const Common::ServerCallPtr &__call)
{
    Common::RecLock lock(_mutex);

    UTIL_LOG_IFO("RtpRunner", "content:process close request");
    if (_runner)
    {
        _runner->close();
        _runner = 0;
    }

    if (_room)
    {
        collectLeaveRoom();
        _room->close();
        _room = 0;
    }

    stopAudioBridge();
    stopVideoBridge();

    _app->indShutdown();
    return true;
}

bool MpCallRtpRunner::genOffer(const Common::ServerCallPtr &__call, const MpCallSip::SdpParams &sdpParams, Common::String &offerSdp)
{
    Common::RecLock lock(_mutex);

    if (_error)
    {
        UTIL_LOG_WRN("RtpRunner", "content:generate offer failed.");
        __call->setError(Error::InvalidState(ELOC));
        return false;
    }

    offerSdp = _rtpClient->GenOfferSdp(sdpParams.videoDirection, sdpParams.precondition).c_str();
    if (offerSdp.empty())
    {
        _error = true;
        UTIL_LOG_WRN("RtpRunner", "content:generate offer failed.");
        __call->setError(Error::GenOfferFailed(ELOC));
        return false;
    }

    UTIL_LOG_IFO("RtpRunner", "content:generate offer:\n" + offerSdp);
    return true;
}

bool MpCallRtpRunner::setOffer(const Common::ServerCallPtr &__call, const Common::String &offerSdp)
{
    Common::RecLock lock(_mutex);

    if (_error)
    {
        UTIL_LOG_WRN("RtpRunner", "content:set offer failed.");
        __call->setError(Error::InvalidState(ELOC));
        return false;
    }

    if (offerSdp.empty())
    {
        _error = true;
        UTIL_LOG_WRN("RtpRunner", "content:set offer with no offer");
        __call->setError(Error::NoOfferSdp(ELOC));
        return false;
    }

    _offerSdp = offerSdp;
    UTIL_LOG_IFO("RtpRunner", "content:set offer:\n" + offerSdp);
    return true;
}

bool MpCallRtpRunner::setOfferAndAnswer(const Common::ServerCallPtr &__call, const Common::String &offerSdp, const MpCallSip::SdpParams &sdpParams, Common::String &answerSdp)
{
    Common::RecLock lock(_mutex);

    if (_error)
    {
        UTIL_LOG_WRN("RtpRunner", "content:set offer and generate answer failed.");
        __call->setError(Error::InvalidState(ELOC));
        return false;
    }

    if (offerSdp.empty())
    {
        _error = true;
        UTIL_LOG_WRN("RtpRunner", "content:set offer and generate answer with no offer");
        __call->setError(Error::NoOfferSdp(ELOC));
        return false;
    }

    answerSdp = _rtpClient->GenAnswerSdp(offerSdp.c_str(), sdpParams.videoDirection, sdpParams.precondition).c_str();
    if (answerSdp.empty())
    {
        _error = true;
        UTIL_LOG_WRN("RtpRunner", "content:set offer and generate answer failed, offer:\n" + offerSdp);
        __call->setError(Error::GenAnswerFailed(ELOC));
        return false;
    }

    _tessarEvent->startRtp();
    _room->setVideoState(__hasVideo(answerSdp));

    _onCallAnswered = true;
    UTIL_LOG_IFO("RtpRunner", "content:set offer and generate answer, offer:\n" + offerSdp + "\nanswer:\n" + answerSdp);
    return true;
}

bool MpCallRtpRunner::genAnswer(const Common::ServerCallPtr &__call, const MpCallSip::SdpParams &sdpParams, Common::String &answerSdp)
{
    Common::RecLock lock(_mutex);

    if (_error)
    {
        UTIL_LOG_WRN("RtpRunner", "content:generate answer failed.");
        __call->setError(Error::InvalidState(ELOC));
        return false;
    }

    if (_offerSdp.empty())
    {
        _error = true;
        UTIL_LOG_WRN("RtpRunner", "content:generate answer with no offer");
        __call->setError(Error::NoOfferSdp(ELOC));
        return false;
    }

    answerSdp = _rtpClient->GenAnswerSdp(_offerSdp.c_str(), sdpParams.videoDirection, sdpParams.precondition).c_str();
    if (answerSdp.empty())
    {
        _error = true;
        UTIL_LOG_WRN("RtpRunner", "content:generate answer failed.");
        __call->setError(Error::GenAnswerFailed(ELOC));
        return false;
    }

    _tessarEvent->startRtp();

    if (!startAudioBridge() || !startVideoBridge())
    {
        _error = true;
        UTIL_LOG_WRN("RtpRunner", "content:generate answer start media bridge failed.");
        __call->setError(Error::StartMediaBrigdeFailed(ELOC));
        return false;
    }

    _room->setVideoState(__hasVideo(answerSdp));

    _onCallAnswered = true;
    UTIL_LOG_IFO("RtpRunner", "content:generate answer:\n" + answerSdp);
    return true;
}

bool MpCallRtpRunner::setAnswer(const Common::ServerCallPtr &__call, const Common::String &answerSdp)
{
    Common::RecLock lock(_mutex);

    if (_error)
    {
        UTIL_LOG_WRN("RtpRunner", "content:set answer failed.");
        __call->setError(Error::InvalidState(ELOC));
        return false;
    }

    if (!_rtpClient->NegoAndApplySdp(answerSdp.c_str(), true))
    {
        _error = true;
        UTIL_LOG_WRN("RtpRunner", "content:set answer failed, answer:\n" + answerSdp);
        __call->setError(Error::NegotiateFailed(ELOC));
        return false;
    }

    _tessarEvent->startRtp();

    if (!startAudioBridge() || !startVideoBridge())
    {
        _error = true;
        UTIL_LOG_WRN("RtpRunner", "content:set answer start media bridge failed.");
        __call->setError(Error::StartMediaBrigdeFailed(ELOC));
        return false;
    }

    _room->setVideoState(__hasVideo(answerSdp));

    _onCallAnswered = true;
    UTIL_LOG_IFO("RtpRunner", "content:set answer ok, answer:\n" + answerSdp);
    return true;
}

bool MpCallRtpRunner::setTalking(const Common::ServerCallPtr &__call, bool talking)
{
    Common::RecLock lock(_mutex);

    if (_error)
    {
        UTIL_LOG_WRN("RtpRunner", "content:generate offer failed.");
        __call->setError(Error::InvalidState(ELOC));
        return false;
    }

    UTIL_LOG_IFO("RtpRunner", "content:set talking.");
    return true;
}

bool MpCallRtpRunner::getRtpStatus(const Common::ServerCallPtr &__call, MpCallSip::RtpStats &stats)
{
    Common::RecLock lock(_mutex);

    if (_error)
    {
        UTIL_LOG_WRN("RtpRunner", "content:get rtp status failed.");
        __call->setError(Error::InvalidState(ELOC));
        return false;
    }

    std::string statsText = _rtpClient->getAudioStats();
    UTIL_LOG_IFO("RtpRunner", "content:get rtp status, audio stats:\n" + Common::String(statsText.c_str()));
    SipClient::StatsDecorder decorder;
    if (decorder.decode(statsText))
    {
        stats.audio.send.totalPackets = __stoll(decorder.get("Sent Statistics#Packets"), -1);
        stats.audio.send.lostPackets = __stoll(decorder.get("Sent Statistics#Lost"), -1);
        stats.audio.send.lostPercent = __stoi(decorder.get("Sent Statistics#Lost Ratio"), -1);
        stats.audio.send.jitter = __stoi(decorder.get("Sent Statistics#Jitter"), -1);
        stats.audio.send.bitrate = __stoi(decorder.get("Sent Statistics#Bitrate"), -1, 1000);

        stats.audio.recv.totalPackets = __stoll(decorder.get("Received Statistics#Packets"), -1);
        stats.audio.recv.lostPackets = __stoll(decorder.get("Received Statistics#Lost"), -1);
        stats.audio.recv.lostPercent = __stoi(decorder.get("Received Statistics#Lost Ratio"), -1);
        stats.audio.recv.jitter = __stoi(decorder.get("Received Statistics#Jitter"), -1);
        stats.audio.recv.bitrate = __stoi(decorder.get("Received Statistics#Bitrate"), -1, 1000);
        stats.audio.recv.tmos = __stof(decorder.get("Received Statistics#TMos"), -1);

        stats.audio.general.rtt = __stoi(decorder.get("General statistics#RTT"), -1);
        stats.audio.general.networkStatus = decorder.get("General statistics#Netw Status").c_str();

        stats.audio.config.localIp = decorder.get("Configuration#Local IP").c_str();
        stats.audio.config.localPort = __stoi(decorder.get("Configuration#Local Port"), -1);
        stats.audio.config.remoteIp = decorder.get("Configuration#Remote IP").c_str();
        stats.audio.config.remotePort = __stoi(decorder.get("Configuration#Remote Port"), -1);
        stats.audio.config.codec = decorder.get("Configuration#Codec").c_str();
        stats.audio.config.payload = __stoi(decorder.get("Configuration#Payload"), -1);
        stats.audio.config.bitrate = __stoi(decorder.get("Configuration#Bitrate"), -1);
        stats.audio.config.packetLen = __stoi(decorder.get("Configuration#Pkt Len"), -1);
    }

    statsText = _rtpClient->getVideoStats();
    UTIL_LOG_IFO("RtpRunner", "content:get rtp status, video stats:\n" + Common::String(statsText.c_str()));
    if (decorder.decode(statsText))
    {
        stats.video.send.totalPackets = __stoll(decorder.get("Sent Statistics#Packets"), -1);
        stats.video.send.lostPackets = __stoll(decorder.get("Sent Statistics#Lost"), -1);
        stats.video.send.lostPercent = __stoi(decorder.get("Sent Statistics#Lost Ratio"), -1);
        stats.video.send.jitter = __stoi(decorder.get("Sent Statistics#Jitter"), -1);
        stats.video.send.frameRate = __getFps(decorder.get("Sent Statistics#FPS/Key-F/SLI-F"));
        stats.video.send.resolution = decorder.get("Sent Statistics#Resolution").c_str();
        stats.video.send.bitrate = __stoi(decorder.get("Sent Statistics#Bitrate(Total)"), -1, 1000);
        stats.video.send.codec = decorder.get("Sent Statistics#Codec").c_str();
        stats.video.send.spmos = __stof(decorder.get("Sent Statistics#SpMos"), -1);
        stats.video.send.pnsr = __stof(decorder.get("Sent Statistics#PSNR"), -1);
        stats.video.send.encodeTime = __stoi(decorder.get("Sent Statistics#EncodeTime"), -1);

        stats.video.recv.totalPackets = __stoll(decorder.get("Received Statistics#Packets"), -1);
        stats.video.recv.lostPackets = __stoll(decorder.get("Received Statistics#Lost"), -1);
        stats.video.recv.lostPercent = __stoi(decorder.get("Received Statistics#Lost Ratio"), -1);
        stats.video.recv.jitter = __stoi(decorder.get("Received Statistics#Jitter"), -1);
        stats.video.recv.frameRate = __stoi(decorder.get("Received Statistics#Framerate"), -1);
        stats.video.recv.resolution = decorder.get("Received Statistics#Resolution").c_str();
        stats.video.recv.bitrate = __stoi(decorder.get("Received Statistics#Bitrate"), -1, 1000);
        stats.video.recv.codec = decorder.get("Received Statistics#Codec").c_str();
        stats.video.recv.pvmos = __stof(decorder.get("Received Statistics#PvMos"), -1);
        stats.video.recv.decodeTime = __stoi(decorder.get("Received Statistics#DecodeTime"), -1);

        stats.video.general.captureResolution = decorder.get("General Statistics#Capture Res").c_str();
        stats.video.general.captureFrameRate = __stoi(decorder.get("General Statistics#Capture Fr"), -1);
        stats.video.general.renderFrameRate = __stoi(decorder.get("General Statistics#Render Fr"), -1);
        stats.video.general.rtt = __stoi(decorder.get("General Statistics#RTT"), -1);
        stats.video.general.networkStatus = decorder.get("General Statistics#Netw Status").c_str();

        stats.video.config.localIp = decorder.get("Configuration#Local IP").c_str();
        stats.video.config.localPort = __stoi(decorder.get("Configuration#Local Port"), -1);
        stats.video.config.remoteIp = decorder.get("Configuration#Remote IP").c_str();
        stats.video.config.remotePort = __stoi(decorder.get("Configuration#Remote Port"), -1);
        stats.video.config.codec = decorder.get("Configuration#Codec").c_str();
        stats.video.config.payload = __stoi(decorder.get("Configuration#Payload"), -1);
        stats.video.config.bitrate = __stoi(decorder.get("Configuration#Bitrate"), -1, 1000);
        stats.video.config.frameRate = __stoi(decorder.get("Configuration#Framerate"), -1);
        stats.video.config.resolution = decorder.get("Configuration#Resolution").c_str();
    }

    return true;
}

void MpCallRtpRunner::setWorking()
{
    _runner->setWorking();
    _audioPortHolder = nullptr;
    _videoPortHolder = nullptr;
}

void MpCallRtpRunner::setError(const Common::String &reason, const char *file, int line)
{
    UTIL_LOG_WRN("RtpRunner", "content:set error, reason:" + reason + " file:" + file + " line:" + Common::String(line));
    _error = true;
}

#define SIPRUNNER_CONFIG_STR_TO_INT(__target, __str, __lower, __upper) \
    do                                                                 \
    {                                                                  \
        int __i = __str.toInt(-1);                                     \
        if (__i < __lower)                                             \
            __i = __lower;                                             \
        if (__i > __upper)                                             \
            __i = __upper;                                             \
        __target = __i;                                                \
    } while (0)

#define SIPRUNNER_GET_CONFIG_STR(__target, __key) \
    do                                            \
    {                                             \
        Common::String __value;                   \
        if (_app->getAppConfig(__key, __value))   \
            __target = __value.c_str();           \
    } while (0)

#define SIPRUNNER_GET_CONFIG_BOOL(__target, __key) \
    do                                             \
    {                                              \
        Common::String __value;                    \
        if (_app->getAppConfig(__key, __value))    \
            __target = __value.toInt(0) != 0;      \
    } while (0)

#define SIPRUNNER_GET_CONFIG(__target, __key, __lower, __upper) \
    do                                                          \
    {                                                           \
        Common::String __value;                                 \
        if (_app->getAppConfig(__key, __value))                 \
            __target = __stoi(__value, __lower, __upper);       \
    } while (0)

#define SIPRUNNER_GET_CONFIG_WITH_DEFAULT(__target, __key, __lower, __upper, __default) \
    do                                                                                  \
    {                                                                                   \
        Common::String __value;                                                         \
        if (_app->getAppConfig(__key, __value))                                         \
            __target = __stoi(__value, __lower, __upper);                               \
        else                                                                            \
            __target = __default;                                                       \
    } while (0)

void MpCallRtpRunner::initRtpConig(SipClient::SipRtpConfig &config)
{
    config.workPath = "./jssi";
    config.audioPorts.upperBound = 17099;
    config.audioPorts.lowerBound = 17000;
    config.videoPorts.upperBound = 17299;
    config.videoPorts.lowerBound = 17200;
    config.videoConfig.width = 352;
    config.videoConfig.height = 288;
    config.videoConfig.frameRate = 15;
    config.videoConfig.bitRate = 768;
    config.overrideVideoConfig = config.videoConfig;
    config.keyframeInterval = 2;
    config.dtmfPayloadType = 101;
}

void MpCallRtpRunner::readBandwidthConfig(SipClient::SipRtpConfig::BandwidthConfig &bandwidthConfig, const char *key)
{
    Common::String value;
    if (!_app->getAppConfig(key, value))
        return;

    Common::StrVec vec;
    value.split(vec, ",");
    if (vec.size() != 3)
        return;

    bandwidthConfig.AS = __stoi(vec[0], 0, 100000);
    bandwidthConfig.RS = __stoi(vec[1], 0, 100000);
    bandwidthConfig.RR = __stoi(vec[2], 0, 100000);
}

void MpCallRtpRunner::readRtpConfig(SipClient::SipRtpConfig &config)
{
    initRtpConig(config);

    SIPRUNNER_GET_CONFIG_STR(config.localIp, "RtpRunner.LocalIp");
    SIPRUNNER_GET_CONFIG_STR(config.workPath, "RtpRunner.RtpWorkPath");
    SIPRUNNER_GET_CONFIG(config.audioPorts.upperBound, "RtpRunner.AudioPorts.UpperBound", 1, 65534);
    SIPRUNNER_GET_CONFIG(config.audioPorts.lowerBound, "RtpRunner.AudioPorts.LowerBound", 1, 65534);
    SIPRUNNER_GET_CONFIG(config.videoPorts.upperBound, "RtpRunner.VideoPorts.UpperBound", 1, 65534);
    SIPRUNNER_GET_CONFIG(config.videoPorts.lowerBound, "RtpRunner.VideoPorts.LowerBound", 1, 65534);
    SIPRUNNER_GET_CONFIG(config.videoConfig.width, "RtpRunner.VideoConfig.Width", 100, 6000);
    SIPRUNNER_GET_CONFIG(config.videoConfig.height, "RtpRunner.VideoConfig.Height", 100, 6000);
    SIPRUNNER_GET_CONFIG(config.videoConfig.frameRate, "RtpRunner.VideoConfig.FrameRate", 1, 120);
    SIPRUNNER_GET_CONFIG(config.videoConfig.bitRate, "RtpRunner.VideoConfig.BitRate", 100, 20000);
    SIPRUNNER_GET_CONFIG_WITH_DEFAULT(config.overrideVideoConfig.width, "RtpRunner.OverrideVideoConfig.Width", 100, 6000, config.videoConfig.width);
    SIPRUNNER_GET_CONFIG_WITH_DEFAULT(config.overrideVideoConfig.height, "RtpRunner.OverrideVideoConfig.Height", 100, 6000, config.videoConfig.height);
    SIPRUNNER_GET_CONFIG_WITH_DEFAULT(config.overrideVideoConfig.frameRate, "RtpRunner.OverrideVideoConfig.FrameRate", 1, 120, config.videoConfig.frameRate);
    SIPRUNNER_GET_CONFIG_WITH_DEFAULT(config.overrideVideoConfig.bitRate, "RtpRunner.OverrideVideoConfig.BitRate", 100, 20000, config.videoConfig.bitRate);
    SIPRUNNER_GET_CONFIG(config.keyframeInterval, "RtpRunner.KeyFrameInterval", 1, 600);
    SIPRUNNER_GET_CONFIG(config.dtmfPayloadType, "RtpRunner.DtmfPayloadType", 96, 127);
    SIPRUNNER_GET_CONFIG_BOOL(config.screenSharingMode, "RtpRunner.ScreenSharingMode");
    readBandwidthConfig(config.audioBandwidth, "RtpRunner.AudioBandwidth");
    readBandwidthConfig(config.videoBandwidth, "RtpRunner.VideoBandwidth");
}

void MpCallRtpRunner::saveRtpConfig(std::ofstream &ofs, const SipClient::SipRtpConfig &config)
{
    ofs << "RtpRunner.LocalIp=" << config.localIp << std::endl;
    ofs << "RtpRunner.RtpWorkPath=" << config.workPath << std::endl;
    ofs << "RtpRunner.AudioPorts.UpperBound=" << config.audioPorts.upperBound << std::endl;
    ofs << "RtpRunner.AudioPorts.LowerBound=" << config.audioPorts.lowerBound << std::endl;
    ofs << "RtpRunner.VideoPorts.UpperBound=" << config.videoPorts.upperBound << std::endl;
    ofs << "RtpRunner.VideoPorts.LowerBound=" << config.videoPorts.lowerBound << std::endl;
    ofs << "RtpRunner.VideoConfig.Width=" << config.videoConfig.width << std::endl;
    ofs << "RtpRunner.VideoConfig.Height=" << config.videoConfig.height << std::endl;
    ofs << "RtpRunner.VideoConfig.FrameRate=" << config.videoConfig.frameRate << std::endl;
    ofs << "RtpRunner.VideoConfig.BitRate=" << config.videoConfig.bitRate << std::endl;
    ofs << "RtpRunner.OverrideVideoConfig.Width=" << config.overrideVideoConfig.width << std::endl;
    ofs << "RtpRunner.OverrideVideoConfig.Height=" << config.overrideVideoConfig.height << std::endl;
    ofs << "RtpRunner.OverrideVideoConfig.FrameRate=" << config.overrideVideoConfig.frameRate << std::endl;
    ofs << "RtpRunner.OverrideVideoConfig.BitRate=" << config.overrideVideoConfig.bitRate << std::endl;
    ofs << "RtpRunner.KeyFrameInterval=" << config.keyframeInterval << std::endl;
    ofs << "RtpRunner.DtmfPayloadType=" << config.dtmfPayloadType << std::endl;
    ofs << "RtpRunner.ScreenSharingMode=" << config.screenSharingMode << std::endl;
}

void MpCallRtpRunner::initRoomConfig(JsmClient::Config &config)
{
    config.DomainId = "100645";
    config.AppId = "0";
    config.Name = "rtprunner";
    config.WorkDir = ".";
    config.Password = "123456";
    config.Sender = true;
    config.EnableDriveZmf = false;
    config.ExtraRole = (RolePstn | RoleAgent);
}

void MpCallRtpRunner::readRoomConfig(JsmClient::Config &config)
{
    initRoomConfig(config);

    SIPRUNNER_GET_CONFIG_STR(config.DomainId, "RtpRunner.DomainId");
    SIPRUNNER_GET_CONFIG_STR(config.AppId, "RtpRunner.AppId");
    SIPRUNNER_GET_CONFIG_STR(config.WorkDir, "RtpRunner.WorkPath");
    SIPRUNNER_GET_CONFIG_WITH_DEFAULT(config.ExtraRole, "RtpRunner.ExtraRole", 0, 0x7fffffff, (RolePstn | RoleAgent));
}

void MpCallRtpRunner::saveRoomConfig(std::ofstream &ofs, const JsmClient::Config &config)
{
    ofs << "RtpRunner.DomainId=" << config.DomainId << std::endl;
    ofs << "RtpRunner.AppId=" << config.AppId << std::endl;
    ofs << "RtpRunner.WorkPath=" << config.WorkDir << std::endl;
    ofs << "RtpRunner.ExtraRole=" << config.ExtraRole << std::endl;
}

void MpCallRtpRunner::updateBackupBasename(const MpCallSip::RtpRunnerConfig &config)
{
    Common::String name = Common::String(config.domainId) + "_" + Common::String(config.appId) + "_" + config.name;

    if (!_room)
    {
        if (!config.oid.empty() && !config.roomId.empty())
            name += "_roomId_" + config.roomId;
        else if (!config.Udid.empty())
            name += "_room_" + config.Udid;
        else
            name += "_roomNo_" + config.confNum;
    }
    else
    {
        Common::String udid = _room->RoomUdid().c_str();
        if (udid.empty())
            udid = config.Udid;
        if (!udid.empty())
            name += "_room_" + udid;
        name += "_roomId_" + Common::String(_room->RoomId().c_str());
    }

    _runner->setBackupBasename(name);
}

void MpCallRtpRunner::dumpConfig()
{
    std::ofstream ofs("rtp_runner_cfg.txt", std::ios::out | std::ios::trunc);
    if (ofs.is_open())
    {
        ofs << "RtpRunner.AudioExcludeRoleMask=" << _audioExcludeRoleMask << std::endl;
        ofs << "RtpRunner.AudioExcludeRole=" << _audioExcludeRole << std::endl;
        ofs << "RtpRunner.AudioMute=" << _audioMute << std::endl;
        ofs << "RtpRunner.VideoExcludeRoleMask=" << _videoExcludeRoleMask << std::endl;
        ofs << "RtpRunner.VideoExcludeRole=" << _videoExcludeRole << std::endl;
        ofs << "RtpRunner.VideoMute=" << _videoMute << std::endl;
        ofs << "RtpRunner.BindingUser=" << _bindingUser.c_str() << std::endl;
        saveRoomConfig(ofs, _roomConfig);
        saveRtpConfig(ofs, _rtpConfig);
        ofs.close();
    }
}

bool MpCallRtpRunner::checkConfig(const MpCallSip::RtpRunnerConfig &config)
{
    if (config.oid.empty() && config.roomId.empty() && config.Udid.empty() && config.confNum.empty())
    {
        UTIL_LOG_WRN("RtpRunner", "content:update room config no valid room config.");
        return false;
    }

    if (config.screenSharingMode)
        _rtpClient->setScreenSharingMode(true);

    return true;
}

bool MpCallRtpRunner::joinRoom(const MpCallSip::RtpRunnerConfig &config)
{
    unsigned int starTicks = Common::getCurTicks();

    if (!config.name.empty())
        _roomConfig.Name = config.name.c_str();
    if (config.domainId > 0)
        _roomConfig.DomainId = std::to_string(config.domainId);
    if (config.appId >= 0)
        _roomConfig.AppId = std::to_string(config.appId);
    if (config.extraRole > 0)
        _roomConfig.ExtraRole = (config.extraRole | RolePstn);

    _audioExcludeRoleMask = config.audioExcludeRoleMask;
    _audioExcludeRole = config.audioExcludeRole;
    _audioMute = config.audioMute;
    _videoExcludeRoleMask = config.videoExcludeRoleMask;
    _videoExcludeRole = config.videoExcludeRole;
    _videoMute = config.videoMute;
    _bindingUser = config.bindingUser;

    dumpConfig();

    _client = JsmClient::Client::create(_roomConfig, _app, _jsm, _zmf);
    if (!_client)
    {
        _error = true;
        UTIL_LOG_ERR("RtpRunner", "content:create jsm client failed.");
        _monitorAgent.onError_begin(0, "CreateClientFailed");
        collectJoinRoomFailed(config, Common::getCurTicks() - starTicks);
        return false;
    }

    Common::schd_release(true);

    if (!config.oid.empty() && !config.roomId.empty())
        _room = _client->joinRoom(config.oid.c_str(), config.roomId.c_str(), this, 5000);
    else if (!config.Udid.empty())
        _room = _client->joinRoom(config.Udid.c_str(), this, 5000);
    else
        _room = _client->joinRoom(config.confNum.toInt(0), this, 5000);
    updateBackupBasename(config);
    if (!_room)
    {
        _error = true;
        if (!config.Udid.empty())
            UTIL_LOG_WRN("RtpRunner", "content:join room failed, udid:" + config.Udid);
        else if (!config.confNum.empty())
            UTIL_LOG_WRN("RtpRunner", "content:join room failed, confNum:" + config.confNum);
        else
            UTIL_LOG_WRN("RtpRunner", "content:join room failed, oid:" + config.oid + " roomId:" + config.roomId);
        _monitorAgent.onError_begin(0, "JoinRoomFailed");
        collectJoinRoomFailed(config, Common::getCurTicks() - starTicks);
        return false;
    }

    if (!config.Udid.empty())
        UTIL_LOG_IFO("RtpRunner", "content:join room ok, udid:" + config.Udid);
    else if (!config.confNum.empty())
        UTIL_LOG_IFO("RtpRunner", "content:join room ok, confNum:" + config.confNum);
    else
        UTIL_LOG_IFO("RtpRunner", "content:join room ok, oid:" + config.oid + " roomId:" + config.roomId);
    collectJoinRommSucceed(config, _room->RoomId(), Common::getCurTicks() - starTicks);

    _room->enableAdaptiveAspect(true, -1);
    if (_audioMute)
        _room->setAudioState(false);
    _jsm->SetAudioDevice(SipClient::SipRtpInterface::AudioPumpDevice, SipClient::SipRtpInterface::AudioPumpDevice);

    for (auto &member : _room->members())
    {
        filterAudio(member);
        filterVideo(member);
    }

    return true;
}

void MpCallRtpRunner::collectJoinRoomFailed(const MpCallSip::RtpRunnerConfig &config, int duration)
{
    JsmClient::TessarEventConfig tessarConfig;
    tessarConfig.domainId = config.domainId;
    tessarConfig.appId = config.appId;
    tessarConfig.roomUdid = config.Udid.c_str();
    _tessarEvent = JsmClient::TessarEvent::create(_collector, tessarConfig, 0, _zmf);
    if (_tessarEvent)
        _tessarEvent->tessarJoinRoom(false, duration);
}

void MpCallRtpRunner::collectJoinRommSucceed(const MpCallSip::RtpRunnerConfig &config, const std::string &roomId, int duration)
{
    JsmClient::TessarEventConfig tessarConfig;
    tessarConfig.domainId = config.domainId;
    tessarConfig.appId = config.appId;
    tessarConfig.roomUdid = config.Udid.c_str();
    tessarConfig.roomId = roomId;
    _tessarEvent = JsmClient::TessarEvent::create(_collector, tessarConfig, _jsm->RoomInterface(roomId.c_str()), _zmf, new MpCallRtpRunnerTesarInterface(_rtpClient));
    if (_tessarEvent)
        _tessarEvent->tessarJoinRoom(true, duration);
}

void MpCallRtpRunner::collectLeaveRoom()
{
    if (!_tessarEvent || !_collector)
        return;

    _tessarEvent->tessarLeaveRoom();
    _tessarEvent = 0;

    _collector->close();
    _collector = 0;
}

bool MpCallRtpRunner::startAudioBridge()
{
    UTIL_LOG_IFO("RtpRunner", "content:start audio bridge, deivce:" + Common::String(SipClient::SipRtpInterface::AudioPumpDevice));
    _rtpClient->muteAudioFromRtp(_audioMute);
    return true;
}

void MpCallRtpRunner::stopAudioBridge()
{
    UTIL_LOG_IFO("RtpRunner", "content:stop audio bridge");
}

bool MpCallRtpRunner::startVideoBridge()
{
    if (!updateVideoPump())
        return false;

    return _room->setVideoCapture(SipClient::SipRtpInterface::VideoPumpDevice);
}

bool MpCallRtpRunner::updateVideoPump()
{
    Common::RecLock lock(_mutex);

    if (!_jsm2RtpVideoPipe)
    {
        Common::String videoPipeMode = _app->getAppConfig("RtpRunner.VideoPipeMode");
        bool videoPipeBridge = !(videoPipeMode == "merged" || videoPipeMode == "Merged");
        try
        {
            if (videoPipeBridge)
                _jsm2RtpVideoPipe = new ZmfBridgeVideoPipe(MpCallRtpRunnerBridgeRenderId, SipClient::SipRtpInterface::VideoCaptureDevice, _zmf, _app->getAppConfig("RtpRunner.VideoPipePrioritys"));
            else
                _jsm2RtpVideoPipe = new ZmfVideoPipe(MpCallRtpRunnerRenderId, SipClient::SipRtpInterface::VideoCaptureDevice, _zmf);
        }
        catch (const std::exception &e)
        {
            UTIL_LOG_ERR("RtpRunner", "content:create zmf video pipe failed, reason:" + Common::String(e.what()));
            return false;
        }

        if (!_jsm2RtpVideoPipe->start())
        {
            _jsm2RtpVideoPipe = 0;
            return false;
        }

        if (!videoPipeBridge)
        {
            if (!_room->publishMergedVideo(MpCallRtpRunnerRenderId))
            {
                UTIL_LOG_ERR("RtpRunner", "content:publish merged video failed.");
                _jsm2RtpVideoPipe->stop();
                _jsm2RtpVideoPipe = 0;
                return false;
            }
            UTIL_LOG_IFO("RtpRunner", "content:update merged video pump");
        }
    }

    if (_jsm2RtpVideoPipe->renderId() == MpCallRtpRunnerBridgeRenderId)
    {
        std::string membername;
        if (_room->hasScreenSharing(membername))
        {
            UTIL_LOG_IFO("RtpRunner", "content:update bridge video pump with screen sharing of member:" + Common::String(membername.c_str()));
            _jsm2RtpVideoPipe->update(_room->RoomId());
        }
        else
        {
            UTIL_LOG_IFO("RtpRunner", "content:update bridge video pump.");
            _jsm2RtpVideoPipe->update(_room->members());
        }
    }

    return true;
}

void MpCallRtpRunner::stopVideoBridge()
{
    if (_jsm2RtpVideoPipe)
        _jsm2RtpVideoPipe->stop();

    if (_room)
    {
        _room->publishMergedVideo("");
        _room->setVideoCapture("");
    }
}

void MpCallRtpRunner::filterAudio(const JsmClient::MemberPtr &member)
{
    if (isBindingMode())
    {
        if (isBindingUser(member))
        {
            UTIL_LOG_IFO("RtpRunner", "content:member " + Common::String(member->Name().c_str()) + " is binding user, skip exlude audio");
        }
        else
        {
            UTIL_LOG_IFO("RtpRunner", "content:member " + Common::String(member->Name().c_str()) + " is not binding user, exclude audio");
            member->requestAudio(false);
        }
    }
    else
    {
        if (matchRole(member->Role(), _audioExcludeRoleMask, _audioExcludeRole))
        {
            UTIL_LOG_IFO("RtpRunner", "content:member " + Common::String(member->Name().c_str()) + " role:" + Common::String(member->Role()) + "/" + Common::String(_audioExcludeRoleMask) + "/" + Common::String(_audioExcludeRole) + " exclude audio");
            member->requestAudio(false);
        }
        else
        {
            UTIL_LOG_DBG("RtpRunner", "content:member " + Common::String(member->Name().c_str()) + " role:" + Common::String(member->Role()) + "/" + Common::String(_audioExcludeRoleMask) + "/" + Common::String(_audioExcludeRole) + " skip exclude audio");
        }
    }
}

void MpCallRtpRunner::filterVideo(const JsmClient::MemberPtr &member)
{
    if (isBindingMode())
    {
        if (isBindingUser(member))
        {
            UTIL_LOG_IFO("RtpRunner", "content:member " + Common::String(member->Name().c_str()) + " is binding user, request video");
            member->requestVideo(2);
        }
        else
        {
            UTIL_LOG_IFO("RtpRunner", "content:member " + Common::String(member->Name().c_str()) + " is not binding user, skip filter video");
        }
    }
    else
    {
        if (matchRole(member->Role(), _videoExcludeRoleMask, _videoExcludeRole))
        {
            UTIL_LOG_IFO("RtpRunner", "content:member " + Common::String(member->Name().c_str()) + " role:" + Common::String(member->Role()) + "/" + Common::String(_videoExcludeRoleMask) + "/" + Common::String(_videoExcludeRole) + " exclude video");
        }
        else
        {
            UTIL_LOG_IFO("RtpRunner", "content:member " + Common::String(member->Name().c_str()) + " role:" + Common::String(member->Role()) + "/" + Common::String(_videoExcludeRoleMask) + "/" + Common::String(_videoExcludeRole) + " request video");
            member->requestVideo(2);
        }
    }
}

bool MpCallRtpRunner::matchRole(int mmeberRole, int matchMask, int matchRole)
{
    if (matchMask <= 0)
        return false;

    if (matchRole >= 0)
        return (mmeberRole & matchMask) == matchRole;

    return (mmeberRole & matchMask) != 0;
}

void MpCallRtpRunner::notifyNetStatusChanged()
{
    if (!_onCallAnswered || _error)
        return;

    if (Common::getCurTicks() - _lastGetNetStatusTicks < 500)
        return;

    MpCallSip::RtpRunnerMonitorAgent monitorAgent = _monitorAgent;
    SipClient::SipRtpInterfacePtr rtpClient = _rtpClient;

    if (!monitorAgent)
    {
        UTIL_LOG_WRN("RtpRunner", "content:monitorAgent not found.");
        return;
    }
    if (!rtpClient)
    {
        UTIL_LOG_WRN("RtpRunner", "content:rtpClient not prepared.");
        return;
    }

    _lastGetNetStatusTicks = Common::getCurTicks();
    std::string statsText;
    SipClient::StatsDecorder decorder;
    Common::String rtpNetworkStatus;
    Common::StrIntMap networkStatusMap;

    if (_roomConfig.AudioOnly)
    {
        statsText = rtpClient->getAudioStats();
        if (decorder.decode(statsText))
        {
            rtpNetworkStatus = decorder.get("General statistics#Netw Status").c_str();
            UTIL_LOG_IFO("RtpRunner", "content:get network status, audio stats: " +  rtpNetworkStatus);
        }
    }
    else
    {
        statsText = rtpClient->getVideoStats();
        if (decorder.decode(statsText))
        {
            rtpNetworkStatus = decorder.get("General Statistics#Netw Status").c_str();
            UTIL_LOG_IFO("RtpRunner", "content:get network status, video stats: " +  rtpNetworkStatus);
        }
    }

    do
    {
        Common::RecLock lock(_mutex);
        networkStatusMap.swap(_jsmNetworkStatus);

        if (rtpNetworkStatus != _rtpNetworkStatus)
        {
            UTIL_LOG_IFO("RtpRunner", "content:rtp network status changed " + Common::String(_rtpNetworkStatus) + "->" + rtpNetworkStatus);
            _rtpNetworkStatus = rtpNetworkStatus;
            networkStatusMap.insert(std::make_pair("RtpRunner", convertNetworkStatusToInt(rtpNetworkStatus)));
        }
    } while (0);

    if (!networkStatusMap.empty())
        monitorAgent.onNetworkStatusChanged_begin(0, networkStatusMap);
}


} // namespace SipMpCall
