//
// *****************************************************************************
// Copyright (c) 2025 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//

#pragma once

#include "Common/Util.h"
#include "SipAdapter/SipCallInterface.h"

namespace SipMpCall
{

class SipLineSessionBaseListener : virtual public Common::Shared
{
public:
    virtual void onCallIncoming(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) {}
    virtual void onCallOutgoing(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) {}
    virtual void onCallAlerted(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) {}
    virtual void onCallAnswered(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) {}
    virtual void onCallUpdate(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) {}
    virtual void onCallRequestUpdate(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) {}
    virtual void onCallResponseUdate(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) {}
    virtual void onCallConnected(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) {}
    virtual void onCallRequestModify(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) {}
    virtual void onCallResponseModify(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) {}
    virtual void onCallTerminated(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) {}
};

class SipLineSessionBase : virtual public Common::Shared
{
public:
    virtual void setSessId(const std::string &sessId) = 0;
    virtual std::string getCallId() const = 0;

    virtual bool SipSetContactUri(const std::string &pcContactUri) = 0;
    virtual bool SipAlert(const std::string &pcAnswerSdp) = 0;
    virtual bool SipAnswer(const std::string &pcAnswerSdp, const SipClient::SipCallExtHdrs &pcExtHdrs) = 0;
    virtual bool SipTerm() = 0;
    virtual bool SipUpdate(const std::string &pcOfferSdp) = 0;
    virtual bool SipUpdateRsp(const std::string &pcAnswerSdp) = 0;
    virtual bool SipAck(const std::string &pcAnswerSdp) = 0;

    virtual bool GetCalledUri(std::string &ppcDispName, std::string &ppcUri) = 0;
    virtual bool GetPeerUri(std::string &ppcDispName, std::string &ppcUri) = 0;
    virtual bool GetCallTermedReason(std::string &ppcSipPhase, std::string &ppcReason) = 0;
};

typedef Common::Handle<SipLineSessionBase> SipLineSessionBasePtr;

} // namespace SipMpCall
