//
// *****************************************************************************
// Copyright (c) 2023 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//
// Created on 2023/5/25 by <PERSON>
//

#pragma once

#include "Common/Common.h"
#include "SimpleSipSession/SipDriver.h"
#include "SimpleSipSession/SipTransport.h"
#include "SipAdapter/SipCallInterface.h"
#include "SipLine/SipLine.h"

#include "SipLineTypes.h"
#include "SipConn.h"
#include "SipLineSessionI.h"

#include <unordered_map>
#include <functional>

namespace SipMpCall
{

class SipLineI : public SipLine, public SipClient::SipCallListener, public SipConnServerManager
{
public:
    SipLineI(const Common::ApplicationPtr &app, const SipLineListnerPtr &listener)
        : _app(app)
        , _sipCallCreated(false)
        , _listener(listener)
        , _configCalleeUriFormat(SimpleSipSession::SipUri)
        , _configCallerUriFormat(SimpleSipSession::SipUri)
        , _configPaiUriFormat(SimpleSipSession::SipUri)
        , _lastSchdTicks(Common::getCurTicks() - 1000)
    {
    }

    bool init();

    // implement SipLine
    bool isReady(Common::String &reason) override;
    void close() override;
    void schd() override;
    void updateConfigs() override;
    int getFreePercent() override;
    SipLineSessionPtr SipCall(const SipLineSessionListenerPtr &listener, const std::string &sessId, const std::string &caller, const std::string &callee, const std::string &pcOfferSdp, const std::map<std::string, std::string> &extHdrs) override;

    // implement SipClient::SipCallListener
    bool onSend(const std::string &message) override;
    SipClient::SipCallSessionListenerPtr onCreateSession(const SipClient::SipCallSessionPtr &session) override;

    void recv(const std::string &data) override;

    SimpleSipSession::SipDriverPtr getSipDriver() override;

    std::string getSessId(const std::string &callId) override;
    vector<std::string> getCallId(const std::string &sessId) override;
    bool getSessStatus(const std::string &sessId, std::map<std::string, SessStatus> &status) override;
    void setKeepTermedStatus(bool keepTermedSession) override;
    std::string getContactUri(const std::string &callId, const std::string &userpart) override;

    std::string getContactUri(const std::string &userpart, const SimpleSipSession::SipHostPort &hostport);
    bool readSipConfig(SipClient::SipCallConfig &config);
    enum SimpleSipSession::SipUriFormat getUriFormatConfig(const char *key, enum SimpleSipSession::SipUriFormat defaultValue);
    bool initSipCall();

public:
    Common::ApplicationPtr _app;
    Common::String _failReason;

    bool _sipCallCreated;
    SipLineListnerPtr _listener;
    SipConnServerPtr _sipConnServer;
    SimpleSipSession::SipDriverPtr _sipDriver;

    Common::String _configUriParams;
    Common::String _configCalleePrefix;
    enum SimpleSipSession::SipUriFormat _configCalleeUriFormat;
    enum SimpleSipSession::SipUriFormat _configCallerUriFormat;
    enum SimpleSipSession::SipUriFormat _configPaiUriFormat;

    SipLineSessionManager _sipLineSessionManager;
    unsigned int _lastSchdTicks;

    friend class SipLineSessionI;
};

} // namespace SipMpCall
