//
// *****************************************************************************
// Copyright (c) 2025 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//

#pragma once

#include "SipAdapter/SipCallInterface.h"
#include "SipLine/SipLine.h"
#include "SipConn.h"
#include "Util/SessionManager.h"

#include <mutex>

namespace SipMpCall
{

class SipLineI;
class SipLineSessionI;
typedef Common::Handle<SipLineSessionI> SipLineSessionIPtr;

enum SipLineSessionIdType
{
    SipLineSessionId = 0,
    SipLineSessionCallId,
    SipLineSessionIdTypeSize
};

class SipLineSessionManager : public SessionIndexManager<SipLineSessionIdTypeSize, SipLineI>
{
public:
    SipLineSessionIPtr getSess(const std::string &sessId)
    {
        return SipLineSessionIPtr::dynamicCast(getObject(ObjectId(SipLineSessionId, sessId)));
    }

    SipLineSessionIPtr getSessByCallId(const std::string &callId)
    {
        return SipLineSessionIPtr::dynamicCast(getObject(ObjectId(SipLineSessionCallId, callId)));
    }
};

class SipLineSessionI : public SipLineSession, public SipClient::SipCallSessionListener, public SipLineSessionManager::Session
{
public:
    explicit SipLineSessionI(SipLineSessionManager *manager)
        : _mamager(manager)
        , _termed(false)
    {
    }

    explicit SipLineSessionI(SipLineSessionManager *manager, const SipClient::SipCallSessionPtr &session)
        : _mamager(manager)
        , _session(session)
        , _termed(false)
    {
    }

    virtual bool open(const Common::ApplicationPtr &app, const SipClient::SipCallSessionPtr &session, const SipLineSessionListenerPtr &listener);
    virtual void close() override;
    virtual bool schd(SipLineI *sipLine) override { return _termed; }
    virtual bool isCallout() const { return false; }

    void setConnId(const std::string &connId);

    void setSessId(const std::string &sessId) override;
    std::string getSessId() const;
    void setCallId(const std::string &callId);
    std::string getCallId() const override;
    vector<std::string> getCallIds() const override;

    // implement SipLineSession
    bool SipSetContactUri(const std::string &pcContactUri) override;
    bool SipAlert(const std::string &pcAnswerSdp) override;
    bool SipAnswer(const std::string &pcAnswerSdp, const SipClient::SipCallExtHdrs &pcExtHdrs) override;
    bool SipTerm() override;
    bool SipUpdate(const std::string &pcOfferSdp) override;
    bool SipUpdateRsp(const std::string &pcAnswerSdp) override;
    bool SipAck(const std::string &pcAnswerSdp) override;
    bool GetCalledUri(std::string &ppcDispName, std::string &ppcUri) override;
    bool GetPeerUri(std::string &ppcDispName, std::string &ppcUri) override;
    bool GetCallTermedReason(std::string &ppcSipPhase, std::string &ppcReason) override;

    // implement SipClient::SipCallSessionListener
    void onCallIncoming(const SipClient::SipCallSessionPtr &session, const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) override;
    void onCallOutgoing(const SipClient::SipCallSessionPtr &session, const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) override;
    void onCallAlerted(const SipClient::SipCallSessionPtr &session, const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) override;
    void onCallAnswered(const SipClient::SipCallSessionPtr &session, const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) override;
    void onCallUpdate(const SipClient::SipCallSessionPtr &session, const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) override;
    void onCallRequestUpdate(const SipClient::SipCallSessionPtr &session, const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) override;
    void onCallResponseUdate(const SipClient::SipCallSessionPtr &session, const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) override;
    void onCallConnected(const SipClient::SipCallSessionPtr &session, const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) override;
    void onCallRequestModify(const SipClient::SipCallSessionPtr &session, const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) override;
    void onCallResponseModify(const SipClient::SipCallSessionPtr &session, const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) override;
    void onCallTerminated(const SipClient::SipCallSessionPtr &session, const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) override;

protected:
    mutable std::mutex _mutex;
    SipLineSessionManager *_mamager;
    SipClient::SipCallSessionPtr _session;
    SipLineSessionListenerPtr _listener;

    // status information
    bool _termed;
    std::string _currentConnId;
    std::vector<std::string> _connIds;

    // locate information
    std::string _sessId;
    std::vector<std::string> _sipCallIds;
};

class SipLineCalloutSession : public SipLineSessionI
{
public:
    SipLineCalloutSession(SipLineSessionManager *manager, const std::string &caller, const std::string &callee, const std::string &offerSdp, const std::map<std::string, std::string> &extHdrs)
        : SipLineSessionI(manager)
        , _startTicks(Common::getCurTicks())
        , _responsed(false)
        , _responseTimeoutMs(4000)
        , _caller(caller)
        , _callee(callee)
        , _offerSdp(offerSdp)
        , _extHdrs(extHdrs)
    {
    }

    virtual bool open(const Common::ApplicationPtr &app, const SipClient::SipCallSessionPtr &session, const SipLineSessionListenerPtr &listener) override;
    virtual bool schd(SipLineI *sipLine) override;
    virtual bool isCallout() const override { return true; }

    bool GetCallTermedReason(std::string &ppcSipPhase, std::string &ppcReason) override;

    void onCallOutgoing(const SipClient::SipCallSessionPtr &session, const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) override;
    void onCallAlerted(const SipClient::SipCallSessionPtr &session, const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) override;
    void onCallAnswered(const SipClient::SipCallSessionPtr &session, const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) override;
    void onCallTerminated(const SipClient::SipCallSessionPtr &session, const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) override;

private:
    bool __retryCallout(SipLineI *sipLine, const SipConnPtr &conn);

private:
    unsigned int _startTicks;
    bool _responsed;
    unsigned int _responseTimeoutMs;

    std::string _caller;
    std::string _callee;
    std::string _offerSdp;
    std::map<std::string, std::string> _extHdrs;
};

} // namespace SipMpCall
