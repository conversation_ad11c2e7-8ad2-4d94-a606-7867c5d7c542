//
// *****************************************************************************
// Copyright (c) 2025 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//

#include "SipAsbcServer.h"
#include "SimpleSipSession/SipTypes.h"
#include "SimpleSipSession/SipUri.h"
#include <algorithm>

namespace SipMpCall
{

void SipRegConn::close()
{
    if (_registration)
        _registration->unregister();
    _sipDriver = 0;
}

void SipRegConn::schd()
{
    if (!_registration)
    {
        _registration = SimpleSipSession::Registration::create(_sipDriver, this, this, _config);
        if (!_registration)
            UTIL_LOG_WRN("SipRegConn", "content:connection " + connectionInfo + " create registration failed.");
    }
    else if (_keepAliveInterval > 0 && Common::getCurTicks() - _lastCrlfSendTicks > _keepAliveInterval)
    {
        _lastCrlfSendTicks = Common::getCurTicks();
        const unsigned char *crlfcrlf = (const unsigned char *)"\r\n\r\n";
        if (!SipConn::__send(crlfcrlf, 4, "CRLFCRLF"))
            UTIL_LOG_WRN("SipRegConn", "content:connection " + connectionInfo + " send CRLFCRLF failed.");
    }
}

std::string SipRegConn::callerUri(const std::string &number, enum SimpleSipSession::SipUriFormat format, const std::string &prefix)
{
    return SimpleSipSession::SipUri::formatUri(format, prefix, _config.username, _config.realm);
}

std::string SipRegConn::calleeUri(const std::string &number, enum SimpleSipSession::SipUriFormat format, const std::string &prefix)
{
    return SimpleSipSession::SipUri::formatUri(format, prefix, number, _config.realm);
}

bool SipRegConn::isPeerAliveIn(unsigned int seconds)
{
    if (_registered && !(_concurrentCallLimit && _duringCall))
    {
        if (_logState.shouldLog(60))
            UTIL_LOG_IFO("SipRegConn", "content:connection " + connectionInfo + " registered");
        return true;
    }

    if (!_registered)
        UTIL_LOG_WRN("SipRegConn", "content:connection " + connectionInfo + " not registered");
    else
        UTIL_LOG_IFO("SipRegConn", "content:connection " + connectionInfo + " during call");

    return false;
}

bool SipRegConn::isClosed()
{
    return !_registered && !_registration;
}

bool SipRegConn::send(const std::string &sessId, const SimpleSipSession::SipMessagePtr &msg)
{
    updateCallingState(msg);
    return SipConn::send(sessId, msg);
}

void SipRegConn::recv(const unsigned char *data, int dataLen)
{
    if (dataLen == 2 && data[0] == '\r' && data[1] == '\n')
    {
        lastRecvTicks = Common::getCurTicks();
        UTIL_LOG_IFO("SipRegConn", "content:connection recv " + connectionInfoRecv + " CRLF");
        return;
    }
    else if (dataLen == 4 && data[0] == '\r' && data[1] == '\n' && data[2] == '\r' && data[3] == '\n')
    {
        lastRecvTicks = Common::getCurTicks();
        UTIL_LOG_IFO("SipRegConn", "content:connection recv " + connectionInfoRecv + " CRLFCRLF");
        __send((const unsigned char *)"\r\n", 2, "CRLF");
        return;
    }

    SimpleSipSession::SipMessagePtr sipMsg = SimpleSipSession::SipMessage::create(std::string((const char *)data, dataLen));
    if (!sipMsg)
    {
        UTIL_LOG_WRN("SipRegConn", "content:connection recv " + connectionInfoRecv + " size:" + Common::String::formatString("%-5d", dataLen) + " invalid sip message:" + Common::String((const char *)data, dataLen));
        return;
    }

    if (_registration && _registration->recvMessage(sipMsg))
    {
        lastRecvTicks = Common::getCurTicks();
        UTIL_LOG_IFO("SipRegConn", "content:registration recv " + connectionInfoRecv + " size:" + Common::String::formatString("%-5d", dataLen) + " digest:" + sipMsg->logInfo().c_str() + " sip message:\n" + Common::String((const char *)data, dataLen));
        return;
    }

    updateCallingState(sipMsg);
    SipConn::__recv(sipMsg, connectionInfoRecv + " size:" + Common::String::formatString("%-5d", dataLen) + " digest:" + sipMsg->logInfo().c_str() + " sip message:\n" + Common::String((const char *)data, dataLen));
}

void SipRegConn::onError()
{
    UTIL_LOG_WRN("SipRegConn", "content:connection " + connectionInfo + " error.");
}

void SipRegConn::onSendMessage(const SimpleSipSession::SipMessagePtr &message)
{
    SipConn::send("", message);
}

void SipRegConn::onRegistered()
{
    UTIL_LOG_IFO("SipRegConn", "content:connection " + connectionInfo + " registered.");
    _registered = true;
}

void SipRegConn::onRegisterFailed()
{
    UTIL_LOG_WRN("SipRegConn", "content:connection " + connectionInfo + " register failed.");
    _registered = false;
}

void SipRegConn::onUnregistered()
{
    UTIL_LOG_IFO("SipRegConn", "content:connection " + connectionInfo + " unregistered.");
    if (_registration)
    {
        _registration->close();
        _registration = 0;
    }
    SipConn::close();
}

void SipRegConn::updateCallingState(const SimpleSipSession::SipMessagePtr &msg)
{
    if (!_duringCall)
    {
        if (msg->isRequest())
        {
            std::string method;
            if (msg->getMethod(method) && method == "INVITE")
            {
                _duringCall = true;
                UTIL_LOG_IFO("SipRegConn", "content:connection " + connectionInfo + " during call");
            }
        }
    }
    else
    {
        int statusCode;
        std::string reasonPhrase, method;
        if (msg->getStatusLine(statusCode, reasonPhrase) && msg->getMethod(method))
        {
            if (method == "BYE" || (method == "INVITE" && statusCode >= 300))
            {
                _duringCall = false;
                UTIL_LOG_IFO("SipRegConn", "content:connection " + connectionInfo + " end call");
            }
        }
    }
}

bool AsbcServer::init(Common::String &reason)
{
    _accountManager = SipAccountManager::create(_app, this);
    if (!_accountManager->activate(reason))
        return false;

    _concurrentCallLimit = _app->getAppConfigAsInt("SipCall.ConcurrentCallLimit") == 1;
    return true;
}

void AsbcServer::schd()
{
    SipConnServer::schd();

    Common::RecLock lock(_mutex);

    if (_accountManager)
        _accountManager->schd();

    std::vector<SipRegConnPtr> activeRegConnections;
    Common::String stats;
    for (auto it = _regConnections.begin(); it != _regConnections.end();)
    {
        it->second->schd();
        if (it->second->isClosed())
        {
            UTIL_LOG_IFO("AsbcServer", "content:remove reg connection:" + it->second->connectionInfo);
            _regConnections.erase(it++);
        }
        else
        {
            if (it->second->isPeerAliveIn(0))
            {
                stats += it->second->connectionInfo + " ";
                activeRegConnections.push_back(it->second);
            }
            ++it;
        }
    }

    _app->setStatistics("SipCall.Connections.Active", stats);
    SERVICE_LOG_EVERY_NSEC(60, Common::LogInfo, "AsbcServer", "content:active connections:" + stats);
    _activeRegConnections.swap(activeRegConnections);
}

void AsbcServer::close()
{
    Common::RecLock lock(_mutex);

    if (_accountManager)
        _accountManager->deactivate();
    _accountManager = 0;

    for (auto kv : _regConnections)
        kv.second->close();
    _regConnections.clear();
    _activeRegConnections.clear();

    _sipLine = 0;
    _app = 0;
}

bool AsbcServer::send(const std::string &data)
{
    // create sip message
    SimpleSipSession::SipMessagePtr sipMsg = SimpleSipSession::SipMessage::create(data);
    if (!sipMsg)
    {
        UTIL_LOG_DBG("AsbcServer", "content:invalid sip message:" + Common::String(data.c_str()));
        return false;
    }

    std::string callId = sipMsg->getHeader(SimpleSipSession::Header::CALL_ID);
    std::string sessId = _sipLine->getSessId(callId);

    SipConnPtr conn = getConnByCallId(callId);
    if (conn)
    {
        if (conn->send(sessId, sipMsg))
        {
            _eventManager.recordSipMessage(sipMsg, "send", conn->localAddress.to_string(), conn->remoteAddress.to_string());
            return true;
        }
        return false;
    }

    if (sipMsg->isRequest())
    {
        conn = getConnByUsername(SimpleSipSession::SipUri::user(SimpleSipSession::SipUri::uri(sipMsg->getHeader(SimpleSipSession::Header::CONTACT))), callId);
        if (conn)
        {
            if (conn->send(sessId, sipMsg))
            {
                _eventManager.recordSipMessage(sipMsg, "send", conn->localAddress.to_string(), conn->remoteAddress.to_string());
                return true;
            }
            return false;
        }
    }
    else
    {
        UTIL_LOG_ERR("AsbcServer", "content:not sip request message:" + Common::String(data.c_str()) + " call-id:" + callId.c_str());
    }

    UTIL_LOG_WRN("AsbcServer", "content:outgoing connection not found for sip message:" + Common::String(data.c_str()) + " call-id:" + callId.c_str());
    return false;
}

bool AsbcServer::isAliveIn()
{
    Common::RecLock lock(_mutex);
    return !_activeRegConnections.empty();
}

int AsbcServer::getFreePercent()
{
    Common::RecLock lock(_mutex);
    if (_regConnections.empty())
        return 0;

    if (!_concurrentCallLimit)
        return 100;

    return (int)(_activeRegConnections.size() * 100 / _regConnections.size());
}

void AsbcServer::recv(const SipConnPtr &conn, const SimpleSipSession::SipMessagePtr &msg, const Common::String &logInfo)
{
    SipConnServerManagerPtr line = _sipLine;
    if (!line)
    {
        UTIL_LOG_WRN("AsbcServer", "content:receive connection no line, recv " + logInfo);
        return;
    }

    std::string callId = msg->getHeader(SimpleSipSession::Header::CALL_ID);
    updateCallIdCache(callId, SipRegConnPtr::dynamicCast(conn)->username());
    std::string sessId = line->getSessId(callId);
    UTIL_LOG_IFO("AsbcServer", "content:connection recv " + (sessId.empty() ? Common::String() : Common::String("sessId:") + sessId.c_str() + " ") + logInfo);
    line->recv(msg->data());
    _eventManager.recordSipMessage(msg, "recv", conn->localAddress.to_string(), conn->remoteAddress.to_string());
}

SipConnPtr AsbcServer::selectConn(const std::vector<std::string> &usedConnIds)
{
    Common::RecLock lock(_mutex);

    if (!_activeRegConnections.empty())
    {
        size_t index = Common::getCurTicks() % _activeRegConnections.size();
        SipConnPtr conn = _activeRegConnections[index];
        if (std::find(usedConnIds.begin(), usedConnIds.end(), conn->connId()) != usedConnIds.end())
            return nullptr;

        _activeRegConnections.erase(_activeRegConnections.begin() + index);
        return conn;
    }

    UTIL_LOG_WRN("AsbcServer", "content:no registered connection.");
    return nullptr;
}

bool AsbcServer::registerAccount(const std::string &username, const std::string &authname, const std::string &password, int port)
{
    SipRegConnPtr conn;
    try
    {
        conn = new SipRegConn(this, nullptr, _sipLine->getSipDriver(), {username, authname, password, _accountManager->getRealm(), _accountManager->getExpireSeconds()}, _concurrentCallLimit);
    }
    catch (const std::exception &e)
    {

        UTIL_LOG_WRN("AsbcServer", "content:create connection for username:" + Common::String(username.c_str()) + " failed");
        return false;
    }

    Common::String localHost = "0.0.0.0";
    int localPort = port;
    SimpleSipSession::SipHostPort serverAddress = _accountManager->getRemoteAddress();
    conn->sender = _app->getDriver()->connect("udp", localHost, localPort, serverAddress.host.c_str(), serverAddress.port, conn.get(), true);
    if (!conn->sender)
    {
        UTIL_LOG_WRN("AsbcServer", "content:connect username:" + Common::String(username.c_str()) + " address:" + Common::String(serverAddress.to_string().c_str()) + " failed");
        return false;
    }
    conn->updateConnectionInfo();
    conn->sender->getLocal(localHost, localPort);

    _regConnections[username] = conn;
    UTIL_LOG_IFO("AsbcServer", "content:connect username:" + Common::String(username.c_str()) + " connection:" + conn->connectionInfo + " ok");
    return true;
}

void AsbcServer::unregisterAccount(const std::string &username)
{
    auto it = _regConnections.find(username);
    if (it == _regConnections.end())
        return;

    UTIL_LOG_IFO("AsbcServer", "content:unregister username:" + Common::String(username.c_str()) + " connection:" + it->second->connectionInfo);
    it->second->close();
}

void AsbcServer::updateCallIdCache(const std::string &callId, const std::string &username)
{
    Common::RecLock lock(_mutex);
    _callId2Username.put(callId, username);
}

SipConnPtr AsbcServer::getConnByCallId(const std::string &callId)
{
    Common::RecLock lock(_mutex);

    std::string username;
    if (!_callId2Username.get(callId, username))
        return nullptr;

    auto it = _regConnections.find(username);
    if (it == _regConnections.end())
        return nullptr;

    return it->second;
}

SipConnPtr AsbcServer::getConnByUsername(const std::string &username, const std::string &callId)
{
    Common::RecLock lock(_mutex);
    auto it = _regConnections.find(username);
    if (it == _regConnections.end())
        return nullptr;

    _callId2Username.put(callId, username);
    return it->second;
}

} // namespace SipMpCall
