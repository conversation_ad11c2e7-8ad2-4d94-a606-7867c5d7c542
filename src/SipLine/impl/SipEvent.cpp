//
// *****************************************************************************
// Copyright (c) 2025 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//

#include "SipEvent.h"
#include "SimpleSipSession/SipTypes.h"

namespace SipMpCall
{

void SipEventManager::saveSipEvent(const std::string &callId, const std::string &type, const std::string &title, const std::string &detail, const std::string localAddr, const std::string remoteAddr)
{
    Common::RecLock lock(_mutex);
    SipLine::SessEvent event;
    event.type = type;
    event.timestamp = Common::getCurTimeMs();
    event.time = Common::getTimeStr("%04d%02d%02d-%02d:%02d:%02d.%03d", event.timestamp).c_str();
    event.title = title;
    event.detail = detail;

    _sipStatuses[callId].events.push_back(event);
    if (_sipStatuses[callId].localAddr.empty() )
    {
        _sipStatuses[callId].localAddr = localAddr;
        _sipStatuses[callId].remoteAddr = remoteAddr;
    }
}

SipLine::SessStatus SipEventManager::getSessEvents(const std::string &callId)
{
    Common::RecLock lock(_mutex);
    auto it = _sipStatuses.find(callId);
    if (it != _sipStatuses.end())
    {
        return it->second;
    }
    return SipLine::SessStatus();
}

void SipEventManager::schd()
{
    Common::RecLock lock(_mutex);
    if (_keepTermedSession)
        return;

    for (auto it = _sipStatuses.begin(); it != _sipStatuses.end();)
    {
        if (Common::getCurTimeMs() - it->second.events.back().timestamp > _expiresMs)
            it = _sipStatuses.erase(it);
        else
            it++;
    }
}

void SipEventManager::recordSipMessage(const SimpleSipSession::SipMessagePtr &msg, const std::string &direction, const std::string localAddr, const std::string remoteAddr)
{
    if (!msg)
        return;

    std::string callId = msg->getHeader(SimpleSipSession::Header::CALL_ID);
    if (callId.empty())
        return;

    std::string method;
    std::string title;

    int statusCode;
    std::string reasonPhrase;
    if (msg->getMethod(method))
    {
        if (method == "OPTIONS" || method == "REGISTER")
            return;
        title = method;
    }
    if (msg->getStatusLine(statusCode, reasonPhrase))
        title = std::to_string(statusCode) + " " + reasonPhrase + " (" + method + ")";

    if (!title.empty())
    {
        saveSipEvent(callId, direction, title, msg->data(), localAddr, remoteAddr);
    }
}

} // namespace SipMpCall
