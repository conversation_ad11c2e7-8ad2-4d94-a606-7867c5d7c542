//
// *****************************************************************************
// Copyright (c) 2025 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//

#pragma once

#include <chrono>
#include <queue>

namespace SipMpCall
{

class SlidingWindowCounter
{
public:
    SlidingWindowCounter(int windowTimeMs)
        : _windowTimeMs(windowTimeMs)
        , _sum(0)
    {
    }

    void add(int count = 1)
    {
        auto now = std::chrono::steady_clock::now();
        _events.push({now, count});
        _sum += count;
        removeExpiredEvents(now);
    }

    void reset()
    {
        _sum = 0;
        while (!_events.empty())
            _events.pop();
    }

    int getSum()
    {
        auto now = std::chrono::steady_clock::now();
        removeExpiredEvents(now);
        return _sum;
    }

private:
    struct Event
    {
        std::chrono::steady_clock::time_point timestamp;
        int count;
    };

    void removeExpiredEvents(std::chrono::steady_clock::time_point now)
    {
        while (!_events.empty() && std::chrono::duration_cast<std::chrono::milliseconds>(now - _events.front().timestamp).count() > _windowTimeMs)
        {
            _sum -= _events.front().count;
            _events.pop();
        }
    }

private:
    const int _windowTimeMs;
    int _sum;
    std::queue<Event> _events;
};

} // namespace SipMpCall
