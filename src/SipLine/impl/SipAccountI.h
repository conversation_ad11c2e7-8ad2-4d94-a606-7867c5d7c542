//
// *****************************************************************************
// Copyright (c) 2024 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//

#pragma once

#include "Common/Common.h"
#include "SipAccount.h"
#include "SimpleSipSession/SipFlow.h"
#include "Util/PortManager.h"

namespace SipMpCall
{

using SipAccountMap = std::map<std::string, std::string>;

struct SipAccountOperation
{
    enum Type
    {
        Add,
        Remove,
    };

    SipAccountOperation(const std::string &username, const std::string &authname, const std::string &password)
        : type(Add)
        , username(username)
        , authname(authname)
        , password(password)
    {
    }

    SipAccountOperation(const std::string &username)
        : type(Remove)
        , username(username)
    {
    }

    Type type;
    std::string username;
    std::string authname;
    std::string password;
};

class SipAccountManagerI : public SipAccountManager
{
public:
    SipAccountManagerI(const Common::ApplicationPtr &app, const SipAccountManagerListenerPtr &listener)
        : _app(app)
        , _listener(listener)
        , _expireSeconds(3600)
    {
    }

    bool activate(Common::String &reason) override;
    void deactivate() override;
    void schd() override;
    void updateConfigs() override;

    SimpleSipSession::SipHostPort getRemoteAddress() override { return _remoteAddress; }
    std::string getRealm() override { return _realm; }
    int getExpireSeconds() override { return _expireSeconds; }

private:
    bool readLocalPorts();

private:
    Common::ApplicationPtr _app;
    SipAccountManagerListenerPtr _listener;
    SimpleSipSession::SipHostPort _remoteAddress;
    std::string _realm;
    int _expireSeconds;
    std::map<std::string, PortsPtr> _activeAccounts;
    std::list<SipAccountOperation> _operations;
    PortManagerPtr _portManager;
};

} // namespace SipMpCall