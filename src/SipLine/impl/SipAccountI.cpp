//
// *****************************************************************************
// Copyright (c) 2024 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//

#include "SipAccountI.h"
#include "Common/Common.h"
#include "Common/Util.h"
#include "SimpleSipSession/SipFlow.h"

namespace SipMpCall
{

extern bool readAddressConfig(const Common::ApplicationPtr &app, const char *key, SimpleSipSession::SipHostPort &address);

bool readUsernamePassword(const Common::ApplicationPtr &app, std::list<SipAccountOperation> &operations)
{
    Common::String value;
    if (!app->getAppConfig("SipCall.Registration.Accounts", value))
        return false;

    std::vector<Common::String> segs;
    value.split(segs, " ");
    for (auto &seg : segs)
    {
        int pos = seg.find('|');
        if (pos <= 0 || pos >= seg.size() - 1)
            continue;

        std::string username = seg.substr(0, pos).c_str();
        std::string authname = username;
        std::string password = seg.substr(pos + 1).c_str();
        size_t pos2 = password.find("|");
        if (pos != std::string::npos)
        {
            authname = password.substr(0, pos2).c_str();
            password = password.substr(pos2 + 1).c_str();
        }

        operations.push_back(SipAccountOperation(username, authname, password));
        UTIL_LOGFMT_IFO("SipAccountManager", "content:add account username:%s", username.c_str());
    }

    return !operations.empty();
}

bool SipAccountManagerI::activate(Common::String &reason)
{
    if (!readAddressConfig(_app, "SipCall.RemoteAddress", _remoteAddress))
    {
        UTIL_LOG_WRN("SipAccountManager", "content:no valid remote address config.");
        reason = "NoValidRemoteAddress";
        return false;
    }

    Common::String value;
    if (_app->getAppConfig("SipCall.Registration.Realm", value))
        _realm = value.c_str();
    else
        _realm = _remoteAddress.to_string();

    if (!_app->getAppConfigAsInt("SipCall.Registration.ExpireSeconds", _expireSeconds))
        _expireSeconds = 3600;

    readLocalPorts();
    readUsernamePassword(_app, _operations);

    UTIL_LOG_IFO("SipAccountManager", "content:activate success.");
    return true;
}

void SipAccountManagerI::deactivate()
{
    UTIL_LOG_IFO("SipAccountManager", "content:deactivate.");
    _listener = 0;
    _portManager = 0;
}

void SipAccountManagerI::schd()
{
    auto listener = _listener;
    auto portManager = _portManager;
    if (!listener)
        return;

    unsigned int startTicks = Common::getCurTicks();
    do
    {
        auto it = _operations.begin();
        if (it == _operations.end())
            break;

        if (it->type == SipAccountOperation::Add)
        {
            auto it2 = _activeAccounts.find(it->username);
            if (it2 != _activeAccounts.end())
            {
                _activeAccounts.erase(it2);
                _listener->unregisterAccount(it->username);
            }

            PortsPtr ports;
            if (portManager)
            {
                ports = portManager->allocatePort();
                if (!ports)
                {
                    UTIL_LOG_ERR("SipAccountManager", "content:allocate port failed for account:" + Common::String(it->username.c_str()));
                    _operations.erase(it);
                    continue;
                }
            }

            if (listener->registerAccount(it->username, it->authname, it->password, ports ? (*ports)[0] : 0))
                _activeAccounts.insert(std::make_pair(it->username, ports));
        }
        else if (it->type == SipAccountOperation::Remove)
        {
            auto it2 = _activeAccounts.find(it->username);
            if (it2 != _activeAccounts.end())
            {
                _activeAccounts.erase(it2);
                _listener->unregisterAccount(it->username);
            }
        }

        _operations.erase(it);
    } while (Common::getCurTicks() - startTicks < 10);
}

void SipAccountManagerI::updateConfigs()
{
}

bool SipAccountManagerI::readLocalPorts()
{
    Common::String value;
    if (!_app->getAppConfig("SipCall.Registration.LocalPorts", value))
        return false;

    int pos = value.find('-');
    int lowerBound, upperBound;
    if (pos <= 0 || pos >= value.size() - 1)
    {
        lowerBound = value.toInt(0);
        upperBound = lowerBound;
    }
    else
    {
        lowerBound = value.substr(0, pos).toInt(0);
        upperBound = value.substr(pos + 1).toInt(0);

    }

    if (lowerBound <= 0 || lowerBound >= 65535 || upperBound <= 0 || upperBound >= 65535 || lowerBound > upperBound)
    {
        UTIL_LOG_IFO("SipAccountManager", "content:invalid local ports config:" + value);
        return false;
    }

    _portManager = PortManager::create(lowerBound, upperBound, 1);
    if (!_portManager)
    {
        UTIL_LOG_ERR("SipAccountManager", "content:create port manager failed with config:" + value);
        return false;
    }

    UTIL_LOG_IFO("SipAccountManager", "content:local ports config:" + value);
    return true;
}

SipAccountManagerPtr SipAccountManager::create(const Common::ApplicationPtr &app, const SipAccountManagerListenerPtr &listener)
{
    try
    {
        SipAccountManagerI *mgr = new SipAccountManagerI(app, listener);
        return mgr;
    }
    catch (const std::exception &e)
    {
        UTIL_LOGFMT_ERR("SipAccountManager", "content:create failed, exception:%s", e.what());
        return nullptr;
    }
}

} // namespace SipMpCall
