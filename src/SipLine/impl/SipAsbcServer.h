//
// *****************************************************************************
// Copyright (c) 2025 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//

#pragma once

#include "Util/LRUCache.h"
#include "SipConn.h"
#include "SipAccount.h"
#include "SimpleSipSession/SipFlowRegistration.h"
#include "ServiceUtil/Log.h"
#include "SimpleSipSession/SipUri.h"

namespace SipMpCall
{

class SipRegConn : public SipConn, public SimpleSipSession::SipTransport, public SimpleSipSession::Registration::Listener
{
public:
    SipRegConn(const SipConnServerPtr _server, const Common::NetSenderPtr &_sender, const SimpleSipSession::SipDriverPtr &sipDriver, const SimpleSipSession::Registration::Config &config, bool concurrentCallLimit)
        : SipConn(_server, _sender)
        , _sipDriver(sipDriver)
        , _config(config)
        , _registered(false)
        , _concurrentCallLimit(concurrentCallLimit)
        , _duringCall(false)
        , _lastCrlfSendTicks(Common::getCurTicks() - 3600000)
        , _keepAliveInterval(30000)
    {
    }

    // override SipConn
    virtual std::string connId() override { return _config.username + "@" + remoteAddress.to_string(); }
    virtual void close() override;
    virtual void schd() override;
    virtual std::string callerUri(const std::string &number, enum SimpleSipSession::SipUriFormat format = SimpleSipSession::SipUriFormat::SipUri, const std::string &prefix = "") override;
    virtual std::string calleeUri(const std::string &number, enum SimpleSipSession::SipUriFormat format = SimpleSipSession::SipUriFormat::SipUri, const std::string &prefix = "") override;

    std::string username() const { return _config.username; }

    bool isPeerAliveIn(unsigned int seconds);
    bool isClosed();

    virtual bool send(const std::string &sessId, const SimpleSipSession::SipMessagePtr &msg) override;
    virtual void recv(const unsigned char *data, int dataLen) override;

    // implement SimpleSipSession::SipTransport
    virtual std::string protocol() override { return "udp"; }
    virtual SimpleSipSession::SipHostPort localHostPort() override { return localAddress; }
    virtual SimpleSipSession::SipHostPort remoteHostPort() override { return remoteAddress; }

    // implement SimpleSipSession::SipFlowListener
    virtual void onError() override;
    virtual void onSendMessage(const SimpleSipSession::SipMessagePtr &message) override;

    // implement SimpleSipSession::Registration::Listener
    virtual void onRegistered() override;
    virtual void onRegisterFailed() override;
    virtual void onUnregistered() override;

private:
    void evictExpiredCallIds();
    void updateCallingState(const SimpleSipSession::SipMessagePtr &msg);

private:
    SimpleSipSession::SipDriverPtr _sipDriver;
    SimpleSipSession::Registration::Config _config;
    SimpleSipSession::Registration::FlowPtr _registration;
    bool _registered;
    bool _concurrentCallLimit;
    bool _duringCall;
    unsigned int _lastCrlfSendTicks;
    int _keepAliveInterval;
    ::ServiceUtil::LogEveryNSecState _logState;
};

typedef Common::Handle<SipRegConn> SipRegConnPtr;

class AsbcServer : public SipConnServer, public SipAccountManagerListener
{
public:
    AsbcServer(const SipConnServerManagerPtr &sipLine, const Common::ApplicationPtr &app)
        : _sipLine(sipLine)
        , _app(app)
        , _concurrentCallLimit(false)
        , _callId2Username(10000)
    {
    }

    // implement SipConnServer
    bool init(Common::String &reason) override;
    void schd() override;
    void close() override;
    bool send(const std::string &data) override;
    bool isAliveIn() override;
    int getFreePercent() override;
    void recv(const SipConnPtr &conn, const SimpleSipSession::SipMessagePtr &msg, const Common::String &logInfo) override;
    SipConnPtr selectConn(const std::vector<std::string> &usedConnIds = std::vector<std::string>()) override;
    SipConnPtr getConnByCallId(const std::string &callId) override;
    void countResult(const std::string &connId, bool success) override {};

    // implement SipAccountManagerListener
    bool registerAccount(const std::string &username, const std::string &authname, const std::string &password, int port) override;
    void unregisterAccount(const std::string &username) override;

private:
    void updateCallIdCache(const std::string &callId, const std::string &username);
    SipConnPtr getConnByUsername(const std::string &username, const std::string &callId);

private:
    Common::RecMutex _mutex;
    SipConnServerManagerPtr _sipLine;
    Common::ApplicationPtr _app;
    bool _concurrentCallLimit;

    std::map<std::string, SipRegConnPtr> _regConnections;
    std::vector<SipRegConnPtr> _activeRegConnections;
    LRUCache<std::string, std::string> _callId2Username;

    SipAccountManagerPtr _accountManager;
};

} // namespace SipMpCall
