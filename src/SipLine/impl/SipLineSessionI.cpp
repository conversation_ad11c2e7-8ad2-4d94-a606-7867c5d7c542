//
// *****************************************************************************
// Copyright (c) 2025 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//

#include "SipLineSessionI.h"
#include "SipAdapter/SipCallInterface.h"
#include "SipLineI.h"

#include "SimpleSipSession/SipTypes.h"
#include "SimpleSipSession/SipMessage.h"

namespace SipMpCall
{

bool SipLineSessionI::open(const Common::ApplicationPtr &app, const SipClient::SipCallSessionPtr &session, const SipLineSessionListenerPtr &listener)
{
    if (!session || !listener)
    {
        UTIL_LOG_ERR("SipLineSession", "content:open sip line session failed, session or listener is null");
        return false;
    }

    if (session)
        _session = session;
    _listener = listener;
    return true;
}

void SipLineSessionI::close()
{
    SipLineSessionManager::ObjectId sessId;
    std::vector<SipLineSessionManager::ObjectId> callIds;

    do
    {
        std::lock_guard<std::mutex> lock(_mutex);
        _session = 0;
        _listener = 0;
        sessId = SipLineSessionManager::ObjectId(SipLineSessionIdType::SipLineSessionId, _sessId);
        for (const auto &callId : _sipCallIds)
            callIds.push_back(SipLineSessionManager::ObjectId(SipLineSessionIdType::SipLineSessionCallId, callId));
    } while (0);

    UTIL_LOG_IFO_BEGIN
        std::string callIdInfo;
        for (auto &callId : callIds)
            callIdInfo += callId.id + ";";
        UTIL_LOG("SipLineSession", "content:close sessId:" + Common::String(sessId.id.c_str()) + " callIds:" + callIdInfo.c_str());
    UTIL_LOG_END

    remove(_mamager);
}

void SipLineSessionI::setConnId(const std::string &connId)
{
    std::lock_guard<std::mutex> lock(_mutex);
    _currentConnId = connId;
}

void SipLineSessionI::setSessId(const std::string &sessId)
{
    std::lock_guard<std::mutex> lock(_mutex);
    _sessId = sessId;
    insert(_mamager, SipLineSessionManager::ObjectId(SipLineSessionIdType::SipLineSessionId, sessId));
}

std::string SipLineSessionI::getSessId() const
{
    std::lock_guard<std::mutex> lock(_mutex);
    return _sessId;
}

void SipLineSessionI::setCallId(const std::string &callId)
{
    std::lock_guard<std::mutex> lock(_mutex);
    _sipCallIds.push_back(callId);
    insert(_mamager, SipLineSessionManager::ObjectId(SipLineSessionIdType::SipLineSessionCallId, callId));
}

std::string SipLineSessionI::getCallId() const
{
    std::lock_guard<std::mutex> lock(_mutex);
    if (_sipCallIds.empty())
    {
        UTIL_LOG_WRN("SipLineSession", "content:getCallId no callId found");
        return "";
    }
    return _sipCallIds.back();
}

vector<std::string> SipLineSessionI::getCallIds() const
{
    std::lock_guard<std::mutex> lock(_mutex);
    if (_sipCallIds.empty())
        UTIL_LOG_WRN("SipLineSession", "content:getCallId no callId found");

    return _sipCallIds;
}

#define CHECK_INVOKE(_obj, _func, _defret, _log)         \
    auto __obj = _obj;                                   \
    if (!__obj)                                          \
    {                                                    \
        UTIL_LOG_WRN("SipLineSession", "content:" _log); \
        return _defret;                                  \
    }                                                    \
    return __obj->_func

#define CHECK_INVOKE_VOID(_obj, _func, _log)             \
    auto __obj = _obj;                                   \
    if (!__obj)                                          \
    {                                                    \
        UTIL_LOG_DBG("SipLineSession", "content:" _log); \
        return;                                          \
    }                                                    \
    __obj->_func

#define CHECK_CURRENT_SESSION(__session, _log)                              \
    if (_session != __session)                                              \
    {                                                                       \
        __session->SipTerm();                                               \
        UTIL_LOG_DBG("SipLineSession", "content:skip session event " _log); \
        return;                                                             \
    }

bool SipLineSessionI::SipSetContactUri(const std::string &pcContactUri)
{
    CHECK_INVOKE(_session, SipSetContactUri(pcContactUri), false, "session is null");
}

bool SipLineSessionI::SipAlert(const std::string &pcAnswerSdp)
{
    CHECK_INVOKE(_session, SipAlert(pcAnswerSdp), false, "session is null");
}

bool SipLineSessionI::SipAnswer(const std::string &pcAnswerSdp, const SipClient::SipCallExtHdrs &pcExtHdrs)
{
    CHECK_INVOKE(_session, SipAnswer(pcAnswerSdp, pcExtHdrs), false, "session is null");
}

bool SipLineSessionI::SipTerm()
{
    _listener = 0;
    auto session = _session;
    if (!session || !session->SipTerm())
    {
        if (!session)
            UTIL_LOG_WRN("SipLineSession", "content:sip term session is null");
        _termed = true;
        return false;
    }

    return true;
}

bool SipLineSessionI::SipUpdate(const std::string &pcOfferSdp)
{
    CHECK_INVOKE(_session, SipUpdate(pcOfferSdp), false, "session is null");
}

bool SipLineSessionI::SipUpdateRsp(const std::string &pcAnswerSdp)
{
    CHECK_INVOKE(_session, SipUpdateRsp(pcAnswerSdp), false, "session is null");
}

bool SipLineSessionI::SipAck(const std::string &pcAnswerSdp)
{
    CHECK_INVOKE(_session, SipAck(pcAnswerSdp), false, "session is null");
}

bool SipLineSessionI::GetCalledUri(std::string &ppcDispName, std::string &ppcUri)
{
    CHECK_INVOKE(_session, GetCalledUri(ppcDispName, ppcUri), false, "session is null");
}

bool SipLineSessionI::GetPeerUri(std::string &ppcDispName, std::string &ppcUri)
{
    CHECK_INVOKE(_session, GetPeerUri(ppcDispName, ppcUri), false, "session is null");
}

bool SipLineSessionI::GetCallTermedReason(std::string &ppcSipPhase, std::string &ppcReason)
{
    CHECK_INVOKE(_session, GetCallTermedReason(ppcSipPhase, ppcReason), false, "session is null");
}

void SipLineSessionI::onCallIncoming(const SipClient::SipCallSessionPtr &session, const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg)
{
    _session = session;
    SimpleSipSession::SipMessagePtr msg = SimpleSipSession::SipMessage::create(pcSipMsg);
    setCallId(msg->getHeader(SimpleSipSession::HeaderCName::CALL_ID));
    CHECK_INVOKE_VOID(_listener, onCallIncoming(pcSdp, iStatCode, bSuptVideo, pcSipMsg), "listener is null");
}

void SipLineSessionI::onCallOutgoing(const SipClient::SipCallSessionPtr &session, const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg)
{
    CHECK_CURRENT_SESSION(session, "outgoing");
    CHECK_INVOKE_VOID(_listener, onCallOutgoing(pcSdp, iStatCode, bSuptVideo, pcSipMsg), "listener is null");
}

void SipLineSessionI::onCallAlerted(const SipClient::SipCallSessionPtr &session, const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg)
{
    CHECK_CURRENT_SESSION(session, "alerted");
    CHECK_INVOKE_VOID(_listener, onCallAlerted(pcSdp, iStatCode, bSuptVideo, pcSipMsg), "listener is null");
}

void SipLineSessionI::onCallAnswered(const SipClient::SipCallSessionPtr &session, const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg)
{
    CHECK_CURRENT_SESSION(session, "answered");
    CHECK_INVOKE_VOID(_listener, onCallAnswered(pcSdp, iStatCode, bSuptVideo, pcSipMsg), "listener is null");
}

void SipLineSessionI::onCallUpdate(const SipClient::SipCallSessionPtr &session, const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg)
{
    CHECK_CURRENT_SESSION(session, "update");
    CHECK_INVOKE_VOID(_listener, onCallUpdate(pcSdp, iStatCode, bSuptVideo, pcSipMsg), "listener is null");
}

void SipLineSessionI::onCallRequestUpdate(const SipClient::SipCallSessionPtr &session, const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg)
{
    CHECK_CURRENT_SESSION(session, "request update");
    CHECK_INVOKE_VOID(_listener, onCallRequestUpdate(pcSdp, iStatCode, bSuptVideo, pcSipMsg), "listener is null");
}

void SipLineSessionI::onCallResponseUdate(const SipClient::SipCallSessionPtr &session, const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg)
{
    CHECK_CURRENT_SESSION(session, "response update");
    CHECK_INVOKE_VOID(_listener, onCallResponseUdate(pcSdp, iStatCode, bSuptVideo, pcSipMsg), "listener is null");
}

void SipLineSessionI::onCallConnected(const SipClient::SipCallSessionPtr &session, const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg)
{
    CHECK_CURRENT_SESSION(session, "connected");
    CHECK_INVOKE_VOID(_listener, onCallConnected(pcSdp, iStatCode, bSuptVideo, pcSipMsg), "listener is null");
}

void SipLineSessionI::onCallRequestModify(const SipClient::SipCallSessionPtr &session, const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg)
{
    CHECK_CURRENT_SESSION(session, "request modify");
    CHECK_INVOKE_VOID(_listener, onCallRequestModify(pcSdp, iStatCode, bSuptVideo, pcSipMsg), "listener is null");
}

void SipLineSessionI::onCallResponseModify(const SipClient::SipCallSessionPtr &session, const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg)
{
    CHECK_CURRENT_SESSION(session, "response modify");
    CHECK_INVOKE_VOID(_listener, onCallResponseModify(pcSdp, iStatCode, bSuptVideo, pcSipMsg), "listener is null");
}

void SipLineSessionI::onCallTerminated(const SipClient::SipCallSessionPtr &session, const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg)
{
    do
    {
        std::lock_guard<std::mutex> lock(_mutex);

        if (_session != session)
        {
            UTIL_LOG_DBG("SipLineSession", "content:skip session event terminated");
            return;
        }

        _termed = true;
    } while (0);

    CHECK_INVOKE_VOID(_listener, onCallTerminated(pcSdp, iStatCode, bSuptVideo, pcSipMsg), "listener is null");
    _listener = 0;
}

bool SipLineCalloutSession::open(const Common::ApplicationPtr &app, const SipClient::SipCallSessionPtr &session, const SipLineSessionListenerPtr &listener)
{
    if (!SipLineSessionI::open(app, session, listener))
        return false;

    int value;
    if (app->getAppConfigAsInt("SipCall.ResponseTimeoutSeconds", value))
    {
        if (value < 0)
            value = 0;
        else if (value > 30)
            value = 30;
        _responseTimeoutMs = value * 1000;
    }

    return true;
}

bool SipLineCalloutSession::schd(SipLineI *sipLine)
{
    std::lock_guard<std::mutex> lock(_mutex);

    if (_termed)
    {
        if (!_currentConnId.empty())
        {
            SipConnServerPtr sipServer = sipLine->_sipConnServer;
            if (sipServer)
                sipServer->countResult(_currentConnId, _responsed);
            _currentConnId.clear();
        }

        return true;
    }

    if (_responseTimeoutMs == 0)
        return false;

    if (_responsed)
    {
        if (!_currentConnId.empty())
        {
            SipConnServerPtr sipServer = sipLine->_sipConnServer;
            if (sipServer)
                sipServer->countResult(_currentConnId, true);
            _currentConnId.clear();
        }

        return false;
    }

    if (Common::getCurTicks() - _startTicks > _responseTimeoutMs)
    {
        _startTicks = Common::getCurTicks();

        if (_currentConnId.empty())
        {
            UTIL_LOG_DBG("SipLineCalloutSession", "content:sip call retry wait last sip call terminated, caller:" + Common::String(_caller.c_str()) + " callee:" + _callee.c_str());
            return false;
        }

        SipConnServerPtr sipServer = sipLine->_sipConnServer;
        if (!sipServer)
        {
            _currentConnId.clear();
            UTIL_LOG_WRN("SipLineCalloutSession", "content:sip call retry no valid server, caller:" + Common::String(_caller.c_str()) + " callee:" + _callee.c_str());
            return false;
        }

        sipServer->countResult(_currentConnId, false);
        _connIds.push_back(_currentConnId);
        _currentConnId.clear();

        SipConnPtr conn = sipServer->selectConn(_connIds);
        if (!conn)
        {
            UTIL_LOG_WRN("SipLineCalloutSession", "content:sip call retry no connection, caller:" + Common::String(_caller.c_str()) + " callee:" + _callee.c_str());
            return false;
        }

        _session->SipTerm();
        _session = nullptr;

        if (!__retryCallout(sipLine, conn))
        {
            _termed = true;
            SipLineSessionListenerPtr listener = _listener;
            if (listener)
                listener->onCallTerminated("", 0, false, "");
            _listener = 0;
            return false;
        }
    }

    return false;
}

bool SipLineCalloutSession::GetCallTermedReason(std::string &ppcSipPhase, std::string &ppcReason)
{
    auto session = _session;

    if (!session)
    {
        if (!_responsed)
        {
            ppcSipPhase = "Timeout";
            ppcReason = "NoResponse";
        }
        else
        {
            ppcSipPhase = "Unknown";
            ppcReason = "Unknown";
        }

        return true;
    }

    return session->GetCallTermedReason(ppcSipPhase, ppcReason);
}

void SipLineCalloutSession::onCallOutgoing(const SipClient::SipCallSessionPtr &session, const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg)
{
    CHECK_CURRENT_SESSION(session, "outgoing");
    _responsed = true;
    CHECK_INVOKE_VOID(_listener, onCallOutgoing(pcSdp, iStatCode, bSuptVideo, pcSipMsg), "listener is null");
}

void SipLineCalloutSession::onCallAlerted(const SipClient::SipCallSessionPtr &session, const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg)
{
    CHECK_CURRENT_SESSION(session, "alerted");
    _responsed = true;
    CHECK_INVOKE_VOID(_listener, onCallAlerted(pcSdp, iStatCode, bSuptVideo, pcSipMsg), "listener is null");
}

void SipLineCalloutSession::onCallAnswered(const SipClient::SipCallSessionPtr &session, const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg)
{
    CHECK_CURRENT_SESSION(session, "answered");
    _responsed = true;
    CHECK_INVOKE_VOID(_listener, onCallAnswered(pcSdp, iStatCode, bSuptVideo, pcSipMsg), "listener is null");
}

void SipLineCalloutSession::onCallTerminated(const SipClient::SipCallSessionPtr &session, const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg)
{
    do
    {
        std::lock_guard<std::mutex> lock(_mutex);
        if (_session != session)
        {
            UTIL_LOG_DBG("SipLineSession", "content:skip session event terminated");
            return;
        }

        _termed = true;
        if (iStatCode >= 300 && iStatCode < 699)
            _responsed = true;
    } while (0);

    CHECK_INVOKE_VOID(_listener, onCallTerminated(pcSdp, iStatCode, bSuptVideo, pcSipMsg), "listener is null");
    _listener = 0;
}

bool SipLineCalloutSession::__retryCallout(SipLineI *sipLine, const SipConnPtr &conn)
{
    SipClient::SipCallInterfacePtr sipCall = SipClient::SipCallInterface::getInstance();
    if (!sipCall)
    {
        UTIL_LOG_WRN("SipLineCalloutSession", "content:sip call retry no client, caller:" + Common::String(_caller.c_str()) + " callee:" + _callee.c_str());
        return false;
    }

    std::string callId = SimpleSipSession::SipMessage::genCallId(_sessId, _caller, _callee, conn->localAddress.host);
    std::string callerUri = conn->callerUri(_caller, sipLine->_configCallerUriFormat);
    std::string calleeUri = conn->calleeUri(_callee, sipLine->_configCalleeUriFormat, sipLine->_configCalleePrefix.c_str());
    std::string contactUri = sipLine->getContactUri(_caller, conn->localAddress);

    std::map<std::string, std::string> extHdrs = _extHdrs;
    if (sipLine->_configPaiUriFormat != SimpleSipSession::UnknownUri && extHdrs.find("P-Asserted-Identity") == extHdrs.end())
        extHdrs["P-Asserted-Identity"] = conn->callerAddr(_caller, sipLine->_configPaiUriFormat);

    _session = sipCall->SipCall(this, callerUri, calleeUri, callId, contactUri, _offerSdp, sipLine->_configUriParams.c_str(), extHdrs);
    if (!_session)
    {
        UTIL_LOG_WRN("SipLineCalloutSession", "content:sip call retry failed, callerUri:" + Common::String(callerUri.c_str()) + " calleeUri:" + calleeUri.c_str());
        return false;
    }

    _currentConnId = conn->connId();
    _sipCallIds.push_back(callId.c_str());
    insert(_mamager, SipLineSessionManager::ObjectId(SipLineSessionIdType::SipLineSessionCallId, callId));
    UTIL_LOG_IFO("SipLineCalloutSession", "content:sip call retry callId:" + Common::String(callId.c_str()) + " sessId:" + _sessId.c_str() + " callerUri:" + Common::String(callerUri.c_str()) + " calleeUri:" + calleeUri.c_str() + " connId:" + conn->connId().c_str());
    return true;
}

} // namespace SipMpCall
