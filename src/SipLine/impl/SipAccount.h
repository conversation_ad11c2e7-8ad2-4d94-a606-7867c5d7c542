//
// *****************************************************************************
// Copyright (c) 2024 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//

#pragma once

#include "Common/Common.h"
#include "Common/Util.h"
#include "SimpleSipSession/SipTransport.h"

namespace SipMpCall
{

class SipAccountManagerListener;
class SipAccountManager;
typedef Common::Handle<SipAccountManagerListener> SipAccountManagerListenerPtr;
typedef Common::Handle<SipAccountManager> SipAccountManagerPtr;

class SipAccountManagerListener : virtual public Common::Shared
{
public:
    virtual bool registerAccount(const std::string &username, const std::string &authname, const std::string &password, int port) = 0;
    virtual void unregisterAccount(const std::string &username) = 0;
};

class SipAccountManager : virtual public Common::Shared
{
public:
    static SipAccountManagerPtr create(const Common::ApplicationPtr &app, const SipAccountManagerListenerPtr &listener);

    virtual bool activate(Common::String &reason) = 0;
    virtual void deactivate() = 0;
    virtual void schd() = 0;
    virtual void updateConfigs() = 0;

    virtual SimpleSipSession::SipHostPort getRemoteAddress() = 0;
    virtual std::string getRealm() = 0;
    virtual int getExpireSeconds() = 0;
};

} // namespace SipMpCall