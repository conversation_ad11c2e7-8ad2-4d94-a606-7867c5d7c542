//
// *****************************************************************************
// Copyright (c) 2025 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//

#include "SipIsbcServer.h"
#include "SimpleSipSession/SipTypes.h"
#include "SimpleSipSession/SipUri.h"
#include "Common/TypesPub.h"
#include <algorithm>

namespace SipMpCall
{

void SipOutgoingConn::close()
{
    if (_keepAlive)
    {
        _keepAlive->close();
        _keepAlive = 0;
    }
    _sipDriver = 0;
    SipConn::close();
}

void SipOutgoingConn::schd()
{
    if ((_keepAliveConfig.mode & KeepAliveCrlf) && Common::getCurTicks() - _lastCrlfSendTicks > _keepAliveConfig.intervalCrlf * 1000)
    {
        _lastCrlfSendTicks = Common::getCurTicks();
        const unsigned char *crlfcrlf = (const unsigned char *)"\r\n\r\n";
        if (!SipConn::__send(crlfcrlf, 4, "CRLFCRLF"))
            UTIL_LOG_WRN("SipOutConn", "content:connection " + connectionInfo + " send CRLFCRLF failed.");
    }

    if (_keepAliveConfig.mode & KeepAliveSipOption)
    {
        if (!_keepAlive)
        {
            _keepAlive = SimpleSipSession::KeepAlive::create(_sipDriver, this, this, _keepAliveConfig.intervalSip);
            if (!_keepAlive)
                UTIL_LOG_WRN("SipOutConn", "content:connection " + connectionInfo + " create keep alive failed.");
        }
    }
}

bool SipOutgoingConn::isPeerAliveIn(unsigned int seconds)
{
    unsigned int now = Common::getCurTicks();
    if (now - lastRecvTicks < seconds * 1000)
    {
        if (_logState.shouldLog(60))
            UTIL_LOG_IFO("SipOutConn", "content:connection " + connectionInfo + " alive in " + Common::String((int)(now - lastRecvTicks) / 1000) + "s.");
        return true;
    }

    UTIL_LOG_WRN("SipOutConn", "content:connection " + connectionInfo + " not alive in " + Common::String((int)seconds) + "s last:" + Common::String((int)(now - lastRecvTicks) / 1000) + "s ago");
    return false;
}

void SipOutgoingConn::resetAlive(unsigned int seconds)
{
    UTIL_LOG_IFO("SipOutConn", "content:connection " + connectionInfo + " reset alive " + Common::String((int)seconds) + "s ago");
    lastRecvTicks = Common::getCurTicks() - seconds * 1000;
}

void SipOutgoingConn::recoverAlive()
{
    UTIL_LOG_IFO("SipOutConn", "content:connection " + connectionInfo + " recover alive");
    if (_keepAlive)
        _keepAlive->trigger();
}

void SipOutgoingConn::countResult(bool success)
{
    if (success)
    {
        UTIL_LOG_DBG("SipOutConn", "content:connection " + connectionInfo + " count result success");
        _continuousFailedCounter.reset();
        _successCounter.add();
    }
    else
    {
        UTIL_LOG_WRN("SipOutConn", "content:connection " + connectionInfo + " count result failed");
        _continuousFailedCounter.add();
        _failedCounter.add();
    }
}

int SipOutgoingConn::failedPercent()
{
    int success = _successCounter.getSum();
    int failed = _failedCounter.getSum();
    int total = success + failed;
    if (total == 0)
        return 0;
    return failed * 100 / total;
}

void SipOutgoingConn::recv(const unsigned char *data, int dataLen)
{
    if (dataLen == 2 && data[0] == '\r' && data[1] == '\n')
    {
        lastRecvTicks = Common::getCurTicks();
        UTIL_LOG_IFO("SipOutConn", "content:connection recv " + connectionInfoRecv + " CRLF");
        return;
    }
    else if (dataLen == 4 && data[0] == '\r' && data[1] == '\n' && data[2] == '\r' && data[3] == '\n')
    {
        lastRecvTicks = Common::getCurTicks();
        UTIL_LOG_IFO("SipOutConn", "content:connection recv " + connectionInfoRecv + " CRLFCRLF");
        __send((const unsigned char *)"\r\n", 2, "CRLF");
        return;
    }

    SimpleSipSession::SipMessagePtr sipMsg = SimpleSipSession::SipMessage::create(std::string((const char *)data, dataLen));
    if (!sipMsg)
    {
        UTIL_LOG_WRN("SipOutConn", "content:connection recv " + connectionInfoRecv + " size:" + Common::String::formatString("%-5d", dataLen) + " invalid sip message:" + Common::String((const char *)data, dataLen));
        return;
    }

    if (_keepAlive && _keepAlive->recvMessage(sipMsg))
    {
        lastRecvTicks = Common::getCurTicks();
        UTIL_LOG_IFO("IsbcServer", "content:keepalive recv " + connectionInfoRecv + " size:" + Common::String::formatString("%-5d", dataLen) + " digest:" + sipMsg->logInfo().c_str() + " sip message:\n" + Common::String((const char *)data, dataLen));
        return;
    }

    SipConn::__recv(sipMsg, connectionInfoRecv + " size:" + Common::String::formatString("%-5d", dataLen) + " digest:" + sipMsg->logInfo().c_str() + " sip message:\n" + Common::String((const char *)data, dataLen));
}

void SipOutgoingConn::onError()
{
    UTIL_LOG_WRN("SipOutConn", "content:connection " + connectionInfo + " error.");
}

void SipOutgoingConn::onSendMessage(const SimpleSipSession::SipMessagePtr &message)
{
    SipConn::send("", message);
}

bool IsbcServer::listen(const Common::String &host, int port, Common::String &localHost, int &localPort)
{
    _listener = _app->getDriver()->listen("udp", host, port, this, true);
    if (!_listener)
    {
        UTIL_LOG_WRN("IsbcServer", "content:listen " + host + ":" + Common::String(port) + " failed.");
        return false;
    }

    _listener->getLocal(localHost, localPort);
    UTIL_LOG_IFO("IsbcServer", "content:listen " + localHost + ":" + Common::String(localPort));
    return true;
}

bool IsbcServer::connect(const Common::String &host, int port, Common::String &localHost, int &localPort, int weight)
{
    SipOutgoingConnPtr conn;
    try
    {
        conn = new SipOutgoingConn(this, nullptr, _sipLine->getSipDriver(), _configKeepAlive, _localAddress, weight, _lineBreakDetectTimeMs);
    }
    catch (const std::exception &e)
    {

        UTIL_LOG_WRN("IsbcServer", "content:create connection for " + host + ":" + Common::String(port) + " failed");
        return false;
    }

    conn->sender = _app->getDriver()->connect("udp", localHost, localPort, host, port, conn.get(), true);
    if (!conn->sender)
    {
        UTIL_LOG_WRN("IsbcServer", "content:connect " + host + ":" + Common::String(port) + " failed");
        return false;
    }
    conn->updateConnectionInfo();
    conn->sender->getLocal(localHost, localPort);
    std::string connId = conn->connId();

    auto it = _outgoingConnections.find(connId);
    if (it != _outgoingConnections.end())
    {
        it->second->close();
        _outgoingConnections.erase(it);
    }
    _outgoingConnections[connId] = conn;

    UTIL_LOG_IFO("IsbcServer", "content:connect " + host + ":" + Common::String(port) + " connection:" + conn->connectionInfo + " ok");
    return true;
}

bool IsbcServer::init(Common::String &reason)
{
    readKeepAliveConfigs();
    if (!readLineAddrConfig(reason))
        return false;
    readLineBreakConfig();
    return true;
}

void IsbcServer::schd()
{
    SipConnServer::schd();

    Common::RecLock lock(_mutex);

    __checkPendingConnections();
    __checkIncomingConnections();

    for (auto kv : _outgoingConnections)
        kv.second->schd();

    __checkBreakOutgoingConnections();
    __checkActiveOutgoingConnections();
}

void IsbcServer::close()
{
    Common::RecLock lock(_mutex);

    for (auto kv : _outgoingConnections)
        kv.second->close();
    _outgoingConnections.clear();
    _activeOutgoingConnections.clear();
    _breakOutgoingConnections.clear();

    for (auto conn : _pendingConnections)
        conn->close();
    _pendingConnections.clear();

    for (auto kv : _incomingConnections)
        kv.second->close();
    _incomingConnections.clear();

    if (_listener)
    {
        _listener->close();
        _listener = 0;
    }

    _sipLine = 0;
    _app = 0;
}

bool IsbcServer::send(const std::string &data)
{
    // create sip message
    SimpleSipSession::SipMessagePtr sipMsg = SimpleSipSession::SipMessage::create(data);
    if (!sipMsg)
    {
        UTIL_LOG_DBG("IsbcServer", "content:invalid sip message:" + Common::String(data.c_str()));
        return false;
    }

    _lastSipSendTime = Common::getCurTimeMs();
    std::string callId = sipMsg->getHeader(SimpleSipSession::Header::CALL_ID);
    std::string sessId = _sipLine->getSessId(callId);

    SipConnPtr conn = getConnByCallId(callId);
    if (conn)
    {
        if (conn->send(sessId, sipMsg))
        {
            _eventManager.recordSipMessage(sipMsg, "send", conn->localAddress.to_string(), conn->remoteAddress.to_string());
            return true;
        }
        return false;
    }

    if (sipMsg->isRequest())
    {
        std::string method, uri;
        sipMsg->getRequestLine(method, uri);
        conn = getConnByHostport(SimpleSipSession::SipUri::hostport(uri), callId);
        if (conn)
        {
            if (conn->send(sessId, sipMsg))
            {
                _eventManager.recordSipMessage(sipMsg, "send", conn->localAddress.to_string(), conn->remoteAddress.to_string());
                return true;
            }
            return false;
        }

        UTIL_LOG_WRN("IsbcServer", "content:outgoing connection not found for sip message:" + Common::String(data.c_str()) + " call-id:" + callId.c_str());
    }
    else
    {
        UTIL_LOG_ERR("IsbcServer", "content:not sip request message:" + Common::String(data.c_str()) + " call-id:" + callId.c_str());
    }

    return false;
}

bool IsbcServer::isAliveIn()
{
    Common::RecLock lock(_mutex);
    return _outgoingConnections.empty() || !_activeOutgoingConnections.empty();
}

int IsbcServer::getFreePercent()
{
    return isAliveIn() ? 100 : 0;
}

void IsbcServer::recv(const SipConnPtr &conn, const SimpleSipSession::SipMessagePtr &msg, const Common::String &logInfo)
{
    SipConnServerManagerPtr line = _sipLine;
    if (!line)
    {
        UTIL_LOG_WRN("IsbcServer", "content:receive connection no line, recv " + logInfo);
        return;
    }

    std::string callId = msg->getHeader(SimpleSipSession::Header::CALL_ID);
    std::string sessId = line->getSessId(callId);
    UTIL_LOG_IFO("IsbcServer", "content:connection recv " + (sessId.empty() ? Common::String() : Common::String("sessId:") + sessId.c_str() + " ") + logInfo);

    do
    {
        Common::RecLock lock(_mutex);

        std::string connId = conn->remoteAddress.to_string();
        if (_outgoingConnections.find(connId) != _outgoingConnections.end())
        {
            updateCallIdCache(callId, connId);
            break;
        }

        auto itPending = _pendingConnections.find(conn);
        if (itPending != _pendingConnections.end())
        {
            _pendingConnections.erase(itPending);
        }

        auto it = _incomingConnections.find(callId);
        if (it == _incomingConnections.end())
        {
            _incomingConnections.insert(make_pair(callId, conn));
            UTIL_LOG_IFO("IsbcServer", "content:add incoming connection:" + conn->connectionInfo + " call-id:" + callId.c_str());
        }
    } while (0);

    _lastSipRecvTime = Common::getCurTimeMs();
    line->recv(msg->data());
    _eventManager.recordSipMessage(msg, "recv", conn->localAddress.to_string(), conn->remoteAddress.to_string());
}

SipConnPtr IsbcServer::selectConn(const std::vector<std::string> &usedConnIds)
{
    Common::RecLock lock(_mutex);

    if (_activeOutgoingConnections.empty())
    {
        UTIL_LOG_WRN("IsbcServer", "content:no active outgoing connection.");
        return nullptr;
    }

    std::vector<int> weights;
    std::vector<SipOutgoingConnPtr> conns;

    for (auto &conn : _activeOutgoingConnections)
    {
        if (std::find(usedConnIds.begin(), usedConnIds.end(), conn->connId()) != usedConnIds.end())
            continue;

        weights.push_back(conn->weight());
        conns.push_back(conn);
    }

    if (conns.empty())
    {
        Common::String usedConnText;
        for (auto id : usedConnIds)
            usedConnText = usedConnText + id.c_str() + ",";
        Common::String activeConnText;
        for (auto &conn : _activeOutgoingConnections)
            activeConnText = activeConnText + conn->connId().c_str() + ",";
        UTIL_LOG_WRN("IsbcServer", "content:no valid outgoing connection, active:" + activeConnText + " used:" + usedConnText);
        return nullptr;
    }

    SipOutgoingConnPtr conn = conns[selectConnIndex(weights)];
    UTIL_LOG_DBG("IsbcServer", "content:select conn:" + conn->connectionInfo);
    return conn;
}

void IsbcServer::recv(const unsigned char *data, int dataLen)
{
    _lastSipRecvTime = Common::getCurTimeMs();
    SipConnServerManagerPtr line = _sipLine;
    if (line)
        line->recv(std::string((const char *)data, dataLen));
}

Common::NetReceiverPtr IsbcServer::recvConnection(const Common::NetSenderPtr &sender)
{
    SipConnPtr conn;
    try
    {
        conn = new SipConn(this, sender);
        conn->updateConnectionInfo();
    }
    catch (const std::exception &e)
    {
        UTIL_LOG_WRN("IsbcServer", "content:receive connection create failed.");
        return nullptr;
    }

    Common::RecLock lock(_mutex);
    _pendingConnections.insert(conn);
    UTIL_LOG_IFO("IsbcServer", "content:create receive connection.");

    return conn.get();
}

void IsbcServer::readKeepAliveConfigs()
{
    Common::String value;
    if (_app->getAppConfig("SipCall.KeepAlive", value))
    {
        Common::StrVec secs;
        value.split(secs, " ,;");

        int mode = 0;
        int crlfInterval = 30;
        int sipInterval = 30;
        bool valid = false;
        for (auto &sec : secs)
        {
            Common::StrVec fields;
            sec.split(fields, ":");
            if (fields[0] == "crlf" || fields[0] == "CRLF")
            {
                if (fields.size() > 1)
                    crlfInterval = fields[1].toInt(30);
                if (crlfInterval > 0)
                {
                    valid = true;
                    mode |= KeepAliveCrlf;
                }
            }
            else if (fields[0] == "option" || fields[0] == "OPTION" || fields[0] == "OPTIONS")
            {
                if (fields.size() > 1)
                    sipInterval = fields[1].toInt(30);
                if (sipInterval > 0)
                {
                    valid = true;
                    mode |= KeepAliveSipOption;
                }
            }
            else if (fields[0] == "off" || fields[0] == "OFF" || fields[0] == "0")
            {
                valid = true;
                mode = KeepAliveOff;
                break;
            }
        }

        if (valid)
        {
            _configKeepAlive.mode = (enum KeepAliveMode)mode;
            _configKeepAlive.intervalCrlf = crlfInterval;
            _configKeepAlive.intervalSip = sipInterval;
        }
    }

    Common::String stat;
    if (_configKeepAlive.mode & KeepAliveCrlf)
        stat += "CRLF:" + Common::String(_configKeepAlive.intervalCrlf) + " ";
    if (_configKeepAlive.mode & KeepAliveSipOption)
        stat += "OPTIONS:" + Common::String(_configKeepAlive.intervalSip) + " ";
    if (stat.empty())
        stat = "OFF";
    _app->setStatistics("SipCall.KeepAlive", stat);
    UTIL_LOG_IFO("IsbcServer", "content:keep alive config:" + stat);
}

bool IsbcServer::readLineAddrConfig(Common::String &reason)
{
    bool listeningLocal = _app->getAppConfigAsInt("SipCall.NoListening") == 0;

    Common::String actualLocalHost;
    int actualLocalPort;
    bool connecting = false;
    bool listening = false;
    if (readAddressConfig(_app, "SipCall.LocalAddress", _localAddress))
    {
        actualLocalHost = _localAddress.host.c_str();
        actualLocalPort = _localAddress.port;
        if (listeningLocal)
        {
            listening = listen(_localAddress.host.c_str(), _localAddress.port, actualLocalHost, actualLocalPort);
            if (!listening)
            {
                UTIL_LOG_WRN("IsbcServer", "content:listen sip call local address:" + Common::String(_localAddress.to_string().c_str()) + " failed.");
            }
            else
            {
                UTIL_LOG_IFO("IsbcServer", "content:listen sip call local address:" + actualLocalHost + ":" + Common::String(actualLocalPort) + " by listening ip");
            }
        }
        else
        {
            UTIL_LOG_IFO("IsbcServer", "content:skip local listening sip call local address:" + Common::String(_localAddress.to_string().c_str()));
        }
    }

    std::vector<SimpleSipSession::SipHostPort> remoteAddresses;
    std::vector<int> weights;
    if (readAddressConfig(_app, "SipCall.RemoteAddress", remoteAddresses, weights))
    {
        Common::String stats;
        for (int i = 0; i < remoteAddresses.size(); ++i)
        {
            SimpleSipSession::SipHostPort &addr = remoteAddresses[i];
            int weight = weights[i];

            Common::String localHost = _localAddress.host.c_str();
            if (listening && actualLocalHost != _localAddress.host.c_str())
                localHost = "0.0.0.0";
            bool ret = connect(addr.host.c_str(), addr.port, localHost, actualLocalPort, weight);
            if (!ret)
            {
                UTIL_LOG_WRN("IsbcServer", "content:connect sip call remote address:" + Common::String(addr.to_string().c_str()) + " failed.");
            }
            else
            {
                connecting = true;
                actualLocalHost = localHost;
                stats += (addr.to_string() + "," + std::to_string(weight) + ";").c_str();
                UTIL_LOG_IFO("IsbcServer", "content:connect sip call address:" + actualLocalHost + ":" + Common::String(actualLocalPort) + "->" + addr.to_string().c_str() + " weight:" + Common::String(weight));
            }
        }
        _app->setStatistics("SipCall.RemoteAddress", stats);
    }

    if (!connecting && !listening)
    {
        UTIL_LOG_WRN("IsbcServer", "content:no valid config for sip call");
        reason = "NoValidConfig";
        return false;
    }

    if (_localAddress.host.empty() || _localAddress.host == "0.0.0.0" && _localAddress.host == "::")
    {
        _localAddress.host = actualLocalHost.c_str();
        _localAddress.port = actualLocalPort;
    }

    _app->setStatistics("SipCall.LocalAddress", actualLocalHost + ":" + Common::String(actualLocalPort));
    if (actualLocalHost != _localAddress.host.c_str())
        _app->setStatistics("SipCall.PublicAddress", Common::String(_localAddress.to_string().c_str()));

    UTIL_LOG_IFO("IsbcServer", "content:sip call config local address:" + Common::String(_localAddress.to_string().c_str()));
    return true;
}

void IsbcServer::readLineBreakConfig()
{
    int value;
    if (_app->getAppConfigAsInt("SipCall.LineBreak.DetectTimeSeconds", value))
    {
        if (value < 10)
            value = 10;
        else if (value > 3600)
            value = 3600;
        _lineBreakDetectTimeMs = value * 1000;
    }
    _app->setStatistics("SipCall.LineBreak.DetectTimeSeconds", Common::String(_lineBreakDetectTimeMs / 1000));

    if (_app->getAppConfigAsInt("SipCall.LineBreak.FailRatio", value))
    {
        if (value < 0)
            value = 0;
        else if (value > 100)
            value = 100;
        _lineBreakFailRatio = value;
    }
    _app->setStatistics("SipCall.LineBreak.FailRatio", Common::String(_lineBreakFailRatio));

    if (_app->getAppConfigAsInt("SipCall.LineBreak.ContinuousFailCount", value))
    {
        if (value < 0)
            value = 0;
        else if (value > 100)
            value = 100;
        _lineBreakContinuousFailCount = value;
    }
    _app->setStatistics("SipCall.LineBreak.ContinuousFailCount", Common::String(_lineBreakContinuousFailCount));

    if (_app->getAppConfigAsInt("SipCall.LineBreak.RecoverySeconds", value))
    {
        if (value < 3)
            value = 3;
        else if (value > 3600)
            value = 3600;
        _lineBreakRecoveryMs = value * 1000;
    }
    _app->setStatistics("SipCall.LineBreak.RecoverySeconds", Common::String(_lineBreakRecoveryMs / 1000));

    UTIL_LOG_IFO("IsbcServer", "content:line break config detect:" + Common::String(_lineBreakDetectTimeMs / 1000) + "s ratio:" + Common::String(_lineBreakFailRatio) + "% contCount:" + Common::String(_lineBreakContinuousFailCount) + " recovery:" + Common::String(_lineBreakRecoveryMs / 1000) + "s");
}

void IsbcServer::updateCallIdCache(const std::string &callId, const std::string &connId)
{
    Common::RecLock lock(_mutex);
    _callId2ConnId.put(callId, connId);
}

SipConnPtr IsbcServer::getConnByCallId(const std::string &callId)
{
    Common::RecLock lock(_mutex);

    auto it = _incomingConnections.find(callId);
    if (it != _incomingConnections.end())
        return it->second;

    std::string connId;
    if (_callId2ConnId.get(callId, connId))
    {
        auto it2 = _outgoingConnections.find(connId);
        if (it2 != _outgoingConnections.end())
            return it2->second;
    }

    return nullptr;
}

void IsbcServer::countResult(const std::string &connId, bool success)
{
    Common::RecLock lock(_mutex);

    auto it = _outgoingConnections.find(connId);
    if (it == _outgoingConnections.end())
    {
        UTIL_LOG_WRN("IsbcServer", "content:count result for unknown outgoing connection:" + Common::String(connId.c_str()));
        return;
    }

    SipOutgoingConnPtr conn = it->second;
    conn->countResult(success);

    bool breaking = false;
    if (_lineBreakFailRatio > 0 && conn->failedPercent() >= _lineBreakFailRatio)
    {
        breaking = true;
        UTIL_LOG_WRN("IsbcServer", "content:set break outgoing connection:" + conn->connectionInfo + " failed%:" + Common::String(conn->failedPercent()) + " target:" + Common::String(_lineBreakFailRatio));
    }
    if (_lineBreakContinuousFailCount > 0 && conn->continuousFailedCount() >= _lineBreakContinuousFailCount)
    {
        breaking = true;
        UTIL_LOG_WRN("IsbcServer", "content:set break outgoing connection:" + conn->connectionInfo + " continuous failed count:" + Common::String(conn->continuousFailedCount()) + " target:" + Common::String(_lineBreakContinuousFailCount));
    }

    if (breaking)
    {
        _activeOutgoingConnections.erase(conn);
        _breakOutgoingConnections[conn] = Common::getCurTicks();
        conn->resetAlive(_configKeepAlive.intervalSip + 30);
    }
}

SipConnPtr IsbcServer::getConnByHostport(const std::string &hostport, const std::string &callId)
{
    Common::RecLock lock(_mutex);

    for (auto kv : _outgoingConnections)
    {
        if (kv.second->remoteAddress.to_string() == hostport)
        {
            _callId2ConnId.put(callId, kv.first);
            return kv.second;
        }
    }

    return nullptr;
}

int IsbcServer::selectConnIndex(const std::vector<int> &weights)
{
    int totalWeight = 0;
    for (int weight : weights)
        totalWeight += weight;

    int target = Common::getCurTicks() % totalWeight;
    int sum = 0;
    for (int i = 0; i < weights.size(); ++i)
    {
        sum += weights[i];
        if (target < sum)
        {
            UTIL_LOG_DBG("IsbcServer", "content:select conn index:" + Common::String(i) + " target:" + Common::String(target) + " sum:" + Common::String(totalWeight));
            return i;
        }
    }

    UTIL_LOG_DBG("IsbcServer", "content:select default conn index:" + Common::String((int)weights.size() - 1) + " target:" + Common::String(target) + " sum:" + Common::String(totalWeight));
    return weights.size() - 1;
}

void IsbcServer::__checkPendingConnections()
{
    Common::String stats;
    for (auto it = _pendingConnections.begin(); it != _pendingConnections.end();)
    {
        if (!(*it)->isActiveIn(300))
        {
            UTIL_LOG_IFO("IsbcServer", "content:remove pending connection:" + (*it)->connectionInfo);
            _pendingConnections.erase(it++);
        }
        else
        {
            stats += (*it)->connectionInfo + " ";
            ++it;
        }
    }
    _app->setStatistics("SipCall.Connections.Pending", stats);
    if (!stats.empty())
        SERVICE_LOG_EVERY_NSEC(60, Common::LogInfo, "IsbcServer", "content:pending connections:" + stats);
}

void IsbcServer::__checkIncomingConnections()
{
    Common::String stats;
    set<Common::String> connections;
    for (auto it = _incomingConnections.begin(); it != _incomingConnections.end();)
    {
        if (!it->second->isActiveIn(3600))
        {
            UTIL_LOG_IFO("IsbcServer", "content:remove incoming connection:" + it->second->connectionInfo + " call-id:" + it->first.c_str());
            it->second->close();
            _incomingConnections.erase(it++);
        }
        else
        {
            auto ret = connections.insert(it->second->connectionInfo);
            if (ret.second)
                stats += it->second->connectionInfo + " ";
            ++it;
        }
    }

    _app->setStatistics("SipCall.Connections.Incoming", stats);
    if (!stats.empty())
        SERVICE_LOG_EVERY_NSEC(60, Common::LogInfo, "IsbcServer", "content:incoming connections:" + stats);

}

void IsbcServer::__checkBreakOutgoingConnections()
{
    for (auto it = _breakOutgoingConnections.begin(); it != _breakOutgoingConnections.end();)
    {
        SipOutgoingConnPtr conn = it->first;
        if (Common::getCurTicks() - it->second > _lineBreakRecoveryMs)
        {
            UTIL_LOG_IFO("IsbcServer", "content:remove break outgoing connection:" + conn->connectionInfo);
            it = _breakOutgoingConnections.erase(it);
            conn->recoverAlive();
        }
        else
        {
            ++it;
        }
    }
}

void IsbcServer::__checkActiveOutgoingConnections()
{
    bool skipCheckAlive = (_configKeepAlive.mode & KeepAliveSipOption) == 0;

    unsigned int seconds = _configKeepAlive.intervalSip + 30;

    std::set<SipOutgoingConnPtr> activeOutgoingConnections;
    Common::String activeStats, breakStats;
    bool ret = false;
    for (auto kv : _outgoingConnections)
    {
        if (_breakOutgoingConnections.find(kv.second) != _breakOutgoingConnections.end())
        {
            breakStats += kv.second->connectionInfo + " ";
        }
        else if (skipCheckAlive || kv.second->isPeerAliveIn(seconds))
        {
            activeStats += kv.second->connectionInfo + " ";
            activeOutgoingConnections.insert(kv.second);
            ret = true;
        }
    }

    _app->setStatistics("SipCall.Connections.Active", activeStats);
    _app->setStatistics("SipCall.Connections.Break", breakStats);
    SERVICE_LOG_EVERY_NSEC_OR_IF(60, _lastActiveStats != activeStats || _lastBreakStats != breakStats, Common::LogInfo, "IsbcServer", "content:connection stats active:" + activeStats + " break:" + breakStats);
    _lastActiveStats = activeStats;
    _lastBreakStats = breakStats;

    _activeOutgoingConnections.swap(activeOutgoingConnections);
}

} // namespace SipMpCall
