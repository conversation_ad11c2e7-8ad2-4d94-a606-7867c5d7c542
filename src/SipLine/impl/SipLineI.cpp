//
// *****************************************************************************
// Copyright (c) 2023 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//
// Created on 2023/5/25 by <PERSON>
//

#include <exception>
#include <string>

#include "Common/Common.h"
#include "Common/TypesPub.h"
#include "SipLineI.h"
#include "Error.h"
#include "SimpleSipSession/SipMessage.h"
#include "SipAsbcServer.h"
#include "SipIsbcServer.h"


namespace SipMpCall
{

bool readAddressConfig(const Common::ApplicationPtr &app, const char *key, SimpleSipSession::SipHostPort &address)
{
    Common::String addr;
    if (!app->getAppConfig(key, addr))
    {
        UTIL_LOG_WRN("SipLine", "content:invalid address config, key:" + Common::String(key));
        return false;
    }

    if (!address.decode(addr.c_str()))
    {
        UTIL_LOG_WRN("SipLine", "content:invalid address config, addr:" + Common::String(addr.c_str()));
        return false;
    }

    UTIL_LOG_IFO("SipLine", "content:read address config, address:" + Common::String(addr.c_str()));
    return true;
}

bool readAddressConfig(const Common::ApplicationPtr &app, const char *key, std::vector<SimpleSipSession::SipHostPort> &addresses, std::vector<int> &weights)
{
    Common::String value;
    if (!app->getAppConfig(key, value))
    {
        UTIL_LOG_WRN("SipLine", "content:invalid address config, key:" + Common::String(key));
        return false;
    }

    Common::StrVec addrs;
    value.split(addrs, ";");

    for (auto addr : addrs)
    {
        int pos = addr.find(",");
        if (pos == 0 || pos == addr.size() - 1)
        {
            UTIL_LOG_WRN("SipLine", "content:invalid address config, addr:" + Common::String(addr.c_str()));
            continue;
        }

        int weight = 1;
        if (pos > 0)
        {
            weight = addr.substr(pos + 1).toInt(-1);
            if (weight <= 0 || weight > 9999)
            {
                UTIL_LOG_WRN("SipLine", "content:invalid weight, addr:" + Common::String(addr.c_str()));
                continue;
            }
            addr = addr.substr(0, pos);
        }

        SimpleSipSession::SipHostPort address;
        if (!address.decode(addr.c_str()))
        {
            UTIL_LOG_WRN("SipLine", "content:invalid address config, addr:" + Common::String(addr.c_str()));
            continue;
        }

        addresses.push_back(address);
        weights.push_back(weight);
        UTIL_LOG_IFO("SipLine", "content:read address config, address:" + Common::String(addr.c_str()) + " weight:" + Common::String(weight));
    }

    return true;
}

bool SipLineI::init()
{
    if (!initSipCall())
        return true;

    std::string lemonVer, avatarVer;
    SipClient::SipCallInterface::getInstance()->GetSipSdkVer(lemonVer, avatarVer);
    Common::String info = Common::String(("Lemon/" + lemonVer + " Avatar/" + avatarVer).c_str());
    _app->setStatistics("Service.Version.Jssi", info);
    UTIL_LOG_IFO("SipLine", "content:init " + info);

    return true;
}

bool SipLineI::isReady(Common::String &failReason)
{
    if (!_failReason.empty())
    {
        failReason = _failReason;
        return false;
    }

    SipClient::SipCallInterfacePtr sipCall = SipClient::SipCallInterface::getInstance();
    if (!(sipCall && _sipConnServer))
    {
        UTIL_LOG_WRN("SipLine", "content:sip call or connection not ready.");
        if (!sipCall)
        {
            Common::setCallError(SipLineError::InvalidSipStack(ELOC));
            failReason = SipLineError::InvalidSipStackReason;
        }
        else
        {
            Common::setCallError(SipLineError::InvalidSipConnection(ELOC));
            failReason = SipLineError::InvalidSipConnectionReason;
        }

        return false;
    }

    if (!_sipConnServer->isAliveIn())
    {
        UTIL_LOG_WRN("SipLine", "content:sip connection not alive");
        Common::setCallError(SipLineError::SipConnectionExpired(ELOC));
        failReason = SipLineError::SipConnectionExpiredReason;
        return false;
    }

    return true;
}

void SipLineI::close()
{
    UTIL_LOG_IFO("SipLine", "content:close.");

    _sipLineSessionManager.reset();

    if (_sipDriver)
    {
        _sipDriver->close();
        _sipDriver = 0;
    }

    if (_sipConnServer)
    {
        _sipConnServer->close();
        _sipConnServer = 0;
    }

    if (_sipCallCreated)
        SipClient::SipCallInterface::destroyInstance();

    _listener = 0;
    _app = 0;
}

void SipLineI::schd()
{
    if (Common::getCurTicks() - _lastSchdTicks < 1000)
        return;
    _lastSchdTicks = Common::getCurTicks();

    _sipLineSessionManager.schd(this);

    SipConnServerPtr sipServer = _sipConnServer;
    if (sipServer)
        sipServer->schd();
}

void SipLineI::updateConfigs()
{
    Common::ApplicationPtr app = _app;
    if (!app)
        return;

    Common::String uriParams;
    if (app->getAppConfig("SipCall.ContactEx", uriParams) || app->getAppConfig("ContactEx", uriParams))
    {
        _configUriParams = uriParams;
        app->setStatistics("SipCall.ContactEx", _configUriParams);
    }

    Common::String prefix;
    if (app->getAppConfig("SipCall.CalleePrefix", prefix) || app->getAppConfig("SipPhonePrefix", prefix))
    {
        _configCalleePrefix = prefix;
        app->setStatistics("SipCall.CalleePrefix", _configCalleePrefix);
    }
}

int SipLineI::getFreePercent()
{
    SipConnServerPtr sipServer = _sipConnServer;
    if (!sipServer)
        return 0;
    return sipServer->getFreePercent();
}

SipLineSessionPtr SipLineI::SipCall(const SipLineSessionListenerPtr &listener, const std::string &sessId, const std::string &caller, const std::string &callee, const std::string &pcOfferSdp, const std::map<std::string, std::string> &extHdrs)
{
    SipClient::SipCallInterfacePtr sipCall = SipClient::SipCallInterface::getInstance();
    if (!sipCall)
    {
        UTIL_LOG_WRN("SipLine", "content:sip call no client, caller:" + Common::String(caller.c_str()) + " callee:" + callee.c_str());
        return nullptr;
    }

    SipConnServerPtr sipServer = _sipConnServer;
    if (!sipServer)
    {
        UTIL_LOG_WRN("SipLine", "content:sip call no valid server, caller:" + Common::String(caller.c_str()) + " callee:" + callee.c_str());
        return nullptr;
    }

    SipConnPtr conn = sipServer->selectConn();
    if (!conn)
    {
        UTIL_LOG_WRN("SipLine", "content:sip call no connection, caller:" + Common::String(caller.c_str()) + " callee:" + callee.c_str());
        return nullptr;
    }

    std::string callId = SimpleSipSession::SipMessage::genCallId(sessId, caller, callee, conn->localAddress.host);
    std::string callerUri = conn->callerUri(caller, _configCallerUriFormat);
    std::string calleeUri = conn->calleeUri(callee, _configCalleeUriFormat, _configCalleePrefix.c_str());
    std::string contactUri = getContactUri(caller, conn->localAddress);

    std::map<std::string, std::string> _extHdrs = extHdrs;
    if (_configPaiUriFormat != SimpleSipSession::UnknownUri && _extHdrs.find("P-Asserted-Identity") == _extHdrs.end())
        _extHdrs["P-Asserted-Identity"] = conn->callerAddr(caller, _configPaiUriFormat);

    SipLineSessionIPtr sipLineSession;
    try
    {
        sipLineSession = new SipLineCalloutSession(&_sipLineSessionManager, caller, callee, pcOfferSdp, _extHdrs);
    }
    catch (const std::exception &e)
    {
        UTIL_LOG_ERR("SipLine", "content:create sip line session failed, reason:" + Common::String(e.what()));
        return nullptr;
    }

    SipClient::SipCallSessionPtr session = sipCall->SipCall(sipLineSession, callerUri, calleeUri, callId, contactUri, pcOfferSdp, _configUriParams.c_str(), _extHdrs);
    if (!sipLineSession->open(_app, session, listener))
    {
        if (session)
            session->SipTerm();
        UTIL_LOG_WRN("SipLine", "content:sip call failed, callerUri:" + Common::String(callerUri.c_str()) + " calleeUri:" + calleeUri.c_str());
        return nullptr;
    }

    sipLineSession->setConnId(conn->connId());
    sipLineSession->setSessId(sessId);
    sipLineSession->setCallId(callId);
    UTIL_LOG_IFO("SipLine", "content:sip call callId:" + Common::String(callId.c_str()) + " sessId:" + sessId.c_str() + " callerUri:" + Common::String(callerUri.c_str()) + " calleeUri:" + calleeUri.c_str() + " connId:" + conn->connId().c_str());
    return sipLineSession;
}

bool SipLineI::onSend(const std::string &message)
{
    SipConnServerPtr conn = _sipConnServer;
    if (!conn)
        return false;

    conn->send(message);
    return true;
}

SipClient::SipCallSessionListenerPtr SipLineI::onCreateSession(const SipClient::SipCallSessionPtr &session)
{
    SipLineListnerPtr listener = _listener;
    if (!listener)
    {
        UTIL_LOG_WRN("SipLine", "content:on call incoming no listener");
        return nullptr;
    }

    SipLineSessionIPtr sipLineSession;
    try
    {
        sipLineSession = new SipLineSessionI(&_sipLineSessionManager, session);
    }
    catch (const std::exception &e)
    {
        UTIL_LOG_ERR("SipLine", "content:create sip line session failed, reason:" + Common::String(e.what()));
        return nullptr;
    }

    SipLineSessionListenerPtr sessionListener = listener->onCreateSession(sipLineSession);
    if (!sipLineSession->open(_app, session, sessionListener))
    {
        UTIL_LOG_WRN("SipLine", "content:on call incoming no session listener");
        return nullptr;
    }

    return sipLineSession;
}

void SipLineI::recv(const std::string &data)
{
    SipClient::SipCallInterfacePtr sipCall = SipClient::SipCallInterface::getInstance();
    if (!sipCall)
        return;

    if (!sipCall->RecvSipMsg(data))
        UTIL_LOG_WRN("SipLine", "content:process received message failed, size:" + Common::String((Common::Long)data.size()));
}

SimpleSipSession::SipDriverPtr SipLineI::getSipDriver()
{
    if (!_sipDriver)
        _sipDriver = SimpleSipSession::SipDriver::create();
    return _sipDriver;
}

std::string SipLineI::getSessId(const std::string &callId)
{
    auto session = _sipLineSessionManager.getSessByCallId(callId);
    if (!session)
    {
        UTIL_LOG_WRN("SipLine", "content:getSessId no session found for callId:" + Common::String(callId.c_str()));
        return "";
    }

    return session->getSessId();
}

vector<std::string>  SipLineI::getCallId(const std::string &sessId)
{
    auto session = _sipLineSessionManager.getSess(sessId);
    if (!session)
    {
        UTIL_LOG_WRN("SipLine", "content:getCallId no session found for sessId:" + Common::String(sessId.c_str()));
        return {};
    }

    return session->getCallIds();
}

bool SipLineI::getSessStatus(const std::string &sessId, std::map<std::string, SessStatus> &statuses)
{
    vector<std::string> callIds = getCallId(sessId);
    if (callIds.empty())
    {
        UTIL_LOG_WRN("SipLine", "content:getSessStatus no callId found for sessId:" + Common::String(sessId.c_str()));
        return false;
    }

    SipConnServerPtr sipServer = _sipConnServer;
    if (!sipServer)
    {
        UTIL_LOG_WRN("SipLine", "content:getSessStatus no valid server, sessId:" + Common::String(sessId.c_str()));
        return false;
    }
    for (auto callId : callIds)
    {
        SessStatus status;
        status = sipServer->getSessEvents(callId);
        statuses[callId] = status;
    }

    return true;
}

void SipLineI::setKeepTermedStatus(bool keepTermedSession)
{
    _sipLineSessionManager.setKeepTermedStatus(keepTermedSession);

    SipConnServerPtr sipServer = _sipConnServer;
    if (!sipServer)
    {
        UTIL_LOG_WRN("SipLineI", "content:setKeepTermedStatus no valid server");
        return;
    }

    sipServer->setKeepTermedStatus(keepTermedSession);
}

std::string SipLineI::getContactUri(const std::string &callId, const std::string &userpart)
{
    SipConnServerPtr sipServer = _sipConnServer;
    if (!sipServer)
    {
        UTIL_LOG_WRN("SipLine", "content:update contact no valid server, userpart:" + Common::String(userpart.c_str()) + " callId:" + callId.c_str());
        return "";
    }

    SipConnPtr conn = sipServer->getConnByCallId(callId);
    if (!conn)
    {
        UTIL_LOG_WRN("SipLine", "content:update contact no connection, userpart:" + Common::String(userpart.c_str()) + " callId:" + callId.c_str());
        return "";
    }

    return getContactUri(userpart, conn->localAddress);
}

std::string SipLineI::getContactUri(const std::string &userpart, const SimpleSipSession::SipHostPort &hostport)
{
    Common::String value;
    std::string contactUri = "sip:" + userpart + "@" + hostport.to_string();
    if (_app->getStatistic("SipCall.PublicAddress", value) || _app->getStatistic("SipCall.LocalAddress", value))
    {
        SimpleSipSession::SipHostPort localAddress;
        localAddress.decode(value.c_str());
        contactUri = "sip:" + userpart + "@" + localAddress.to_string();
    }

    if (!_configUriParams.empty())
        contactUri += ";" + std::string(_configUriParams.c_str());

    return contactUri;
}

bool SipLineI::readSipConfig(SipClient::SipCallConfig &config)
{
    config.workPath = "./rtc/";
    config.callNumber = "123456";

    config.earlyMedia = (_app->getAppConfigAsInt("SipCall.EarlyMedia") == 1);
    _app->setStatisticsLong("SipCall.EarlyMedia", config.earlyMedia ? 1 : 0);

    config.precondition = (_app->getAppConfigAsInt("SipCall.Precondition") == 1);
    _app->setStatisticsLong("SipCall.Precondition", config.precondition ? 1 : 0);

    _configCalleeUriFormat = getUriFormatConfig("SipCall.CalleeUriFormat", SimpleSipSession::SipUri);
    _configCallerUriFormat = getUriFormatConfig("SipCall.CallerUriFormat", SimpleSipSession::SipUri);
    _configPaiUriFormat = getUriFormatConfig("SipCall.PAIUriFormat", SimpleSipSession::UnknownUri);
    return true;
}

enum SimpleSipSession::SipUriFormat SipLineI::getUriFormatConfig(const char *key, enum SimpleSipSession::SipUriFormat defaultValue)
{
    Common::String value = _app->getAppConfig(key);

    if (value == "sip" || value == "Sip" || value == "sipuri" || value == "SipUri" || value == "0")
        defaultValue = SimpleSipSession::SipUri;
    else if (value == "sipphone" || value == "SipPhone" || value == "sipphoneuri" || value == "SipPhoneUri" || value == "1")
        defaultValue = SimpleSipSession::SipPhoneUri;
    else if (value == "teluri" || value == "TelUri" || value == "tel" || value == "Tel" || value == "2")
        defaultValue = SimpleSipSession::TelUri;

    switch (defaultValue)
    {
    case SimpleSipSession::SipUri:
        _app->setStatistics(key, "SipUri");
        break;
    case SimpleSipSession::SipPhoneUri:
        _app->setStatistics(key, "SipPhoneUri");
        break;
    case SimpleSipSession::TelUri:

        _app->setStatistics(key, "TelUri");
        break;
    default:
        break;
    }

    return defaultValue;
}

bool SipLineI::initSipCall()
{
    SipClient::SipCallConfig config;
    if (!readSipConfig(config))
    {
        _failReason = "ReadConfigFailed";
        return false;
    }

    SipClient::SipCallInterfacePtr sipCall = SipClient::SipCallInterface::getInstance(_app, config, this, _sipCallCreated);
    if (!sipCall)
    {
        UTIL_LOG_WRN("SipLine", "content:create sip call interface failed.");
        _failReason = "CreateSipCallFailed";
        return false;
    }

    try
    {
        int value = _app->getAppConfigAsInt("SipCall.ASBC");
        _app->setStatisticsLong("SipCall.ASBC", value);
        if (value)
            _sipConnServer = new AsbcServer(this, _app);
        else
            _sipConnServer = new IsbcServer(this, _app);
        if (!_sipConnServer->init(_failReason))
        {
            if (_sipCallCreated)
                SipClient::SipCallInterface::destroyInstance();

            _sipConnServer = 0;
            return false;
        }
    }
    catch (const std::exception &e)
    {
        if (_sipCallCreated)
            SipClient::SipCallInterface::destroyInstance();

        UTIL_LOG_ERR("SipLine", "content:create sip connection server failed.");
        _failReason = "CreateSipConnFailed";
        return false;
    }

    UTIL_LOG_IFO("SipLine", "content:init sip call ok.");
    return true;
}

std::string SipLine::getNumber(const std::string &uri)
{
    size_t pos = uri.find("sip:");
    if (pos != 0)
    {
        pos = uri.find("tel:");
        if (pos != 0)
        {
            UTIL_LOG_WRN("SipLine", "content:invalid uri:" + Common::String(uri.c_str()));
            return uri;
        }

        return uri.substr(4);
    }

    pos = uri.find('@', 4);
    if (pos == std::string::npos)
    {
        UTIL_LOG_WRN("SipLine", "content:invalid sip uri:" + Common::String(uri.c_str()));
        return uri;
    }

    return uri.substr(4, pos - 4);
}

SipLinePtr SipLine::create(const SipLineListnerPtr &listener, const Common::ApplicationPtr &app)
{
    SipLineIPtr sipline;
    try
    {
        sipline = new SipLineI(app, listener);
        if (!sipline->init())
            return nullptr;
    }
    catch (const std::exception &e)
    {
        UTIL_LOG_ERR("SipLine", "content:create sip line failed, reason:" + Common::String(e.what()));
        return nullptr;
    }

    return sipline;
}

} // namespace SipMpCall
