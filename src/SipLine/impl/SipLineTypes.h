//
// *****************************************************************************
// Copyright (c) 2025 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//

#pragma once

#include "Common/Common.h"
#include "SimpleSipSession/SipTransport.h"

namespace SipMpCall
{

enum KeepAliveMode
{
    KeepAliveOff = 0,
    KeepAliveCrlf = 0x01,
    KeepAliveSipOption = 0x02,
    KeepAliveCrlfSipOption = 0x03
};

struct KeepAliveConfig
{
    KeepAliveConfig()
        : mode(KeepAliveMode::KeepAliveCrlfSipOption)
        , intervalCrlf(30)
        , intervalSip(1800)
    {
    }

    enum KeepAliveMode mode;
    int intervalCrlf;
    int intervalSip;
};

class SipLineI;
typedef Common::Handle<SipLineI> SipLineIPtr;

bool readAddressConfig(const Common::ApplicationPtr &app, const char *key, SimpleSipSession::SipHostPort &address);
bool readAddressConfig(const Common::ApplicationPtr &app, const char *key, std::vector<SimpleSipSession::SipHostPort> &addresses, std::vector<int> &weights);

} // namespace SipMpCall
