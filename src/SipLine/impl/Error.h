//
// *****************************************************************************
// Copyright (c) 2023 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//
// Created on 2023/5/29 by <PERSON>
//

#pragma once

#include "Common/Error.h"

namespace SipMpCall
{

namespace SipLineError
{
    static const char *const DomainCode = "M30";

    ERROR_DECLARE_F(7500, InvalidSipStack);
    ERROR_DECLARE_F(7501, InvalidSipConnection);
    ERROR_DECLARE_F(7502, SipConnectionExpired);
} // namespace Error

} // namespace SipMpCall
