//
// *****************************************************************************
// Copyright (c) 2025 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//

#pragma once

#include "Common/Common.h"
#include "SimpleSipSession/SipMessage.h"
#include "SimpleSipSession/SipTransport.h"
#include "SimpleSipSession/SipUri.h"
#include "SipEvent.h"
#include "SipLineTypes.h"
#include "SimpleSipSession/SipDriver.h"

namespace SipMpCall
{

class SipConn;
class SipConnServer;
typedef Common::Handle<SipConn> SipConnPtr;
typedef Common::Handle<SipConnServer> SipConnServerPtr;

class SipConn : public Common::NetReceiver
{
public:
    SipConn(const SipConnServerPtr _server, const Common::NetSenderPtr &_sender)
        : server(_server)
        , sender(_sender)
        , lastRecvTicks(Common::getCurTicks() - 3600000)
        , lastSendTicks(Common::getCurTicks() - 3600000)
    {
    }

    ~SipConn() {}

    virtual std::string connId() { return remoteAddress.to_string(); }
    virtual void close();
    virtual void schd() {}
    virtual bool send(const std::string &sessId, const SimpleSipSession::SipMessagePtr &msg);
    void updateConnectionInfo();
    bool isActiveIn(unsigned int seconds);

    // implement Common::NetReceiver
    virtual void recv(const unsigned char *data, int dataLen) override;
    void onConnClose() override;

    virtual std::string callerUri(const std::string &number, enum SimpleSipSession::SipUriFormat format = SimpleSipSession::SipUri, const std::string &prefix = "");
    virtual std::string callerAddr(const std::string &number, enum SimpleSipSession::SipUriFormat format = SimpleSipSession::SipUri, const std::string &prefix = "");
    virtual std::string calleeUri(const std::string &number, enum SimpleSipSession::SipUriFormat format = SimpleSipSession::SipUri, const std::string &prefix = "");

protected:
    bool __send(const unsigned char *data, int dataLen, const Common::String &logInfo);
    bool __send(const SimpleSipSession::SipMessagePtr &msg, const Common::String &logInfo);
    void __recv(const SimpleSipSession::SipMessagePtr &msg, const Common::String &logInfo);

public:
    SipConnServerPtr server;
    Common::NetSenderPtr sender;
    unsigned int lastRecvTicks;
    unsigned int lastSendTicks;
    SimpleSipSession::SipHostPort localAddress;
    SimpleSipSession::SipHostPort remoteAddress;
    Common::String connectionInfo;
    Common::String connectionInfoRecv;
};

class SipConnServer : virtual public Common::Shared
{
public:
    virtual bool init(Common::String &reason) = 0;
    virtual void close() = 0;
    virtual void schd();
    virtual bool isAliveIn() = 0;
    virtual int getFreePercent() = 0;
    virtual SipConnPtr selectConn(const std::vector<std::string> &usedConnIds = std::vector<std::string>()) = 0;
    virtual SipConnPtr getConnByCallId(const std::string &callId) = 0;
    virtual void countResult(const std::string &connId, bool success) = 0;

    virtual bool send(const std::string &data) = 0;
    virtual void recv(const SipConnPtr &conn, const SimpleSipSession::SipMessagePtr &msg, const Common::String &logInfo) = 0;

    // Event management
    virtual SipLine::SessStatus getSessEvents(const std::string &callId);
    virtual void setKeepTermedStatus(bool keepTermedSession);

protected:
    SipEventManager _eventManager;
};

class SipConnServerManager : virtual public Common::Shared
{
public:
    virtual std::string getSessId(const std::string &callId) = 0;
    virtual SimpleSipSession::SipDriverPtr getSipDriver() = 0;
    virtual void recv(const std::string &data) = 0;
};

typedef Common::Handle<SipConnServerManager> SipConnServerManagerPtr;

} // namespace SipMpCall
