//
// *****************************************************************************
// Copyright (c) 2025 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//

#pragma once

#include "SipLine/SipLine.h"
#include "SimpleSipSession/SipMessage.h"

#include <unordered_map>

namespace SipMpCall
{

class SipEventManager
{
public:
    SipEventManager(unsigned int expiresMs = 3600000)
        : _keepTermedSession(false)
        , _expiresMs(expiresMs)
    {
    }

    void saveSipEvent(const std::string &callId, const std::string &type, const std::string &title, const std::string &detail, const std::string localAddr, const std::string remoteAddr);
    SipLine::SessStatus getSessEvents(const std::string &callId);
    void schd();
    void recordSipMessage(const SimpleSipSession::SipMessagePtr &msg, const std::string &direction, const std::string localAddr, const std::string remoteAddr);
    void setKeepTermedStatus(bool keepTermedSession) { _keepTermedSession = keepTermedSession; }

private:
    Common::RecMutex _mutex;
    std::unordered_map<std::string, SipLine::SessStatus> _sipStatuses;
    bool _keepTermedSession;
    unsigned int _expiresMs;
};

} // namespace SipMpCall
