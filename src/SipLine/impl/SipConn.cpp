//
// *****************************************************************************
// Copyright (c) 2025 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//

#include "SipConn.h"
#include "SimpleSipSession/SipUri.h"

namespace SipMpCall
{

void SipConn::close()
{
    UTIL_LOG_IFO("SipConn", "content:close connection:" + connectionInfo);
    Common::NetSenderPtr _sender = sender;
    if (_sender)
        _sender->close();
    sender = 0;
    lastRecvTicks = Common::getCurTicks() - 3600000;
    lastSendTicks = Common::getCurTicks() - 3600000;
}

bool SipConn::send(const std::string &sessId, const SimpleSipSession::SipMessagePtr &msg)
{
    Common::String logInfo = (sessId.empty() ? Common::String() : Common::String("sessId:") + sessId.c_str() + " ") + Common::String("digest:") + msg->logInfo().c_str() + " sip message:\n" + Common::String((const char *)msg->data().c_str(), msg->data().size());
    return __send(msg, logInfo);
}

void SipConn::updateConnectionInfo()
{
    if (!connectionInfo.empty())
        return;

    Common::NetSenderPtr _sender = sender;
    if (!_sender)
        return;

    Common::String host;
    int port;
    if (!_sender->getLocal(host, port))
        return;
    localAddress = SimpleSipSession::SipHostPort(host.c_str(), port);
    if (!_sender->getRemote(host, port))
        return;
    remoteAddress = SimpleSipSession::SipHostPort(host.c_str(), port);

    connectionInfo = (localAddress.to_string() + "-->" + remoteAddress.to_string()).c_str();
    connectionInfoRecv = (localAddress.to_string() + "<--" + remoteAddress.to_string()).c_str();
}

bool SipConn::isActiveIn(unsigned int seconds)
{
    unsigned int now = Common::getCurTicks();
    return (now - lastSendTicks < seconds * 1000) || (now - lastRecvTicks < seconds * 1000);
}

// implement Common::NetReceiver
void SipConn::recv(const unsigned char *data, int dataLen)
{
    if (dataLen == 2 && data[0] == '\r' && data[1] == '\n')
    {
        lastRecvTicks = Common::getCurTicks();
        UTIL_LOG_IFO("SipConn", "content:connection recv " + connectionInfoRecv + " CRLF");
        return;
    }
    else if (dataLen == 4 && data[0] == '\r' && data[1] == '\n' && data[2] == '\r' && data[3] == '\n')
    {
        lastRecvTicks = Common::getCurTicks();
        UTIL_LOG_IFO("SipConn", "content:connection recv " + connectionInfoRecv + " CRLFCRLF");
        __send((const unsigned char *)"\r\n", 2, "CRLF");
        return;
    }

    SimpleSipSession::SipMessagePtr sipMsg = SimpleSipSession::SipMessage::create(std::string((const char *)data, dataLen));
    if (!sipMsg)
    {
        UTIL_LOG_WRN("SipConn", "content:connection recv " + connectionInfoRecv + " size:" + Common::String::formatString("%-5d", dataLen) + " invalid sip message:" + Common::String((const char *)data, dataLen));
        return;
    }

    __recv(sipMsg, connectionInfoRecv + " size:" + Common::String::formatString("%-5d", dataLen) + " digest:" + sipMsg->logInfo().c_str() + " sip message:\n" + Common::String((const char *)data, dataLen));
}

void SipConn::onConnClose()
{
    lastRecvTicks = Common::getCurTicks() - 3600000;
    lastSendTicks = Common::getCurTicks() - 3600000;
}

std::string SipConn::callerUri(const std::string &number, enum SimpleSipSession::SipUriFormat format, const std::string &prefix)
{
    return SimpleSipSession::SipUri::formatUri(format, prefix, number, localAddress.to_string());
}

std::string SipConn::callerAddr(const std::string &number, enum SimpleSipSession::SipUriFormat format, const std::string &prefix)
{
    return SimpleSipSession::SipUri::formatNameAddr(format, prefix, number, localAddress.to_string(), "");
}

std::string SipConn::calleeUri(const std::string &number, enum SimpleSipSession::SipUriFormat format, const std::string &prefix)
{
    return SimpleSipSession::SipUri::formatUri(format, prefix, number, remoteAddress.to_string());
}

bool SipConn::__send(const unsigned char *data, int dataLen, const Common::String &logInfo)
{
    lastSendTicks = Common::getCurTicks();
    Common::NetSenderPtr _sender = sender;
    if (!_sender)
    {
        UTIL_LOG_WRN("SipConn", "content:connection send " + connectionInfo + " closed size:" + Common::String::formatString("%-5d", dataLen) + " " + logInfo);
        return false;
    }

    int sent = _sender->send(data, dataLen);
    if (sent != dataLen)
    {
        UTIL_LOG_WRN("SipConn", "content:connection send " + connectionInfo + " imcomplete size:" + Common::String::formatString("%-5d", sent) + " " + logInfo);
        return false;
    }

    UTIL_LOG_IFO("SipConn", "content:connection send " + connectionInfo + " size:" + Common::String::formatString("%-5d", sent) + " " + logInfo);

    return true;
}

bool SipConn::__send(const SimpleSipSession::SipMessagePtr &msg, const Common::String &logInfo)
{
    lastSendTicks = Common::getCurTicks();
    Common::NetSenderPtr _sender = sender;
    if (!_sender)
    {
        UTIL_LOG_WRN("SipConn", "content:connection send " + connectionInfo + " closed size:" + Common::String::formatString("%-5d", msg->data().size()) + " " + logInfo);
        return false;
    }

    int sent = _sender->send((const unsigned char *)msg->data().c_str(), msg->data().size());
    if (sent != msg->data().size())
    {
        UTIL_LOG_WRN("SipConn", "content:connection send " + connectionInfo + " imcomplete size:" + Common::String::formatString("%-5d", sent) + " " + logInfo);
        return false;
    }

    UTIL_LOG_IFO("SipConn", "content:connection send " + connectionInfo + " size:" + Common::String::formatString("%-5d", sent) + " " + logInfo);

    return true;
}

void SipConn::__recv(const SimpleSipSession::SipMessagePtr &msg, const Common::String &logInfo)
{
    lastRecvTicks = Common::getCurTicks();

    SipConnServerPtr _server = server;
    if (_server)
        _server->recv(this, msg, logInfo);
}

void SipConnServer::schd()
{
    _eventManager.schd();
}

SipLine::SessStatus SipConnServer::getSessEvents(const std::string &callId)
{
    return _eventManager.getSessEvents(callId);
}

void SipConnServer::setKeepTermedStatus(bool keepTermedSession)
{
    _eventManager.setKeepTermedStatus(keepTermedSession);
}

} // namespace SipMpCall
