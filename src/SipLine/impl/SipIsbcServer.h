//
// *****************************************************************************
// Copyright (c) 2025 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//

#pragma once

#include "Util/LRUCache.h"
#include "SipConn.h"
#include "SipLineTypes.h"
#include "SimpleSipSession/SipFlowKeepAlive.h"
#include "ServiceUtil/Log.h"
#include "SlidingWindowCounter.h"

namespace SipMpCall
{

class SipOutgoingConn : public SipConn, public SimpleSipSession::SipTransport, public SimpleSipSession::SipFlowListener
{
public:
    SipOutgoingConn(const SipConnServerPtr _server, const Common::NetSenderPtr &_sender, const SimpleSipSession::SipDriverPtr &sipDriver, const KeepAliveConfig &config, const SimpleSipSession::SipHostPort &localHostPort, int weight, int countDurationMs)
        : SipConn(_server, _sender)
        , _lastCrlfSendTicks(Common::getCurTicks() - 3600000)
        , _sipDriver(sipDriver)
        , _keepAliveConfig(config)
        , _localHostPort(localHostPort)
        , _weight(weight)
        , _successCounter(countDurationMs)
        , _failedCounter(countDurationMs)
        , _continuousFailedCounter(countDurationMs)
    {
    }

    virtual void close() override;
    virtual void schd() override;
    bool isPeerAliveIn(unsigned int seconds);
    void resetAlive(unsigned int seconds);
    void recoverAlive();
    int weight() const { return _weight; }
    void countResult(bool success);
    int failedPercent();
    int continuousFailedCount() { return _continuousFailedCounter.getSum(); }

    virtual void recv(const unsigned char *data, int dataLen) override;

    // implement SimpleSipSession::SipTransport
    virtual std::string protocol() override { return "udp"; }
    virtual SimpleSipSession::SipHostPort localHostPort() override { return _localHostPort; }
    virtual SimpleSipSession::SipHostPort remoteHostPort() override { return remoteAddress; }

    // implement SimpleSipSession::SipFlowListener
    virtual void onError() override;
    virtual void onSendMessage(const SimpleSipSession::SipMessagePtr &message) override;

private:
    int _weight;
    unsigned int _lastCrlfSendTicks;
    KeepAliveConfig _keepAliveConfig;
    SimpleSipSession::SipDriverPtr _sipDriver;
    SimpleSipSession::SipHostPort _localHostPort;
    SimpleSipSession::KeepAlive::FlowPtr _keepAlive;
    ::ServiceUtil::LogEveryNSecState _logState;
    SlidingWindowCounter _successCounter;
    SlidingWindowCounter _failedCounter;
    SlidingWindowCounter _continuousFailedCounter;
};

typedef Common::Handle<SipOutgoingConn> SipOutgoingConnPtr;

class IsbcServer : public SipConnServer, public Common::NetReceiver
{
public:
    IsbcServer(const SipConnServerManagerPtr &sipLine, const Common::ApplicationPtr &app)
        : _sipLine(sipLine)
        , _app(app)
        , _callId2ConnId(10000)
        , _lineBreakDetectTimeMs(60000)
        , _lineBreakFailRatio(50)
        , _lineBreakContinuousFailCount(3)
        , _lineBreakRecoveryMs(60000)
    {
    }

    bool init(Common::String &reason) override;
    void schd() override;
    void close() override;
    bool send(const std::string &data) override;
    bool isAliveIn() override;
    int getFreePercent() override;
    void recv(const SipConnPtr &conn, const SimpleSipSession::SipMessagePtr &msg, const Common::String &logInfo) override;
    SipConnPtr selectConn(const std::vector<std::string> &usedConnIds = std::vector<std::string>()) override;
    SipConnPtr getConnByCallId(const std::string &callId) override;
    void countResult(const std::string &connId, bool success) override;

    // implement Common::NetReceiver
    void recv(const unsigned char *data, int dataLen) override;
    Common::NetReceiverPtr recvConnection(const Common::NetSenderPtr &sender) override;

private:
    bool listen(const Common::String &host, int port, Common::String &localHost, int &localPort);
    bool connect(const Common::String &host, int port, Common::String &localHost, int &localPort, int weight);

    void readKeepAliveConfigs();
    bool readLineAddrConfig(Common::String &reason);
    void readLineBreakConfig();

    void updateCallIdCache(const std::string &callId, const std::string &connId);
    SipConnPtr getConnByHostport(const std::string &hostport, const std::string &callId);

    int selectConnIndex(const std::vector<int> &weights);
    void __checkPendingConnections();
    void __checkIncomingConnections();
    void __checkBreakOutgoingConnections();
    void __checkActiveOutgoingConnections();

public:
    Common::RecMutex _mutex;
    SipConnServerManagerPtr _sipLine;
    Common::ApplicationPtr _app;

    SimpleSipSession::SipHostPort _localAddress;
    KeepAliveConfig _configKeepAlive;

    int _lineBreakDetectTimeMs;
    int _lineBreakFailRatio;
    int _lineBreakContinuousFailCount;
    int _lineBreakRecoveryMs;
    std::map<std::string, SipOutgoingConnPtr> _outgoingConnections;
    std::set<SipOutgoingConnPtr> _activeOutgoingConnections;
    std::map<SipOutgoingConnPtr, unsigned int> _breakOutgoingConnections;
    LRUCache<std::string, std::string> _callId2ConnId;
    Common::String _lastActiveStats;
    Common::String _lastBreakStats;

    Common::NetSenderPtr _listener;
    std::set<SipConnPtr> _pendingConnections;
    std::map<std::string, SipConnPtr> _incomingConnections;

    Common::Long _lastSipSendTime;
    Common::Long _lastSipRecvTime;
};

} // namespace SipMpCall
