//
// *****************************************************************************
// Copyright (c) 2024 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//
// Created on 2024/5/21 by <PERSON>
//

#pragma once

#include "gmock/gmock.h"
#include "SipLine/SipLine.h"

namespace SipMpCall
{

class SipLineSessionListenerMock : public SipLineSessionListener
{
public:
    MOCK_METHOD(void, onCallIncoming, (const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg), (override));
    MOCK_METHOD(void, onCallOutgoing, (const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg), (override));
    MOCK_METHOD(void, onCallAlerted, (const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg), (override));
    MOCK_METHOD(void, onCallAnswered, (const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg), (override));
    MOCK_METHOD(void, onCallUpdate, (const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg), (override));
    MOCK_METHOD(void, onCallRequestUpdate, (const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg), (override));
    MOCK_METHOD(void, onCallResponseUdate, (const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg), (override));
    MOCK_METHOD(void, onCallConnected, (const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg), (override));
    MOCK_METHOD(void, onCallRequestModify, (const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg), (override));
    MOCK_METHOD(void, onCallResponseModify, (const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg), (override));
    MOCK_METHOD(void, onCallTerminated, (const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg), (override));
};

typedef Common::Handle<SipLineSessionListenerMock> SipLineSessionListenerMockPtr;

class SipLineSessionMock : public SipLineSession
{
public:
    MOCK_METHOD(void, setSessId, (const std::string &sessId), (override));
    MOCK_METHOD(std::string, getCallId, (), (const, override));
    MOCK_METHOD(vector<std::string>, getCallIds, (), (const, override));

    MOCK_METHOD(bool, SipSetContactUri, (const std::string &pcContactUri), (override));
    MOCK_METHOD(bool, SipAlert, (const std::string &pcAnswerSdp), (override));
    MOCK_METHOD(bool, SipAnswer, (const std::string &pcAnswerSdp, const SipClient::SipCallExtHdrs &pcExtHdrs), (override));
    MOCK_METHOD(bool, SipTerm, (), (override));
    MOCK_METHOD(bool, SipUpdate, (const std::string &pcOfferSdp), (override));
    MOCK_METHOD(bool, SipUpdateRsp, (const std::string &pcAnswerSdp), (override));
    MOCK_METHOD(bool, SipAck, (const std::string &pcAnswerSdp), (override));

    MOCK_METHOD(bool, GetCalledUri, (std::string &ppcDispName, std::string &ppcUri), (override));
    MOCK_METHOD(bool, GetPeerUri, (std::string &ppcDispName, std::string &ppcUri), (override));
    MOCK_METHOD(bool, GetCallTermedReason, (std::string &ppcSipPhase, std::string &ppcReason), (override));
};

typedef Common::Handle<SipLineSessionMock> SipLineSessionMockPtr;

class SipLineListnerMock : public SipLineListner
{
public:
    MOCK_METHOD(SipLineSessionListenerPtr, onCreateSession, (const SipLineSessionPtr &session), (override));
};

typedef Common::Handle<SipLineListnerMock> SipLineListnerMockPtr;

using StrStrMap = std::map<std::string, std::string>;
using SessStatusMap = std::map<std::string, SipLine::SessStatus>;

class SipLineMock : public SipLine
{
public:
    MOCK_METHOD(bool, isReady, (Common::String &reason), (override));
    MOCK_METHOD(void, close, (), (override));
    MOCK_METHOD(void, schd, (), (override));
    MOCK_METHOD(void, updateConfigs, (), (override));
    MOCK_METHOD(int, getFreePercent, (), (override));

    MOCK_METHOD(SipLineSessionPtr, SipCall, (const SipLineSessionListenerPtr &listener, const std::string &sessId, const std::string &caller, const std::string &callee, const std::string &pcOfferSdp, const StrStrMap &extHdrs), (override));
    MOCK_METHOD(std::vector<std::string>, getCallId, (const std::string &sessId), (override));
    MOCK_METHOD(bool, getSessStatus, (const std::string &sessId, SessStatusMap &statuses), (override));
    MOCK_METHOD(std::string, getContactUri, (const std::string &callId, const std::string &userpart), (override));
    MOCK_METHOD(void, setKeepTermedStatus, (bool keepTermedSession), (override));

    void delegateToNice(const SipLineSessionPtr &session = 0)
    {
        ON_CALL(*this, isReady).WillByDefault(testing::Return(true));
        ON_CALL(*this, close).WillByDefault(testing::Invoke([]() {}));
        ON_CALL(*this, schd).WillByDefault(testing::Invoke([]() {}));
        ON_CALL(*this, updateConfigs).WillByDefault(testing::Invoke([]() {}));
        ON_CALL(*this, getFreePercent).WillByDefault(testing::Return(100));
        ON_CALL(*this, SipCall).WillByDefault(testing::Return(_session));
        ON_CALL(*this, getCallId).WillByDefault(testing::Return(std::vector<std::string>()));
        ON_CALL(*this, getSessStatus).WillByDefault(testing::Return(true));
        ON_CALL(*this, getContactUri).WillByDefault(testing::Return(""));
        ON_CALL(*this, setKeepTermedStatus).WillByDefault(testing::Invoke([](bool) {}));
    }

    SipLineSessionPtr _session;
};

typedef Common::Handle<SipLineMock> SipLineMockPtr;

} // namespace SipMpCall
