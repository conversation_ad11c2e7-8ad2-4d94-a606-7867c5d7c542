//
// *****************************************************************************
// Copyright (c) 2023 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//
// Created on 2023/5/25 by <PERSON>
//

#pragma once

#include "Common/Common.h"
#include "SipAdapter/SipCallInterface.h"
#include "SipLineBase.h"

namespace SipMpCall
{

using SipLineSessionListener = SipLineSessionBaseListener;
typedef Common::Handle<SipLineSessionListener> SipLineSessionListenerPtr;

class SipLineSession : public SipLineSessionBase
{
public:
    virtual vector<std::string> getCallIds() const = 0;
};

typedef Common::Handle<SipLineSession> SipLineSessionPtr;

class SipLineListner : virtual public Common::Shared
{
public:
    virtual SipLineSessionListenerPtr onCreateSession(const SipLineSessionPtr &session) = 0;
};

typedef Common::Handle<SipLineListner> SipLineListnerPtr;

class SipLine;
typedef Common::Handle<SipLine> SipLinePtr;
class SipLine : virtual public Common::Shared
{
public:
    struct SessEvent
    {
        std::string type;
        std::string time;
        std::string title;
        std::string detail;
        int64_t timestamp;
    };

    struct SessStatus
    {
        std::string localAddr;
        std::string remoteAddr;
        std::vector<SessEvent> events;
    };

public:
    static SipLinePtr create(const SipLineListnerPtr &listener, const Common::ApplicationPtr &app);
    static std::string getNumber(const std::string &uri);

    virtual bool isReady(Common::String &reason) = 0;
    virtual void close() = 0;
    virtual void schd() = 0;
    virtual void updateConfigs() = 0;
    virtual int getFreePercent() = 0;

    virtual SipLineSessionPtr SipCall(const SipLineSessionListenerPtr &listener, const std::string &sessId, const std::string &caller, const std::string &callee, const std::string &pcOfferSdp, const std::map<std::string, std::string> &extHdrs) = 0;
    virtual vector<std::string> getCallId(const std::string &sessId) = 0;
    virtual bool getSessStatus(const std::string &sessId, std::map<std::string, SessStatus> &statuses) = 0;
    virtual std::string getContactUri(const std::string &callId, const std::string &userpart) = 0;
    virtual void setKeepTermedStatus(bool keepTermedSession) = 0;
};

} // namespace SipMpCall