# library
aux_source_directory(impl SIP_LINE_SRC)
add_library(SipLine
    ${SIP_LINE_SRC}
)
target_include_directories(SipLine PRIVATE
    ${DEFAULT_INCLUDE_DIRECTORIES}
    ${PROJECT_SOURCE_DIR}/3rd/jssi/inc
)
target_compile_definitions(SipLine PRIVATE
    ${DEFAULT_COMPILE_DEFINITIONS}
)

# UnitTest
aux_source_directory(test SIP_LINE_TEST_SRC)
add_executable(SipLineUnitTest ${SIP_LINE_TEST_SRC})
target_include_directories(SipLineUnitTest PRIVATE
    ${DEFAULT_INCLUDE_DIRECTORIES}
    ${PROJECT_SOURCE_DIR}/3rd
)
target_compile_definitions(SipLineUnitTest PRIVATE
    ${DEFAULT_COMPILE_DEFINITIONS}
)

target_link_libraries(SipLineUnitTest SipLine SimpleSipSession SipAdapter Util JsmLog
    ${DEFAULT_SERVER_LIBRARIES}
    $ENV{JssSharedPath}/lib/${DEFAULT_LIB_PATH}/libgtest.a
    $ENV{JssSharedPath}/lib/${DEFAULT_LIB_PATH}/libgmock.a
    $ENV{JssSharedPath}/lib/${DEFAULT_LIB_PATH}/libgtest_main.a
    ${DEFAULT_SYS_LIBRARIES}
)
include(GoogleTest)
gtest_discover_tests(SipLineUnitTest)
