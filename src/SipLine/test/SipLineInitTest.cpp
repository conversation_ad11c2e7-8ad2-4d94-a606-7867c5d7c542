//
// *****************************************************************************
// Copyright (c) 2024 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//
// Created on 2024/5/21 by <PERSON>
//

#include "gmock/gmock.h"
#include "SipAdapter/SipCallInterfaceMock.h"
#include "SipLine/mock/SipLineMock.h"

#include "SipLine/SipLine.h"
#include "Common/TypesPub.h"

class SipLineInitTest : public testing::Test
{
public:
    void SetUp(Common::StrStrMap configs)
    {
        configs["global.Log.Level"] = "2";
        configs["global.Log.Print"] = "1";
        configs["global.SipCall.KeepAlive"] = "OFF";

        _app = Common::Application::create("SipLineInitTest", "", 0, configs);
        _app->activate();

        _sipLine = SipMpCall::SipLine::create(new SipMpCall::SipLineListnerMock(), _app);
        _sipLine->schd();
    }

    virtual void TearDown() override
    {
        _sipLine->close();
        _app->indShutdown();
        while (!_app->isShutdown())
            Common::sleep(10);
    }

    Common::ApplicationPtr _app;
    SipMpCall::SipLinePtr _sipLine;
    SipClient::SipCallInterfaceInjection _sipCallInjection;
};

TEST_F(SipLineInitTest, LocalAddress)
{
    Common::StrStrMap configs = {
        {"global.SipCall.LocalAddress", "127.0.0.1"}
    };
    SetUp(configs);

    Common::String reason;
    ASSERT_TRUE(_sipLine->isReady(reason));
    ASSERT_TRUE(_app->getStatistic("SipCall.LocalAddress") == "127.0.0.1:5060");
    ASSERT_TRUE(_app->getStatistic("SipCall.RemoteAddress") == "");
}

TEST_F(SipLineInitTest, RemoteAddress)
{
    Common::StrStrMap configs = {
        {"global.SipCall.RemoteAddress", "127.0.0.1:5070"}
    };
    SetUp(configs);

    Common::String reason;
    ASSERT_TRUE(_sipLine->isReady(reason));
    ASSERT_TRUE(_app->getStatistic("SipCall.LocalAddress") != "");
    ASSERT_TRUE(_app->getStatistic("SipCall.RemoteAddress") == "127.0.0.1:5070,1;");
}

TEST_F(SipLineInitTest, NoLocalListen)
{
    Common::StrStrMap configs = {
        {"global.SipCall.NoListening", "1"},
        {"global.SipCall.LocalAddress", "127.0.0.1:5060"},
        {"global.SipCall.RemoteAddress", "127.0.0.1:5070"}
    };
    SetUp(configs);

    Common::String reason;
    ASSERT_TRUE(_sipLine->isReady(reason));
    ASSERT_TRUE(_app->getStatistic("SipCall.LocalAddress") == "127.0.0.1:5060");
    ASSERT_TRUE(_app->getStatistic("SipCall.RemoteAddress") == "127.0.0.1:5070,1;");
}

TEST_F(SipLineInitTest, NoLocalListenPublicIp)
{
    Common::StrStrMap configs = {
        {"global.SipCall.NoListening", "1"},
        {"global.SipCall.LocalAddress", "************:5060"},
        {"global.SipCall.RemoteAddress", "127.0.0.1:5070"}};
    SetUp(configs);

    Common::String reason;
    ASSERT_TRUE(_sipLine->isReady(reason));
    ASSERT_TRUE(_app->getStatistic("SipCall.LocalAddress") != "");
    ASSERT_TRUE(_app->getStatistic("SipCall.PublicAddress") == "************:5060");
    ASSERT_TRUE(_app->getStatistic("SipCall.RemoteAddress") == "127.0.0.1:5070,1;");
}

TEST_F(SipLineInitTest, LocalRemoteAddress)
{
    Common::StrStrMap configs = {
        {"global.SipCall.LocalAddress", "127.0.0.1:5060"},
        {"global.SipCall.RemoteAddress", "127.0.0.1:5070"}
    };
    SetUp(configs);

    Common::String reason;
    ASSERT_TRUE(_sipLine->isReady(reason));
    ASSERT_TRUE(_app->getStatistic("SipCall.LocalAddress") == "127.0.0.1:5060");
    ASSERT_TRUE(_app->getStatistic("SipCall.RemoteAddress") == "127.0.0.1:5070,1;");
}

TEST_F(SipLineInitTest, LocalRemoteAddressPublicIp)
{
    Common::StrStrMap configs = {
        {"global.SipCall.LocalAddress", "************:5060"},
        {"global.SipCall.RemoteAddress", "127.0.0.1:5070"}
    };
    SetUp(configs);

    Common::String reason;
    ASSERT_TRUE(_sipLine->isReady(reason));
    ASSERT_TRUE(_app->getStatistic("SipCall.LocalAddress") != "");
    ASSERT_TRUE(_app->getStatistic("SipCall.PublicAddress") == "************:5060");
    ASSERT_TRUE(_app->getStatistic("SipCall.RemoteAddress") == "127.0.0.1:5070,1;");
}

TEST_F(SipLineInitTest, Weights)
{
    Common::StrStrMap configs = {
        {"global.SipCall.LocalAddress", "************:5060"},
        {"global.SipCall.RemoteAddress", "127.0.0.1:5070;127.0.0.1:5071,10;"}};
    SetUp(configs);

    Common::String reason;
    ASSERT_TRUE(_sipLine->isReady(reason));
    ASSERT_TRUE(_app->getStatistic("SipCall.LocalAddress") != "");
    ASSERT_TRUE(_app->getStatistic("SipCall.PublicAddress") == "************:5060");
    ASSERT_TRUE(_app->getStatistic("SipCall.RemoteAddress") == "127.0.0.1:5070,1;127.0.0.1:5071,10;");
}