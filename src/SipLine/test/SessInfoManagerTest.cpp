//
// *****************************************************************************
// Copyright (c) 2025 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//

#include "gtest/gtest.h"
#include "Common/Util.h"
#include "SipLine/impl/SipLineSessionI.h"

TEST(SipLineSessionManagerTest, NormalOperation)
{
    SipMpCall::SipLineSessionManager manager;

    // Test setSessId functionality
    SipMpCall::SipLineSessionIPtr sess1 = new SipMpCall::SipLineSessionI(&manager);
    ASSERT_NE(sess1, nullptr);
    sess1->setSessId("sess1");
    sess1->setCallId("call1");

    EXPECT_EQ(manager.getSessByCallId("call1")->getSessId(), "sess1");
    EXPECT_EQ(manager.getSess("sess1")->getCallId(), "call1");

    sess1->setCallId("call2");
    EXPECT_EQ(manager.getSessByCallId("call1")->getSessId(), "sess1");
    EXPECT_EQ(manager.getSessByCallId("call2")->getSessId(), "sess1");
    EXPECT_EQ(manager.getSess("sess1")->getCallId(), "call2");

    sess1->close();
    EXPECT_EQ(manager.getSessByCallId("call1"), nullptr);
    EXPECT_EQ(manager.getSessByCallId("call2"), nullptr);
    EXPECT_EQ(manager.getSess("sess1"), nullptr);
}

TEST(SipLineSessionManagerTest, NonExistentEntries)
{
    SipMpCall::SipLineSessionManager manager;
    // Test retrieving non-existent entries
    EXPECT_EQ(manager.getSess("nonexistent"), nullptr);
    EXPECT_EQ(manager.getSessByCallId("nonexistent"), nullptr);
}
