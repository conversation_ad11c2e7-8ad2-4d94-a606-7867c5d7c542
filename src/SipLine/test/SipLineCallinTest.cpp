//
// *****************************************************************************
// Copyright (c) 2025 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//

#include "gmock/gmock-spec-builders.h"
#include "gmock/gmock.h"
#include "gtest/gtest.h"
#include "SimpleSipSession/SipTypes.h"
#include "SipLine/mock/SipLineMock.h"
#include "SipTestServer.h"
#include "SipLineTestSchder.h"

#include "Common/Common.h"
#include "Common/TypesPub.h"
#include "JsmLog/JsmLog.h"

static void SipLineCallinTestLog(const char *file, int file_size, const char *func, int func_size, int line, int level, const char *mod, const char *info)
{
    Common::log(file, file_size, func, func_size, line, level, mod, info);
}

class SipLineCallinTest : public testing::Test
{
protected:
    void SetUp(Common::StrStrMap configs)
    {
        JsmClient::setLogCallback(SipLineCallinTestLog);
        configs["global.Log.Level"] = "2";
        configs["global.Log.Print"] = "1";
        configs["global.SipCall.LocalAddress"] = "127.0.0.1:5060";
        configs["global.SipCall.RemoteAddress"] = "127.0.0.1:5080";
        configs["global.SipCall.KeepAlive"] = "CRLF:30";

        _app = Common::Application::create("SipLineCallinTest", "", 0, configs);
        _app->activate();

        _sipServer = new SipTestServer();
        ASSERT_TRUE(_sipServer->open(_app->getDriver(), "127.0.0.1", 5080));

        _sipLineListner = new SipMpCall::SipLineListnerMock();
        _sipLine = SipMpCall::SipLine::create(_sipLineListner, _app);
        ASSERT_NE(_sipLine, nullptr);
        _app->addScheduler(new SipLineTestSchder(_sipLine));

        _sessionListener = new SipMpCall::SipLineSessionListenerMock();
    }

    void TearDown() override
    {
        _sipLine->close();
        _sipServer->close();
        _app->indShutdown();
        while (!_app->isShutdown())
            Common::sleep(10);
    }

    Common::ApplicationPtr _app;
    SipTestServerPtr _sipServer;
    SipMpCall::SipLineListnerMockPtr _sipLineListner;
    SipMpCall::SipLinePtr _sipLine;

    SipMpCall::SipLineSessionPtr _session;
    SipMpCall::SipLineSessionListenerMockPtr _sessionListener;
};

using ::testing::_;

static const char *INVITE = "INVITE sip:127.0.0.1:5060 SIP/2.0\r\n"
"Via: SIP/2.0/UDP 127.0.0.1:5080;branch=z9hG4bK-524288-1;rport\r\n"
"Max-Forwards: 70\r\n"
"From: <sip:alice@127.0.0.1:5080>;tag=1234567890\r\n"
"To: <sip:bob@127.0.0.1:5060>\r\n"
"Contact: <sip:alice@127.0.0.1:5080>\r\n"
"Call-ID: 1234567890\r\n"
"CSeq: 1 INVITE\r\n"
"User-Agent: Juphoon/1.0\r\n"
"Content-Length: 0\r\n"
"Content-Type: application/sdp\r\n"
"\r\n"
"v=0\r\n"
"o=alice 1234567890 1234567890 IN IP4 127.0.0.1\r\n"
"s=Session\r\n"
"c=IN IP4 127.0.0.1\r\n"
"t=0 0\r\n"
"m=audio 10000 RTP/AVP 0\r\n"
"\r\n";

static const char *ACK = "ACK sip:127.0.0.1:5060 SIP/2.0\r\n"
"Via: SIP/2.0/UDP 127.0.0.1:5080;branch=z9hG4bK-524288-1;rport\r\n"
"Max-Forwards: 70\r\n"
"From: <sip:alice@127.0.0.1:5080>;tag=1234567890\r\n"
"To: <sip:bob@127.0.0.1:5060>\r\n"
"Contact: <sip:alice@127.0.0.1:5080>\r\n"
"Call-ID: 1234567890\r\n"
"CSeq: 1 ACK\r\n"
"User-Agent: Juphoon/1.0\r\n"
"Content-Length: 0\r\n"
"\r\n";

TEST_F(SipLineCallinTest, Reject)
{
    SetUp({});

    EXPECT_CALL(*(_sipLineListner.get()), onCreateSession(_)).WillOnce([&](const SipMpCall::SipLineSessionPtr &session) {
        _session = session;
        return _sessionListener;
    });

    EXPECT_CALL(_sipServer->processor, process(_, _)).WillRepeatedly([&](SimpleSipSession::SipMessagePtr msg, const Common::NetSenderPtr &sender) {
        int statusCode;
        std::string reasonPhrase;
        msg->getStatusLine(statusCode, reasonPhrase);
        if (statusCode >= 200)
        {
            return SimpleSipSession::SipMessage::create(ACK);
        }

        return SimpleSipSession::SipMessagePtr();
    });

    EXPECT_CALL(*(_sessionListener.get()), onCallIncoming(_, _, _, _)).WillOnce([&](const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) {
        _session->setSessId("1234");
        _session->SipTerm();
    });

    Common::sleep(1000);

    SimpleSipSession::SipMessagePtr invite = SimpleSipSession::SipMessage::create(INVITE);
    invite->encode();
    _sipServer->send(invite->data());

    Common::sleep(6000);
}

TEST_F(SipLineCallinTest, AnswerAndTerminate)
{
    SetUp({});

    EXPECT_CALL(*(_sipLineListner.get()), onCreateSession(_)).WillOnce([&](const SipMpCall::SipLineSessionPtr &session) {
        _session = session;
        return _sessionListener;
    });

    EXPECT_CALL(_sipServer->processor, process(_, _)).WillRepeatedly([&](SimpleSipSession::SipMessagePtr msg, const Common::NetSenderPtr &sender) {
        if (msg->isRequest())
        {
            std::string method;
            msg->getMethod(method);
            if (method == "BYE")
            {
                return SimpleSipSession::SipMessage::genResponse(msg, 200, "OK");
            }
        }
        else
        {
            int statusCode;
            std::string reasonPhrase;
            msg->getStatusLine(statusCode, reasonPhrase);
            if (statusCode >= 200)
            {
                SimpleSipSession::SipMessagePtr ack = SimpleSipSession::SipMessage::create(ACK);
                ack->setHeader(SimpleSipSession::HeaderCName::TO, msg->getHeader(SimpleSipSession::HeaderCName::TO));
                return ack;
            }
        }

        return SimpleSipSession::SipMessagePtr();
    });

    EXPECT_CALL(*(_sessionListener.get()), onCallIncoming(_, _, _, _)).WillOnce([&](const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) {
        _session->setSessId("1234");
        _session->SipAlert("");
        _session->SipAnswer("", SipClient::SipCallExtHdrs());
    });

    EXPECT_CALL(*(_sessionListener.get()), onCallConnected(_, _, _, _)).Times(1);

    Common::sleep(1000);

    SimpleSipSession::SipMessagePtr invite = SimpleSipSession::SipMessage::create(INVITE);
    invite->encode();
    _sipServer->send(invite->data());

    Common::sleep(1000);
    _session->SipTerm();

    Common::sleep(1000);
}