//
// *****************************************************************************
// Copyright (c) 2025 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//

#include "gmock/gmock.h"
#include <thread>
#include "SipLine/mock/SipLineMock.h"
#include "SipTestServer.h"
#include "SipLineTestSchder.h"

#include "Common/Common.h"
#include "Common/TypesPub.h"
#include "SipLine/SipLine.h"
#include "SimpleSipSession/SipMessage.h"
#include "JsmLog/JsmLog.h"

void SipLineRetryTestLog(const char *file, int file_size, const char *func, int func_size, int line, int level, const char *mod, const char *info)
{
    Common::log(file, file_size, func, func_size, line, level, mod, info);
}

class SipLineRetryTest : public testing::Test
{
public:
    void SetUp(Common::StrStrMap configs)
    {
        JsmClient::setLogCallback(SipLineRetryTestLog);
        configs["global.Log.Level"] = "3";
        configs["global.Log.Print"] = "1";
        configs["global.SipCall.RemoteAddress"] = "127.0.0.1:5070,9999;127.0.0.1:5080,1";
        configs["global.SipCall.KeepAlive"] = "OFF";

        _app = Common::Application::create("SipLineRetryTest", "", 0, configs);
        _app->activate();

        _sipServer1 = new SipTestServer();
        ASSERT_TRUE(_sipServer1->open(_app->getDriver(), "127.0.0.1", 5070));

        _sipServer2 = new SipTestServer();
        ASSERT_TRUE(_sipServer2->open(_app->getDriver(), "127.0.0.1", 5080));

        _sipLine = SipMpCall::SipLine::create(new SipMpCall::SipLineListnerMock(), _app);
        ASSERT_NE(_sipLine, nullptr);
        _app->addScheduler(new SipLineTestSchder(_sipLine));
    }

    virtual void TearDown() override
    {
        _sipLine->close();
        _sipServer2->close();
        _sipServer1->close();
        _app->indShutdown();
        while (!_app->isShutdown())
            Common::sleep(10);
    }

    SipTestServerPtr _sipServer1;
    SipTestServerPtr _sipServer2;
    Common::ApplicationPtr _app;
    SipMpCall::SipLinePtr _sipLine;
};

using ::testing::_;
using ::testing::Return;

TEST_F(SipLineRetryTest, Normal)
{
    SetUp({});

    EXPECT_CALL(_sipServer1->processor, process(_, _)).WillRepeatedly(Return(SimpleSipSession::SipMessagePtr()));
    EXPECT_CALL(_sipServer2->processor, process(_, _)).WillRepeatedly([](SimpleSipSession::SipMessagePtr msg, const Common::NetSenderPtr &sender) {
        static SimpleSipSession::SipMessagePtr rsp480;
        std::string method;
        msg->getMethod(method);
        if (method == "INVITE")
        {
            if (!rsp480)
                rsp480 = SimpleSipSession::SipMessage::genResponse(msg, 480, "Temporarily Unavailable");
            return SimpleSipSession::SipMessage::genResponse(msg, 100, "Trying");
        }
        else if (method == "CANCEL")
        {
            SimpleSipSession::SipMessagePtr rsp200 = SimpleSipSession::SipMessage::genResponse(msg, 200, "OK");
            rsp200->encode();
            sender->send((const unsigned char *)rsp200->data().c_str(), rsp200->data().size());

            if (rsp480)
            {
                rsp480->encode();
                sender->send((const unsigned char *)rsp480->data().c_str(), rsp480->data().size());
                rsp480 = nullptr;
            }
        }

        return SimpleSipSession::SipMessagePtr();
    });

    SipMpCall::SipLineSessionListenerMockPtr listener = new SipMpCall::SipLineSessionListenerMock();
    EXPECT_CALL(*(listener.get()), onCallOutgoing(_, _, _, _)).Times(1);

    Common::sleep(4000);

    SipMpCall::SipLineSessionPtr session = _sipLine->SipCall(listener, "1234", "alice", "bob", "", std::map<std::string, std::string>());
    ASSERT_NE(session, nullptr);
    Common::sleep(4500);
    session->SipTerm();
    Common::sleep(1000);
}

TEST_F(SipLineRetryTest, NoRetry)
{
    SetUp({{"global.SipCall.ResponseTimeoutSeconds", "0"}});

    EXPECT_CALL(_sipServer1->processor, process(_, _)).WillRepeatedly(Return(SimpleSipSession::SipMessagePtr()));
    EXPECT_CALL(_sipServer2->processor, process(_, _)).WillRepeatedly([](SimpleSipSession::SipMessagePtr msg, const Common::NetSenderPtr &sender) {
        static SimpleSipSession::SipMessagePtr rsp480;
        std::string method;
        msg->getMethod(method);
        if (method == "INVITE")
        {
            if (!rsp480)
                rsp480 = SimpleSipSession::SipMessage::genResponse(msg, 480, "Temporarily Unavailable");
            return SimpleSipSession::SipMessage::genResponse(msg, 100, "Trying");
        }
        else if (method == "CANCEL")
        {
            SimpleSipSession::SipMessagePtr rsp200 = SimpleSipSession::SipMessage::genResponse(msg, 200, "OK");
            rsp200->encode();
            sender->send((const unsigned char *)rsp200->data().c_str(), rsp200->data().size());

            if (rsp480)
            {
                rsp480->encode();
                sender->send((const unsigned char *)rsp480->data().c_str(), rsp480->data().size());
                rsp480 = nullptr;
            }
        }

        return SimpleSipSession::SipMessagePtr();
    });

    unsigned int startTicks = Common::getCurTicks();
    unsigned int durationMs = 0;
    SipMpCall::SipLineSessionListenerMockPtr listener = new SipMpCall::SipLineSessionListenerMock();
    EXPECT_CALL(*(listener.get()), onCallOutgoing(_, _, _, _)).Times(0);
    EXPECT_CALL(*(listener.get()), onCallTerminated(_, _, _, _)).WillOnce([&](const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) {
        durationMs = Common::getCurTicks() - startTicks;
    });

    Common::sleep(4000);

    SipMpCall::SipLineSessionPtr session = _sipLine->SipCall(listener, "1234", "alice", "bob", "", std::map<std::string, std::string>());
    ASSERT_NE(session, nullptr);
    while (durationMs == 0)
        Common::sleep(1000);
    ASSERT_GT(durationMs, 32000);
}

TEST_F(SipLineRetryTest, Failed)
{
    SetUp({});

    EXPECT_CALL(_sipServer1->processor, process(_, _)).WillRepeatedly([](SimpleSipSession::SipMessagePtr msg, const Common::NetSenderPtr &sender) {
        std::thread([msg, sender]() {
            Common::sleep(6000);
            std::string method;
            msg->getMethod(method);
            SimpleSipSession::SipMessagePtr rsp;
            if (method == "INVITE")
                rsp = SimpleSipSession::SipMessage::genResponse(msg, 100, "Trying");
            else if (method == "CANCEL")
                rsp = SimpleSipSession::SipMessage::genResponse(msg, 200, "OK");

            if (rsp)
            {
                rsp->encode();
                sender->send((const unsigned char *)rsp->data().c_str(), rsp->data().size());
            }
        }).detach();

        return SimpleSipSession::SipMessagePtr();
    });
    EXPECT_CALL(_sipServer2->processor, process(_, _)).WillRepeatedly(Return(SimpleSipSession::SipMessagePtr()));

    unsigned int startTicks = Common::getCurTicks();
    unsigned int durationMs = 0;
    SipMpCall::SipLineSessionListenerMockPtr listener = new SipMpCall::SipLineSessionListenerMock();
    EXPECT_CALL(*(listener.get()), onCallTerminated(_, _, _, _)).WillOnce([&](const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) {
        durationMs = Common::getCurTicks() - startTicks;
    });

    Common::sleep(4000);

    SipMpCall::SipLineSessionPtr session = _sipLine->SipCall(listener, "1234", "alice", "bob", "", std::map<std::string, std::string>());
    ASSERT_NE(session, nullptr);
    Common::sleep(38000);
    ASSERT_GE(durationMs, 36000);
}