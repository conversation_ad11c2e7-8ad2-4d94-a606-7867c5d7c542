//
// *****************************************************************************
// Copyright (c) 2024 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//
// Created on 2024/5/21 by <PERSON>
//

#include "gmock/gmock.h"
#include "SipAdapter/SipCallInterfaceMock.h"
#include "SipLine/mock/SipLineMock.h"
#include "SipLineTestSchder.h"
#include "Common/Common.h"
#include "Common/TypesPub.h"
#include "SipLine/SipLine.h"
#include "SimpleSipSession/SipMessage.h"
#include "JsmLog/JsmLog.h"


class SipLineKeepAliveTestConn : public Common::NetReceiver
{
public:
    virtual void close()
    {
        _sender->close();
        _sender = 0;
    }

    virtual void recv(const unsigned char *data, int dataLen)
    {
        if (dataLen == 4 && memcmp(data, "\r\n\r\n", 4) == 0)
        {
            _crlfCount++;
        }
        else
        {
            SimpleSipSession::SipMessagePtr msg = SimpleSipSession::SipMessage::create(std::string((const char*)data, dataLen));
            std::string method;
            if (msg->getMethod(method) && method == "OPTIONS")
            {
                _optCount++;
                SimpleSipSession::SipMessagePtr res = SimpleSipSession::SipMessage::genResponse(msg, 200, "OK");
                res->encode();
                _sender->send((const unsigned char*)res->data().c_str(), res->data().size());
            }
        }
    }

    Common::NetSenderPtr _sender;
    int _crlfCount = 0;
    int _optCount = 0;
};

class SipLineKeepAliveTestListener : public Common::NetReceiver
{
public:
    void close()
    {
        _sender->close();
        _sender = 0;

        if (_conn)
        {
            _conn->close();
            _conn = 0;
        }
    }

    virtual Common::NetReceiverPtr recvConnection(const Common::NetSenderPtr &sender)
    {
        if (_conn)
        {
            _conn->close();
            _conn = 0;
        }

        _conn = new SipLineKeepAliveTestConn();
        _conn->_sender = sender;
        return _conn;
    }

    Common::NetSenderPtr _sender;
    Common::Handle<SipLineKeepAliveTestConn> _conn;
};

void SipLineKeepAliveTestLog(const char *file, int file_size, const char *func, int func_size, int line, int level, const char *mod, const char *info)
{
    Common::log(file, file_size, func, func_size, line, level, mod, info);
}

class SipLineKeepAliveTest : public testing::Test
{
public:
    void SetUp(Common::StrStrMap configs)
    {
        JsmClient::setLogCallback(SipLineKeepAliveTestLog);
        configs["global.Log.Level"] = "2";
        configs["global.Log.Print"] = "1";
        configs["global.SipCall.RemoteAddress"] = "127.0.0.1:5070";

        _app = Common::Application::create("SipLineKeepAliveTest", "", 0, configs);
        _app->activate();

        _listener = new SipLineKeepAliveTestListener();
        _listener->_sender = _app->getDriver()->listen("udp", "127.0.0.1", 5070, _listener);

        _sipLine = SipMpCall::SipLine::create(new SipMpCall::SipLineListnerMock(), _app);
        _app->addScheduler(new SipLineTestSchder(_sipLine));
    }

    virtual void TearDown() override
    {
        _sipLine->close();
        _listener->close();
        _app->indShutdown();
        while (!_app->isShutdown())
            Common::sleep(10);
    }

    Common::Handle<SipLineKeepAliveTestListener> _listener;
    Common::ApplicationPtr _app;
    SipMpCall::SipLinePtr _sipLine;
    SipClient::SipCallInterfaceInjection _sipCallInjection;
};

TEST_F(SipLineKeepAliveTest, Crlf)
{
    Common::StrStrMap configs = {
        {"global.SipCall.KeepAlive", "CRLF:5"}
    };
    SetUp(configs);
    Common::sleep(28000);
    EXPECT_EQ(_listener->_conn->_crlfCount, 6);
}

TEST_F(SipLineKeepAliveTest, Option)
{
    Common::StrStrMap configs = {
        {"global.SipCall.KeepAlive", "OPTIONS:15"}};
    SetUp(configs);
    Common::sleep(35000);
    EXPECT_EQ(_listener->_conn->_optCount, 3);
}

class SipLineKeepAliveTest2 : public testing::Test
{
public:
    void SetUp(Common::StrStrMap configs)
    {
        JsmClient::setLogCallback(SipLineKeepAliveTestLog);
        configs["global.Log.Level"] = "2";
        configs["global.Log.Print"] = "1";
        configs["global.SipCall.RemoteAddress"] = "127.0.0.1:5070;127.0.0.1:5080";

        _app = Common::Application::create("SipLineKeepAliveTest", "", 0, configs);
        _app->activate();

        for (int i = 0; i < 2; i++)
        {
            _listeners[i] = new SipLineKeepAliveTestListener();
            _listeners[i]->_sender = _app->getDriver()->listen("udp", "127.0.0.1", 5070 + i * 10, _listeners[i]);
        }

        _sipLine = SipMpCall::SipLine::create(new SipMpCall::SipLineListnerMock(), _app);
        _app->addScheduler(new SipLineTestSchder(_sipLine));
    }

    virtual void TearDown() override
    {
        _sipLine->close();
        for (int i = 0; i < 2; i++)
            _listeners[i]->close();
        _app->indShutdown();
        while (!_app->isShutdown())
            Common::sleep(10);
    }

    Common::Handle<SipLineKeepAliveTestListener> _listeners[2];
    Common::ApplicationPtr _app;
    SipMpCall::SipLinePtr _sipLine;
    SipClient::SipCallInterfaceInjection _sipCallInjection;
};

TEST_F(SipLineKeepAliveTest2, Crlf)
{
    Common::StrStrMap configs = {
        {"global.SipCall.KeepAlive", "CRLF:5"}};
    SetUp(configs);
    Common::sleep(28000);
    for (int i = 0; i < 2; i++)
        EXPECT_EQ(_listeners[i]->_conn->_crlfCount, 6);
}

TEST_F(SipLineKeepAliveTest2, Option)
{
    Common::StrStrMap configs = {
        {"global.SipCall.KeepAlive", "OPTIONS:15"}};
    SetUp(configs);
    Common::sleep(35000);
    for (int i = 0; i < 2; i++)
        EXPECT_EQ(_listeners[i]->_conn->_optCount, 3);
}
