//
// *****************************************************************************
// Copyright (c) 2025 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//

#include "gmock/gmock.h"
#include "SipLine/mock/SipLineMock.h"
#include "SipTestServer.h"
#include "SipLineTestSchder.h"

#include "Common/Common.h"
#include "Common/TypesPub.h"
#include "JsmLog/JsmLog.h"

void SipLineTestLog(const char *file, int file_size, const char *func, int func_size, int line, int level, const char *mod, const char *info)
{
    Common::log(file, file_size, func, func_size, line, level, mod, info);
}

class SipLineTest : public testing::Test
{
public:
    void SetUp(Common::StrStrMap configs)
    {
        JsmClient::setLogCallback(SipLineTestLog);
        configs["global.Log.Level"] = "3";
        configs["global.Log.Print"] = "1";
        configs["global.SipCall.RemoteAddress"] = "127.0.0.1:5080";
        configs["global.SipCall.KeepAlive"] = "OFF";

        _app = Common::Application::create("SipLineTest", "", 0, configs);
        _app->activate();

        _sipServer = new SipTestServer();
        ASSERT_TRUE(_sipServer->open(_app->getDriver(), "127.0.0.1", 5080));

        _sipLine = SipMpCall::SipLine::create(new SipMpCall::SipLineListnerMock(), _app);
        ASSERT_NE(_sipLine, nullptr);
        _app->addScheduler(new SipLineTestSchder(_sipLine));
    }

    virtual void TearDown() override
    {
        _sipLine->close();
        _sipServer->close();
        _app->indShutdown();
        while (!_app->isShutdown())
            Common::sleep(10);
    }

    SipTestServerPtr _sipServer;
    Common::ApplicationPtr _app;
    SipMpCall::SipLinePtr _sipLine;
};

using ::testing::_;
using ::testing::Return;

TEST_F(SipLineTest, CalloutCancel)
{
    Common::StrStrMap configs;
    SetUp(configs);

    EXPECT_CALL(_sipServer->processor, process(_, _)).WillRepeatedly([](SimpleSipSession::SipMessagePtr msg, const Common::NetSenderPtr &sender) {
        static SimpleSipSession::SipMessagePtr rsp487;
        std::string method;
        msg->getMethod(method);
        if (method == "INVITE")
        {
            SimpleSipSession::SipMessagePtr rsp = SimpleSipSession::SipMessage::genResponse(msg, 100, "Trying");
            rsp->encode();
            sender->send((const unsigned char *)rsp->data().c_str(), rsp->data().size());

            rsp = SimpleSipSession::SipMessage::genResponse(msg, 180, "Ringing");
            rsp->encode();
            sender->send((const unsigned char *)rsp->data().c_str(), rsp->data().size());

            rsp487 = SimpleSipSession::SipMessage::genResponse(msg, 487, "Request Terminated");
        }
        else if (method == "CANCEL")
        {
            SimpleSipSession::SipMessagePtr rsp = SimpleSipSession::SipMessage::genResponse(msg, 200, "OK");
            rsp->encode();
            sender->send((const unsigned char *)rsp->data().c_str(), rsp->data().size());

            return rsp487;
        }

        return SimpleSipSession::SipMessagePtr();
    });

    SipMpCall::SipLineSessionPtr session;

    SipMpCall::SipLineSessionListenerMockPtr listener = new SipMpCall::SipLineSessionListenerMock();
    EXPECT_CALL(*(listener.get()), onCallOutgoing(_, _, _, _)).Times(1);
    EXPECT_CALL(*(listener.get()), onCallAlerted(_, _, _, _)).Times(1);

    Common::sleep(4000);

    session = _sipLine->SipCall(listener, "1234", "alice", "bob", "", std::map<std::string, std::string>());
    ASSERT_NE(session, nullptr);
    Common::sleep(1000);
    session->SipTerm();
    Common::sleep(6000);
}

TEST_F(SipLineTest, CalloutBye)
{
    Common::StrStrMap configs;
    SetUp(configs);

    EXPECT_CALL(_sipServer->processor, process(_, _)).WillRepeatedly([](SimpleSipSession::SipMessagePtr msg, const Common::NetSenderPtr &sender) {
        std::string method;
        msg->getMethod(method);
        if (method == "INVITE")
        {
            SimpleSipSession::SipMessagePtr rsp = SimpleSipSession::SipMessage::genResponse(msg, 100, "Trying");
            rsp->encode();
            sender->send((const unsigned char *)rsp->data().c_str(), rsp->data().size());

            rsp = SimpleSipSession::SipMessage::genResponse(msg, 180, "Ringing");
            rsp->encode();
            sender->send((const unsigned char *)rsp->data().c_str(), rsp->data().size());

            rsp = SimpleSipSession::SipMessage::genResponse(msg, 200, "OK");
            rsp->encode();
            sender->send((const unsigned char *)rsp->data().c_str(), rsp->data().size());
        }
        else if (method == "ACK")
        {
            return SimpleSipSession::SipMessage::genRequest(msg, "BYE", "sip:127.0.0.1:5080");
        }

        return SimpleSipSession::SipMessagePtr();
    });

    SipMpCall::SipLineSessionPtr session;

    SipMpCall::SipLineSessionListenerMockPtr listener = new SipMpCall::SipLineSessionListenerMock();
    EXPECT_CALL(*(listener.get()), onCallOutgoing(_, _, _, _)).Times(1);
    EXPECT_CALL(*(listener.get()), onCallAlerted(_, _, _, _)).Times(1);
    EXPECT_CALL(*(listener.get()), onCallAnswered(_, _, _, _)).WillOnce([&session](const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) {
        session->SipAck("");
    });
    EXPECT_CALL(*(listener.get()), onCallTerminated(_, _, _, _)).Times(1);

    Common::sleep(4000);

    session = _sipLine->SipCall(listener, "1234", "alice", "bob", "", std::map<std::string, std::string>());
    ASSERT_NE(session, nullptr);
    Common::sleep(1000);
}

TEST_F(SipLineTest, CalloutTerm)
{
    Common::StrStrMap configs;
    SetUp(configs);

    EXPECT_CALL(_sipServer->processor, process(_, _)).WillRepeatedly([](SimpleSipSession::SipMessagePtr msg, const Common::NetSenderPtr &sender) {
        std::string method;
        msg->getMethod(method);
        if (method == "INVITE")
        {
            SimpleSipSession::SipMessagePtr rsp = SimpleSipSession::SipMessage::genResponse(msg, 100, "Trying");
            rsp->encode();
            sender->send((const unsigned char *)rsp->data().c_str(), rsp->data().size());

            rsp = SimpleSipSession::SipMessage::genResponse(msg, 180, "Ringing");
            rsp->encode();
            sender->send((const unsigned char *)rsp->data().c_str(), rsp->data().size());

            rsp = SimpleSipSession::SipMessage::genResponse(msg, 200, "OK");
            rsp->encode();
            sender->send((const unsigned char *)rsp->data().c_str(), rsp->data().size());
        }
        else if (method == "BYE")
        {
            return SimpleSipSession::SipMessage::genResponse(msg, 200, "OK");
        }

        return SimpleSipSession::SipMessagePtr();
    });

    SipMpCall::SipLineSessionPtr session;

    SipMpCall::SipLineSessionListenerMockPtr listener = new SipMpCall::SipLineSessionListenerMock();
    EXPECT_CALL(*(listener.get()), onCallOutgoing(_, _, _, _)).Times(1);
    EXPECT_CALL(*(listener.get()), onCallAlerted(_, _, _, _)).Times(1);
    EXPECT_CALL(*(listener.get()), onCallAnswered(_, _, _, _)).WillOnce([&session](const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) {
        session->SipAck("");
    });

    Common::sleep(4000);

    session = _sipLine->SipCall(listener, "1234", "alice", "bob", "", std::map<std::string, std::string>());
    ASSERT_NE(session, nullptr);
    Common::sleep(1000);
    session->SipTerm();
    Common::sleep(1000);
}

TEST_F(SipLineTest, CalloutReject)
{
    Common::StrStrMap configs;
    SetUp(configs);

    EXPECT_CALL(_sipServer->processor, process(_, _)).WillRepeatedly([](SimpleSipSession::SipMessagePtr msg, const Common::NetSenderPtr &sender) {
        std::string method;
        msg->getMethod(method);
        if (method == "INVITE")
        {
            SimpleSipSession::SipMessagePtr rsp = SimpleSipSession::SipMessage::genResponse(msg, 100, "Trying");
            rsp->encode();
            sender->send((const unsigned char *)rsp->data().c_str(), rsp->data().size());

            rsp = SimpleSipSession::SipMessage::genResponse(msg, 180, "Ringing");
            rsp->encode();
            sender->send((const unsigned char *)rsp->data().c_str(), rsp->data().size());

            rsp = SimpleSipSession::SipMessage::genResponse(msg, 486, "Busy Here");
            rsp->encode();
            sender->send((const unsigned char *)rsp->data().c_str(), rsp->data().size());
        }
        else if (method == "BYE")
        {
            return SimpleSipSession::SipMessage::genResponse(msg, 200, "OK");
        }

        return SimpleSipSession::SipMessagePtr();
    });

    SipMpCall::SipLineSessionPtr session;

    SipMpCall::SipLineSessionListenerMockPtr listener = new SipMpCall::SipLineSessionListenerMock();
    EXPECT_CALL(*(listener.get()), onCallOutgoing(_, _, _, _)).Times(1);
    EXPECT_CALL(*(listener.get()), onCallAlerted(_, _, _, _)).Times(1);
    EXPECT_CALL(*(listener.get()), onCallTerminated(_, _, _, _)).Times(1);

    Common::sleep(4000);

    session = _sipLine->SipCall(listener, "1234", "alice", "bob", "", std::map<std::string, std::string>());
    ASSERT_NE(session, nullptr);
    Common::sleep(1000);
    session->SipTerm();
    Common::sleep(1000);
}