//
// *****************************************************************************
// Copyright (c) 2025 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//

#include "SipLine/impl/SipLineI.h"
#include "gtest/gtest.h"
#include "Common/Util.h"
#include "SimpleSipSession/SipMessage.h"

using namespace SipMpCall;

TEST(SipEventManagerTest, SaveAndGetEvents)
{
    SipEventManager eventManager;

    eventManager.saveSipEvent("call1", "invite", "Call Invite", "Invite sent to user", "127.0.0.1", "***********");
    eventManager.saveSipEvent("call1", "ringing", "Ringing", "User phone is ringing", "127.0.0.1", "***********");
    eventManager.saveSipEvent("call1", "answer", "Call Answered", "User answered the call", "127.0.0.1", "***********");

    auto status = eventManager.getSessEvents("call1");
    ASSERT_EQ(status.events.size(), 3);

    EXPECT_EQ(status.events[0].type, "invite");
    EXPECT_EQ(status.events[0].title, "Call Invite");
    EXPECT_EQ(status.events[0].detail, "Invite sent to user");

    EXPECT_EQ(status.events[1].type, "ringing");
    EXPECT_EQ(status.events[1].title, "Ringing");
    EXPECT_EQ(status.events[1].detail, "User phone is ringing");

    EXPECT_EQ(status.events[2].type, "answer");
    EXPECT_EQ(status.events[2].title, "Call Answered");
    EXPECT_EQ(status.events[2].detail, "User answered the call");

    auto nonExistentEvents = eventManager.getSessEvents("nonexistent");
    EXPECT_TRUE(nonExistentEvents.events.empty());
}

TEST(SipEventManagerTest, MultipleCallIds)
{
    SipEventManager eventManager;

    eventManager.saveSipEvent("call1", "invite", "Call Invite", "Call 1 invite", "127.0.0.1", "***********");
    eventManager.saveSipEvent("call2", "invite", "Call Invite", "Call 2 invite", "127.0.0.1", "***********");
    eventManager.saveSipEvent("call3", "invite", "Call Invite", "Call 3 invite", "127.0.0.1", "***********");

    auto status1 = eventManager.getSessEvents("call1");
    ASSERT_EQ(status1.events.size(), 1);
    EXPECT_EQ(status1.events[0].detail, "Call 1 invite");

    auto status2 = eventManager.getSessEvents("call2");
    ASSERT_EQ(status2.events.size(), 1);
    EXPECT_EQ(status2.events[0].detail, "Call 2 invite");

    auto status3 = eventManager.getSessEvents("call3");
    ASSERT_EQ(status3.events.size(), 1);
    EXPECT_EQ(status3.events[0].detail, "Call 3 invite");
}

TEST(SipEventManagerTest, TermedSessionsHandling)
{
    SipEventManager eventManager(100);

    eventManager.saveSipEvent("call1", "invite", "Call Invite", "Invite details", "127.0.0.1", "***********");

    Common::sleep(150);

    eventManager.schd();
    auto status = eventManager.getSessEvents("call1");
    EXPECT_TRUE(status.events.empty());

    eventManager.setKeepTermedStatus(true);

    eventManager.saveSipEvent("call2", "invite", "Call Invite", "Invite details", "127.0.0.1", "***********");

    eventManager.schd();
    auto status2 = eventManager.getSessEvents("call2");
    EXPECT_FALSE(status2.events.empty());

    Common::sleep(150);
}

TEST(SipEventManagerTest, RecordSipMessage)
{
    SipEventManager eventManager;

    std::string inviteMsg =
        "INVITE sip:<EMAIL> SIP/2.0\r\n"
        "Via: SIP/2.0/UDP ***********:5060;branch=z9hG4bK776asdhds\r\n"
        "Max-Forwards: 70\r\n"
        "To: User <sip:<EMAIL>>\r\n"
        "From: Caller <sip:<EMAIL>>;tag=1928301774\r\n"
        "Call-ID: call1\r\n"
        "CSeq: 314159 INVITE\r\n"
        "Contact: <sip:caller@***********>\r\n"
        "Content-Type: application/sdp\r\n"
        "Content-Length: 0\r\n\r\n";

    auto sipMessage = SimpleSipSession::SipMessage::create(inviteMsg);

    eventManager.recordSipMessage(sipMessage, "send", "127.0.0.1", "***********");

    auto status = eventManager.getSessEvents("call1");
    ASSERT_FALSE(status.events.empty());
    EXPECT_EQ(status.events[0].type, "send");
    EXPECT_TRUE(status.events[0].title.find("INVITE") != std::string::npos);

    std::string okMsg =
        "SIP/2.0 200 OK\r\n"
        "Via: SIP/2.0/UDP ***********:5060;branch=z9hG4bK776asdhds\r\n"
        "To: User <sip:<EMAIL>>;tag=a6c85cf\r\n"
        "From: Caller <sip:<EMAIL>>;tag=1928301774\r\n"
        "Call-ID: call1\r\n"
        "CSeq: 314159 INVITE\r\n"
        "Contact: <sip:user@***********>\r\n"
        "Content-Type: application/sdp\r\n"
        "Content-Length: 0\r\n\r\n";

    auto responseMessage = SimpleSipSession::SipMessage::create(okMsg);
    eventManager.recordSipMessage(responseMessage, "recv", "127.0.0.1", "***********");

    status = eventManager.getSessEvents("call1");
    ASSERT_EQ(status.events.size(), 2);
    EXPECT_EQ(status.events[1].type, "recv");
    EXPECT_TRUE(status.events[1].title.find("INVITE") != std::string::npos);
}