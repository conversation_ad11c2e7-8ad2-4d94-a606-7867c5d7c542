//
// *****************************************************************************
// Copyright (c) 2025 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//

#include "gmock/gmock.h"
#include <thread>
#include "SipLine/mock/SipLineMock.h"
#include "SipTestServer.h"
#include "SipLineTestSchder.h"

#include "Common/Common.h"
#include "Common/TypesPub.h"
#include "SipLine/SipLine.h"
#include "SimpleSipSession/SipMessage.h"
#include "JsmLog/JsmLog.h"

void SipLineBreakTestLog(const char *file, int file_size, const char *func, int func_size, int line, int level, const char *mod, const char *info)
{
    Common::log(file, file_size, func, func_size, line, level, mod, info);
}

class SipLineBreakTest : public testing::Test
{
public:
    void SetUp(Common::StrStrMap configs)
    {
        JsmClient::setLogCallback(SipLineBreakTestLog);
        configs["global.Log.Level"] = "2";
        configs["global.Log.Print"] = "1";
        configs["global.SipCall.RemoteAddress"] = "127.0.0.1:5070,999;127.0.0.1:5080,1";
        configs["global.SipCall.KeepAlive"] = "OPTIONS:120";

        _app = Common::Application::create("SipLineBreakTest", "", 0, configs);
        _app->activate();

        _sipServer1 = new SipTestServer();
        ASSERT_TRUE(_sipServer1->open(_app->getDriver(), "127.0.0.1", 5070));

        _sipServer2 = new SipTestServer();
        ASSERT_TRUE(_sipServer2->open(_app->getDriver(), "127.0.0.1", 5080));

        _sipLine = SipMpCall::SipLine::create(new SipMpCall::SipLineListnerMock(), _app);
        ASSERT_NE(_sipLine, nullptr);
        _app->addScheduler(new SipLineTestSchder(_sipLine));
    }

    virtual void TearDown() override
    {
        _sipLine->close();
        _sipServer2->close();
        _sipServer1->close();
        _app->indShutdown();
        while (!_app->isShutdown())
            Common::sleep(10);
    }

    SipTestServerPtr _sipServer1;
    SipTestServerPtr _sipServer2;
    Common::ApplicationPtr _app;
    SipMpCall::SipLinePtr _sipLine;
};

class SipLineBreakTestSession;
typedef Common::Handle<SipLineBreakTestSession> SipLineBreakTestSessionPtr;

class SipLineBreakTestSession : public SipMpCall::SipLineSessionListenerMock
{
public:
    SipLineBreakTestSession()
        : _startTicks(Common::getCurTicks())
        , durationMs(0)
    {
    }

    bool isTerminated() const
    {
        return durationMs > 0;
    }

    void onCallTerminated(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) override
    {
        if (durationMs == 0)
            durationMs = Common::getCurTicks() - _startTicks;
        if (durationMs == 0)
            durationMs = 1;
    }

    static SipLineBreakTestSessionPtr call(const SipMpCall::SipLinePtr &sipLine)
    {
        static int sessId = 1000;
        SipLineBreakTestSessionPtr session = new SipLineBreakTestSession();
        session->session = sipLine->SipCall(session, std::to_string(sessId++), "alice", "bob", "", std::map<std::string, std::string>());
        return session;
    }

public:
    SipMpCall::SipLineSessionPtr session;
    unsigned int durationMs;

private:
    unsigned int _startTicks;
};

using ::testing::_;
using ::testing::Return;

TEST_F(SipLineBreakTest, FailRatio)
{
    Common::StrStrMap configs = {{"global.SipCall.LineBreak.RecoverySeconds", "10"}};
    SetUp(configs);

    EXPECT_CALL(_sipServer1->processor, process(_, _)).WillRepeatedly([](SimpleSipSession::SipMessagePtr msg, const Common::NetSenderPtr &sender) {
        std::string method;
        msg->getMethod(method);
        if (method == "OPTIONS")
            return SimpleSipSession::SipMessage::genResponse(msg, 200, "OK");
        return SimpleSipSession::SipMessagePtr();
    });
    EXPECT_CALL(_sipServer2->processor, process(_, _)).WillRepeatedly([](SimpleSipSession::SipMessagePtr msg, const Common::NetSenderPtr &sender) {
        std::string method;
        msg->getMethod(method);
        if (method == "OPTIONS")
            return SimpleSipSession::SipMessage::genResponse(msg, 200, "OK");
        else if (method == "INVITE")
            return SimpleSipSession::SipMessage::genResponse(msg, 486, "Busy Here");

        return SimpleSipSession::SipMessagePtr();
    });

    Common::sleep(4000);

    SipLineBreakTestSessionPtr session = SipLineBreakTestSession::call(_sipLine);
    while (!session->isTerminated())
        Common::sleep(10);
    ASSERT_GE(session->durationMs, 4000);

    for (int i = 0; i < 4; i++)
    {
        SipLineBreakTestSessionPtr session = SipLineBreakTestSession::call(_sipLine);
        while (!session->isTerminated())
            Common::sleep(10);
        ASSERT_LT(session->durationMs, 100);
        Common::sleep(2000);
    }

    Common::sleep(6000);

    session = SipLineBreakTestSession::call(_sipLine);
    while (!session->isTerminated())
        Common::sleep(10);
    ASSERT_GE(session->durationMs, 4000);
}

TEST_F(SipLineBreakTest, ContinuousFail)
{
    Common::StrStrMap configs = {{"global.SipCall.LineBreak.RecoverySeconds", "10"}, {"global.SipCall.LineBreak.FailRatio", "0"}};
    SetUp(configs);

    EXPECT_CALL(_sipServer1->processor, process(_, _)).WillRepeatedly([](SimpleSipSession::SipMessagePtr msg, const Common::NetSenderPtr &sender) {
        std::string method;
        msg->getMethod(method);
        if (method == "OPTIONS")
            return SimpleSipSession::SipMessage::genResponse(msg, 200, "OK");
        return SimpleSipSession::SipMessagePtr();
    });
    EXPECT_CALL(_sipServer2->processor, process(_, _)).WillRepeatedly([](SimpleSipSession::SipMessagePtr msg, const Common::NetSenderPtr &sender) {
        std::string method;
        msg->getMethod(method);
        if (method == "OPTIONS")
            return SimpleSipSession::SipMessage::genResponse(msg, 200, "OK");
        else if (method == "INVITE")
            return SimpleSipSession::SipMessage::genResponse(msg, 486, "Busy Here");

        return SimpleSipSession::SipMessagePtr();
    });

    Common::sleep(4000);

    for (int i = 0; i < 3; i++)
    {
        SipLineBreakTestSessionPtr session = SipLineBreakTestSession::call(_sipLine);
        while (!session->isTerminated())
            Common::sleep(10);
        ASSERT_GE(session->durationMs, 4000);
    }

    for (int i = 0; i < 4; i++)
    {
        SipLineBreakTestSessionPtr session = SipLineBreakTestSession::call(_sipLine);
        while (!session->isTerminated())
            Common::sleep(10);
        ASSERT_LT(session->durationMs, 100);
        Common::sleep(2000);
    }

    Common::sleep(6000);

    SipLineBreakTestSessionPtr session = SipLineBreakTestSession::call(_sipLine);
    while (!session->isTerminated())
        Common::sleep(10);
    ASSERT_GE(session->durationMs, 4000);
}

TEST_F(SipLineBreakTest, NoRetry)
{
    Common::StrStrMap configs = {
        {"global.SipCall.LineBreak.ContinuousFailCount", "2"},
        {"global.SipCall.LineBreak.DetectTimeSeconds", "120"},
        {"global.SipCall.LineBreak.RecoverySeconds", "10"},
        {"global.SipCall.LineBreak.FailRatio", "0"},
        {"global.SipCall.ResponseTimeoutSeconds", "0"},
    };
    SetUp(configs);

    EXPECT_CALL(_sipServer1->processor, process(_, _)).WillRepeatedly([](SimpleSipSession::SipMessagePtr msg, const Common::NetSenderPtr &sender) {
        std::string method;
        msg->getMethod(method);
        if (method == "OPTIONS")
            return SimpleSipSession::SipMessage::genResponse(msg, 200, "OK");
        return SimpleSipSession::SipMessagePtr();
    });
    EXPECT_CALL(_sipServer2->processor, process(_, _)).WillRepeatedly([](SimpleSipSession::SipMessagePtr msg, const Common::NetSenderPtr &sender) {
        std::string method;
        msg->getMethod(method);
        if (method == "OPTIONS")
            return SimpleSipSession::SipMessage::genResponse(msg, 200, "OK");
        else if (method == "INVITE")
            return SimpleSipSession::SipMessage::genResponse(msg, 486, "Busy Here");

        return SimpleSipSession::SipMessagePtr();
    });

    Common::sleep(4000);

    for (int i = 0; i < 2; i++)
    {
        SipLineBreakTestSessionPtr session = SipLineBreakTestSession::call(_sipLine);
        while (!session->isTerminated())
            Common::sleep(10);
        ASSERT_GE(session->durationMs, 30000);
    }

    Common::sleep(1000);

    for (int i = 0; i < 4; i++)
    {
        SipLineBreakTestSessionPtr session = SipLineBreakTestSession::call(_sipLine);
        while (!session->isTerminated())
            Common::sleep(10);
        ASSERT_LT(session->durationMs, 100);
        Common::sleep(2000);
    }

    Common::sleep(6000);

    SipLineBreakTestSessionPtr session = SipLineBreakTestSession::call(_sipLine);
    while (!session->isTerminated())
        Common::sleep(10);
    ASSERT_GE(session->durationMs, 32000);
}