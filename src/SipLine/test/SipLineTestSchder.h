//
// *****************************************************************************
// Copyright (c) 2025 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//

#pragma once

#include "Common/Common.h"
#include "SipLine/SipLine.h"

class SipLineTestSchder : public Common::AppScheduler
{
public:
    SipLineTestSchder(SipMpCall::SipLinePtr sipLine)
        : _sipLine(sipLine)
    {
    }

    virtual bool onActivate() override { return true; }
    virtual void onDeactivate() override {}
    virtual void onShutdown() override {}
    virtual void onSchd() override
    {
        if (_sipLine)
            _sipLine->schd();
    }
    virtual void onUpdateConfigs() override
    {
        if (_sipLine)
        {
            Common::String reason;
            _sipLine->isReady(reason);
            _sipLine->updateConfigs();
        }
    }

    SipMpCall::SipLinePtr _sipLine;
};

typedef Common::Handle<SipLineTestSchder> SipLineTestSchderPtr;
