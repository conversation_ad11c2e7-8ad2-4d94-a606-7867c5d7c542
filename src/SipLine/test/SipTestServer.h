//
// *****************************************************************************
// Copyright (c) 2025 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//

#pragma once

#include "gmock/gmock.h"
#include "SimpleSipSession/SipMessage.h"
#include "SimpleSipSession/SipServerRegistration.h"
#include "Common/Net.h"

class SipTestServerProcessor
{
public:
    virtual SimpleSipSession::SipMessagePtr process(SimpleSipSession::SipMessagePtr msg, const Common::NetSenderPtr &sender) = 0;
};

class SipTestServerProcessorMock : public SipTestServerProcessor
{
public:
    MOCK_METHOD(SimpleSipSession::SipMessagePtr, process, (SimpleSipSession::SipMessagePtr msg, const Common::NetSenderPtr &sender), (override));
};

class SipTestServerConn : public Common::NetReceiver
{
public:
    SipTestServerConn(SipTestServerProcessor *processor)
        : _processor(processor)
    {
    }

    virtual void close()
    {
        _sender->close();
        _sender = 0;
    }

    virtual void recv(const unsigned char *data, int dataLen)
    {
        SimpleSipSession::SipMessagePtr msg = SimpleSipSession::SipMessage::create(std::string((const char *)data, dataLen));
        if (!msg)
            return;

        if (onRecv(msg))
            return;

        msg = _processor->process(msg, _sender);
        if (msg)
        {
            msg->encode();
            _sender->send((const unsigned char *)msg->data().c_str(), msg->data().size());
        }
    }

    virtual bool onRecv(const SimpleSipSession::SipMessagePtr &message) { return false; }

    SipTestServerProcessor *_processor;
    Common::NetSenderPtr _sender;
};

typedef Common::Handle<SipTestServerConn> SipTestServerConnPtr;

class SipTestServer : public Common::NetReceiver
{
public:
    virtual bool open(const Common::NetDriverPtr &driver, const std::string &ip, int port)
    {
        _sender = driver->listen("udp", ip.c_str(), port, this);
        return _sender != nullptr;
    }

    virtual void close()
    {
        _sender->close();
        _sender = 0;

        if (_conn)
        {
            _conn->close();
            _conn = 0;
        }
    }

    virtual Common::NetReceiverPtr recvConnection(const Common::NetSenderPtr &sender) override
    {
        if (_conn)
        {
            _conn->close();
            _conn = 0;
        }

        _conn = onCreateConnection(&processor);
        _conn->_sender = sender;
        return _conn;
    }

    virtual bool send(const std::string &data)
    {
        return send((const unsigned char *)data.c_str(), data.size());
    }

    virtual bool send(const unsigned char *data, int dataLen)
    {
        if (!_conn)
            return false;

        _conn->_sender->send(data, dataLen);
        return true;
    }

    virtual SipTestServerConnPtr onCreateConnection(SipTestServerProcessor *processor)
    {
        return new SipTestServerConn(processor);
    }

public:
    SipTestServerProcessorMock processor;

private:
    Common::NetSenderPtr _sender;
    SipTestServerConnPtr _conn;
};

typedef Common::Handle<SipTestServer> SipTestServerPtr;

class SipTestRegistrationServer : public SipTestServer, public SimpleSipSession::ServerRegistration::Listener
{
public:
    class Connection : public SimpleSipSession::ServerRegistration::Connection, public SipTestServerConn
    {
    public:
        Connection(SipTestServerProcessor *processor, SimpleSipSession::ServerRegistrationPtr registration)
            : SipTestServerConn(processor)
            , _registration(registration)
        {
        }

        virtual void close() override
        {
            SipTestServerConn::close();
            _registration = 0;
        }

        virtual void onSendMessage(const SimpleSipSession::SipMessagePtr &message) override
        {
            message->encode();
            _sender->send((const unsigned char *)message->data().c_str(), message->data().size());
        }

        virtual bool onRecv(const SimpleSipSession::SipMessagePtr &message) override
        {
            return _registration->recv(message, this);
        }

        virtual std::string protocol() override
        {
            return "udp";
        }

        virtual SimpleSipSession::SipHostPort localHostPort() override
        {
            Common::String host;
            int port;
            _sender->getLocal(host, port);
            return SimpleSipSession::SipHostPort(host.c_str(), port);
        }

        virtual SimpleSipSession::SipHostPort remoteHostPort() override
        {
            Common::String host;
            int port;
            _sender->getRemote(host, port);
            return SimpleSipSession::SipHostPort(host.c_str(), port);
        }

    private:
        SimpleSipSession::ServerRegistrationPtr _registration;
    };

public:
    virtual bool open(const Common::NetDriverPtr &driver, const std::string &ip, int port) override
    {
        if (!SipTestServer::open(driver, ip, port))
            return false;

        _sipDriver = SimpleSipSession::SipDriver::create();
        _registration = SimpleSipSession::ServerRegistration::create(this,_sipDriver);
        return true;
    }

    virtual void close() override
    {
        SipTestServer::close();
        _registration->close();
        _registration = 0;
        _sipDriver->close();
        _sipDriver = 0;
    }

    virtual void onError(const Common::Error &error) override
    {
    }

    virtual SipTestServerConnPtr onCreateConnection(SipTestServerProcessor *processor) override
    {
        return new Connection(processor, _registration);
    }

private:
    SimpleSipSession::SipDriverPtr _sipDriver;
    SimpleSipSession::ServerRegistrationPtr _registration;
};

typedef Common::Handle<SipTestRegistrationServer> SipTestRegistrationServerPtr;
