//
// *****************************************************************************
// Copyright (c) 2025 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//

#include "SipLine/mock/SipLineMock.h"

#include "SipLineTestSchder.h"
#include "SipTestServer.h"

#include "SipLine/SipLine.h"
#include "Common/Common.h"
#include "Common/TypesPub.h"
#include "JsmLog/JsmLog.h"

using ::testing::_;

void SipLineBalanceTestLog(const char *file, int file_size, const char *func, int func_size, int line, int level, const char *mod, const char *info)
{
    Common::log(file, file_size, func, func_size, line, level, mod, info);
}

class SipLineBalanceTest : public testing::Test
{
public:
    void SetUp(Common::StrStrMap configs)
    {
        JsmClient::setLogCallback(SipLineBalanceTestLog);
        _sipServer1Count = 0;
        _sipServer2Count = 0;
        _sipServer3Count = 0;

        // configs["global.Log.Level"] = "2";
        // configs["global.Log.Print"] = "1";
        configs["global.SipCall.LocalAddress"] = "127.0.0.1:5060";
        configs["global.SipCall.KeepAlive"] = "OFF";

        _app = Common::Application::create("SipLineBalanceTest", "", 0, configs);
        _app->activate();

        _sipServer1 = new SipTestServer();
        ASSERT_TRUE(_sipServer1->open(_app->getDriver(), "127.0.0.1", 5070));

        _sipServer2 = new SipTestServer();
        ASSERT_TRUE(_sipServer2->open(_app->getDriver(), "127.0.0.1", 5080));

        _sipServer3 = new SipTestServer();
        ASSERT_TRUE(_sipServer3->open(_app->getDriver(), "127.0.0.1", 5090));

        _sipLine = SipMpCall::SipLine::create(new SipMpCall::SipLineListnerMock(), _app);
        ASSERT_NE(_sipLine, nullptr);
        _app->addScheduler(new SipLineTestSchder(_sipLine));

        EXPECT_CALL(_sipServer1->processor, process(_, _)).WillRepeatedly([this](SimpleSipSession::SipMessagePtr msg, const Common::NetSenderPtr &sender) {
            std::string method;
            msg->getMethod(method);
            if (method == "INVITE")
            {
                _sipServer1Count++;
                return SimpleSipSession::SipMessage::genResponse(msg, 486, "Busy Here");
            }

            return SimpleSipSession::SipMessagePtr();
        });

        EXPECT_CALL(_sipServer2->processor, process(_, _)).WillRepeatedly([this](SimpleSipSession::SipMessagePtr msg, const Common::NetSenderPtr &sender) {
            std::string method;
            msg->getMethod(method);
            if (method == "INVITE")
            {
                _sipServer2Count++;
                return SimpleSipSession::SipMessage::genResponse(msg, 486, "Busy Here");
            }

            return SimpleSipSession::SipMessagePtr();
        });

        EXPECT_CALL(_sipServer3->processor, process(_, _)).WillRepeatedly([this](SimpleSipSession::SipMessagePtr msg, const Common::NetSenderPtr &sender) {
            std::string method;
            msg->getMethod(method);
            if (method == "INVITE")
            {
                _sipServer3Count++;
                return SimpleSipSession::SipMessage::genResponse(msg, 486, "Busy Here");
            }

            return SimpleSipSession::SipMessagePtr();
        });

        Common::sleep(4000);
    }

    void TearDown() override
    {
        if (_sipServer1)
        {
            _sipServer1->close();
            _sipServer1 = nullptr;
        }

        if (_sipServer2)
        {
            _sipServer2->close();
            _sipServer2 = nullptr;
        }

        if (_sipServer3)
        {
            _sipServer3->close();
            _sipServer3 = nullptr;
        }

        if (_sipLine)
        {
            _sipLine->close();
            _sipLine = nullptr;
        }

        if (_app)
        {
            _app->indShutdown();
            while (!_app->isShutdown())
                Common::sleep(10);
            _app = nullptr;
        }
    }

    Common::ApplicationPtr _app;
    SipMpCall::SipLinePtr _sipLine;
    SipTestServerPtr _sipServer1;
    SipTestServerPtr _sipServer2;
    SipTestServerPtr _sipServer3;
    int _sipServer1Count;
    int _sipServer2Count;
    int _sipServer3Count;
};

class SipLineBalanceTestSession;
typedef Common::Handle<SipLineBalanceTestSession> SipLineBalanceTestSessionPtr;

class SipLineBalanceTestSession : public SipMpCall::SipLineSessionListenerMock
{
public:
    SipLineBalanceTestSession()
        : _startTicks(Common::getCurTicks())
        , durationMs(0)
    {
    }

    bool isTerminated() const
    {
        return durationMs > 0;
    }

    void onCallTerminated(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) override
    {
        if (durationMs == 0)
            durationMs = Common::getCurTicks() - _startTicks;
        if (durationMs == 0)
            durationMs = 1;
    }

    static SipLineBalanceTestSessionPtr call(const SipMpCall::SipLinePtr &sipLine)
    {
        static int sessId = 1000;
        SipLineBalanceTestSessionPtr session = new SipLineBalanceTestSession();
        session->session = sipLine->SipCall(session, std::to_string(sessId++), "alice", "bob", "", std::map<std::string, std::string>());
        return session;
    }

public:
    SipMpCall::SipLineSessionPtr session;
    unsigned int durationMs;

private:
    unsigned int _startTicks;
};

TEST_F(SipLineBalanceTest, Even)
{
    SetUp({{"global.SipCall.RemoteAddress", "127.0.0.1:5070,1;127.0.0.1:5080,1"}});

    for (int i = 0; i < 1000; i++)
    {
        SipLineBalanceTestSessionPtr session = SipLineBalanceTestSession::call(_sipLine);
        while (!session->isTerminated())
            Common::sleep(10);
        ASSERT_LT(session->durationMs, 500);
        if (i > 0 && i % 100 == 0)
            std::cout << "sipServer1Count:" << _sipServer1Count << " " << _sipServer1Count * 100 / (i + 1) << "%" << " sipServer2Count:" << _sipServer2Count << " " << _sipServer2Count * 100 / (i + 1) << "%" << std::endl;
    }

    std::cout << "sipServer1Count:" << _sipServer1Count << " " << _sipServer1Count * 100 / 1000 << "%" << " sipServer2Count:" << _sipServer2Count << " " << _sipServer2Count * 100 / 1000 << "%" << std::endl;
    ASSERT_GT(_sipServer1Count, 0);
    ASSERT_GT(_sipServer2Count, 0);
    ASSERT_NEAR(_sipServer1Count * 100 / (_sipServer1Count + _sipServer2Count), 50, 5);
}

TEST_F(SipLineBalanceTest, 9to1)
{
    SetUp({{"global.SipCall.RemoteAddress", "127.0.0.1:5070,9;127.0.0.1:5080,1"}});

    for (int i = 0; i < 1000; i++)
    {
        SipLineBalanceTestSessionPtr session = SipLineBalanceTestSession::call(_sipLine);
        while (!session->isTerminated())
            Common::sleep(10);
        ASSERT_LT(session->durationMs, 500);
        if (i > 0 && i % 100 == 0)
            std::cout << "sipServer1Count:" << _sipServer1Count << " " << _sipServer1Count * 100 / (i + 1) << "%" << " sipServer2Count:" << _sipServer2Count << " " << _sipServer2Count * 100 / (i + 1) << "%" << std::endl;
    }

    std::cout << "sipServer1Count:" << _sipServer1Count << " " << _sipServer1Count * 100 / 1000 << "%" << " sipServer2Count:" << _sipServer2Count << " " << _sipServer2Count * 100 / 1000 << "%" << std::endl;
    ASSERT_GT(_sipServer1Count, 0);
    ASSERT_GT(_sipServer2Count, 0);
    ASSERT_NEAR(_sipServer1Count * 100 / (_sipServer1Count + _sipServer2Count), 90, 3);
}

TEST_F(SipLineBalanceTest, Even3)
{
    SetUp({{"global.SipCall.RemoteAddress", "127.0.0.1:5070,1;127.0.0.1:5080,1;127.0.0.1:5090,1"}});

    for (int i = 0; i < 1000; i++)
    {
        SipLineBalanceTestSessionPtr session = SipLineBalanceTestSession::call(_sipLine);
        while (!session->isTerminated())
            Common::sleep(10);
        ASSERT_LT(session->durationMs, 500);
        if (i > 0 && i % 100 == 0)
            std::cout << "sipServer1Count:" << _sipServer1Count << " " << _sipServer1Count * 100 / (i + 1) << "%" << " sipServer2Count:" << _sipServer2Count << " " << _sipServer2Count * 100 / (i + 1) << "%" << " sipServer3Count:" << _sipServer3Count << " " << _sipServer3Count * 100 / (i + 1) << "%" << std::endl;
    }

    std::cout << "sipServer1Count:" << _sipServer1Count << " " << _sipServer1Count * 100 / 1000 << "%" << " sipServer2Count:" << _sipServer2Count << " " << _sipServer2Count * 100 / 1000 << "%" << " sipServer3Count:" << _sipServer3Count << " " << _sipServer3Count * 100 / 1000 << "%" << std::endl;
    ASSERT_GT(_sipServer1Count, 0);
    ASSERT_GT(_sipServer2Count, 0);
    ASSERT_GT(_sipServer3Count, 0);
    ASSERT_NEAR(_sipServer1Count * 100 / (_sipServer1Count + _sipServer2Count + _sipServer3Count), 33, 3);
    ASSERT_NEAR(_sipServer2Count * 100 / (_sipServer1Count + _sipServer2Count + _sipServer3Count), 33, 3);
    ASSERT_NEAR(_sipServer3Count * 100 / (_sipServer1Count + _sipServer2Count + _sipServer3Count), 33, 3);
}

TEST_F(SipLineBalanceTest, 631)
{
    SetUp({{"global.SipCall.RemoteAddress", "127.0.0.1:5070,6;127.0.0.1:5080,3;127.0.0.1:5090,1"}});

    for (int i = 0; i < 1000; i++)
    {
        SipLineBalanceTestSessionPtr session = SipLineBalanceTestSession::call(_sipLine);
        while (!session->isTerminated())
            Common::sleep(10);
        ASSERT_LT(session->durationMs, 500);
        if (i > 0 && i % 100 == 0)
            std::cout << "sipServer1Count:" << _sipServer1Count << " " << _sipServer1Count * 100 / (i + 1) << "%" << " sipServer2Count:" << _sipServer2Count << " " << _sipServer2Count * 100 / (i + 1) << "%" << " sipServer3Count:" << _sipServer3Count << " " << _sipServer3Count * 100 / (i + 1) << "%" << std::endl;
    }

    std::cout << "sipServer1Count:" << _sipServer1Count << " " << _sipServer1Count * 100 / 1000 << "%" << " sipServer2Count:" << _sipServer2Count << " " << _sipServer2Count * 100 / 1000 << "%" << " sipServer3Count:" << _sipServer3Count << " " << _sipServer3Count * 100 / 1000 << "%" << std::endl;
    ASSERT_GT(_sipServer1Count, 0);
    ASSERT_GT(_sipServer2Count, 0);
    ASSERT_GT(_sipServer3Count, 0);
    ASSERT_NEAR(_sipServer1Count * 100 / (_sipServer1Count + _sipServer2Count + _sipServer3Count), 60, 3);
    ASSERT_NEAR(_sipServer2Count * 100 / (_sipServer1Count + _sipServer2Count + _sipServer3Count), 30, 3);
    ASSERT_NEAR(_sipServer3Count * 100 / (_sipServer1Count + _sipServer2Count + _sipServer3Count), 10, 3);
}