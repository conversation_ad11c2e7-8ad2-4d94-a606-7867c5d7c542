//
// *****************************************************************************
// Copyright (c) 2025 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//

#include "gtest/gtest.h"
#include "SipLine/impl/SlidingWindowCounter.h"
#include "Common/Util.h"

TEST(SlidingWindowCounterTest, test)
{
    SipMpCall::SlidingWindowCounter counter(1000);
    for (int i = 0; i < 50; i++)
    {
        Common::sleep(100);
        counter.add();
        if (i > 10 && i % 3 == 0)
        {
            int sum = counter.getSum();
            EXPECT_NEAR(sum, 10, 1);
        }
    }
    EXPECT_NEAR(counter.getSum(), 10, 1);
}
