﻿//
// *****************************************************************************
// Copyright(c) 2017-2022 Juphoon System Software Co., LTD. All rights reserved.
// *****************************************************************************
//
// Auto generated from: EventSip.def
// Warning: do not edit this file.
//

#ifndef __SipEvent_EventSipPub_h
#define __SipEvent_EventSipPub_h

#include "Common/Common.h"

namespace SipEvent
{

/**
 * SIP事件类型枚举
 */
enum SipEventType
{
    /** 呼叫事件 */
    SipEventCall = 0,
    /** 振铃事件 */
    SipEventAlerted = 1,
    /** 通话事件 */
    SipEventConnected = 2,
    /** 结束事件 */
    SipEventTerminated = 3,
    /** 通话质量变化事件 */
    SipEventNetQualityChanged = 5,
    /** Rtp网络质量变化事件 */
    SipEventRtpNetQualityChanged = 6,
};
const char* SipEventType_toString(SipEvent::SipEventType val);

/**
 * 统一的SIP事件数据结构
 * 适用于：呼叫事件、振铃事件、通话事件、结束事件
 */
class EventSipDetail
{
public:
    EventSipDetail();
    EventSipDetail(Common::Long,Common::Long,Common::Long,Common::Long,const Common::String&,const Common::String&,const Common::String&,const Common::String&,int,Common::Long);

    bool operator<(const EventSipDetail&) const;
    bool operator==(const EventSipDetail&) const;
    bool operator!=(const EventSipDetail&__obj) const { return !operator==(__obj);}
    void __write(const Common::OputStreamPtr&) const;
    void __read(const Common::IputStreamPtr&);
    void __textWrite(const Common::OputStreamPtr&,const Common::String&) const;
    bool __textRead(const Common::IputStreamPtr&,const Common::String&,int = 0);
public:
    /** 域ID */
    Common::Long domainId;
    /** 应用ID */
    Common::Long appId;
    /** 开始时间 */
    Common::Long beginTimestamp;
    /** 结束时间（用于结束事件，其他事件为0）*/
    Common::Long endTimestamp;
    /** 通话ID */
    Common::String callId;
    /** SIP呼叫ID */
    Common::String sipCallId;
    /** 主叫号码 */
    Common::String caller;
    /** 被叫号码 */
    Common::String callee;
    /** 结束类型 */
    int endType;
    /** 通话时长(秒) */
    Common::Long duration;
};
void __write_EventSipDetail(const Common::OputStreamPtr&,const SipEvent::EventSipDetail&);
void __read_EventSipDetail(const Common::IputStreamPtr&,SipEvent::EventSipDetail&);
void __textWrite_EventSipDetail(const Common::OputStreamPtr&,const Common::String&,const SipEvent::EventSipDetail&);
bool __textRead_EventSipDetail(const Common::IputStreamPtr&,const Common::String&,SipEvent::EventSipDetail&,int = 0);

/**
 * SIP网络质量变化事件
 */
class EventSipNetworkChanged
{
public:
    EventSipNetworkChanged();
    EventSipNetworkChanged(Common::Long,Common::Long,Common::Long,Common::Long,const Common::String&,const Common::String&,const Common::String&,const Common::String&,Common::Long,int);

    bool operator<(const EventSipNetworkChanged&) const;
    bool operator==(const EventSipNetworkChanged&) const;
    bool operator!=(const EventSipNetworkChanged&__obj) const { return !operator==(__obj);}
    void __write(const Common::OputStreamPtr&) const;
    void __read(const Common::IputStreamPtr&);
    void __textWrite(const Common::OputStreamPtr&,const Common::String&) const;
    bool __textRead(const Common::IputStreamPtr&,const Common::String&,int = 0);
public:
    /** 域ID */
    Common::Long domainId;
    /** 应用ID */
    Common::Long appId;
    /** 开始时间 */
    Common::Long beginTimestamp;
    /** 结束时间（用于结束事件，其他事件为0）*/
    Common::Long endTimestamp;
    /** 通话ID */
    Common::String callId;
    /** SIP呼叫ID */
    Common::String sipCallId;
    /** 主叫号码 */
    Common::String caller;
    /** 被叫号码 */
    Common::String callee;
    /** 通话时长(秒) */
    Common::Long duration;
    /** 网络状态 */
    int networkStatus;
};
void __write_EventSipNetworkChanged(const Common::OputStreamPtr&,const SipEvent::EventSipNetworkChanged&);
void __read_EventSipNetworkChanged(const Common::IputStreamPtr&,SipEvent::EventSipNetworkChanged&);
void __textWrite_EventSipNetworkChanged(const Common::OputStreamPtr&,const Common::String&,const SipEvent::EventSipNetworkChanged&);
bool __textRead_EventSipNetworkChanged(const Common::IputStreamPtr&,const Common::String&,SipEvent::EventSipNetworkChanged&,int = 0);

/**
 * SIP通话记录数据结构
 * Topic: jrtc.records
 * 类型: 14
 * 编号: 1
 * 名称: 话单
 */
class SipCallRecord
{
public:
    SipCallRecord();
    SipCallRecord(Common::Long,Common::Long,Common::Long,Common::Long,const Common::String&,const Common::String&,const Common::String&,const Common::String&,int,Common::Long,const Common::String&,const Common::String&);

    bool operator<(const SipCallRecord&) const;
    bool operator==(const SipCallRecord&) const;
    bool operator!=(const SipCallRecord&__obj) const { return !operator==(__obj);}
    void __write(const Common::OputStreamPtr&) const;
    void __read(const Common::IputStreamPtr&);
    void __textWrite(const Common::OputStreamPtr&,const Common::String&) const;
    bool __textRead(const Common::IputStreamPtr&,const Common::String&,int = 0);
public:
    /** 域ID */
    Common::Long domainId;
    /** 应用ID */
    Common::Long appId;
    /** 开始时间 */
    Common::Long beginTimestamp;
    /** 结束时间 */
    Common::Long endTimestamp;
    /** 唯一Call-Id */
    Common::String callId;
    /** 业务ID(暂为空) */
    Common::String bizId;
    /** 主叫号码 */
    Common::String caller;
    /** 被叫号码 */
    Common::String callee;
    /** 结束类型 */
    int endType;
    /** 振铃时间 */
    Common::Long ringingTimeStamp;
    /** 来源：服务Id */
    Common::String source;
    /** 创建时间 */
    Common::String gmtCreate;
};
void __write_SipCallRecord(const Common::OputStreamPtr&,const SipEvent::SipCallRecord&);
void __read_SipCallRecord(const Common::IputStreamPtr&,SipEvent::SipCallRecord&);
void __textWrite_SipCallRecord(const Common::OputStreamPtr&,const Common::String&,const SipEvent::SipCallRecord&);
bool __textRead_SipCallRecord(const Common::IputStreamPtr&,const Common::String&,SipEvent::SipCallRecord&,int = 0);

};//namespace: SipEvent

#endif //__SipEvent_EventSipPub_h
