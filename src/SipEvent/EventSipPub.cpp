﻿//
// *****************************************************************************
// Copyright(c) 2017-2022 Juphoon System Software Co., LTD. All rights reserved.
// *****************************************************************************
//
// Auto generated from: EventSip.def
// Warning: do not edit this file.
//

#include "SipEvent/EventSipPub.h"

namespace SipEvent
{
const char* SipEventType_toString(SipEvent::SipEventType val)
{
    switch (val)
    {
    case SipEventCall: return (const char *)"SipEventCall";
    case SipEventAlerted: return (const char *)"SipEventAlerted";
    case SipEventConnected: return (const char *)"SipEventConnected";
    case SipEventTerminated: return (const char *)"SipEventTerminated";
    case SipEventNetQualityChanged: return (const char *)"SipEventNetQualityChanged";
    case SipEventRtpNetQualityChanged: return (const char *)"SipEventRtpNetQualityChanged";
    default: return (const char *)"unknown";
    }
}

EventSipDetail::EventSipDetail() :
    domainId(0),
    appId(0),
    beginTimestamp(0),
    endTimestamp(0),
    endType(0),
    duration(0)
{
}

EventSipDetail::EventSipDetail(Common::Long x_domainId,Common::Long x_appId,Common::Long x_beginTimestamp,Common::Long x_endTimestamp,const Common::String& x_callId,const Common::String& x_sipCallId,const Common::String& x_caller,const Common::String& x_callee,int x_endType,Common::Long x_duration) :
    domainId(x_domainId),appId(x_appId),beginTimestamp(x_beginTimestamp),endTimestamp(x_endTimestamp),callId(x_callId),sipCallId(x_sipCallId),caller(x_caller),callee(x_callee),endType(x_endType),duration(x_duration)
{
}

bool EventSipDetail::operator<(const EventSipDetail&__obj) const
{
    if (this == &__obj) return false;
    if (domainId < __obj.domainId) return true;
    if (__obj.domainId < domainId) return false;
    if (appId < __obj.appId) return true;
    if (__obj.appId < appId) return false;
    if (beginTimestamp < __obj.beginTimestamp) return true;
    if (__obj.beginTimestamp < beginTimestamp) return false;
    if (endTimestamp < __obj.endTimestamp) return true;
    if (__obj.endTimestamp < endTimestamp) return false;
    if (callId < __obj.callId) return true;
    if (__obj.callId < callId) return false;
    if (sipCallId < __obj.sipCallId) return true;
    if (__obj.sipCallId < sipCallId) return false;
    if (caller < __obj.caller) return true;
    if (__obj.caller < caller) return false;
    if (callee < __obj.callee) return true;
    if (__obj.callee < callee) return false;
    if (endType < __obj.endType) return true;
    if (__obj.endType < endType) return false;
    if (duration < __obj.duration) return true;
    if (__obj.duration < duration) return false;
    return false;
}

bool EventSipDetail::operator==(const EventSipDetail&__obj) const
{
    if (this == &__obj) return true;
    if (domainId != __obj.domainId) return false;
    if (appId != __obj.appId) return false;
    if (beginTimestamp != __obj.beginTimestamp) return false;
    if (endTimestamp != __obj.endTimestamp) return false;
    if (callId != __obj.callId) return false;
    if (sipCallId != __obj.sipCallId) return false;
    if (caller != __obj.caller) return false;
    if (callee != __obj.callee) return false;
    if (endType != __obj.endType) return false;
    if (duration != __obj.duration) return false;
    return true;
}

void EventSipDetail::__write(const Common::OputStreamPtr& __oput) const
{
    __write_EventSipDetail(__oput,*this);
}

void EventSipDetail::__read(const Common::IputStreamPtr& __iput)
{
    __read_EventSipDetail(__iput,*this);
}

void EventSipDetail::__textWrite(const Common::OputStreamPtr& __oput,const Common::String& __name) const
{
    __textWrite_EventSipDetail(__oput,__name,*this);
}

bool EventSipDetail::__textRead(const Common::IputStreamPtr& __iput,const Common::String& __name,int __idx)
{
    return __textRead_EventSipDetail(__iput,__name,*this,__idx);
}

void __write_EventSipDetail(const Common::OputStreamPtr& __oput,const SipEvent::EventSipDetail& __obj)
{
    __oput->write(__obj.domainId);
    __oput->write(__obj.appId);
    __oput->write(__obj.beginTimestamp);
    __oput->write(__obj.endTimestamp);
    __oput->write(__obj.callId);
    __oput->write(__obj.sipCallId);
    __oput->write(__obj.caller);
    __oput->write(__obj.callee);
    __oput->write(__obj.endType);
    __oput->write(__obj.duration);
}

void __read_EventSipDetail(const Common::IputStreamPtr& __iput,SipEvent::EventSipDetail& __obj)
{
    __iput->read(__obj.domainId);
    __iput->read(__obj.appId);
    __iput->read(__obj.beginTimestamp);
    __iput->read(__obj.endTimestamp);
    __iput->read(__obj.callId);
    __iput->read(__obj.sipCallId);
    __iput->read(__obj.caller);
    __iput->read(__obj.callee);
    __iput->read(__obj.endType);
    __iput->read(__obj.duration);
}

void __textWrite_EventSipDetail(const Common::OputStreamPtr& __oput,const Common::String& __name,const SipEvent::EventSipDetail& __obj)
{
    __oput->textStart(__name);
    __oput->textWrite("domainId",__obj.domainId);
    __oput->textWrite("appId",__obj.appId);
    __oput->textWrite("beginTimestamp",__obj.beginTimestamp);
    __oput->textWrite("endTimestamp",__obj.endTimestamp);
    __oput->textWrite("callId",__obj.callId);
    __oput->textWrite("sipCallId",__obj.sipCallId);
    __oput->textWrite("caller",__obj.caller);
    __oput->textWrite("callee",__obj.callee);
    __oput->textWrite("endType",__obj.endType);
    __oput->textWrite("duration",__obj.duration);
    __oput->textEnd();
}

bool __textRead_EventSipDetail(const Common::IputStreamPtr& __iput,const Common::String& __name,SipEvent::EventSipDetail& __obj,int __idx)
{
    if (!__iput->textStart(__name,__idx)) return false;
    __iput->textRead("domainId",__obj.domainId,0);
    __iput->textRead("appId",__obj.appId,0);
    __iput->textRead("beginTimestamp",__obj.beginTimestamp,0);
    __iput->textRead("endTimestamp",__obj.endTimestamp,0);
    __iput->textRead("callId",__obj.callId,0);
    __iput->textRead("sipCallId",__obj.sipCallId,0);
    __iput->textRead("caller",__obj.caller,0);
    __iput->textRead("callee",__obj.callee,0);
    __iput->textRead("endType",__obj.endType,0);
    __iput->textRead("duration",__obj.duration,0);
    __iput->textEnd();
    return true;
}

EventSipNetworkChanged::EventSipNetworkChanged() :
    domainId(0),
    appId(0),
    beginTimestamp(0),
    endTimestamp(0),
    duration(0),
    networkStatus(0)
{
}

EventSipNetworkChanged::EventSipNetworkChanged(Common::Long x_domainId,Common::Long x_appId,Common::Long x_beginTimestamp,Common::Long x_endTimestamp,const Common::String& x_callId,const Common::String& x_sipCallId,const Common::String& x_caller,const Common::String& x_callee,Common::Long x_duration,int x_networkStatus) :
    domainId(x_domainId),appId(x_appId),beginTimestamp(x_beginTimestamp),endTimestamp(x_endTimestamp),callId(x_callId),sipCallId(x_sipCallId),caller(x_caller),callee(x_callee),duration(x_duration),networkStatus(x_networkStatus)
{
}

bool EventSipNetworkChanged::operator<(const EventSipNetworkChanged&__obj) const
{
    if (this == &__obj) return false;
    if (domainId < __obj.domainId) return true;
    if (__obj.domainId < domainId) return false;
    if (appId < __obj.appId) return true;
    if (__obj.appId < appId) return false;
    if (beginTimestamp < __obj.beginTimestamp) return true;
    if (__obj.beginTimestamp < beginTimestamp) return false;
    if (endTimestamp < __obj.endTimestamp) return true;
    if (__obj.endTimestamp < endTimestamp) return false;
    if (callId < __obj.callId) return true;
    if (__obj.callId < callId) return false;
    if (sipCallId < __obj.sipCallId) return true;
    if (__obj.sipCallId < sipCallId) return false;
    if (caller < __obj.caller) return true;
    if (__obj.caller < caller) return false;
    if (callee < __obj.callee) return true;
    if (__obj.callee < callee) return false;
    if (duration < __obj.duration) return true;
    if (__obj.duration < duration) return false;
    if (networkStatus < __obj.networkStatus) return true;
    if (__obj.networkStatus < networkStatus) return false;
    return false;
}

bool EventSipNetworkChanged::operator==(const EventSipNetworkChanged&__obj) const
{
    if (this == &__obj) return true;
    if (domainId != __obj.domainId) return false;
    if (appId != __obj.appId) return false;
    if (beginTimestamp != __obj.beginTimestamp) return false;
    if (endTimestamp != __obj.endTimestamp) return false;
    if (callId != __obj.callId) return false;
    if (sipCallId != __obj.sipCallId) return false;
    if (caller != __obj.caller) return false;
    if (callee != __obj.callee) return false;
    if (duration != __obj.duration) return false;
    if (networkStatus != __obj.networkStatus) return false;
    return true;
}

void EventSipNetworkChanged::__write(const Common::OputStreamPtr& __oput) const
{
    __write_EventSipNetworkChanged(__oput,*this);
}

void EventSipNetworkChanged::__read(const Common::IputStreamPtr& __iput)
{
    __read_EventSipNetworkChanged(__iput,*this);
}

void EventSipNetworkChanged::__textWrite(const Common::OputStreamPtr& __oput,const Common::String& __name) const
{
    __textWrite_EventSipNetworkChanged(__oput,__name,*this);
}

bool EventSipNetworkChanged::__textRead(const Common::IputStreamPtr& __iput,const Common::String& __name,int __idx)
{
    return __textRead_EventSipNetworkChanged(__iput,__name,*this,__idx);
}

void __write_EventSipNetworkChanged(const Common::OputStreamPtr& __oput,const SipEvent::EventSipNetworkChanged& __obj)
{
    __oput->write(__obj.domainId);
    __oput->write(__obj.appId);
    __oput->write(__obj.beginTimestamp);
    __oput->write(__obj.endTimestamp);
    __oput->write(__obj.callId);
    __oput->write(__obj.sipCallId);
    __oput->write(__obj.caller);
    __oput->write(__obj.callee);
    __oput->write(__obj.duration);
    __oput->write(__obj.networkStatus);
}

void __read_EventSipNetworkChanged(const Common::IputStreamPtr& __iput,SipEvent::EventSipNetworkChanged& __obj)
{
    __iput->read(__obj.domainId);
    __iput->read(__obj.appId);
    __iput->read(__obj.beginTimestamp);
    __iput->read(__obj.endTimestamp);
    __iput->read(__obj.callId);
    __iput->read(__obj.sipCallId);
    __iput->read(__obj.caller);
    __iput->read(__obj.callee);
    __iput->read(__obj.duration);
    __iput->read(__obj.networkStatus);
}

void __textWrite_EventSipNetworkChanged(const Common::OputStreamPtr& __oput,const Common::String& __name,const SipEvent::EventSipNetworkChanged& __obj)
{
    __oput->textStart(__name);
    __oput->textWrite("domainId",__obj.domainId);
    __oput->textWrite("appId",__obj.appId);
    __oput->textWrite("beginTimestamp",__obj.beginTimestamp);
    __oput->textWrite("endTimestamp",__obj.endTimestamp);
    __oput->textWrite("callId",__obj.callId);
    __oput->textWrite("sipCallId",__obj.sipCallId);
    __oput->textWrite("caller",__obj.caller);
    __oput->textWrite("callee",__obj.callee);
    __oput->textWrite("duration",__obj.duration);
    __oput->textWrite("networkStatus",__obj.networkStatus);
    __oput->textEnd();
}

bool __textRead_EventSipNetworkChanged(const Common::IputStreamPtr& __iput,const Common::String& __name,SipEvent::EventSipNetworkChanged& __obj,int __idx)
{
    if (!__iput->textStart(__name,__idx)) return false;
    __iput->textRead("domainId",__obj.domainId,0);
    __iput->textRead("appId",__obj.appId,0);
    __iput->textRead("beginTimestamp",__obj.beginTimestamp,0);
    __iput->textRead("endTimestamp",__obj.endTimestamp,0);
    __iput->textRead("callId",__obj.callId,0);
    __iput->textRead("sipCallId",__obj.sipCallId,0);
    __iput->textRead("caller",__obj.caller,0);
    __iput->textRead("callee",__obj.callee,0);
    __iput->textRead("duration",__obj.duration,0);
    __iput->textRead("networkStatus",__obj.networkStatus,0);
    __iput->textEnd();
    return true;
}

SipCallRecord::SipCallRecord() :
    domainId(0),
    appId(0),
    beginTimestamp(0),
    endTimestamp(0),
    endType(0),
    ringingTimeStamp(0)
{
}

SipCallRecord::SipCallRecord(Common::Long x_domainId,Common::Long x_appId,Common::Long x_beginTimestamp,Common::Long x_endTimestamp,const Common::String& x_callId,const Common::String& x_bizId,const Common::String& x_caller,const Common::String& x_callee,int x_endType,Common::Long x_ringingTimeStamp,const Common::String& x_source,const Common::String& x_gmtCreate) :
    domainId(x_domainId),appId(x_appId),beginTimestamp(x_beginTimestamp),endTimestamp(x_endTimestamp),callId(x_callId),bizId(x_bizId),caller(x_caller),callee(x_callee),endType(x_endType),ringingTimeStamp(x_ringingTimeStamp),source(x_source),gmtCreate(x_gmtCreate)
{
}

bool SipCallRecord::operator<(const SipCallRecord&__obj) const
{
    if (this == &__obj) return false;
    if (domainId < __obj.domainId) return true;
    if (__obj.domainId < domainId) return false;
    if (appId < __obj.appId) return true;
    if (__obj.appId < appId) return false;
    if (beginTimestamp < __obj.beginTimestamp) return true;
    if (__obj.beginTimestamp < beginTimestamp) return false;
    if (endTimestamp < __obj.endTimestamp) return true;
    if (__obj.endTimestamp < endTimestamp) return false;
    if (callId < __obj.callId) return true;
    if (__obj.callId < callId) return false;
    if (bizId < __obj.bizId) return true;
    if (__obj.bizId < bizId) return false;
    if (caller < __obj.caller) return true;
    if (__obj.caller < caller) return false;
    if (callee < __obj.callee) return true;
    if (__obj.callee < callee) return false;
    if (endType < __obj.endType) return true;
    if (__obj.endType < endType) return false;
    if (ringingTimeStamp < __obj.ringingTimeStamp) return true;
    if (__obj.ringingTimeStamp < ringingTimeStamp) return false;
    if (source < __obj.source) return true;
    if (__obj.source < source) return false;
    if (gmtCreate < __obj.gmtCreate) return true;
    if (__obj.gmtCreate < gmtCreate) return false;
    return false;
}

bool SipCallRecord::operator==(const SipCallRecord&__obj) const
{
    if (this == &__obj) return true;
    if (domainId != __obj.domainId) return false;
    if (appId != __obj.appId) return false;
    if (beginTimestamp != __obj.beginTimestamp) return false;
    if (endTimestamp != __obj.endTimestamp) return false;
    if (callId != __obj.callId) return false;
    if (bizId != __obj.bizId) return false;
    if (caller != __obj.caller) return false;
    if (callee != __obj.callee) return false;
    if (endType != __obj.endType) return false;
    if (ringingTimeStamp != __obj.ringingTimeStamp) return false;
    if (source != __obj.source) return false;
    if (gmtCreate != __obj.gmtCreate) return false;
    return true;
}

void SipCallRecord::__write(const Common::OputStreamPtr& __oput) const
{
    __write_SipCallRecord(__oput,*this);
}

void SipCallRecord::__read(const Common::IputStreamPtr& __iput)
{
    __read_SipCallRecord(__iput,*this);
}

void SipCallRecord::__textWrite(const Common::OputStreamPtr& __oput,const Common::String& __name) const
{
    __textWrite_SipCallRecord(__oput,__name,*this);
}

bool SipCallRecord::__textRead(const Common::IputStreamPtr& __iput,const Common::String& __name,int __idx)
{
    return __textRead_SipCallRecord(__iput,__name,*this,__idx);
}

void __write_SipCallRecord(const Common::OputStreamPtr& __oput,const SipEvent::SipCallRecord& __obj)
{
    __oput->write(__obj.domainId);
    __oput->write(__obj.appId);
    __oput->write(__obj.beginTimestamp);
    __oput->write(__obj.endTimestamp);
    __oput->write(__obj.callId);
    __oput->write(__obj.bizId);
    __oput->write(__obj.caller);
    __oput->write(__obj.callee);
    __oput->write(__obj.endType);
    __oput->write(__obj.ringingTimeStamp);
    __oput->write(__obj.source);
    __oput->write(__obj.gmtCreate);
}

void __read_SipCallRecord(const Common::IputStreamPtr& __iput,SipEvent::SipCallRecord& __obj)
{
    __iput->read(__obj.domainId);
    __iput->read(__obj.appId);
    __iput->read(__obj.beginTimestamp);
    __iput->read(__obj.endTimestamp);
    __iput->read(__obj.callId);
    __iput->read(__obj.bizId);
    __iput->read(__obj.caller);
    __iput->read(__obj.callee);
    __iput->read(__obj.endType);
    __iput->read(__obj.ringingTimeStamp);
    __iput->read(__obj.source);
    __iput->read(__obj.gmtCreate);
}

void __textWrite_SipCallRecord(const Common::OputStreamPtr& __oput,const Common::String& __name,const SipEvent::SipCallRecord& __obj)
{
    __oput->textStart(__name);
    __oput->textWrite("domainId",__obj.domainId);
    __oput->textWrite("appId",__obj.appId);
    __oput->textWrite("beginTimestamp",__obj.beginTimestamp);
    __oput->textWrite("endTimestamp",__obj.endTimestamp);
    __oput->textWrite("callId",__obj.callId);
    __oput->textWrite("bizId",__obj.bizId);
    __oput->textWrite("caller",__obj.caller);
    __oput->textWrite("callee",__obj.callee);
    __oput->textWrite("endType",__obj.endType);
    __oput->textWrite("ringingTimeStamp",__obj.ringingTimeStamp);
    __oput->textWrite("source",__obj.source);
    __oput->textWrite("gmtCreate",__obj.gmtCreate);
    __oput->textEnd();
}

bool __textRead_SipCallRecord(const Common::IputStreamPtr& __iput,const Common::String& __name,SipEvent::SipCallRecord& __obj,int __idx)
{
    if (!__iput->textStart(__name,__idx)) return false;
    __iput->textRead("domainId",__obj.domainId,0);
    __iput->textRead("appId",__obj.appId,0);
    __iput->textRead("beginTimestamp",__obj.beginTimestamp,0);
    __iput->textRead("endTimestamp",__obj.endTimestamp,0);
    __iput->textRead("callId",__obj.callId,0);
    __iput->textRead("bizId",__obj.bizId,0);
    __iput->textRead("caller",__obj.caller,0);
    __iput->textRead("callee",__obj.callee,0);
    __iput->textRead("endType",__obj.endType,0);
    __iput->textRead("ringingTimeStamp",__obj.ringingTimeStamp,0);
    __iput->textRead("source",__obj.source,0);
    __iput->textRead("gmtCreate",__obj.gmtCreate,0);
    __iput->textEnd();
    return true;
}

};//namespace: SipEvent
