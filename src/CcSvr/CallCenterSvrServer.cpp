﻿//
// *****************************************************************************
// Copyright(c) 2017-2022 Juphoon System Software Co., LTD. All rights reserved.
// *****************************************************************************
//
// Auto generated from: CallCenterSvr.def
// Warning: do not edit this file.
//

#include "CcSvr/CallCenterSvrServer.h"

namespace CcSvr
{

bool CcSvrServer::__ex(const Common::ServerCallPtr& __call,const Common::String& __cmd,const Common::IputStreamPtr& __iput)
{
    if (__cmd == "keepalive.CcSvr.CcSvr") { __cmd_keepalive(__call,__iput);return true;}
    if (__cmd == "checkin.CcSvr.CcSvr") { __cmd_checkin(__call,__iput);return true;}
    if (__cmd == "checkout.CcSvr.CcSvr") { __cmd_checkout(__call,__iput);return true;}
    return false;
}

void CcSvrServer::keepalive_end(const Common::ServerCallPtr& __call,bool __ret)
{
    Common::VerListPtr __vers = __call->verList();
    Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
    short __ver = (__vers)?__vers->ver(true):0;
    switch (__ver)
    {
    case 0:
        __oput->write(__ret);
        break;
    }
    __call->cmdResult(__ver,__oput);
}

void CcSvrServer::checkin_end(const Common::ServerCallPtr& __call,bool __ret,const Common::String& key)
{
    Common::VerListPtr __vers = __call->verList();
    Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
    short __ver = (__vers)?__vers->ver(true):0;
    switch (__ver)
    {
    case 0:
        __oput->write(__ret);
        __oput->write(key);
        break;
    }
    __call->cmdResult(__ver,__oput);
}

void CcSvrServer::checkout_end(const Common::ServerCallPtr& __call,bool __ret)
{
    Common::VerListPtr __vers = __call->verList();
    Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
    short __ver = (__vers)?__vers->ver(true):0;
    switch (__ver)
    {
    case 0:
        __oput->write(__ret);
        break;
    }
    __call->cmdResult(__ver,__oput);
}

void CcSvrServer::__cmd_keepalive(const Common::ServerCallPtr& __call,const Common::IputStreamPtr& __iput)
{
    do
    {
        Common::VerListPtr __vers = __call->verList();
        Common::StrStrMap params;
        switch (__vers->ver(false))
        {
        case 0:
            Common::__read_StrStrMap(__iput,params);
            break;
        default: goto __ver_err;
        }
        __start(false);
        keepalive_begin(__call,params);
        return;
    } while(0);
__ver_err:
    Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
    __oput->write((short)1);
    __oput->write((short)0);
    __call->cmdResult(VersionException<<16,__oput);
}

void CcSvrServer::__cmd_checkin(const Common::ServerCallPtr& __call,const Common::IputStreamPtr& __iput)
{
    do
    {
        Common::VerListPtr __vers = __call->verList();
        switch (__vers->ver(false))
        {
        case 0:
            break;
        default: goto __ver_err;
        }
        __start(false);
        checkin_begin(__call);
        return;
    } while(0);
__ver_err:
    Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
    __oput->write((short)1);
    __oput->write((short)0);
    __call->cmdResult(VersionException<<16,__oput);
}

void CcSvrServer::__cmd_checkout(const Common::ServerCallPtr& __call,const Common::IputStreamPtr& __iput)
{
    do
    {
        Common::VerListPtr __vers = __call->verList();
        switch (__vers->ver(false))
        {
        case 0:
            break;
        default: goto __ver_err;
        }
        __start(false);
        checkout_begin(__call);
        return;
    } while(0);
__ver_err:
    Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
    __oput->write((short)1);
    __oput->write((short)0);
    __call->cmdResult(VersionException<<16,__oput);
}

};//namespace: CcSvr
