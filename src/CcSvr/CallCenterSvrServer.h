﻿//
// *****************************************************************************
// Copyright(c) 2017-2022 Juphoon System Software Co., LTD. All rights reserved.
// *****************************************************************************
//
// Auto generated from: CallCenterSvr.def
// Warning: do not edit this file.
//

#ifndef __CcSvr_CallCenterSvrServer_h
#define __CcSvr_CallCenterSvrServer_h

#include "CcSvr/CallCenterSvrPub.h"

namespace CcSvr
{

class CcSvrServer : virtual public Common::ObjectServer
{
public:
    virtual bool __ex(const Common::ServerCallPtr& __call,const Common::String& __cmd,const Common::IputStreamPtr& __iput);

    virtual void keepalive_begin(const Common::ServerCallPtr& __call,const Common::StrStrMap& params) = 0;
    virtual void checkin_begin(const Common::ServerCallPtr& __call) = 0;
    virtual void checkout_begin(const Common::ServerCallPtr& __call) = 0;

    static void keepalive_end(const Common::ServerCallPtr& __call,bool __ret);
    static void checkin_end(const Common::ServerCallPtr& __call,bool __ret,const Common::String& key);
    static void checkout_end(const Common::ServerCallPtr& __call,bool __ret);

    static inline void keepalive_end(const Common::ServerCallPtr &__call,const Common::CallError &error)
    {
        __call->setError(error);
        keepalive_end(__call,false);
    }
    static inline void checkin_end(const Common::ServerCallPtr &__call,const Common::CallError &error)
    {
        __call->setError(error);
        checkin_end(__call,false,"");
    }
    static inline void checkout_end(const Common::ServerCallPtr &__call,const Common::CallError &error)
    {
        __call->setError(error);
        checkout_end(__call,false);
    }

private:
    void __cmd_keepalive(const Common::ServerCallPtr& __call,const Common::IputStreamPtr& __iput);
    void __cmd_checkin(const Common::ServerCallPtr& __call,const Common::IputStreamPtr& __iput);
    void __cmd_checkout(const Common::ServerCallPtr& __call,const Common::IputStreamPtr& __iput);
};

};//namespace: CcSvr

#endif //__CcSvr_CallCenterSvrServer_h
