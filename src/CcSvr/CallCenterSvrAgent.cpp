﻿//
// *****************************************************************************
// Copyright(c) 2017-2022 Juphoon System Software Co., LTD. All rights reserved.
// *****************************************************************************
//
// Auto generated from: CallCenterSvr.def
// Warning: do not edit this file.
//

#include "CcSvr/CallCenterSvrAgent.h"

namespace CcSvr
{

bool CcSvrAgent::keepalive(const Common::StrStrMap& params,const Common::CallParamsPtr& __params) const throw()
{
    try
    {
        int __loop = 0;
        while (1)
        {
            Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
            Common::VerListPtr __vers = __agent->verList("keepalive.CcSvr.CcSvr");
            short __ver = 0;
            if (__vers)
            {
                __ver = __vers->ver(true);
                if (__ver > 0) __ver = 0;
            }
            switch (__ver)
            {
            case 0:
                __oput->write((short)1);
                __oput->write((short)__ver);
                Common::__write_StrStrMap(__oput,params);
                break;
            default:
                throw Common::CallException(Common::ObjectAgent::versionError("keepalive.CcSvr.CcSvr"));
            }
            Common::IputStreamPtr __iput;
            Common::CallError __error;
            int __rslt = __agent->ex_sync("keepalive.CcSvr.CcSvr",__oput,__iput,__params,__error);
            if (__rslt == -1)
                throw Common::CallException(__error);
            if (__rslt>>16)
            {
                Assert((__rslt>>16) == VersionException);
                __loop ++;
                if (__loop >= 3)
                    throw Common::CallException(Common::ObjectAgent::versionError("keepalive.CcSvr.CcSvr"));
                continue;
            }
            bool __ret;
            switch (__rslt)
            {
            case 0:
                __iput->read(__ret);
                break;
            default:
                throw Common::CallException(Common::ObjectAgent::versionError("keepalive.CcSvr.CcSvr"));
            }
            Common::ObjectAgent::processFinal(__iput);
            return __ret;
        }
    }
    catch (const Common::CallException& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
    catch (const Common::Exception& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
}

void CcSvrAgent::keepalive_begin(const Common::AgentAsyncPtr& __async,const Common::StrStrMap& params,const Common::CallParamsPtr& __params,const Common::ObjectPtr& __userdata) const throw()
{
    class __AgentAsync : public Common::AgentAsync
    {
    public:
        __AgentAsync(const Common::ObjectAgentPtr& __agent,const Common::AgentAsyncPtr& __async,const Common::StrStrMap& params,const Common::CallParamsPtr& __params,const Common::ObjectPtr& __userdata)
            : x__agent(__agent),x__async(__async),x_params(params),x__params(__params),x__userdata(__userdata),x__loop(0) {}

        void start()
        {
            Common::AgentAsyncPtr pthis = this;
            try
            {
                Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
                Common::VerListPtr __vers = x__agent->verList("keepalive.CcSvr.CcSvr");
                short __ver = 0;
                if (__vers)
                {
                    __ver = __vers->ver(true);
                    if (__ver > 0) __ver = 0;
                }
                switch (__ver)
                {
                case 0:
                    __oput->write((short)1);
                    __oput->write((short)__ver);
                    Common::__write_StrStrMap(__oput,x_params);
                    break;
                default:
                    throw Common::CallException(Common::ObjectAgent::versionError("keepalive.CcSvr.CcSvr"));
                }
                x__agent->ex_async(this,"keepalive.CcSvr.CcSvr",__oput,x__params,0);
            }
            catch (const Common::CallException& ex)
            {
                Common::ObjectAgent::throwException(x__async,ex,x__userdata);
            }
            catch (const Common::Exception& ex)
            {
                Common::ObjectAgent::throwException(x__async,ex,x__userdata);
            }
        }
        void cmdResult(int __rslt,const Common::IputStreamPtr& __iput,const Common::ObjectPtr& __userdata)
        {
            if ((__rslt>>16)!=VersionException)
            {
                if (x__async) x__async->cmdResult(__rslt,__iput,x__userdata);
                return;
            }
            x__loop ++;
            if (x__loop >= 3)
            {
                if (x__async)
                    Common::ObjectAgent::throwException(x__async, Common::CallException(Common::ObjectAgent::versionError("keepalive.CcSvr.CcSvr")), x__userdata);
                return;
            }
            start();
        }
    private:
        Common::ObjectAgentPtr x__agent;
        Common::AgentAsyncPtr x__async;
        Common::StrStrMap x_params;
        Common::CallParamsPtr x__params;
        Common::ObjectPtr x__userdata;
        int x__loop;
    };
    (new __AgentAsync(__agent,__async,params,__params,__userdata))->start();
}

bool CcSvrAgent::keepalive_end(int __rslt,const Common::IputStreamPtr& __iput) throw()
{
    try
    {
        Common::CallError __error;
        if (Common::ObjectAgent::processException(__rslt,__iput,__error))
            throw Common::CallException(__error);
        Assert((__rslt>>16) == 0);
        bool __ret;
        switch (__rslt)
        {
        case 0:
            __iput->read(__ret);
            break;
        default:
            throw Common::CallException(Common::ObjectAgent::versionError("keepalive.CcSvr.CcSvr"));
        }
        Common::ObjectAgent::processFinal(__iput);
        return __ret;
    }
    catch (const Common::CallException& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
    catch (const Common::Exception& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
}

bool CcSvrAgent::checkin(Common::String& key,const Common::CallParamsPtr& __params) const throw()
{
    try
    {
        int __loop = 0;
        while (1)
        {
            Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
            Common::VerListPtr __vers = __agent->verList("checkin.CcSvr.CcSvr");
            short __ver = 0;
            if (__vers)
            {
                __ver = __vers->ver(true);
                if (__ver > 0) __ver = 0;
            }
            switch (__ver)
            {
            case 0:
                __oput->write((short)1);
                __oput->write((short)__ver);
                break;
            default:
                throw Common::CallException(Common::ObjectAgent::versionError("checkin.CcSvr.CcSvr"));
            }
            Common::IputStreamPtr __iput;
            Common::CallError __error;
            int __rslt = __agent->ex_sync("checkin.CcSvr.CcSvr",__oput,__iput,__params,__error);
            if (__rslt == -1)
                throw Common::CallException(__error);
            if (__rslt>>16)
            {
                Assert((__rslt>>16) == VersionException);
                __loop ++;
                if (__loop >= 3)
                    throw Common::CallException(Common::ObjectAgent::versionError("checkin.CcSvr.CcSvr"));
                continue;
            }
            bool __ret;
            switch (__rslt)
            {
            case 0:
                __iput->read(__ret);
                __iput->read(key);
                break;
            default:
                throw Common::CallException(Common::ObjectAgent::versionError("checkin.CcSvr.CcSvr"));
            }
            Common::ObjectAgent::processFinal(__iput);
            return __ret;
        }
    }
    catch (const Common::CallException& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
    catch (const Common::Exception& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
}

void CcSvrAgent::checkin_begin(const Common::AgentAsyncPtr& __async,const Common::CallParamsPtr& __params,const Common::ObjectPtr& __userdata) const throw()
{
    class __AgentAsync : public Common::AgentAsync
    {
    public:
        __AgentAsync(const Common::ObjectAgentPtr& __agent,const Common::AgentAsyncPtr& __async,const Common::CallParamsPtr& __params,const Common::ObjectPtr& __userdata)
            : x__agent(__agent),x__async(__async),x__params(__params),x__userdata(__userdata),x__loop(0) {}

        void start()
        {
            Common::AgentAsyncPtr pthis = this;
            try
            {
                Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
                Common::VerListPtr __vers = x__agent->verList("checkin.CcSvr.CcSvr");
                short __ver = 0;
                if (__vers)
                {
                    __ver = __vers->ver(true);
                    if (__ver > 0) __ver = 0;
                }
                switch (__ver)
                {
                case 0:
                    __oput->write((short)1);
                    __oput->write((short)__ver);
                    break;
                default:
                    throw Common::CallException(Common::ObjectAgent::versionError("checkin.CcSvr.CcSvr"));
                }
                x__agent->ex_async(this,"checkin.CcSvr.CcSvr",__oput,x__params,0);
            }
            catch (const Common::CallException& ex)
            {
                Common::ObjectAgent::throwException(x__async,ex,x__userdata);
            }
            catch (const Common::Exception& ex)
            {
                Common::ObjectAgent::throwException(x__async,ex,x__userdata);
            }
        }
        void cmdResult(int __rslt,const Common::IputStreamPtr& __iput,const Common::ObjectPtr& __userdata)
        {
            if ((__rslt>>16)!=VersionException)
            {
                if (x__async) x__async->cmdResult(__rslt,__iput,x__userdata);
                return;
            }
            x__loop ++;
            if (x__loop >= 3)
            {
                if (x__async)
                    Common::ObjectAgent::throwException(x__async, Common::CallException(Common::ObjectAgent::versionError("checkin.CcSvr.CcSvr")), x__userdata);
                return;
            }
            start();
        }
    private:
        Common::ObjectAgentPtr x__agent;
        Common::AgentAsyncPtr x__async;
        Common::CallParamsPtr x__params;
        Common::ObjectPtr x__userdata;
        int x__loop;
    };
    (new __AgentAsync(__agent,__async,__params,__userdata))->start();
}

bool CcSvrAgent::checkin_end(int __rslt,const Common::IputStreamPtr& __iput,Common::String& key) throw()
{
    try
    {
        Common::CallError __error;
        if (Common::ObjectAgent::processException(__rslt,__iput,__error))
            throw Common::CallException(__error);
        Assert((__rslt>>16) == 0);
        bool __ret;
        switch (__rslt)
        {
        case 0:
            __iput->read(__ret);
            __iput->read(key);
            break;
        default:
            throw Common::CallException(Common::ObjectAgent::versionError("checkin.CcSvr.CcSvr"));
        }
        Common::ObjectAgent::processFinal(__iput);
        return __ret;
    }
    catch (const Common::CallException& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
    catch (const Common::Exception& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
}

bool CcSvrAgent::checkout(const Common::CallParamsPtr& __params) const throw()
{
    try
    {
        int __loop = 0;
        while (1)
        {
            Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
            Common::VerListPtr __vers = __agent->verList("checkout.CcSvr.CcSvr");
            short __ver = 0;
            if (__vers)
            {
                __ver = __vers->ver(true);
                if (__ver > 0) __ver = 0;
            }
            switch (__ver)
            {
            case 0:
                __oput->write((short)1);
                __oput->write((short)__ver);
                break;
            default:
                throw Common::CallException(Common::ObjectAgent::versionError("checkout.CcSvr.CcSvr"));
            }
            Common::IputStreamPtr __iput;
            Common::CallError __error;
            int __rslt = __agent->ex_sync("checkout.CcSvr.CcSvr",__oput,__iput,__params,__error);
            if (__rslt == -1)
                throw Common::CallException(__error);
            if (__rslt>>16)
            {
                Assert((__rslt>>16) == VersionException);
                __loop ++;
                if (__loop >= 3)
                    throw Common::CallException(Common::ObjectAgent::versionError("checkout.CcSvr.CcSvr"));
                continue;
            }
            bool __ret;
            switch (__rslt)
            {
            case 0:
                __iput->read(__ret);
                break;
            default:
                throw Common::CallException(Common::ObjectAgent::versionError("checkout.CcSvr.CcSvr"));
            }
            Common::ObjectAgent::processFinal(__iput);
            return __ret;
        }
    }
    catch (const Common::CallException& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
    catch (const Common::Exception& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
}

void CcSvrAgent::checkout_begin(const Common::AgentAsyncPtr& __async,const Common::CallParamsPtr& __params,const Common::ObjectPtr& __userdata) const throw()
{
    class __AgentAsync : public Common::AgentAsync
    {
    public:
        __AgentAsync(const Common::ObjectAgentPtr& __agent,const Common::AgentAsyncPtr& __async,const Common::CallParamsPtr& __params,const Common::ObjectPtr& __userdata)
            : x__agent(__agent),x__async(__async),x__params(__params),x__userdata(__userdata),x__loop(0) {}

        void start()
        {
            Common::AgentAsyncPtr pthis = this;
            try
            {
                Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
                Common::VerListPtr __vers = x__agent->verList("checkout.CcSvr.CcSvr");
                short __ver = 0;
                if (__vers)
                {
                    __ver = __vers->ver(true);
                    if (__ver > 0) __ver = 0;
                }
                switch (__ver)
                {
                case 0:
                    __oput->write((short)1);
                    __oput->write((short)__ver);
                    break;
                default:
                    throw Common::CallException(Common::ObjectAgent::versionError("checkout.CcSvr.CcSvr"));
                }
                x__agent->ex_async(this,"checkout.CcSvr.CcSvr",__oput,x__params,0);
            }
            catch (const Common::CallException& ex)
            {
                Common::ObjectAgent::throwException(x__async,ex,x__userdata);
            }
            catch (const Common::Exception& ex)
            {
                Common::ObjectAgent::throwException(x__async,ex,x__userdata);
            }
        }
        void cmdResult(int __rslt,const Common::IputStreamPtr& __iput,const Common::ObjectPtr& __userdata)
        {
            if ((__rslt>>16)!=VersionException)
            {
                if (x__async) x__async->cmdResult(__rslt,__iput,x__userdata);
                return;
            }
            x__loop ++;
            if (x__loop >= 3)
            {
                if (x__async)
                    Common::ObjectAgent::throwException(x__async, Common::CallException(Common::ObjectAgent::versionError("checkout.CcSvr.CcSvr")), x__userdata);
                return;
            }
            start();
        }
    private:
        Common::ObjectAgentPtr x__agent;
        Common::AgentAsyncPtr x__async;
        Common::CallParamsPtr x__params;
        Common::ObjectPtr x__userdata;
        int x__loop;
    };
    (new __AgentAsync(__agent,__async,__params,__userdata))->start();
}

bool CcSvrAgent::checkout_end(int __rslt,const Common::IputStreamPtr& __iput) throw()
{
    try
    {
        Common::CallError __error;
        if (Common::ObjectAgent::processException(__rslt,__iput,__error))
            throw Common::CallException(__error);
        Assert((__rslt>>16) == 0);
        bool __ret;
        switch (__rslt)
        {
        case 0:
            __iput->read(__ret);
            break;
        default:
            throw Common::CallException(Common::ObjectAgent::versionError("checkout.CcSvr.CcSvr"));
        }
        Common::ObjectAgent::processFinal(__iput);
        return __ret;
    }
    catch (const Common::CallException& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
    catch (const Common::Exception& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
}

};//namespace: CcSvr
