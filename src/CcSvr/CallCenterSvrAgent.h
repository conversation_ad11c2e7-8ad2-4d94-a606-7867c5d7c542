﻿//
// *****************************************************************************
// Copyright(c) 2017-2022 Juphoon System Software Co., LTD. All rights reserved.
// *****************************************************************************
//
// Auto generated from: CallCenterSvr.def
// Warning: do not edit this file.
//

#ifndef __CcSvr_CallCenterSvrAgent_h
#define __CcSvr_CallCenterSvrAgent_h

#include "CcSvr/CallCenterSvrPub.h"

namespace CcSvr
{

class CcSvrAgent : public Common::Agent
{
public:
    CcSvrAgent(int zero = 0) : Common::Agent(zero) {}
    CcSvrAgent(const Common::Agent& agent) : Common::Agent(agent) {}
    CcSvrAgent(const Common::ObjectAgentPtr& agent) : Common::Agent(agent) {}

    bool keepalive(const Common::StrStrMap& params,const Common::CallParamsPtr& __params = 0) const throw();
    void keepalive_begin(const Common::AgentAsyncPtr& __async,const Common::StrStrMap& params,const Common::CallParamsPtr& __params = 0,const Common::ObjectPtr& __userdata = 0) const throw();
    static bool keepalive_end(int __rslt,const Common::IputStreamPtr& __iput) throw();

    bool checkin(Common::String& key,const Common::CallParamsPtr& __params = 0) const throw();
    void checkin_begin(const Common::AgentAsyncPtr& __async,const Common::CallParamsPtr& __params = 0,const Common::ObjectPtr& __userdata = 0) const throw();
    static bool checkin_end(int __rslt,const Common::IputStreamPtr& __iput,Common::String& key) throw();

    bool checkout(const Common::CallParamsPtr& __params = 0) const throw();
    void checkout_begin(const Common::AgentAsyncPtr& __async,const Common::CallParamsPtr& __params = 0,const Common::ObjectPtr& __userdata = 0) const throw();
    static bool checkout_end(int __rslt,const Common::IputStreamPtr& __iput) throw();
};

};//namespace: CcSvr

#endif //__CcSvr_CallCenterSvrAgent_h
