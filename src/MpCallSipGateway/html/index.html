<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MpCallSipGateway 统计信息</title>
    <link rel="stylesheet" href="index.css">
</head>

<body>
    <h1>MpCallSipGateway 统计信息</h1>

    <div id="callList">
        <h2>活跃的会话</h2>
        <div>
            <label for="KeepTermedSession">保持结束会话的信息:</label>
            <select id="KeepTermedSession" name="KeepTermedSession">
                <option value="0">否</option>
                <option value="1">是</option>
            </select>
        </div>

        <button id="refreshCallList">刷新列表</button>
        <div id="callListContent"></div>
    </div>

    <div id="callDetails">
        <h2>会话详情</h2>
        <div id="callDetailsContent">
            <p>选择一个会话查看详情</p>
        </div>
    </div>

    <script>
        let apiBaseUrl = window.location.protocol + '//' + window.location.host;
        let selectedSessionId = null;

        function showSipMessageDetail(message) {
            console.log('显示SIP消息详情, ID:', message.id);

            const detailPanel = document.getElementById('sip-message-detail-panel');
            const title = document.getElementById('detail-message-title');
            const content = document.getElementById('detail-message-content');

            title.textContent = `${message.method || message.type || 'SIP消息'} (${message.direction === 'outgoing' ? '发送' : '接收'})`;
            content.textContent = message.content || 'No content available';
            detailPanel.style.display = 'block';
        }

        function loadConfig(configName) {
            fetch('/api/v1/config/' + configName)
                .then(response => {
                    if (!response.ok) {
                        throw new Error('获取配置失败, 状态码: ' + response.status);
                    }
                    return response.text();
                })
                .then(value => {
                    const select = document.getElementById(configName);
                    select.value = value;
                })
                .catch(error => {
                    console.error('获取配置失败:', error);
                });
        }

        function setupConfigChangeListener(configName) {
            const element = document.getElementById(configName);
            element.addEventListener('change', function () {
                const value = this.value;

                fetch('/api/v1/config/' + configName, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'text/plain'
                    },
                    body: value
                })
                    .then(response => {
                        if (!response.ok) {
                            throw new Error('配置更新失败, 状态码: ' + response.status);
                        }
                        return response.text();
                    })
                    .then(result => {
                        console.log('配置更新成功: ' + configName);
                    })
                    .catch(error => {
                        console.error('配置更新失败:', error);
                        // Revert select state on error
                        loadConfig(configName);
                    });
            });
        }

        // Get all calls
        function refreshCallList() {
            fetch('/api/v1/call/status')
                .then(response => {
                    if (!response.ok) {
                        throw new Error('获取会话列表失败, 状态码: ' + response.status);
                    }
                    return response.json();
                })
                .then(data => {
                    const listContent = document.getElementById('callListContent');
                    if (data.count === 0) {
                        listContent.innerHTML = '<p>没有活跃的会话</p>';
                        return;
                    }

                    data.sessions.sort((a, b) => a.startTimeMs - b.startTimeMs);
                    let html = '<table border="1"><tr><th>开始时间</th><th>会话ID</th><th>呼叫方向</th><th>主叫</th><th>被叫</th><th>Call-ID</th><th>随路数据</th><th>状态</th><th>时长(秒)</th><th>原因</th><th>操作</th></tr>';
                    data.sessions.forEach(session => {
                        html += `<tr>
                        <td>${formatTime(session.startTimeMs, true)}</td>
                        <td>${session.sessionId}</td>
                        <td>${session.direction == "outgoing" ? "呼出" : "呼入"}</td>
                        <td>${session.caller}</td>
                        <td>${session.callee}</td>
                        <td>${session.callId.replace(/;/g, ';<br>')}</td>
                        <td>${session.uui}</td>
                        <td>${session.state}</td>
                        <td>${session.durationMs / 1000}</td>
                        <td>${session.termedReason}</td>
                        <td><button onclick="getCallDetails('${session.sessionId}')">查看详情</button></td>
                    </tr>`;
                    });
                    html += '</table>';
                    listContent.innerHTML = html;
                })
                .catch(error => {
                    document.getElementById('callListContent').innerHTML =
                        `<p style="color: red">获取会话列表失败: ${error.message}</p>`;
                });
        }

        // Get call details
        function getCallDetails(sessionId) {
            fetch(`/api/v1/call/${encodeURIComponent(sessionId)}/status`)
                .then(response => {
                    if (!response.ok) {
                        throw new Error('获取会话详情失败, 状态码: ' + response.status);
                    }
                    return response.json();
                })
                .then(data => {
                    const detailsContent = document.getElementById('callDetailsContent');
                    let html = `<h3>会话ID: ${data.sessionId}</h3>`;
                    html += `<p>状态: ${data.state}</p>`;

                    html += '<h4>SIP消息流程图</h4>';
                    html += '<div id="sip-flow-container" style="display: flex; flex-direction: row; margin-bottom: 20px;">';
                    html += '<div id="sip-flow" style="flex: 1;"></div>';
                    html += '<div id="sip-message-detail-panel" style="flex: 1; margin-left: 20px; display: none;">';
                    html += '<h5 id="detail-message-title">SIP消息详情</h5>';
                    html += '<pre id="detail-message-content" style="white-space: pre-wrap; overflow-x: auto; border: 1px solid #ccc; padding: 10px; background-color: #f9f9f9;"></pre>';
                    html += '</div></div>';

                    if (data.rtpStats) {
                        html += '<h4>RTP 统计</h4>';
                        html += '<div style="display: flex; gap: 20px;">';

                        html += '<div>';
                        html += '<table border="1">';
                        html += '<colgroup><col style="width: 120px;"><col style="width: 120px;"><col style="width: 120px;"><col style="width: 120px;"><col style="width: 120px;"></colgroup>';
                        html += '<tr><th></th><th colspan="2">音频</th><th colspan="2">视频</th></tr>';

                        html += `<tr><td></td><td colspan="4">配置</td></tr>`;
                        html += `<tr><td>本地IP</td><td colspan="2">${data.rtpStats.audio.config.localIp}</td><td colspan="2">${data.rtpStats.video.config.localIp}</td></tr>`;
                        html += `<tr><td>本地端口</td><td colspan="2">${data.rtpStats.audio.config.localPort}</td><td colspan="2">${data.rtpStats.video.config.localPort}</td></tr>`;
                        html += `<tr><td>远程IP</td><td colspan="2">${data.rtpStats.audio.config.remoteIp}</td><td colspan="2">${data.rtpStats.video.config.remoteIp}</td></tr>`;
                        html += `<tr><td>远程端口</td><td colspan="2">${data.rtpStats.audio.config.remotePort}</td><td colspan="2">${data.rtpStats.video.config.remotePort}</td></tr>`;
                        html += `<tr><td>编解码</td><td colspan="2">${data.rtpStats.audio.config.codec}</td><td colspan="2">${data.rtpStats.video.config.codec}</td></tr>`;
                        html += `<tr><td>Payload</td><td colspan="2">${data.rtpStats.audio.config.payload}</td><td colspan="2">${data.rtpStats.video.config.payload}</td></tr>`;
                        html += `<tr><td>码率</td><td colspan="2">${data.rtpStats.audio.config.bitrate}</td><td colspan="2">${data.rtpStats.video.config.bitrate}</td></tr>`;
                        html += `<tr><td>打包时长</td><td colspan="2">${data.rtpStats.audio.config.packetLen}</td><td colspan="2"></td></tr>`;
                        html += `<tr><td>帧率</td><td colspan="2"></td><td colspan="2">${data.rtpStats.video.config.frameRate}</td></tr>`;
                        html += `<tr><td>分辨率</td><td colspan="2"></td><td colspan="2">${data.rtpStats.video.config.resolution}</td></tr>`;

                        html += `<tr><td></td><td>发送</td><td>接收</td><td>发送</td><td>接收</td></tr>`;
                        html += `<tr><td>包数量</td><td>${data.rtpStats.audio.send.totalPackets}</td><td>${data.rtpStats.audio.recv.totalPackets}</td><td>${data.rtpStats.video.send.totalPackets}</td><td>${data.rtpStats.video.recv.totalPackets}</td></tr>`;
                        html += `<tr><td>丢包数量</td><td>${data.rtpStats.audio.send.lostPackets}</td><td>${data.rtpStats.audio.recv.lostPackets}</td><td>${data.rtpStats.video.send.lostPackets}</td><td>${data.rtpStats.video.recv.lostPackets}</td></tr>`;
                        html += `<tr><td>丢包率</td><td>${data.rtpStats.audio.send.lostPercent}%</td><td>${data.rtpStats.audio.recv.lostPercent}%</td><td>${data.rtpStats.video.send.lostPercent}%</td><td>${data.rtpStats.video.recv.lostPercent}%</td></tr>`;
                        html += `<tr><td>抖动</td><td>${data.rtpStats.audio.send.jitter}</td><td>${data.rtpStats.audio.recv.jitter}</td><td>${data.rtpStats.video.send.jitter}</td><td>${data.rtpStats.video.recv.jitter}</td></tr>`;
                        html += `<tr><td>码率</td><td>${data.rtpStats.audio.send.bitrate}</td><td>${data.rtpStats.audio.recv.bitrate}</td><td>${data.rtpStats.video.send.bitrate}</td><td>${data.rtpStats.video.recv.bitrate}</td></tr>`;
                        html += `<tr><td>MOS</td><td></td><td>${data.rtpStats.audio.recv.tmos}</td><td>${data.rtpStats.video.send.spmos}</td><td>${data.rtpStats.video.recv.pvmos}</td></tr>`;
                        html += `<tr><td>帧率</td><td></td><td></td><td>${data.rtpStats.video.send.frameRate}</td><td>${data.rtpStats.video.recv.frameRate}</td></tr>`;
                        html += `<tr><td>分辨率</td><td></td><td></td><td>${data.rtpStats.video.send.resolution}</td><td>${data.rtpStats.video.recv.resolution}</td></tr>`;
                        html += `<tr><td>编解码</td><td></td><td></td><td>${data.rtpStats.video.send.codec}</td><td>${data.rtpStats.video.recv.codec}</td></tr>`;
                        html += `<tr><td>编码/解码时间</td><td></td><td></td><td>${data.rtpStats.video.send.encodeTime}</td><td>${data.rtpStats.video.recv.decodeTime}</td></tr>`;
                        html += `<tr><td>PNSR</td><td></td><td></td><td>${data.rtpStats.video.send.pnsr}</td><td></td></tr>`;

                        html += `<tr><td></td><td colspan="4">其它</td></tr>`;
                        html += `<tr><td>RTT</td><td colspan="2">${data.rtpStats.audio.general.rtt}</td><td colspan="2">${data.rtpStats.video.general.rtt}</td></tr>`;
                        html += `<tr><td>网络状态</td><td colspan="2">${data.rtpStats.audio.general.networkStatus}</td><td colspan="2">${data.rtpStats.video.general.networkStatus}</td></tr>`;
                        html += `<tr><td>采集分辨率</td><td colspan="2"></td><td colspan="2">${data.rtpStats.video.general.captureResolution}</td></tr>`;
                        html += `<tr><td>采集帧率</td><td colspan="2"></td><td colspan="2">${data.rtpStats.video.general.captureFrameRate}</td></tr>`;
                        html += `<tr><td>渲染帧率</td><td colspan="2"></td><td colspan="2">${data.rtpStats.video.general.renderFrameRate}</td></tr>`;

                        html += '</table>';
                        html += '</div>';

                        html += '</div>'; // End flex container
                    }

                    detailsContent.innerHTML = html;

                    // 在HTML渲染完成后加载SIP消息流程图
                    updateSipFlow(data.sipStats);
                })
                .catch(error => {
                    document.getElementById('callDetailsContent').innerHTML =
                        `<p style="color: red">获取会话详情失败: ${error.message}</p>`;
                });
        }

        // Format bytes to human-readable format
        function formatBytes(bytes) {
            if (bytes === 0) return '0 Bytes';

            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));

            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // Format bitrate to human-readable format
        function formatBitrate(bps) {
            if (bps === 0) return '0 bps';

            if (bps < 1000) return bps + ' bps';
            if (bps < 1000000) return (bps / 1000).toFixed(2) + ' Kbps';
            return (bps / 1000000).toFixed(2) + ' Mbps';
        }

        // Set up refresh button
        document.getElementById('refreshCallList').addEventListener('click', refreshCallList);

        // Initial load of call list
        loadConfig('KeepTermedSession');
        setupConfigChangeListener('KeepTermedSession');
        refreshCallList();

        function updateSipFlow(sipStats) {
            const flowContainer = document.getElementById('sip-flow');
            if (!flowContainer) return;
            flowContainer.innerHTML = '';

            const detailPanel = document.getElementById('sip-message-detail-panel');
            if (detailPanel) {
                detailPanel.style.display = 'none';
            }

            const allMessages = [];
            let localEndpoint = '本地端点';

            // 遍历 sipStats map，收集所有消息
            for (const [callId, stats] of Object.entries(sipStats)) {
                // 获取本地地址
                if (stats.local) {
                    localEndpoint = stats.local;
                }

                if (stats.events) {
                    // 收集所有消息
                    allMessages.push(...stats.events.map(event => {
                        return {
                            id: event.title,
                            method: event.title,
                            type: event.type,
                            direction: (event.type === 'send') ? 'outgoing' : 'incoming',
                            timestamp: event.timestamp,
                            content: event.detail,
                            remoteEndpoint: stats.remote,
                            callId: callId
                        };
                    }));
                }
            }

            // 按时间戳排序所有消息
            allMessages.sort((a, b) => a.timestamp - b.timestamp);

            // 根据消息时间顺序收集远程端点
            const remoteEndpoints = [];
            const remoteSet = new Set();
            allMessages.forEach(msg => {
                if (msg.remoteEndpoint && !remoteSet.has(msg.remoteEndpoint)) {
                    remoteEndpoints.push(msg.remoteEndpoint);
                    remoteSet.add(msg.remoteEndpoint);
                }
            });

            const sipData = {
                localEndpoint: localEndpoint,
                remoteEndpoints: remoteEndpoints,
                messages: allMessages
            };

            showSelectedSessionFlow(sipData);
        }

        function showSelectedSessionFlow(data) {
            const flowContainer = document.getElementById('sip-flow');
            flowContainer.innerHTML = '';

            if (!data || !data.messages || data.messages.length === 0) {
                flowContainer.innerHTML = '<div class="flow-no-messages">该会话没有SIP消息</div>';
                return;
            }

            const localEndpoint = data.localEndpoint || '本地端点';
            const remoteEndpoints = data.remoteEndpoints || ['远程端点'];

            const endpointDiv = document.createElement('div');
            endpointDiv.className = 'flow-endpoint';

            const localDiv = document.createElement('div');
            localDiv.className = 'endpoint endpoint-local';
            localDiv.textContent = localEndpoint;
            endpointDiv.appendChild(localDiv);

            // 添加远程端点
            const remoteCount = remoteEndpoints.length;
            remoteEndpoints.forEach((remoteEndpoint, index) => {
                const remoteDiv = document.createElement('div');
                remoteDiv.className = 'endpoint endpoint-remote';
                remoteDiv.textContent = remoteEndpoint;
                remoteDiv.style.marginLeft = '20px';
                remoteDiv.style.setProperty('--endpoint-index', index + 1);
                remoteDiv.style.setProperty('--remote-count', remoteCount);
                endpointDiv.appendChild(remoteDiv);
            });

            flowContainer.appendChild(endpointDiv);

            // 创建本地端点线
            const localLine = document.createElement('div');
            localLine.className = 'endpoint-line endpoint-line-left';
            flowContainer.appendChild(localLine);

            // 为每个远程端点创建线
            const remoteLines = remoteEndpoints.map((_, index) => {
                const line = document.createElement('div');
                line.className = 'endpoint-line endpoint-line-right';
                line.style.setProperty('--endpoint-index', index + 1);
                line.style.setProperty('--remote-count', remoteCount);
                flowContainer.appendChild(line);
                return line;
            });

            // 添加消息
            data.messages.forEach(message => {
                const isOutgoing = message.direction === 'outgoing';
                const remoteIndex = message.remoteEndpoint ?
                    remoteEndpoints.indexOf(message.remoteEndpoint) : 0;

                const messageDiv = document.createElement('div');
                messageDiv.className = `flow-message ${isOutgoing ? 'flow-outgoing' : 'flow-incoming'}`;

                const contentDiv = document.createElement('div');
                contentDiv.className = 'flow-content';

                const lineDiv = document.createElement('div');
                lineDiv.className = 'flow-line';
                lineDiv.style.setProperty('--endpoint-index', remoteIndex + 1);
                lineDiv.style.setProperty('--remote-count', remoteCount);

                contentDiv.appendChild(lineDiv);

                const arrowDiv = document.createElement('div');
                arrowDiv.className = `flow-arrow ${isOutgoing ? 'flow-arrow-right' : 'flow-arrow-left'}`;
                if (isOutgoing) {
                    arrowDiv.style.setProperty('--endpoint-index', remoteIndex + 1);
                    arrowDiv.style.setProperty('--remote-count', remoteCount);
                }
                contentDiv.appendChild(arrowDiv);

                const labelDiv = document.createElement('div');
                labelDiv.className = `flow-label ${isOutgoing ? 'flow-label-right' : 'flow-label-left'}`;
                labelDiv.textContent = `${message.method || message.type || 'SIP消息'}`;
                labelDiv.style.setProperty('--line-width', `${((remoteIndex + 1) * 450 / remoteCount)}px`);
                labelDiv.onclick = function () {
                    showSipMessageDetail(message);
                };
                contentDiv.appendChild(labelDiv);

                if (message.timestamp) {
                    const timeDiv = document.createElement('div');
                    timeDiv.className = `flow-timestamp ${isOutgoing ? 'flow-timestamp-right' : 'flow-timestamp-left'}`;
                    timeDiv.textContent = formatTime(message.timestamp, false);
                    contentDiv.appendChild(timeDiv);
                }

                messageDiv.appendChild(contentDiv);
                flowContainer.appendChild(messageDiv);
            });
        }

        function formatTime(timestamp, withDay) {
            const date = new Date(timestamp);
            const options = {
                fractionalSecondDigits: 3,
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit',
                hour12: false // 使用24小时制
            };
            if (withDay) {
                options.day = '2-digit';
                options.month = '2-digit';
                options.year = '2-digit';
            }
            return date.toLocaleTimeString('zh-CN', options);
        }

        window.getCallDetails = getCallDetails;
    </script>
</body>

</html>