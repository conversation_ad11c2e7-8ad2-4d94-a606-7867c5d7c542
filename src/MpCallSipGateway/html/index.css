/* SIP消息流程图样式 */
#sip-flow-container {
    margin: 15px 0;
    padding: 15px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: #f9f9f9;
    overflow-y: auto;
    position: relative;
}

#sip-flow {
    position: relative;
    margin: 0;
    max-width: 700px;
    /* 限制最大宽度 */
    margin-right: auto;
}

/* 端点和垂直线 */
.flow-endpoint {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
    position: relative;
}

.endpoint {
    position: absolute;
    background-color: #4a86e8;
    color: white;
    border-radius: 4px;
    text-align: center;
    width: 135px;
    font-size: 13px;
}

.endpoint-local {
    left: 135px; /* 与左侧竖线对齐 */
    transform: translateX(-50%);
}

.endpoint-remote {
    left: calc(var(--endpoint-index) * (450px/var(--remote-count)) + 135px);
    transform: translateX(-50%);
}

.endpoint-line {
    position: absolute;
    width: 2px;
    background-color: #4a86e8;
    top: 40px;
    bottom: 0;
    z-index: 0;
}

.endpoint-line-left {
    left: 135px;
    /* 将左侧竖线向右移动 */
}

.endpoint-line-right {
    left: calc(var(--endpoint-index) * (450px/var(--remote-count)) + 135px);
    /* 计算右侧竖线位置 */
}

/* 消息流样式 */
.flow-message {
    position: relative;
    min-height: 30px;
    margin: 15px 0;
    clear: both;
}

/* 消息内容 */
.flow-content {
    position: relative;
    margin: 8px 0;
    min-height: 24px;
    top: 18px;
}

/* 水平线和箭头 */
.flow-line {
    position: absolute;
    height: 2px;
    background-color: #333;
    top: 50%;
    transform: translateY(-50%);
    z-index: 1;
    /* 确保线条在垂直线上方 */
}

/* 入站消息（从右到左） */
.flow-incoming .flow-line {
    width: calc(var(--endpoint-index) * (450px/var(--remote-count)));
    /* 精确对准右侧垂直线 */
    left: 135px;
    /* 适应左侧垂直线的新位置 */
}

/* 出站消息（从左到右） */
.flow-outgoing .flow-line {
    left: 135px;
    /* 适应左侧垂直线的新位置 */
    width: calc(var(--endpoint-index) * (450px/var(--remote-count)));
    /* 精确对准右侧垂直线 */
}

/* 箭头样式 */
.flow-arrow {
    position: absolute;
    width: 0;
    height: 0;
    border-style: solid;
    top: 50%;
    margin-top: -6px;
    z-index: 2;
    /* 确保箭头在最上层 */
}

.flow-arrow-right {
    /* right: 83px; */
    /* 保持右侧箭头不变 */
    left: calc(var(--endpoint-index) * (450px/var(--remote-count)) + 125px);
    border-width: 6px 0 6px 10px;
    border-color: transparent transparent transparent #333;
}

.flow-arrow-left {
    left: 135px;
    /* 适应左侧垂直线的新位置 */
    border-width: 6px 10px 6px 0;
    border-color: transparent #333 transparent transparent;
}

/* 消息标签 */
.flow-label {
    position: absolute;
    background-color: #f0f0f0;
    border: 1px solid #ccc;
    border-radius: 4px;
    padding: 4px 8px;
    cursor: pointer;
    z-index: 3;
    /* 确保标签在最上层 */
    top: 50%;
    transform: translate(-50%, -50%);
    max-width: 300px;
    /* 增加最大宽度 */
    white-space: normal;
    /* 允许文本换行 */
    overflow: visible;
    /* 移除溢出隐藏 */
    text-align: center;
    left: calc(var(--line-width) / 2 + 135px);
    /* 居中显示 */
}

.flow-label:hover {
    background-color: #e0e0e0;
}

/* 时间戳 */
.flow-timestamp {
    position: absolute;
    color: #666;
    white-space: nowrap;
    top: 50%;
    transform: translateY(-50%);
}

/* 所有时间戳都显示在左侧垂直线左边 */
.flow-timestamp-right,
.flow-timestamp-left {
    left: 15px;
    /* 调整为左侧垂直线左边但更靠右 */
}

.flow-no-messages {
    text-align: center;
    color: #999;
    padding: 20px;
}

/* SIP消息详情模态框样式 */
#sip-message-detail-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.6);
    z-index: 1000;
}

.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.4);
}

.modal-content {
    background-color: #fefefe;
    margin: 5% auto;
    padding: 20px;
    border: 1px solid #888;
    width: 80%;
    max-width: 800px;
    max-height: 80vh;
    overflow-y: auto;
    border-radius: 5px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.close-button {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close-button:hover,
.close-button:focus {
    color: black;
    text-decoration: none;
}

#message-title {
    margin-top: 0;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
}

#message-content {
    white-space: pre-wrap;
    font-family: monospace;
    background-color: #f5f5f5;
    padding: 10px;
    border-radius: 4px;
    max-height: 50vh;
    overflow-y: auto;
}