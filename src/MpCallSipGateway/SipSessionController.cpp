//
// *****************************************************************************
// Copyright (c) 2025 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//

#include "SipSessionController.h"
#include "Error.h"
#include <exception>

namespace SipMpCall
{

SipSessionController::SipSessionController(const Common::ObjectAgentPtr &agent)
    : _agent(agent)
{
    if (!agent)
    {
        UTIL_LOG_ERR("SipSessionController", "content:invalid agent");
        throw std::runtime_error("InvalidAgent");
    }
}

bool SipSessionController::onIncomingCall(const CallParams &inParams, CallParams &outParams, AppParams &appParams)
{
    outParams.calleeType = MpCall::TypeApp;

    if (!_agent.onCallin(inParams.sessId, inParams.caller, inParams.callee, inParams.inviteConfig, outParams.sessId, outParams.callee, outParams.inviteConfig))
    {
        Common::setCallError(Error::ControllerFailed(EWRAP));
        return false;
    }

    return true;
}

bool SipSessionController::onOutgoingCall(const CallParams &inParams, CallParams &outParams)
{
    if (!_agent.onCallout(inParams.sessId, inParams.caller, inParams.callee, inParams.inviteConfig, outParams.callee, outParams.inviteConfig))
    {
        Common::setCallError(Error::ControllerFailed(EWRAP));
        return false;
    }

    return true;
}

bool SipSessionController::onHolded(const Common::String &sessId, bool hold)
{
    _agent.onHolded_begin(0, sessId, hold);
    return true;
}

bool SipSessionController::onTermed(const Common::String &sessId)
{
    _agent.onTermed_begin(0, sessId);
    return true;
}

SipSessionControllerManager::SipSessionControllerManager(const Common::ApplicationExPtr &app, const Common::String &controllerOid)
    : _app(app)
    , _controllerOid(controllerOid)
{
    if (_controllerOid.empty())
    {
        UTIL_LOG_ERR("SipSessionControllerManager", "content:invalid controller server oid");
        throw std::runtime_error("CreateSipSessionControllerManager:NoControllerServerConfig");
    }
}

SipSessionConfiguratorPtr SipSessionControllerManager::getConfigurator()
{
    try
    {
        return new SipSessionController(_app->createAgent(_controllerOid, false));
    }
    catch (const std::exception &e)
    {
        UTIL_LOG_ERR("SipSessionControllerManager", "content:getConfigurator exception:" + Common::String(e.what()));
        return nullptr;
    }
}

SipSessionEbServiceManager::SipSessionEbServiceManager(const Common::ApplicationExPtr &app)
    : SipSessionControllerManager(app, "EbService")
{
    app->setConfig("global.SipGateway.DummyRtpRunner", "1");
}

} // namespace SipMpCall
