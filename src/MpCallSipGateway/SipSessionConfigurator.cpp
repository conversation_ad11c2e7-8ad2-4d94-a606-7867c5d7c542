//
// *****************************************************************************
// Copyright (c) 2025 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//

#include "SipSessionConfigurator.h"
#include "SipSessionConfiguratorBase.h"
#include "SipSessionController.h"
#include <exception>

namespace SipMpCall
{

SipSessionConfiguratorManagerPtr SipSessionConfiguratorManager::create(const Common::ApplicationExPtr &app)
{
    try
    {
        if (app->getPrefixName().find("Eb") >= 0)
            return new SipSessionEbServiceManager(app);

        return new SipSessionConfiguratorBaseManager(app);
    }
    catch (std::exception &e)
    {
        UTIL_LOG_ERR("SipSessionConfiguratorManager", "content:create sip session configurator manager failed, reason:" + Common::String(e.what()));
        return nullptr;
    }
}

} // namespace SipMpCall
