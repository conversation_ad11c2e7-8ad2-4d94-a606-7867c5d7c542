# embeded html and css files
FILE(GLOB_RECURSE SIPGATEWAY_EMBEDED_RESOURCES "html/*.html" "html/*.css")
FOREACH(INPUT_FILE ${SIPGATEWAY_EMBEDED_RESOURCES})
    SET(OUTPUT_FILE ${INPUT_FILE}.h)
    LIST(APPEND SIPGATEWAY_EMBEDED_RESOURCES_SRC ${OUTPUT_FILE})
ENDFOREACH()

ADD_CUSTOM_COMMAND(
    OUTPUT html/__header.h ${SIPGATEWAY_EMBEDED_RESOURCES_SRC}
    COMMAND ${CMAKE_CURRENT_SOURCE_DIR}/xxd.sh ${SIPGATEWAY_EMBEDED_RESOURCES}
    COMMENT "Generate embeded html file"
)

# implement
aux_source_directory(. MPCALL_SIPGATEWAY_SRC)
add_library(MpCallSipGatewayImpl
    ${MPCALL_SIPGATEWAY_SRC}
    ${SIPGATEWAY_EMBEDED_RESOURCES_SRC}
    ../SipEvent/EventSipPub.cpp
)

target_include_directories(MpCallSipGatewayImpl PRIVATE
    ${DEFAULT_INCLUDE_DIRECTORIES}
    ${PROJECT_SOURCE_DIR}/3rd
)
target_compile_definitions(MpCallSipGatewayImpl PRIVATE
    ${DEFAULT_COMPILE_DEFINITIONS}
)

# service
aux_source_directory(service MPCALL_SIPGATEWAY_SERVICE_SRC)
add_executable(MpCallSipGateway
    ${WARP_GLIBC_SRC}
    ${MPCALL_SIPGATEWAY_SERVICE_SRC}
    ${WARP_GLIBC_SRC}
)

target_include_directories(MpCallSipGateway PRIVATE
    ${DEFAULT_INCLUDE_DIRECTORIES}
    ${PROJECT_SOURCE_DIR}/3rd
)
target_compile_definitions(MpCallSipGateway PRIVATE
    ${DEFAULT_COMPILE_DEFINITIONS}
)

target_link_libraries(MpCallSipGateway MpCallSipGatewayImpl SipLine SimpleSipSession JsmLog ServiceRunnerManager ServiceRunnerRpc SessionLocator MpCallSipRpc MpCallRpc Util ServiceI NmsFoundation SipAdapter ServiceMain EventCollector DataCollection MpCallHigherConfig HigherConfig
    $ENV{JssSharedPath}/lib/${DEFAULT_LIB_PATH}/libprotobuf.a
    $ENV{JssSharedPath}/lib/${DEFAULT_LIB_PATH}/libyaml-cpp.a
    ${DEFAULT_SERVER_LIBRARIES}
    ${DEFAULT_SYS_LIBRARIES}
)
target_link_options(MpCallSipGateway PRIVATE
    ${DEFAULT_LINK_OPTIONS}
)

# cli
add_executable(MpCallSipCli
    cli/MpCallSipCli.cpp
    ${WARP_GLIBC_SRC}
)

target_include_directories(MpCallSipCli PRIVATE
    ${DEFAULT_INCLUDE_DIRECTORIES}
    ${PROJECT_SOURCE_DIR}/3rd
)
target_compile_definitions(MpCallSipCli PRIVATE
    ${DEFAULT_COMPILE_DEFINITIONS}
)

target_link_libraries(MpCallSipCli MpCallRpc JsmInterface CliMain
    ${DEFAULT_SERVER_LIBRARIES}
    ${DEFAULT_SYS_LIBRARIES}
)
target_link_options(MpCallSipCli PRIVATE
    ${DEFAULT_LINK_OPTIONS}
)

# embeded html and css files
FILE(GLOB_RECURSE EMBEDED_DIALER_RESOURCES "dialer/*.html" "dialer/*.css")
FOREACH(INPUT_FILE ${EMBEDED_DIALER_RESOURCES})
    SET(OUTPUT_FILE ${INPUT_FILE}.h)
    LIST(APPEND EMBEDED_DIALER_RESOURCES_SRC ${OUTPUT_FILE})
ENDFOREACH()

ADD_CUSTOM_COMMAND(
    OUTPUT dialer/__header.h ${EMBEDED_DIALER_RESOURCES_SRC}
    COMMAND ${CMAKE_CURRENT_SOURCE_DIR}/xxd.sh ${EMBEDED_DIALER_RESOURCES}
    COMMENT "Generate embeded html file"
)

# dialer
aux_source_directory(dialer MPCALL_SIPGATEWAY_DIALER_SRC)
add_executable(MpCallSipDialer
    ../SipDialer/MpCallSipDialerPub.cpp
    ${WARP_GLIBC_SRC}
    ${MPCALL_SIPGATEWAY_DIALER_SRC}
    ${EMBEDED_DIALER_RESOURCES_SRC}
)

target_include_directories(MpCallSipDialer PRIVATE
    ${DEFAULT_INCLUDE_DIRECTORIES}
    ${PROJECT_SOURCE_DIR}/3rd
)
target_compile_definitions(MpCallSipDialer PRIVATE
    ${DEFAULT_COMPILE_DEFINITIONS}
)

target_link_libraries(MpCallSipDialer AgentGateway SipRegLine SipLine SimpleSipSession SimpleRtpSession SipAdapter Util ServiceI NmsFoundation JsmLog ServiceMain
    ${DEFAULT_SERVER_LIBRARIES}
    ${DEFAULT_SYS_LIBRARIES}
)
target_link_options(MpCallSipDialer PRIVATE
    ${DEFAULT_LINK_OPTIONS}
)

# UnitTest
aux_source_directory(test MPCALL_SIPGATEWAY_TEST_SRC)

add_executable(MpCallSipGatewayUnitTest ${MPCALL_SIPGATEWAY_TEST_SRC} ${MPCALL_SIPGATEWAY_SERVICE_SRC})
target_include_directories(MpCallSipGatewayUnitTest PRIVATE
    ${DEFAULT_INCLUDE_DIRECTORIES}
    ${PROJECT_SOURCE_DIR}/3rd
)
target_compile_definitions(MpCallSipGatewayUnitTest PRIVATE
    ${DEFAULT_COMPILE_DEFINITIONS}
)

target_link_libraries(MpCallSipGatewayUnitTest MpCallSipGatewayImpl SipLine SimpleSipSession JsmLog ServiceRunnerManager ServiceRunnerRpc SessionLocator MpCallSipRpc MpCallRpc SipAdapter Util EventCollector DataCollection ServiceI NmsFoundation MpCallHigherConfig HigherConfig
    $ENV{JssSharedPath}/lib/${DEFAULT_LIB_PATH}/libprotobuf.a
    $ENV{JssSharedPath}/lib/${DEFAULT_LIB_PATH}/libyaml-cpp.a
    ${DEFAULT_SERVER_LIBRARIES}
    $ENV{JssSharedPath}/lib/${DEFAULT_LIB_PATH}/libgtest.a
    $ENV{JssSharedPath}/lib/${DEFAULT_LIB_PATH}/libgmock.a
    $ENV{JssSharedPath}/lib/${DEFAULT_LIB_PATH}/libgtest_main.a
    $ENV{JssSharedPath}/lib/${DEFAULT_LIB_PATH}/libjssisipcall.so
    ${DEFAULT_SYS_LIBRARIES}
)
include(GoogleTest)
gtest_discover_tests(MpCallSipGatewayUnitTest)
