//
// *****************************************************************************
// Copyright (c) 2023 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//
// Created on 2023/12/8 by <PERSON>
//

#include "DummyRtpManager.h"

static const char *__initSdp = "\
v=0\r\n\
o=- 3455926086 1 IN IP4 ***********\r\n\
s=-\r\n\
c=IN IP4 ***********\r\n\
t=0 0\r\n\
m=audio 39478 RTP/AVP 0 8 101\r\n\
a=ptime:20\r\n\
a=maxptime:400\r\n\
a=rtpmap:101 telephone-event/8000\r\n\
m=video 39712 RTP/AVP 121\r\n\
a=rtpmap:121 H264/90000\r\n\
a=fmtp:121 profile-level-id=42800C; packetization-mode=1\r\n\
\r\n\
";

namespace SipMpCall
{

DummyRtpRunner::DummyRtpRunner(const Common::String &name, const Common::String &localIp, int port, const ServiceRunner::RunnerManagerItemListenerPtr &listener)
    : _name(name)
    , _localIp(localIp)
    , _port(port)
    , _listener(listener)
    , _closed(false)
{
}

void DummyRtpRunner::callout_begin(const Common::ServerCallPtr &__call, const Common::String &targetOid, const Common::String &monitorOid, const MpCallSip::RtpRunnerConfig &config, const MpCallSip::SdpParams &sdpParams)
{
    initOfferAnswer();
    _offerAnswer.setOption(SimpleSipSession::OfferAnswerModel::OptionVideoState, std::to_string(sdpParams.videoDirection));
    MpCallSip::RtpRunnerServer::callout_end(__call, true, _offerAnswer.genOffer().c_str());
}

void DummyRtpRunner::callin_begin(const Common::ServerCallPtr &__call, const Common::String &targetOid, const Common::String &monitorOid, const MpCallSip::RtpRunnerConfig &config, const Common::String &peerSdp)
{
    initOfferAnswer();
    _peerOffer = peerSdp;
    MpCallSip::RtpRunnerServer::callin_end(__call, true);
}

void DummyRtpRunner::callinAndOffer_begin(const Common::ServerCallPtr &__call, const Common::String &targetOid, const Common::String &monitorOid, const MpCallSip::RtpRunnerConfig &config, const MpCallSip::SdpParams &sdpParams)
{
    initOfferAnswer();
    _offerAnswer.setOption(SimpleSipSession::OfferAnswerModel::OptionVideoState, std::to_string(sdpParams.videoDirection));
    MpCallSip::RtpRunnerServer::callinAndOffer_end(__call, true, _offerAnswer.genOffer().c_str());
}

void DummyRtpRunner::callinAndAnswer_begin(const Common::ServerCallPtr &__call, const Common::String &targetOid, const Common::String &monitorOid, const MpCallSip::RtpRunnerConfig &config, const Common::String &peerSdp, const MpCallSip::SdpParams &sdpParams)
{
    initOfferAnswer();
    _offerAnswer.setOption(SimpleSipSession::OfferAnswerModel::OptionVideoState, std::to_string(sdpParams.videoDirection));
    MpCallSip::RtpRunnerServer::callinAndAnswer_end(__call, true, _offerAnswer.genAnswer(peerSdp.c_str()).c_str());
}

bool DummyRtpRunner::close(const Common::ServerCallPtr &__call)
{
    UTIL_LOG_IFO("DummyRtp", "content:runner closed, name:" + _name);
    _closed = true;
    return true;
}

bool DummyRtpRunner::genOffer(const Common::ServerCallPtr &__call, const MpCallSip::SdpParams &sdpParams, Common::String &offerSdp)
{
    _offerAnswer.setOption(SimpleSipSession::OfferAnswerModel::OptionVideoState, std::to_string(sdpParams.videoDirection));
    offerSdp = _offerAnswer.genOffer().c_str();
    return true;
}

bool DummyRtpRunner::setOffer(const Common::ServerCallPtr &__call, const Common::String &offerSdp)
{
    _peerOffer = offerSdp;
    return true;
}

bool DummyRtpRunner::setOfferAndAnswer(const Common::ServerCallPtr &__call, const Common::String &offerSdp, const MpCallSip::SdpParams &sdpParams, Common::String &answerSdp)
{
    _offerAnswer.setOption(SimpleSipSession::OfferAnswerModel::OptionVideoState, std::to_string(sdpParams.videoDirection));
    answerSdp = _offerAnswer.genAnswer(offerSdp.c_str()).c_str();
    return true;
}

bool DummyRtpRunner::genAnswer(const Common::ServerCallPtr &__call, const MpCallSip::SdpParams &sdpParams, Common::String &answerSdp)
{
    _offerAnswer.setOption(SimpleSipSession::OfferAnswerModel::OptionVideoState, std::to_string(sdpParams.videoDirection));
    answerSdp = _offerAnswer.genAnswer(_peerOffer.c_str()).c_str();
    return true;
}

bool DummyRtpRunner::setAnswer(const Common::ServerCallPtr &__call, const Common::String &answerSdp)
{
    _offerAnswer.setAnswer(answerSdp.c_str());
    return true;
}

bool DummyRtpRunner::setTalking(const Common::ServerCallPtr &__call, bool talking)
{
    return true;
}

bool DummyRtpRunner::getRtpStatus(const Common::ServerCallPtr &__call, MpCallSip::RtpStats &stats)
{
    return true;
}

void DummyRtpRunner::initOfferAnswer()
{
    _offerAnswer.init(__initSdp);
    _offerAnswer.setOption(SimpleSipSession::OfferAnswerModel::OptionIpv4, _localIp.c_str());
    _offerAnswer.setOption(SimpleSipSession::OfferAnswerModel::OptionAudioPort, std::to_string(_port));
    _offerAnswer.setOption(SimpleSipSession::OfferAnswerModel::OptionVideoPort, std::to_string(_port + 2));
}

bool DummyRtpAllocHandle::getRunner(Common::String &name, Common::String &objectId, Common::Error &error)
{
    name = _runner->_name;
    objectId = _runner->_objectId;
    return true;
}

ServiceRunner::RunnerManagerPtr DummyRtpManager::create(const Common::ApplicationPtr &app, const Common::AdapterPtr &adapter)
{
    DummyRtpManagerPtr mgr;
    try
    {
        mgr = new DummyRtpManager(app);
        if (!mgr->activate(adapter))
        {
            UTIL_LOG_ERR("DummyRtp", "content:init dummy rtp manager failed.");
            return nullptr;
        }
    }
    catch (const std::exception &e)
    {
        UTIL_LOG_ERR("DummyRtp", "content:create dummy rtp manager failed:" + Common::String(e.what()));
        return nullptr;
    }

    UTIL_LOG_IFO("DummyRtp", "content:create dummy rtp manager succeed.");
    return mgr.get();
}

bool DummyRtpManager::activate(const Common::AdapterPtr &adapter)
{
    _adapter = adapter;
    if (!_adapter)
    {
        Common::String config;
        if (_app->getAppConfig("DummyRtp.Endpoints", config))
            _app->setConfig("global.DummyRtp,Endpoints", "sudp -h 127.0.0.1");
        _adapter = _app->createAdapter("DummyRtp", true);
        if (!_adapter || !_adapter->activate())
        {
            UTIL_LOG_ERR("DummyRtp", "content:create adapter failed.");
            return false;
        }
    }

    getLocalIp();
    return true;
}

void DummyRtpManager::close()
{
    UTIL_LOG_IFO("DummyRtp", "content:close dummy rtp manager.");

    Common::RecLock lock(_mutex);

    if (_adapter)
    {
        for (auto kv : _runners)
            _adapter->removeServer(kv.first);
        _runners.clear();

        _adapter = 0;
    }

    _app = 0;
}

void DummyRtpManager::updateConfigs()
{
    Common::RecLock lock(_mutex);

    for (auto it = _runners.begin(); it != _runners.end();)
    {
        if (it->second->_closed)
            _runners.erase(it++);
        else
            ++it;
    }
}

int DummyRtpManager::totalRunnerCount()
{
    Common::RecLock lock(_mutex);
    return _runners.size() + 1;
}

ServiceRunner::RunnerAllocHandlePtr DummyRtpManager::allocRunner(const ServiceRunner::RuleParams &rules, const ServiceRunner::RunnerManagerItemListenerPtr &listener)
{
    Common::RecLock lock(_mutex);

    if (!_adapter)
    {
        UTIL_LOG_WRN("DummyRtp", "content:allocRunner after closed");
        return nullptr;
    }

    int port;
    Common::String serverId;
    for (;;)
    {
        port = (Common::Long)Common::getRand(4999) * 4 + 20000;
        serverId = Common::String(port);
        if (_runners.find(serverId) == _runners.end())
            break;
    }

    DummyRtpRunnerPtr runner = new DummyRtpRunner(serverId, _ipv4, port, listener);
    if (!runner)
    {
        UTIL_LOG_WRN("DummyRtp", "content:allocRunner create runner failed.");
        return nullptr;
    }

    _adapter->addServer(serverId, runner);
    for (;;)
    {
        runner->_objectId = _adapter->getObjectId(serverId);
        if (!runner->_objectId.empty())
            break;
        Common::sleep(1000);
    }

    _runners.insert(make_pair(serverId, runner));
    UTIL_LOG_IFO("DummyRtp", "content:allocRunner create runner name:" + serverId + " objectId:" + runner->_objectId);
    return new DummyRtpAllocHandle(runner);
}

void DummyRtpManager::releaseRunner(const Common::String &name)
{
    Common::RecLock lock(_mutex);

    auto it = _runners.find(name);
    if (it == _runners.end())
    {
        UTIL_LOG_WRN("DummyRtp", "content:releaseRunner not found:" + name);
        return;
    }

    UTIL_LOG_IFO("DummyRtp", "content:releaseRunner name:" + name);
    _adapter->removeServer(name);
    _runners.erase(it);
}

void DummyRtpManager::getLocalIp()
{
    Common::NetDriverPtr driver = _app->getDriver();
    Common::NetSenderPtr sender = driver->listen("udp", "0.0.0.0", 0, new Common::NetReceiver());
    if (!sender)
    {
        _ipv4 = "127.0.0.1";
        return;
    }

    int port;
    sender->getLocal(_ipv4, port);
    sender->close();
}

} // namespace SipMpCall