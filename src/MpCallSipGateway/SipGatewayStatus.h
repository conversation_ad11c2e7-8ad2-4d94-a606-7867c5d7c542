//
// *****************************************************************************
// Copyright (c) 2025 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//

#pragma once

#include "Common/Common.h"
#include "MpCallSip/MpCallSipStatusPub.h"

namespace SipMpCall
{

class SipGatewayStatusInterface : virtual public Common::Shared
{
public:
    virtual bool setConfig(const Common::String &key, const Common::String &value) = 0;
    virtual bool getConfig(const Common::String &key, Common::String &value) = 0;
    virtual bool getCallStatusList(MpCallSip::ResponseAllCallStatus &status) = 0;
    virtual bool getCallStatus(const Common::String &sessId, MpCallSip::ResponseStatus &status) = 0;
};

typedef Common::Handle<SipGatewayStatusInterface> SipGatewayStatusInterfacePtr;

class SipGatewayStatus;
typedef Common::Handle<SipGatewayStatus> SipGatewayStatusPtr;

class SipGatewayStatus : virtual public Common::Shared
{
public:
    static SipGatewayStatusPtr create(const Common::ApplicationPtr &application, const SipGatewayStatusInterfacePtr &listener);

    virtual void close() = 0;
};

} // namespace SipMpCall
