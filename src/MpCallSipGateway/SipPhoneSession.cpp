//
// *****************************************************************************
// Copyright (c) 2023 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//
// Created on 2023/5/27 by <PERSON>
//

#include "SipPhoneSession.h"
#include "Common/Util.h"
#include "Error.h"
#include "MpCall/MpCallPub.h"
#include "MpCallSip/MpCallRtpAgent.h"
#include "MpCallSip/MpCallRtpPub.h"
#include "MpCall/MpCallSipAgent.h"
#include "MpCallSipGateway.h"
#include "MpCall/MpCallEventPub.h"
#include "MpCall/MpCallErrorPub.h"
#include "ServiceUtil/Struct2Json.h"
#include "SimpleSipSession/SipMessage.h"
#include "SimpleSipSession/SdpMessage.h"
#include "SimpleSipSession/SipTypes.h"

static int to_int(const std::string &str, int dflt = 0)
{
    if (str.empty())
        return dflt;

    size_t pos = 0;
    int value;
    int base = 10;
    if (str.size() > 2 && str.substr(0, 2) == "0x")
        base = 16;

    try
    {
        value = std::stoi(str, &pos, base);
        if (pos == str.size())
            return value;
    }
    catch (...)
    {
    }

    return dflt;
}

namespace SipMpCall
{

void SipPhoneSession::setState(enum SessionState state, const Common::String &termedReason, EndType endType)
{
    if (_sessState == state)
        return;

    if (state == StateTermed)
    {
        UTIL_LOG_CONTEXT_IFO("SipPhoneSession", _logCtx, "content:update state:" + to_string(_sessState) + "->" + to_string(state) + " reason:" + termedReason);
        _termedReason = termedReason;
        _termedTimeMs = Common::getCurTimeMs();
        _endType = static_cast<int>(endType);
    }
    else
    {
        UTIL_LOG_CONTEXT_IFO("SipPhoneSession", _logCtx, "content:update state:" + to_string(_sessState) + "->" + to_string(state));
    }

    EventBaseInfo eventBaseInfo(_domainId, _appId, _startTimeMs, _termedTimeMs, _callId, _caller, _callee, _endType);
    switch (state)
    {
    case StateIncoming:
    case StateOutgoing:
        _sipEventNotify.notifySipEvent(eventBaseInfo, SipEvent::SipEventCall, __getSipCallId());
        break;
    case StateTalking:
        _sipEventNotify.notifySipEvent(eventBaseInfo, SipEvent::SipEventConnected, __getSipCallId());
        break;
    case StateTermed:
        _sipEventNotify.notifySipEvent(eventBaseInfo, SipEvent::SipEventTerminated, __getSipCallId());
        _sipEventNotify.notifySipCallRecord(eventBaseInfo, _ringingTimeMs, _app->getAppName());
        break;
    default:
        break;
    }

    _sessState = state;
    _stateChanged.notify_all();
}

bool SipPhoneSession::waitState(std::unique_lock<std::mutex> &lock, enum SessionState state, int timeoutMs)
{
    std::chrono::system_clock::time_point toTime = std::chrono::system_clock::now() + std::chrono::milliseconds(timeoutMs);
    while (_sessState != state)
    {
        if (_stateChanged.wait_until(lock, toTime) == std::cv_status::timeout)
            return false;
    }

    return _sessState == state;
}

void SipPhoneSession::setState(enum UpdateState state)
{
    if (_updateState == state)
        return;

    UTIL_LOG_CONTEXT_IFO("SipPhoneSession", _logCtx, "content:update state:" + to_string(_updateState) + "->" + to_string(state));
    _updateState = state;
}

void SipPhoneSession::setState(enum RtpState state)
{
    if (_rtpState == state)
        return;

    UTIL_LOG_CONTEXT_IFO("SipPhoneSession", _logCtx, "content:update rtp state:" + to_string(_rtpState) + "->" + to_string(state));
    _rtpState = state;
}

bool SipPhoneSession::getStatus(Common::String &caller, Common::String &callee, Common::String &callId, Common::String &direction, Common::String &state, Common::Long &startTimeMs, Common::Long &durationMs, Common::String &termedReason)
{
    std::lock_guard<std::mutex> lock(_mutex);

    caller = _caller;
    callee = _callee;
    direction = _callout ? "outgoing" : "incoming";
    state = to_string(_sessState);
    startTimeMs = _startTimeMs;
    durationMs = _termedTimeMs > 0 ? _termedTimeMs - startTimeMs : Common::getCurTimeMs() - startTimeMs;
    termedReason = _termedReason;
    std::vector<std::string> sipCallIds;
    std::string callId_str = "";
    if (_sipSess)
        sipCallIds = _sipSess->getCallIds();

    for (size_t i = 0; i < sipCallIds.size(); ++i)
    {
        callId_str += sipCallIds[i] + ";";
    }
    callId = callId_str.c_str();
    return true;
}

bool SipPhoneSession::getRtpStatus(MpCallSip::RtpStats &rtpStats)
{
    MpCallSip::RtpRunnerAgent rtpAgent = _rtpAgent;
    if (rtpAgent && rtpAgent.getRtpStatus(rtpStats))
        _rtpStats = rtpStats;
    else
        rtpStats = _rtpStats;
    return true;
}

Common::String SipPhoneSession::checkState(bool &closed)
{
    enum SessionState state = _sessState;
    closed = (state == StateTermed);
    if (closed)
    {
        _app = 0;
        _sipLine = 0;
        _sessionManager = 0;
        _eventCollector = 0;
        _allocHandle = 0;
    }

    MpCall::SessionEndpointAgent mpcall = _mpcallAgent;
    if (mpcall && !closed)
    {
        if (Common::getCurTicks() - _lastKeepAliveTicks > 30000)
        {
            mpcall.event_begin(new EventAsync(MpCall::Event_toString(MpCall::EventKeepAlive)), MpCall::Event_toString(MpCall::EventKeepAlive), "", 0, this);
            _lastKeepAliveTicks = Common::getCurTicks();
        }
    }
    return to_string(state);
}

void SipPhoneSession::onError(const Common::CallError &error)
{
    std::lock_guard<std::mutex> lock(_mutex);

    setState(StateTermed, error.logInfo(2), EndType::InvalidSessionState);

    if (_mpcallAgent)
    {
        _mpcallAgent.error_begin(0, MpCall::ErrorType_toString(MpCall::ErrorServerInternal), error.logInfo(2));
        _mpcallAgent = 0;
    }

    if (_sipSess && !_sipTermCalled)
    {
        _sipSess->SipTerm();
        _sipTermCalled = true;
    }

    if (!_runnerName.empty())
    {
        _runnerManager->releaseRunner(_runnerName);
        _runnerName.clear();
    }

    if (_rtpAgent)
    {
        _rtpAgent.close_begin(0);
        _rtpAgent = 0;
    }

    __release("", EndType::InvalidSessionState);
}

bool SipPhoneSession::invitation(const Common::ServerCallPtr &__call, const Common::String &sessId, const Common::String &caller, const Common::String &callee, const MpCall::RoomConfig &roomConfig, const MpCall::InviteConfig &inviteConfig)
{
    std::lock_guard<std::mutex> lock(_mutex);

    _logCtx->setContext("caller", caller, Common::LogContext::LogMaskKeep);
    _logCtx->setContext("callee", callee, Common::LogContext::LogMaskKeep);

    if (_sessState != StateIdle)
    {
        UTIL_LOG_CONTEXT_ERR("SipPhoneSession", _logCtx, "content:sip session inviation invalid state:" + to_string(_sessState));
        __call->setError(Error::InvalidState(EARGS("state", _sessState)));
        __releaseByMpCall("InvalidState", EndType::InvalidSessionState);
        return false;
    }

    SipSessionConfigurator::CallParams outParams;
    if (!_configurator->onOutgoingCall(SipSessionConfigurator::CallParams(_sessId, caller, callee, inviteConfig), outParams))
    {
        UTIL_LOG_CONTEXT_WRN("SipPhoneSession", _logCtx, "content:sip session inviation callback failed, sessId:" + _sessId + " error:" + Common::getLastCallError().logInfo(2));
        __call->setError(Common::getLastCallError());
        __releaseByMpCall("OnOutgoingCallFailed" + Common::getLastCallError().logInfo(2), EndType::SipInviteConfigFailed);
        return false;
    }
    else
    {
        UTIL_LOG_IFO("SipPhoneSession", "content:sip session inviation callback sessId:" + _sessId + " callee:" + callee + "->" + outParams.callee + " uui:" + inviteConfig.uui + "->" + outParams.inviteConfig.uui);
    }

    _requireAlert = outParams.inviteConfig.requireAlert;
    updateLogContext(outParams.inviteConfig);

    if (_app->getAppConfig("MpCallSipGateway.TargetOid", _targetOid))
        UTIL_LOG_CONTEXT_WRN("SipPhoneSession", _logCtx, "content:create agent for testing from target oid:" + _targetOid);
    else
        _targetOid = "TpSipAgent/" + callee + "/" + sessId; // this must not be replaced by outCallee
    _mpcallAgent = _app->createAgent(_targetOid, false);

    if (!allocRtpAgent(roomConfig))
    {
        UTIL_LOG_CONTEXT_WRN("SipPhoneSession", _logCtx, "content:invitation allocate rtp agent failed.");
        __call->setError(Error::AllocRtpRunnerFailed(ELOC));
        __releaseByMpCall("RtpAgent:AllocateFailed:" + ServiceUtil::to_json(roomConfig), EndType::RtpAgentError);
        return false;
    }

    Common::Error error;
    if (!createRtpAgent(error))
    {
        UTIL_LOG_CONTEXT_WRN("SipPhoneSession", _logCtx, "content:invitation allocate rtp agent failed:" + error._reason);
        __call->setError(Common::CallError(error));
        __releaseByMpCall("RtpAgent:CreateFailed:" + error._reason, EndType::RtpAgentError);
        return false;
    }

    MpCallSip::RtpRunnerConfig rtpConfig;
    rtpConfig.name = outParams.callee;
    if (!configRoom2Rtp(rtpConfig, roomConfig))
    {
        UTIL_LOG_CONTEXT_WRN("SipPhoneSession", _logCtx, "content:invitation no valid room config");
        __call->setError(Error::NoValidRoomConfig(ELOC));
        __releaseByMpCall("NoValidRoomConfig:" + ServiceUtil::to_json(roomConfig), EndType::RtpConfigError);
        return false;
    }
    configInvite2Rtp(rtpConfig, inviteConfig);

    if (__call->getTraceContext().traceId.empty())
        _rtpAgent->setParams(Common::CallParams::create()->enableTrace("MPCALL" + sessId));

    Common::String offerSdp;
    if (!_rtpAgent.callout(_targetOid, _objectId, rtpConfig, configInvite2Sdp(outParams.inviteConfig), offerSdp))
    {
        UTIL_LOG_CONTEXT_WRN("SipPhoneSession", _logCtx, "content:invitation start callout on rtp agent failed:" + Common::ObjectAgent::getLogInfo(2));
        __call->setError(Error::RtpRunnerError(EWRAP));
        __releaseByMpCall("RtpAgent:CalloutFailed:" + Common::ObjectAgent::getLogInfo(2), EndType::RtpCalloutFailed);
        return false;
    }

    std::map<std::string, std::string> extHdrs;
    if (!outParams.inviteConfig.uui.empty())
        extHdrs[_app->getAppConfig("SipCall.UuiHeader").c_str()] = outParams.inviteConfig.uui.c_str();

    _sipSess = _sipLine->SipCall(this,  sessId.c_str(), caller.c_str(), outParams.callee.c_str(), offerSdp.c_str(), extHdrs);
    if (!_sipSess)
    {
        UTIL_LOG_CONTEXT_WRN("SipPhoneSession", _logCtx, "content:invitation start sip callout failed");
        __call->setError(Error::SipCalloutFailed(EARGS("caller", caller, "callee", outParams.callee)));
        __releaseByMpCall("SipCallFailed", EndType::SipCalloutFailed);
        return false;
    }

    _caller = caller;
    _callee = outParams.callee;
    _domainId = roomConfig.domainId;
    _appId = roomConfig.appId;
    _callId = roomConfig.roomId;
    _jsmSipUser = caller;

    setState(StateOutgoing);
    UTIL_LOG_CONTEXT_IFO("SipPhoneSession", _logCtx, "content:invitation call out");

    return true;
}

bool SipPhoneSession::canceled(const Common::ServerCallPtr &__call, const Common::String &userId, const Common::String &reason)
{
    std::lock_guard<std::mutex> lock(_mutex);

    if (_sessState != StateOutgoing)
    {
        UTIL_LOG_CONTEXT_ERR("SipPhoneSession", _logCtx, "content:sip session canceled invalid state:" + to_string(_sessState));
        __call->setError(Error::InvalidState(EARGS("state", _sessState)));
        __releaseByMpCall("InvalidState", EndType::InvalidSessionState);
        return false;
    }

    UTIL_LOG_CONTEXT_IFO("SipPhoneSession", _logCtx, "content:canceled");
    __releaseByMpCall("Canceled", EndType::Canceled);
    return true;
}

bool SipPhoneSession::alerted(const Common::ServerCallPtr &__call, const Common::String &userId)
{
    std::lock_guard<std::mutex> lock(_mutex);

    if (_sessState != StateIncoming)
    {
        UTIL_LOG_ERR("SipPhoneSession", "content:sip session alerted invalid state, sessId:" + _sessId + " state:" + to_string(_sessState));
        __call->setError(Error::InvalidState(EARGS("state", _sessState)));
        __releaseByMpCall("InvalidState", EndType::InvalidSessionState);
        return false;
    }

    if (!_sipSess->SipAlert(""))
    {
        UTIL_LOG_WRN("SipPhoneSession", "content:sip session alerted sending ringing failed.");
        __releaseBySip(MpCall::ErrorType_toString(MpCall::ErrorSipAlert), "", EndType::SipAlertFailed);
        return false;
    }

    UTIL_LOG_IFO( "SipPhoneSession", "content:alerted, sessId:" + _sessId);
    _mpcallAgent.event_begin(new EventAsync(MpCall::Event_toString(MpCall::EventSipAlerted)), MpCall::Event_toString(MpCall::EventSipAlerted), ServiceUtil::to_json(MpCall::EventSipAlertedDetail()), 0, this);

    _ringingTimeMs = Common::getCurTimeMs();
    EventBaseInfo eventBaseInfo(_domainId, _appId, _startTimeMs, _termedTimeMs, _callId, _caller, _callee, _endType);
    _sipEventNotify.notifySipEvent(eventBaseInfo, SipEvent::SipEventAlerted, __getSipCallId());

    return true;
}

bool SipPhoneSession::accepted(const Common::ServerCallPtr &__call, const Common::String &userId, bool isVideo, const MpCall::RoomConfig &roomConfig, const Common::String &uui)
{
    std::lock_guard<std::mutex> lock(_mutex);

    if (_sessState != StateIncoming)
    {
        UTIL_LOG_CONTEXT_ERR("SipPhoneSession", _logCtx, "content:sip session accepted invalid state:" + to_string(_sessState));
        __call->setError(Error::InvalidState(EARGS("state", _sessState)));
        __releaseByMpCall("InvalidState", EndType::InvalidSessionState);
        return false;
    }

    MpCallSip::SdpParams sdpParams = configInvite2Sdp(isVideo ? MpCall::MediaSendRecv : MpCall::MediaInactive);

    Common::String localSdp;
    if (!_rtpAgent)
    {
        if (!allocRtpAgent(roomConfig))
        {
            UTIL_LOG_CONTEXT_WRN("SipPhoneSession", _logCtx, "content:accepted allocate rtp agent failed.");
            __call->setError(Error::AllocRtpRunnerFailed(ELOC));
            __releaseByMpCall("RtpAgent:AllocateFailed:" + ServiceUtil::to_json(roomConfig), EndType::RtpAgentError);
            return false;
        }

        Common::Error error;
        if (!createRtpAgent(error))
        {
            UTIL_LOG_CONTEXT_WRN("SipPhoneSession", _logCtx, "content:accepted allocate rtp agent failed:" + error._reason);
            __call->setError(Common::CallError(error));
            __releaseByMpCall("RtpAgent:CreateFailed:" + error._reason, EndType::RtpAgentError);
            return false;
        }

        MpCallSip::RtpRunnerConfig rtpConfig;
        rtpConfig.name = _caller;
        if (!configRoom2Rtp(rtpConfig, roomConfig))
        {
            UTIL_LOG_CONTEXT_WRN("SipPhoneSession", _logCtx, "content:accepted no valid room config");
            __call->setError(Error::NoValidRoomConfig(ELOC));
            __releaseByMpCall("NoValidRoomConfig:" + ServiceUtil::to_json(roomConfig), EndType::RtpConfigError);
            return false;
        }

        if (__call->getTraceContext().traceId.empty())
            _rtpAgent->setParams(Common::CallParams::create()->enableTrace("MPCALL" + _sessId));

        if (_peerSdp.empty())
        {
            if (!_rtpAgent.callinAndOffer(_targetOid, _objectId, rtpConfig, sdpParams, localSdp))
            {
                UTIL_LOG_CONTEXT_WRN("SipPhoneSession", _logCtx, "content:accepted start callin and gen offer on rtp agent failed:" + Common::ObjectAgent::getLogInfo(2));
                __call->setError(Error::RtpRunnerError(EWRAP));
                __releaseByMpCall("RtpAgent:CallinFailed:" + Common::ObjectAgent::getLogInfo(2), EndType::RtpCallinFailed);
                return false;
            }
        }
        else
        {
            if (!_rtpAgent.callinAndAnswer(_targetOid, _objectId, rtpConfig, _peerSdp, sdpParams, localSdp))
            {
                UTIL_LOG_CONTEXT_WRN("SipPhoneSession", _logCtx, "content:accepted start callin and answer on rtp agent failed:" + Common::ObjectAgent::getLogInfo(2));
                __call->setError(Error::RtpRunnerError(EWRAP));
                __releaseByMpCall("RtpAgent:CallinFailed:" + Common::ObjectAgent::getLogInfo(2), EndType::RtpCallinFailed);
                return false;
            }
        }
    }
    else
    {
        if (_peerSdp.empty())
        {
            if (!_rtpAgent.genOffer(sdpParams, localSdp))
            {
                UTIL_LOG_CONTEXT_WRN("SipPhoneSession", _logCtx, "content:accepted and gen offer on rtp agent failed:" + Common::ObjectAgent::getLogInfo(2));
                __call->setError(Error::RtpRunnerError(EWRAP));
                __releaseByMpCall("RtpAgent:GenOfferFailed:" + Common::ObjectAgent::getLogInfo(2), EndType::RtpConfigError);
                return false;
            }
        }
        else
        {
            if (!_rtpAgent.genAnswer(sdpParams, localSdp))
            {
                UTIL_LOG_CONTEXT_WRN("SipPhoneSession", _logCtx, "content:accepted and gen answer on rtp agent failed:" + Common::ObjectAgent::getLogInfo(2));
                __call->setError(Error::RtpRunnerError(EWRAP));
                __releaseByMpCall("RtpAgent:GenAnswerFailed:" + Common::ObjectAgent::getLogInfo(2), EndType::RtpConfigError);
                return false;
            }
        }
    }

    std::map<std::string, std::string> extHdrs;
    if (!uui.empty())
        extHdrs[_app->getAppConfig("SipCall.UuiHeader").c_str()] = uui.c_str();

    if (!_sipSess->SipAnswer(localSdp.c_str(), extHdrs))
    {
        UTIL_LOG_CONTEXT_WRN("SipPhoneSession", _logCtx, "content:accepted answer sip call failed");
        __call->setError(Error::SipAnswerFailed(ELOC));
        __releaseBySip(MpCall::ErrorType_toString(MpCall::ErrorSipAnswer), "", EndType::SipAnswerFailed);
        return false;
    }

    if (!_peerSdp.empty())
        __updateStateBySdp(localSdp.c_str(), true);

    setState(StateConnecting);
    UTIL_LOG_CONTEXT_IFO("SipPhoneSession", _logCtx, "content:accepted answer sip call");
    return true;
}

bool SipPhoneSession::rejected(const Common::ServerCallPtr &__call, const Common::String &userId, const Common::String &reason)
{
    std::lock_guard<std::mutex> lock(_mutex);

    if (_sessState != StateIncoming)
    {
        UTIL_LOG_CONTEXT_ERR("SipPhoneSession", _logCtx, "content:sip session rejected invalid state:" + to_string(_sessState));
        __call->setError(Error::InvalidState(EARGS("state", _sessState)));
        __releaseByMpCall("InvalidState", EndType::InvalidSessionState);
        return false;
    }

    UTIL_LOG_CONTEXT_IFO("SipPhoneSession", _logCtx, "content:rejected term sip call");
    __releaseByMpCall("MpCall:Rejected:" + reason, EndType::Rejected);
    return true;
}

bool SipPhoneSession::leaved(const Common::ServerCallPtr &__call, const Common::String &userId)
{
    std::lock_guard<std::mutex> lock(_mutex);
    int value = 0;
    if (_app->getAppConfigAsInt("SipCall.LeaveSync", value) && value)
    {
        UTIL_LOG_CONTEXT_IFO("SipPhoneSession", _logCtx, "content:leaved userId:" + userId + " leave synchronously");
        __releaseByMpCall("MpCall:Leaved", EndType::SipUserLeave);
    }
    else
    {
        UTIL_LOG_CONTEXT_IFO("SipPhoneSession", _logCtx, "content:leaved userId:" + userId);
    }

    return true;
}

bool SipPhoneSession::terminated(const Common::ServerCallPtr &__call, const Common::String &reason)
{
    std::lock_guard<std::mutex> lock(_mutex);

    if (_sessState == StateIdle)
    {
        UTIL_LOG_CONTEXT_ERR("SipPhoneSession", _logCtx, "content:sip session terminated invalid state:" + to_string(_sessState));
        __call->setError(Error::InvalidState(EARGS("state", _sessState)));
        __releaseByMpCall("InvalidState", EndType::InvalidSessionState);
        return false;
    }

    __releaseByMpCall("MpCall:Terminated:" + reason);
    UTIL_LOG_CONTEXT_IFO("SipPhoneSession", _logCtx, "content:terminated term sip call");
    return true;
}

bool SipPhoneSession::requestVideo(const Common::ServerCallPtr &__call)
{
    std::unique_lock<std::mutex> lock(_mutex);

    if (_isVideo)
    {
        UTIL_LOG_CONTEXT_IFO("SipPhoneSession", _logCtx, "content:already has video");
        return true;
    }

    if (_sessState != StateTalking)
    {
        if (_sessState != StateConnecting || !waitState(lock, StateTalking))
        {
            UTIL_LOG_CONTEXT_ERR("SipPhoneSession", _logCtx, "content:sip session request video invalid state:" + to_string(_sessState));
            __call->setError(Error::InvalidState(EARGS("state", _sessState)));
            __releaseByMpCall("InvalidState", EndType::InvalidSessionState);
            return false;
        }
    }

    if (_updateState != UpdateStateIdle)
    {
        UTIL_LOG_CONTEXT_ERR("SipPhoneSession", _logCtx, "content:sip session request video invalid state:" + to_string(_sessState) + " update:" + to_string(_updateState));
        __call->setError(Error::InvalidState(EARGS("updateState", _updateState)));
        return false;
    }

    Common::String offerSdp;
    if (!_rtpAgent.genOffer(configInvite2Sdp(MpCall::MediaSendRecv), offerSdp))
    {
        UTIL_LOG_CONTEXT_WRN("SipPhoneSession", _logCtx, "content:sip session request video rtp agent failed:" + Common::ObjectAgent::getLogInfo(2));
        __call->setError(Error::RtpRunnerError(EWRAP));
        __releaseByMpCall("RtpAgent:GenOfferFailed:" + Common::ObjectAgent::getLogInfo(2), EndType::RtpConfigError);
        return false;
    }

    if (!_sipSess->SipUpdate(offerSdp.c_str()))
    {
        UTIL_LOG_CONTEXT_WRN("SipPhoneSession", _logCtx, "content:sip session request video update sip call failed");
        __call->setError(Error::SipAnswerFailed(ELOC));
        __releaseBySip(MpCall::ErrorType_toString(MpCall::ErrorSipUpdate), "", EndType::SipUpdateFailed);
        return false;
    }

    setState(UpdateStateOutgoing);
    UTIL_LOG_CONTEXT_IFO("SipPhoneSession", _logCtx, "content:sip session request video update sip call");
    return true;
}

void SipPhoneSession::onCallIncoming(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg)
{
    std::lock_guard<std::mutex> lock(_mutex);

    if (_sessState != StateIdle)
    {
        UTIL_LOG_CONTEXT_ERR("SipPhoneSession", _logCtx, "content:sip session incoming invalid state:" + to_string(_sessState));
        __release("InvalidStateOnCallIncoming:" + to_string(_sessState), EndType::InvalidSessionState);
        return;
    }

    MpCall::SessionAgent mpcallAgent = _app->createAgent(getMpCallServerOid());

    std::string dispname, uri;
    _sipSess->GetCalledUri(dispname, uri);
    _callee = SipLine::getNumber(uri).c_str();
    _sipSess->GetPeerUri(dispname, uri);
    _caller = SipLine::getNumber(uri).c_str();
    _logCtx->setContext("caller", _caller, Common::LogContext::LogMaskKeep);
    _logCtx->setContext("callee", _callee, Common::LogContext::LogMaskKeep);

    SimpleSipSession::SipMessagePtr sipMsg = SimpleSipSession::SipMessage::create(pcSipMsg);
    MpCall::InviteConfig inviteConfig;
    inviteConfig.isVideo = hasVideo(pcSdp);
    inviteConfig.uui = sipMsg->getHeader(_app->getAppConfig("SipCall.UuiHeader").c_str()).c_str();
    inviteConfig.displayName = dispname.c_str();
    inviteConfig.mediaDirection = inviteConfig.isVideo ? MpCall::MediaSendRecv : MpCall::MediaInactive;
    inviteConfig.audioMediaDirection = MpCall::MediaSendRecv;

    std::string sipCallId = sipMsg->getHeader(SimpleSipSession::HeaderCName::CALL_ID).c_str();
    std::string contactUri = _sipLine->getContactUri(sipCallId, _callee.c_str());
    if (!contactUri.empty())
        _sipSess->SipSetContactUri(contactUri);

    SipSessionConfigurator::CallParams outParams;
    SipSessionConfigurator::AppParams appParams;
    if (!_configurator->onIncomingCall(SipSessionConfigurator::CallParams(_sessId, _caller, _callee, inviteConfig), outParams, appParams))
    {
        UTIL_LOG_WRN("SipPhoneSession", "content:onCallIncoming callback failed, sessId:" + _sessId + " error:" + Common::getLastCallError().logInfo(2));
        __release("OnIncomingCallFailed:" + Common::getLastCallError().logInfo(2), EndType::SipInviteConfigFailed);
        return;
    }
    else
    {
        mpcallAgent = _app->createAgent(getMpCallServerOid(outParams.sessId));
        _callee = outParams.callee;
        inviteConfig = outParams.inviteConfig;
    }

    updateLogContext(inviteConfig);

    MpCall::RoomConfig roomConfig;
    if (!mpcallAgent.call(MpCall::TypeSip, _caller, outParams.calleeType, _callee, inviteConfig, _sessionManager->getRoomConfig(), outParams.sessId, roomConfig, configApp2CallParams(appParams)))
    {
        UTIL_LOG_CONTEXT_WRN("SipPhoneSession", _logCtx, "content:onCallIncoming create call failed, reason:" + Common::ObjectAgent::getLogInfo(2));
        __releaseByMpCall("MpCall:CallFailed:" + Common::ObjectAgent::getLogInfo(2), EndType::MpCallIncomingFailed);
        return;
    }

    _sessId = outParams.sessId;
    _sipSess->setSessId(_sessId.c_str());

    _logCtx->setContext("sessId", _sessId, Common::LogContext::LogMaskKeep);
    _objectId = _sessionManager->addSession(_caller + "/" + _sessId, this);
    if (_objectId.empty())
    {
        UTIL_LOG_CONTEXT_WRN("SipPhoneSession", _logCtx, "content:onCallIncoming add session to manager failed");
        __release("AddSessionFailed", EndType::InvalidSessionState);
        return;
    }

    if (_app->getAppConfig("MpCallSipGateway.TargetOid", _targetOid))
        UTIL_LOG_CONTEXT_WRN("SipPhoneSession", _logCtx , "content:create agent for testing from target oid:" + _targetOid);
    else
        _targetOid = "TpSipAgent/" + _caller + "/" + _sessId;
    _mpcallAgent = _app->createAgent(_targetOid, false);
    if (!_mpcallAgent)
    {
        UTIL_LOG_CONTEXT_WRN("SipPhoneSession", _logCtx, "content:onCallIncoming create agent failed");
        __releaseByMpCall("MpCall:CreateAgentFailed:" + _targetOid, EndType::MpCallIncomingFailed);
        return;
    }
    _mpcallAgent->setParams(Common::CallParams::create()->enableTrace("MPCALL" + _sessId));
    _peerSdp = pcSdp.c_str();

    MpCallSip::RtpRunnerConfig rtpConfig;
    rtpConfig.name = _caller;
    if (!configRoom2Rtp(rtpConfig, roomConfig))
    {
        UTIL_LOG_CONTEXT_IFO("SipPhoneSession", _logCtx, "content:onCallIncoming no roomId.");
    }
    else
    {
        if (!allocRtpAgent(roomConfig))
        {
            UTIL_LOG_CONTEXT_WRN("SipPhoneSession", _logCtx, "content:onCallIncoming allocate rtp agent failed.");
            __releaseByRtp(MpCall::ErrorType_toString(MpCall::ErrorAllocRtpRunner), "", EndType::RtpAgentError);
            return;
        }

        Common::Error error;
        if (!createRtpAgent(error))
        {
            UTIL_LOG_CONTEXT_WRN("SipPhoneSession", _logCtx, "content:onCallIncoming allocate rtp agent failed:" + error._reason);
            __releaseByRtp(MpCall::ErrorType_toString(MpCall::ErrorAllocRtpRunner), ServiceUtil::to_json(error), EndType::RtpAgentError);
            return;
        }

        _rtpAgent->setParams(Common::CallParams::create()->enableTrace("MPCALL" + _sessId));

        if (!_rtpAgent.callin(_targetOid, _objectId, rtpConfig, _peerSdp))
        {
            UTIL_LOG_CONTEXT_WRN("SipPhoneSession", _logCtx, "content:onCallIncoming start callin on rtp agent failed:" + Common::ObjectAgent::getLogInfo(2));
            __releaseByRtp(MpCall::ErrorType_toString(MpCall::ErrorAllocRtpRunner), ServiceUtil::callerror_to_json(), EndType::RtpCallinFailed);
            return;
        }

        UTIL_LOG_CONTEXT_IFO("SipPhoneSession", _logCtx, "content:onCallIncoming start callin");
    }

    _domainId = appParams.domainId.toLong(0);
    _appId = appParams.appId.toLong(0);
    _callId = roomConfig.roomId;
    _jsmSipUser = _callee;

    if (inviteConfig.requireAlert)
    {
        UTIL_LOG_CONTEXT_IFO("SipPhoneSession", _logCtx, "content:onCallIncoming disable auto alert.");
    }
    else
    {
        if (!_sipSess->SipAlert(""))
        {
            UTIL_LOG_CONTEXT_WRN("SipPhoneSession", _logCtx, "content:onCallIncoming alert failed.");
            __releaseBySip(MpCall::ErrorType_toString(MpCall::ErrorSipAlert), "", EndType::SipAlertFailed);
            return;
        }
        _mpcallAgent.event_begin(new EventAsync(MpCall::Event_toString(MpCall::EventSipAlerted)), MpCall::Event_toString(MpCall::EventSipAlerted), ServiceUtil::to_json(MpCall::EventSipAlertedDetail()), 0, this);
        EventBaseInfo eventBaseInfo(_domainId, _appId, _startTimeMs, _termedTimeMs, _callId, _caller, _callee, _endType);
        _sipEventNotify.notifySipEvent(eventBaseInfo, SipEvent::SipEventAlerted, __getSipCallId());
    }

    setState(StateIncoming);
}

void SipPhoneSession::onCallAlerted(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg)
{
    std::lock_guard<std::mutex> lock(_mutex);

    _ringingTimeMs = Common::getCurTimeMs();

    if (_sessState != StateOutgoing)
    {
        UTIL_LOG_CONTEXT_ERR("SipPhoneSession", _logCtx, "content:sip session alerted invalid state:" + to_string(_sessState));
        __release("InvalidStateOnCallAlerted:" + to_string(_sessState), EndType::InvalidSessionState);
        return;
    }

    if (_requireAlert)
    {
        if (!_mpcallAgent.alert(_callee))
        {
            UTIL_LOG_WRN("SipPhoneSession", "content:onCallAlerted mpcall alerted failed, reason:" + Common::ObjectAgent::getLogInfo(2));
            __releaseByMpCall("MpCall:AlertedFailed:" + Common::ObjectAgent::getLogInfo(2), EndType::MpCallAlertFailed);
            return;
        }
    }

    if (!pcSdp.empty() && !_rtpAgent.setAnswer(pcSdp.c_str()))
    {
        UTIL_LOG_CONTEXT_WRN("SipPhoneSession", _logCtx, "content:onCallAlerted set answer sdp failed, reason:" + Common::ObjectAgent::getLogInfo(2));
        __releaseByRtp(MpCall::ErrorType_toString(MpCall::ErrorRtpRunnerSetAnswer), ServiceUtil::callerror_to_json(), EndType::RtpConfigError);
        return;
    }

    if (!pcSdp.empty())
        __updateStateBySdp(pcSdp, false);
    UTIL_LOG_CONTEXT_IFO("SipPhoneSession", _logCtx, "content:onCallAlerted set answer.");

    _mpcallAgent.event_begin(new EventAsync(MpCall::Event_toString(MpCall::EventSipAlerted)), MpCall::Event_toString(MpCall::EventSipAlerted), ServiceUtil::to_json(MpCall::EventSipAlertedDetail(!pcSdp.empty(), _isVideo)), 0, this);

    EventBaseInfo eventBaseInfo(_domainId, _appId, _startTimeMs, _termedTimeMs, _callId, _caller, _callee, _endType);
    _sipEventNotify.notifySipEvent(eventBaseInfo, SipEvent::SipEventAlerted, __getSipCallId());
}

void SipPhoneSession::onCallAnswered(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg)
{
    std::lock_guard<std::mutex> lock(_mutex);

    if (_sessState != StateOutgoing)
    {
        UTIL_LOG_CONTEXT_ERR("SipPhoneSession", _logCtx, "content:sip session answered invalid state:" + to_string(_sessState));
        __release("InvalidStateOnCallAnswered:" + to_string(_sessState), EndType::InvalidSessionState);
        return;
    }

    if (!pcSdp.empty() && !_rtpAgent.setAnswer(pcSdp.c_str()))
    {
        UTIL_LOG_CONTEXT_WRN("SipPhoneSession", _logCtx, "content:onCallAnswered set answer sdp failed, reason:" + Common::ObjectAgent::getLogInfo(2));
        _sipSess->SipAck("");
        __releaseByRtp(MpCall::ErrorType_toString(MpCall::ErrorRtpRunnerSetAnswer), ServiceUtil::callerror_to_json(), EndType::RtpConfigError);
        return;
    }

    if (!pcSdp.empty())
        __updateStateBySdp(pcSdp, false);

    SimpleSipSession::SipMessagePtr sipMsg = SimpleSipSession::SipMessage::create(pcSipMsg);

    MpCall::RoomConfig config;
    MpCall::MemberInfoList members;
    if (!_mpcallAgent.accept(_caller, _isVideo, sipMsg->getHeader(_app->getAppConfig("SipCall.UuiHeader").c_str()).c_str(), Common::StrStrMap(), config, members))
    {
        UTIL_LOG_CONTEXT_WRN("SipPhoneSession", _logCtx, "content:onCallAnswered accept failed, reason:" + Common::ObjectAgent::getLogInfo(2));
        _sipSess->SipAck("");
        __releaseByMpCall("MpCall:AcceptFailed:" + Common::ObjectAgent::getLogInfo(2), EndType::MpCallAcceptFailed);
        return;
    }

    _sipSess->SipAck("");
    setState(StateTalking);
    UTIL_LOG_CONTEXT_IFO("SipPhoneSession", _logCtx, "content:onCallAnswered notify connected event.");
    _mpcallAgent.event_begin(new EventAsync(MpCall::Event_toString(MpCall::EventSipConnected)), MpCall::Event_toString(MpCall::EventSipConnected), ServiceUtil::to_json(MpCall::EventSipConnectedDetail(_isVideo)), 0, this);
}

void SipPhoneSession::onCallUpdate(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg)
{
    UTIL_LOG_CONTEXT_WRN("SipPhoneSession", _logCtx, "content:skip unsupport update flow");
    // __release();
}

void SipPhoneSession::onCallRequestUpdate(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg)
{
    std::lock_guard<std::mutex> lock(_mutex);

    if (_sessState == StateOutgoing)
    {
        if (_updateState != UpdateStateIdle)
        {
            UTIL_LOG_CONTEXT_WRN("SipPhoneSession", _logCtx, "content:sip outgoing session process update invalid state:" + to_string(_updateState));
            __release("InvalidStateOnCallRequestUpdate:" + to_string(_sessState), EndType::InvalidSessionState);
            return;
        }
    }
    else if (_sessState == StateTalking)
    {
        if (_updateState != UpdateStateIdle)
        {
            UTIL_LOG_CONTEXT_WRN("SipPhoneSession", _logCtx, "content:sip talking session process update invalid state:" + to_string(_updateState));
            __release("InvalidStateOnCallRequestUpdate:" + to_string(_sessState), EndType::InvalidSessionState);
            return;
        }

    }
    else
    {
        UTIL_LOG_CONTEXT_WRN("SipPhoneSession", _logCtx, "content:sip session process update invalid state:" + to_string(_updateState));
        __release("InvalidStateOnCallRequestUpdate:" + to_string(_sessState), EndType::InvalidSessionState);
        return;
    }

    if (pcSdp.empty())
    {
        _sipSess->SipUpdateRsp("");
        return;
    }

    Common::String answerSdp;
    if (!_rtpAgent.setOfferAndAnswer(pcSdp.c_str(), configInvite2Sdp(hasVideo(pcSdp) ? MpCall::MediaSendRecv : MpCall::MediaInactive), answerSdp))
    {
        UTIL_LOG_CONTEXT_WRN("SipPhoneSession", _logCtx, "content:sip session update negotiate failed:" + to_string(_sessState));
        __releaseByRtp(MpCall::ErrorType_toString(MpCall::ErrorRtpRunnerNegotiate), ServiceUtil::callerror_to_json(), EndType::RtpNegotiate);
        return;
    }

    __updateStateBySdp(answerSdp.c_str(), true);
    if (!_sipSess->SipUpdateRsp(answerSdp.c_str()))
    {
        UTIL_LOG_CONTEXT_WRN("SipPhoneSession", _logCtx, "content:sip session response update failed:" + to_string(_sessState));
        __releaseBySip(MpCall::ErrorType_toString(MpCall::ErrorSipAnswer), "", EndType::SipUpdateFailed);
        return;
    }

    _mpcallAgent.event_begin(new EventAsync(MpCall::Event_toString(MpCall::EventSipRenegotiated)), MpCall::Event_toString(MpCall::EventSipRenegotiated), ServiceUtil::to_json(MpCall::EventSipRenegotiatedDetail(_isVideo)), 0, this);
    UTIL_LOG_CONTEXT_IFO("SipPhoneSession", _logCtx, "content:sip session response update:" + to_string(_sessState));
}

void SipPhoneSession::onCallResponseUdate(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg)
{
    std::lock_guard<std::mutex> lock(_mutex);

    if (_updateState != UpdateStateOutgoing)
    {
        UTIL_LOG_CONTEXT_ERR("SipPhoneSession", _logCtx, "content:sip session process update response invalid state:" + to_string(_sessState) + " update:" + to_string(_updateState));
        __release("InvalidStateOnCallResponseUpdate:" + to_string(_sessState), EndType::InvalidSessionState);
        return;
    }

    if (!_rtpAgent.setAnswer(pcSdp.c_str()))
    {
        UTIL_LOG_CONTEXT_WRN("SipPhoneSession", _logCtx, "content:sip session process update response negotiate failed:" + to_string(_sessState));
        __releaseByRtp(MpCall::ErrorType_toString(MpCall::ErrorRtpRunnerNegotiate), ServiceUtil::callerror_to_json(), EndType::RtpNegotiate);
        return;
    }

    __updateStateBySdp(pcSdp, false);
    setState(UpdateStateIdle);
    UTIL_LOG_CONTEXT_IFO("SipPhoneSession", _logCtx, "content:sip session process update response:" + to_string(_sessState) + " update:" + to_string(_updateState));
}

void SipPhoneSession::onCallConnected(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg)
{
    std::lock_guard<std::mutex> lock(_mutex);

    if (!_rtpAgent)
    {
        UTIL_LOG_CONTEXT_WRN("SipPhoneSession", _logCtx, "content:onCallConnected no rtp agent");
        __release("InvalidStateOnCallConnected", EndType::InvalidSessionState);
        return;
    }

    if (!pcSdp.empty())
    {
        setState(UpdateStateIdle);

        if (!_rtpAgent.setAnswer(pcSdp.c_str()))
        {
            UTIL_LOG_CONTEXT_WRN("SipPhoneSession", _logCtx, "content:onCallConnected set answer failed");
            __releaseByRtp(MpCall::ErrorType_toString(MpCall::ErrorRtpRunnerNegotiate), ServiceUtil::callerror_to_json(), EndType::RtpNegotiate);
            return;
        }

        UTIL_LOG_CONTEXT_IFO("SipPhoneSession", _logCtx, "content:onCallConnected set answer ok");
        __updateStateBySdp(pcSdp, false);
    }

    if (_sessState == StateConnecting)
    {
        _mpcallAgent.event_begin(new EventAsync(MpCall::Event_toString(MpCall::EventSipConnected)), MpCall::Event_toString(MpCall::EventSipConnected), ServiceUtil::to_json(MpCall::EventSipConnectedDetail(_isVideo)), 0, this);
        setState(StateTalking);
        UTIL_LOG_CONTEXT_IFO("SipPhoneSession", _logCtx, "content:onCallConnected notify event");
    }
}

void SipPhoneSession::onCallRequestModify(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg)
{
    std::lock_guard<std::mutex> lock(_mutex);

    if (!_rtpAgent)
    {
        UTIL_LOG_CONTEXT_WRN("SipPhoneSession", _logCtx, "content:onCallRequestModify no rtp agent");
        __release("InvalidStateOnCallRequestModify", EndType::InvalidSessionState);
        return;
    }

    if (pcSdp.empty())
    {
        Common::String offerSdp;
        if (!_rtpAgent.genOffer(configInvite2Sdp(_isVideo ? MpCall::MediaSendRecv : MpCall::MediaInactive), offerSdp))
        {
            UTIL_LOG_CONTEXT_WRN("SipPhoneSession", _logCtx, "content:onCallRequestModify gen offer failed, reason:" + Common::ObjectAgent::getLogInfo(2));
            __releaseByRtp(MpCall::ErrorType_toString(MpCall::ErrorRtpRunnerNegotiate), ServiceUtil::callerror_to_json(), EndType::RtpNegotiate);
            return;
        }

        if (!_sipSess->SipUpdateRsp(offerSdp.c_str()))
        {
            UTIL_LOG_CONTEXT_WRN("SipPhoneSession", _logCtx, "content:onCallRequestModify update response failed:" + to_string(_sessState));
            __releaseBySip(MpCall::ErrorType_toString(MpCall::ErrorRtpRunnerNegotiate), ServiceUtil::callerror_to_json(), EndType::SipUpdateFailed);
            return;
        }

        UTIL_LOG_CONTEXT_IFO("SipPhoneSession", _logCtx, "content:onCallRequestModify gen offer ok, video:" + Common::String(_isVideo ? MpCall::MediaSendRecv : MpCall::MediaInactive));
        setState(UpdateStateOutgoing);
    }
    else
    {
        Common::String answerSdp;
        if (!_rtpAgent.setOfferAndAnswer(pcSdp.c_str(), configInvite2Sdp(hasVideo(pcSdp) ? MpCall::MediaSendRecv : MpCall::MediaInactive), answerSdp))
        {
            UTIL_LOG_CONTEXT_WRN("SipPhoneSession", _logCtx, "content:onCallRequestModify negotiate failed, reason:" + Common::ObjectAgent::getLogInfo(2));
            __releaseByRtp(MpCall::ErrorType_toString(MpCall::ErrorRtpRunnerNegotiate), ServiceUtil::callerror_to_json(), EndType::RtpNegotiate);
            return;
        }

        __updateStateBySdp(answerSdp.c_str(), true);
        if (!_sipSess->SipUpdateRsp(answerSdp.c_str()))
        {
            UTIL_LOG_CONTEXT_WRN("SipPhoneSession", _logCtx, "content:onCallRequestModify response update failed:" + to_string(_sessState));
            __releaseBySip(MpCall::ErrorType_toString(MpCall::ErrorRtpRunnerNegotiate), "", EndType::RtpNegotiate);
            return;
        }

        _mpcallAgent.event_begin(new EventAsync(MpCall::Event_toString(MpCall::EventSipRenegotiated)), MpCall::Event_toString(MpCall::EventSipRenegotiated), ServiceUtil::to_json(MpCall::EventSipRenegotiatedDetail(_isVideo)), 0, this);
        UTIL_LOG_CONTEXT_IFO("SipPhoneSession", _logCtx, "content:onCallRequestModify ok, video:" + Common::String(_isVideo));
    }
}

void SipPhoneSession::onCallResponseModify(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg)
{
    std::lock_guard<std::mutex> lock(_mutex);

    if (_updateState != UpdateStateOutgoing)
    {
        UTIL_LOG_CONTEXT_ERR("SipPhoneSession", _logCtx, "content:sip session process modify response invalid state:" + to_string(_sessState) + " update:" + to_string(_updateState));
        __release("InvalidStateOnCallResponseModify:" + to_string(_sessState), EndType::InvalidSessionState);
        return;
    }

    if (!_rtpAgent.setAnswer(pcSdp.c_str()))
    {
        UTIL_LOG_CONTEXT_WRN("SipPhoneSession", _logCtx, "content:sip session process modify response negotiate failed:" + to_string(_sessState));
        __releaseByRtp(MpCall::ErrorType_toString(MpCall::ErrorRtpRunnerNegotiate), ServiceUtil::callerror_to_json(), EndType::RtpNegotiate);
        return;
    }

    _sipSess->SipAck("");
    __updateStateBySdp(pcSdp, false);
    setState(UpdateStateIdle);
    UTIL_LOG_CONTEXT_IFO("SipPhoneSession", _logCtx, "content:sip session process modify response:" + to_string(_sessState) + " update:" + to_string(_updateState));
}

void SipPhoneSession::onCallTerminated(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg)
{
    std::lock_guard<std::mutex> lock(_mutex);

    if (_mpcallAgent)
    {
        std::string phase, reason;
        _sipSess->GetCallTermedReason(phase, reason);

        if (_sessState == StateOutgoing)
        {
            UTIL_LOG_CONTEXT_IFO("SipPhoneSession", _logCtx, "content:onCallTerminated reject invitiation, status:" + Common::String((int)iStatCode) + " phase:" + phase.c_str() + " reason:" + reason.c_str());
            _mpcallAgent.reject_begin(0, _caller, ServiceUtil::to_json(MpCall::SipTermedDetail(iStatCode, phase.c_str(), reason.c_str())));
        }
        else if (_sessState == StateIncoming)
        {
            UTIL_LOG_CONTEXT_IFO("SipPhoneSession", _logCtx, "content:onCallTerminated cancel invitiation, status" + Common::String((int)iStatCode) + " phase:" + phase.c_str() + " reason:" + reason.c_str());
            _mpcallAgent.cancel_begin(0, _callee);
        }
        else
        {
            UTIL_LOG_CONTEXT_IFO("SipPhoneSession", _logCtx, "content:onCallTerminated sip session, status:" + Common::String((int)iStatCode) + " phase:" + phase.c_str() + " reason:" + reason.c_str());
            _mpcallAgent.leave_begin(0, false, ServiceUtil::to_json(MpCall::SipTermedDetail(iStatCode, phase.c_str(), reason.c_str())));
        }

        _mpcallAgent = 0;
    }
    else
    {
        UTIL_LOG_CONTEXT_IFO("SipPhoneSession", _logCtx, "content:onCallTerminated, status:" + Common::String((int)iStatCode));
    }

    __release("SipCallTermed");
    return;
}

void SipPhoneSession::onError(const Common::ServerCallPtr &__call, const Common::String &reason)
{
    std::lock_guard<std::mutex> lock(_mutex);

    UTIL_LOG_CONTEXT_IFO("SipPhoneSession", _logCtx, "content:onError reason:" + reason);
    __releaseByRtp(MpCall::ErrorType_toString(MpCall::ErrorRtpRunnerOther), "{\"reason\":\"" + reason + "\"}", EndType::RtpInternalError);
}

void SipPhoneSession::onNetworkStatusChanged(const Common::ServerCallPtr& __call, const Common::StrIntMap& networkStatus)
{
    std::lock_guard<std::mutex> lock(_mutex);

    EventBaseInfo eventBaseInfo(_domainId, _appId, _startTimeMs, _termedTimeMs, _callId, _caller, _callee, _endType);
    if (!networkStatus.empty())
    {
        for (const auto& pair : networkStatus)
        {
            if (pair.first == "RtpRunner")
            {
                int statusValue = pair.second;
                UTIL_LOG_CONTEXT_IFO("SipPhoneSession", _logCtx, "content:rtp network status:" + Common::String(statusValue));
                _sipEventNotify.notifySipNetworkEvent(eventBaseInfo, SipEvent::SipEventRtpNetQualityChanged, statusValue, __getSipCallId());
            }
            else if (pair.first.find(_jsmSipUser) != -1)
            {
                int statusValue = pair.second;
                UTIL_LOG_CONTEXT_IFO("SipPhoneSession", _logCtx, "content:jsm network status username is " + pair.first + " status: " + Common::String(statusValue));
                _sipEventNotify.notifySipNetworkEvent(eventBaseInfo, SipEvent::SipEventNetQualityChanged, statusValue, __getSipCallId());
            }
        }
    }
}

void SipPhoneSession::onRunnerTermed()
{
    std::lock_guard<std::mutex> lock(_mutex);

    UTIL_LOG_CONTEXT_IFO("SipPhoneSession", _logCtx, "content:onRunnerTermed ");
    _runnerName.clear();
    __releaseByRtp(MpCall::ErrorType_toString(MpCall::ErrorRtpRunnerTermed), "", EndType::RtpAbnormalTermed);
}

Common::String SipPhoneSession::getMpCallServerOid(const Common::String &sessId)
{
    Common::String oid;

    if (_app->getAppConfig("MpCallSipGateway.TargetOid", oid))
        UTIL_LOG_CONTEXT_WRN("SipPhoneSession", _logCtx, "content:create agent for testing from target oid:" + oid);
    else if (sessId.empty())
    {
        if (_app->getPrefixName().find("Acd") >= 0)
            oid = "#AcdCall";
        else
            oid = "#MpCall";
    }
    else
    {
        if (_app->getPrefixName().find("Acd") >= 0)
            oid = "AcdCall/" + sessId;
        else
            oid = "MpCall/" + sessId;
    }

    return oid;
}

bool SipPhoneSession::hasVideo(const std::string &sdp)
{
    size_t pos = sdp.find("\r\nm=video ");
    if (pos == std::string::npos)
        return false;

    size_t pos2 = sdp.find(' ', pos + 10);
    if (pos2 == std::string::npos)
        return false;

    try
    {
        int port = std::stoi(sdp.substr(pos + 10, pos2 - (pos + 10)));
        return port > 0;
    }
    catch (...)
    {
    }

    return false;
}

bool SipPhoneSession::isHolded(const std::string &sdp, bool local)
{
    SimpleSipSession::SdpSess sdpSess;
    if (!sdpSess.from_string(sdp))
        return false;

    if (local)
    {
        if (sdpSess.mstreams["audio"].state == SimpleSipSession::SdpMstream::StateInactive || sdpSess.mstreams["audio"].state == SimpleSipSession::SdpMstream::StateRecvOnly)
            return true;
        return false;
    }

    if (sdpSess.mstreams["audio"].state == SimpleSipSession::SdpMstream::StateInactive || sdpSess.mstreams["audio"].state == SimpleSipSession::SdpMstream::StateSendOnly)
        return true;
    return false;
}

bool SipPhoneSession::allocRtpAgent(const MpCall::RoomConfig &roomConfig)
{
    if (_rtpAgent || _allocHandle)
        return true;

    ServiceRunner::RuleParams rules;
    bool empty = true;
    for (auto &attr : roomConfig.attrs)
    {
        if (attr.first != ServiceRunner::AttrType_toString(ServiceRunner::AttrDataCenter)
            && attr.first != ServiceRunner::AttrType_toString(ServiceRunner::AttrHostId))
            continue;

        Common::StrSet ruleValue;
        ruleValue.insert(attr.second);
        rules.affinities[attr.first] = ruleValue;
        empty = false;
    }
    if (empty && !_app->getDataCenter().empty())
    {
        Common::StrSet ruleValue;
        ruleValue.insert(_app->getDataCenter());
        rules.affinities[ServiceRunner::AttrType_toString(ServiceRunner::AttrDataCenter)] = ruleValue;
    }

    _allocHandle = _runnerManager->allocRunner(rules, this);
    return _allocHandle ? true : false;
}

bool SipPhoneSession::createRtpAgent(Common::Error &error)
{
    if (_rtpAgent)
        return true;

    Common::String runnerOid;
    while (!_allocHandle->getRunner(_runnerName, runnerOid, error))
        Common::sleep(1000);

    if (runnerOid.empty())
        return false;

    _rtpAgent = _app->createAgent(runnerOid, false);
    if (!_rtpAgent)
    {
        UTIL_LOG_CONTEXT_WRN("SipPhoneSession", _logCtx, "content:create rtp runner object agent failed, runner:" + _runnerName + " oid:" + runnerOid);
        error = Error::CreateAgentFailed(__ERROR_LOC__);
        return false;
    }
    _rtpAgent->setRqstTimeout(6);

    UTIL_LOG_CONTEXT_IFO("SipPhoneSession", _logCtx, "content:create rtp runner agent runner:" + _runnerName + " oid:" + runnerOid);
    return true;
}

bool SipPhoneSession::configRoom2Rtp(MpCallSip::RtpRunnerConfig &rtpConfig, const MpCall::RoomConfig &roomConfig)
{
    if (roomConfig.udId.empty() && roomConfig.confNum.empty() && roomConfig.roomId.empty() && roomConfig.jsmiOid.empty() && roomConfig.attrs.empty())
        return false;

    rtpConfig.domainId = roomConfig.domainId;
    rtpConfig.appId = roomConfig.appId;
    rtpConfig.Udid = roomConfig.udId;
    rtpConfig.confNum = roomConfig.confNum;
    rtpConfig.oid = roomConfig.jsmiOid;
    rtpConfig.roomId = roomConfig.roomId;
    getAudioExcludeConfig(rtpConfig);

    auto it = roomConfig.attrs.find(MpCall::RoomAttr_toString(MpCall::AttrExtraRole));
    if (it != roomConfig.attrs.end())
        rtpConfig.extraRole = it->second.toInt(0);

    rtpConfig.screenSharingMode = (_app->getAppConfigAsInt("SipCall.ScreenSharingMode") != 0);

    return true;
}

void SipPhoneSession::configInvite2Rtp(MpCallSip::RtpRunnerConfig &rtpConfig, const MpCall::InviteConfig &inviteConfig)
{
    rtpConfig.audioExcludeRole = inviteConfig.audioExcludeRole;
    rtpConfig.audioExcludeRoleMask = inviteConfig.audioExcludeRoleMask;
    rtpConfig.audioMute = inviteConfig.audioMute;

    rtpConfig.videoExcludeRole = inviteConfig.videoExcludeRole;
    rtpConfig.videoExcludeRoleMask = inviteConfig.videoExcludeRoleMask;
    rtpConfig.videoMute = inviteConfig.videoMute;

    rtpConfig.bindingUser = inviteConfig.bindingUser;
}

void SipPhoneSession::getAudioExcludeConfig(MpCallSip::RtpRunnerConfig &rtpConfig)
{
    Common::String value;
    if (!_app->getAppConfig("SipCall.AudioExcludeRole", value))
        return;

    int role, roleMask;

    int pos = value.find('/');
    if (pos < 0)
    {
        role = to_int(value.trim().c_str(), -1);
        roleMask = 0x7fffffff;
    }
    else if (pos > 0 && pos < value.size() - 1)
    {
        role = to_int(value.substr(0, pos).trim().c_str(), -1);
        roleMask = to_int(value.substr(pos + 1).trim().c_str(), -1);
    }
    else
        return;

    if (role < 0 || roleMask < 0)
        return;

    rtpConfig.audioExcludeRole = role;
    rtpConfig.audioExcludeRoleMask = roleMask;
}

MpCallSip::SdpParams SipPhoneSession::configInvite2Sdp(const MpCall::InviteConfig &inviteConfig)
{
    return configInvite2Sdp(inviteConfig.isVideo ? inviteConfig.mediaDirection : MpCall::MediaInactive);
}

MpCallSip::SdpParams SipPhoneSession::configInvite2Sdp(MpCall::MediaDirection direction)
{
    bool precondition = (_app->getAppConfigAsInt("SipCall.Precondition") != 0);
    _logCtx->setContext("video", Common::String(direction));
    return MpCallSip::SdpParams((MpCallSip::MediaDirection)direction, MpCallSip::MediaSendRecv, precondition);
}

Common::CallParamsPtr SipPhoneSession::configApp2CallParams(const SipSessionConfigurator::AppParams &appParams)
{
    if (appParams.domainId.empty())
        return nullptr;

    Common::CallParamsPtr callParams = Common::CallParams::create();
    callParams->setParam(CALL_PARAMS_DOMAIN, appParams.domainId);
    callParams->setParam(CALL_PARAMS_APP, appParams.appId.empty() ? "0" : appParams.appId);
    return callParams;
}

void SipPhoneSession::updateLogContext(const MpCall::InviteConfig &inviteConfig)
{
    _logCtx->setContext("video", inviteConfig.isVideo ? Common::String(inviteConfig.mediaDirection) : Common::String("false"));
    if (inviteConfig.audioMediaDirection != MpCall::MediaSendRecv)
        _logCtx->setContext("audio", Common::String(inviteConfig.audioMediaDirection));
    if (inviteConfig.audioMute || inviteConfig.videoMute)
        _logCtx->setContext("mute", Common::String(inviteConfig.audioMute ? "Audio" : "") + Common::String(inviteConfig.videoMute ? "Video" : ""));
    if (inviteConfig.audioExcludeRoleMask)
        _logCtx->setContext("audioExcludeRole", Common::String(inviteConfig.audioExcludeRole) + "/" + Common::String(inviteConfig.audioExcludeRoleMask));
    if (inviteConfig.videoExcludeRoleMask)
        _logCtx->setContext("vudioExcludeRole", Common::String(inviteConfig.videoExcludeRole) + "/" + Common::String(inviteConfig.videoExcludeRoleMask));
}

Common::String SipPhoneSession::__getSipCallId()
{
    if (!_sipSess)
        return "";
    return _sipSess->getCallId().c_str();
}

void SipPhoneSession::__releaseBySip(const Common::String &reason, const Common::String &detail, EndType endType)
{
    if (_mpcallAgent)
    {
        _mpcallAgent.error_begin(0, reason, detail);
        _mpcallAgent = 0;
    }

    __release(reason + ":" + detail, endType);
}

void SipPhoneSession::__releaseByMpCall(const Common::String &reason, EndType endType)
{
    if (_sipSess && !_sipTermCalled)
    {
        _sipSess->SipTerm();
        _sipTermCalled = true;
    }

    _mpcallAgent = 0;
    __release(reason, endType);
}

void SipPhoneSession::__releaseByRtp(const Common::String &reason, const Common::String &detail, EndType endType)
{
    setState(StateTermed, reason + ":" + detail, endType);
    __notifyTermed();

    if (_mpcallAgent)
    {
        _mpcallAgent.error_begin(0, reason, detail);
        _mpcallAgent = 0;
    }

    if (_sipSess && !_sipTermCalled)
    {
        _sipSess->SipTerm();
        _sipTermCalled = true;
    }

    if (!_runnerName.empty())
    {
        _runnerManager->releaseRunner(_runnerName);
        _runnerName.clear();
    }

    if (_rtpAgent)
    {
        _rtpAgent.close_begin(0);
        _rtpAgent = 0;
    }
}

void SipPhoneSession::__release(const Common::String &reason, EndType endType)
{
    setState(StateTermed, reason, endType);
    __notifyTermed();

    if (!_runnerName.empty())
    {
        _runnerManager->releaseRunner(_runnerName);
        _runnerName.clear();
    }

    if (_rtpAgent)
    {
        _rtpAgent.close_begin(0);
        _rtpAgent = 0;
    }

    if (_sipSess && !_sipTermCalled)
    {
        _sipSess->SipTerm();
        _sipTermCalled = true;
    }

    if (_mpcallAgent)
    {
        _mpcallAgent.leave_begin(0, false, "");
        _mpcallAgent = 0;
    }

    _sipLine = 0;
    _sessionManager = 0;
    _eventCollector = 0;
}

void SipPhoneSession::__notifyHolded(bool hold)
{
    if (_sessId.empty())
        return;

    UTIL_LOG_IFO("SipPhoneSession", "content:__notifyHolded sessId:" + _sessId + " hold:" + Common::String(hold ? "true" : "false"));
    _configurator->onHolded(_sessId, hold);
}

void SipPhoneSession::__notifyTermed()
{
    if (_sessId.empty())
        return;

    UTIL_LOG_IFO("SipPhoneSession", "content:__notifyTermed sessId:" + _sessId);
    _configurator->onTermed(_sessId);
}

void SipPhoneSession::__updateStateBySdp(const std::string &sdp, bool local)
{
    bool isVideo = hasVideo(sdp);
    if (_isVideo != isVideo)
    {
        _isVideo = isVideo;
        UTIL_LOG_IFO("SipPhoneSession", "content:update media sessId:" + _sessId + " state video:" + Common::String(_isVideo));
    }

    bool holded = isHolded(sdp, local);
    if (_holded != holded)
    {
        _holded = holded;
        UTIL_LOG_IFO("SipPhoneSession", "content:update hold sessId:" + _sessId + " state " + Common::String(_holded ? "hold" : "unhold"));
        __notifyHolded(_holded);
    }
}

void SipPhoneSession::onNotifyEventFailed(const Common::String &name, const Common::String &logInfo)
{
    std::lock_guard<std::mutex> lock(_mutex);

    UTIL_LOG_CONTEXT_WRN("SipPhoneSession", _logCtx, "content:notify " + name + " event failed, reason:" + logInfo);
    __releaseByMpCall("MpCallEventFailed", EndType::MpCallEventFailed);
}

void SipPhoneSession::EventAsync::cmdResult(int rslt, const Common::IputStreamPtr &iput, const Common::ObjectPtr &userdata)
{
    SipPhoneSessionPtr session = SipPhoneSessionPtr::dynamicCast(userdata);
    if (!session)
        return;

    if (!MpCall::SessionEndpointAgent::event_end(rslt, iput))
        session->onNotifyEventFailed(_eventName, Common::ObjectAgent::getLogInfo(2));
}

} // namespace SipMpCall
