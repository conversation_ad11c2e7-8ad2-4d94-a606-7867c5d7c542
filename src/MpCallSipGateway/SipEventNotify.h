#pragma once

#include <string>
#include "SipEvent/EventSipPub.h"
#include "EventCollector/EventCollector.h"
#include "ServiceUtil/Struct2Json.h"
#include "Common/Common.h"
#include "Common/Util.h"

namespace SipMpCall
{
struct EventBaseInfo
{
    EventBaseInfo(Common::Long _domain, Common::Long _appId, Common::Long _beginTs, Common::Long _endTs, Common::String& _callId, Common::String& _caller, Common::String& _callee, int _endType)
        : domainId(_domain)
        , appId(_appId)
        , beginTimestamp(_beginTs)
        , endTimestamp(_endTs)
        , callId(_callId)
        , caller(_caller)
        , callee(_callee)
        , endType(_endType)
    {}
    Common::Long domainId;
    Common::Long appId;
    Common::Long beginTimestamp;
    Common::Long endTimestamp;
    Common::String callId;
    Common::String caller;
    Common::String callee;
    int endType;
};

template<typename EventType>
inline void setCommonFields(EventType &event, const EventBaseInfo &baseInfo)
{
    event.domainId = baseInfo.domainId;
    event.appId = baseInfo.appId;
    event.beginTimestamp = baseInfo.beginTimestamp;
    event.endTimestamp = baseInfo.endTimestamp;
    event.callId = baseInfo.callId;
    event.caller = baseInfo.caller;
    event.callee = baseInfo.callee;
}

class SipEventNotify
{
public:
    SipEventNotify(const JsmClient::EventCollectorPtr &eventCollector)
        : _eventCollector(eventCollector)
    {}

    void notifySipEvent(const EventBaseInfo &baseInfo, enum SipEvent::SipEventType eventType, Common::String sipCallId);
    void notifySipNetworkEvent(const EventBaseInfo &baseInfo, enum SipEvent::SipEventType eventType, int networkStatus, Common::String sipCallId);
    void notifySipCallRecord(const EventBaseInfo &baseInfo, Common::Long ringingTimeMs, Common::String source);

private:
    template<typename EventData>
    void sendDataCollectionEvent(const EventData &eventData, DataCollection::EventType eventType, int eventNumber)
    {
        DataCollection::Event event;
        event.type = eventType;
        event.eventNumber = eventNumber;
        event.domainId = eventData.domainId;
        event.appId = eventData.appId;

        event.params = ServiceUtil::to_json(eventData);

        _eventCollector->write(event, false);
    }

    SipEvent::EventSipDetail getSipEvent(const EventBaseInfo &baseInfo, Common::String sipCallId);
    SipEvent::EventSipNetworkChanged getSipNetworkEvent(const EventBaseInfo &baseInfo, int networkStatus, Common::String sipCallId);
    SipEvent::SipCallRecord getSipCallRecord(const EventBaseInfo &baseInfo, Common::Long ringingTimeMs, Common::String source);

private:
    JsmClient::EventCollectorPtr _eventCollector;
};
}