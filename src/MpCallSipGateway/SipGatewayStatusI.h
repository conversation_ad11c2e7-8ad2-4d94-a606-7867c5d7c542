//
// *****************************************************************************
// Copyright (c) 2025 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//

#pragma once

#include "SipGatewayStatus.h"
#include "cpp-httplib/httplib.h"

namespace SipMpCall
{

class SipGatewayStatusI : public SipGatewayStatus, public Common::Thread
{
    enum HttpServerState
    {
        HttpServerIdle,
        HttpServerListened,
        HttpServerDone,
        HttpServerError
    };

public:
    SipGatewayStatusI(const SipGatewayStatusInterfacePtr &listener)
        : _listener(listener)
        , _httpServerState(HttpServerIdle)
    {
    }

    bool open(const Common::String &host, int &port, Common::String &failReason);

    // implement SipGatewayStatus
    virtual void close() override;

    // implement Common::Thread
    virtual void onRun() override;

private:
    SipGatewayStatusInterfacePtr _listener;
    httplib::Server _httpServer;
    int _httpServerState;
};

} // namespace SipMpCall
