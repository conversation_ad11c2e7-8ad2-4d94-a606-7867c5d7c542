//
// *****************************************************************************
// Copyright (c) 2023 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//
// Created on 2023/5/27 by <PERSON>
//

#include "MpCallSipGateway.h"
#include "MpCallRtpRunner/MpCallRtpRunnerType.h"
#include "MpCallSip/MpCallSipStatusPub.h"
#include "MpCallSipGateway/DummyRtpManager.h"
#include "MpCallSipGateway/SipSessionConfigurator.h"
#include "SipPhoneSession.h"
#include <exception>
#include "Error.h"
#include "JsmLog/JsmLog.h"

#include "MpCallHigherConfig/MpCallHigherConfig.h"

static void log_callback(const char *file, int file_size, const char *func, int func_size, int line,
                         int level, const char *mod, const char *info)
{
    Common::log(file, file_size, func, func_size, line, level, mod, info);
}

namespace SipMpCall
{

MpCallSipGateway::MpCallSipGateway(const Common::ApplicationExPtr &application, const SessionLocator::SessionLocatorPtr &locator, const SipLinePtr &sipLine, const ServiceRunner::RunnerManagerPtr &runnerManager, const SipSessionConfiguratorManagerPtr &configuratorManager)
    : Service::ServiceManagerI(application)
    , _sessionType(SipPhone)
    , _locator(locator)
    , _sipLine(sipLine)
    , _rtpRunnerManager(runnerManager)
    , _configuratorManager(configuratorManager)
    , _keepTermedSession(false)
{
    Common::String locatorOid = "MpCallLocator";
    if (application->getPrefixName().find("Acd") >= 0)
        locatorOid = "AcdCallLocator";

    Common::String value;
    if (!application->getAppConfig("Locators.TpSipAgent", value))
        application->setConfig("global.Locators.TpSipAgent", locatorOid);
    if (!application->getAppConfig("Locators.MpCall", value))
        application->setConfig("global.Locators.MpCall", locatorOid);

    if (!application->getAppConfig("EventManager.MaxProcessors", value))
        application->setConfig("global.EventManager.MaxProcessors", "10");

    JsmClient::setLogCallback(log_callback);
    JsmClient::setLogLevel(JsmClient::LogInfo);

    applyHigherConfig(application);
}

bool MpCallSipGateway::onActivate()
{
    Common::String sessionType = _application->getAppConfig("SipCall.SessionType");
    if (sessionType == "volte" || sessionType == "VoLTE")
        _sessionType = SipVolte;

    if (_application->getAppConfig("SipCall.UuiHeader").empty())
        _application->setConfig("global.SipCall.UuiHeader", "User-to-User");

    if (!_locator)
    {
        _locator = SessionLocator::SessionLocator::create(_application.get(), this, "MpCallSipGateway");
        if (!_locator)
        {
            UTIL_LOG_ERR("MpCallSipGateway", "content:create session locator failed.");
            return false;
        }
    }

    if (!_configuratorManager)
    {
        _configuratorManager = SipSessionConfiguratorManager::create(_application);
        if (!_configuratorManager)
        {
            UTIL_LOG_ERR("MpCallSipGateway", "content:create configurator manager failed.");
            return false;
        }
    }

    if (!_rtpRunnerManager)
    {
        Common::String value = _application->getAppConfig("SipGateway.DummyRtpRunner");
        if (value.empty() || value != "1")
        {
            Common::String type;

            type = RtpRunnerType(_application.get());
            if (_application->getAppConfigAsInt("SipCall.ExclusiveRtpRunner") == 1)
            {
                _application->setStatistics("SipCall.ExclusiveRtpRunner", "1");
                type += _application->getAppConfig("SipCall.CoreNetId");
            }

            _rtpRunnerManager = ServiceRunner::RunnerManager::create(_application.get(), type);
        }
        else
        {
            _rtpRunnerManager = DummyRtpManager::create(_application.get());
            _application->setStatistics("SipGateway.DummyRtpRunner", "1");
        }
         if (!_rtpRunnerManager)
        {
            UTIL_LOG_ERR("MpCallSipGateway", "content:create rtp runner manager failed.");
            return false;
        }
    }

    if (!_sipLine)
    {
        _sipLine = SipLine::create(this, _application.get());
        if (!_sipLine)
        {
            UTIL_LOG_ERR("MpCallSipGateway", "content:create sip line failed.");
            return false;
        }
    }

    if (!_status)
    {
        _status = SipGatewayStatus::create(_application.get(), this);
    }

    if (!_eventCollector)
    {
        _eventCollector = JsmClient::EventCollector::create(_application.get());
        if (!_eventCollector)
        {
            UTIL_LOG_ERR("MpCallSipGateway", "content:create event collector failed.");
            return false;
        }
    }

    readRoomConfig();

    return Service::ServiceManagerI::onActivate();
}

void MpCallSipGateway::onDeactivate()
{
    if (_status)
    {
        _status->close();
        _status = 0;
    }

    if (_rtpRunnerManager)
    {
        _rtpRunnerManager->close();
        _rtpRunnerManager = 0;
    }

    if (_locator)
    {
        if (!_locator->close())
            Common::sleep(1500);
        _locator = 0;
    }

    if (_sipLine)
    {
        _sipLine->close();
        _sipLine = 0;
    }

    if (_eventCollector)
    {
        _eventCollector->close();
        _eventCollector = 0;
    }

    _configuratorManager = 0;
    Service::ServiceManagerI::onDeactivate();
}

void MpCallSipGateway::onShutdown()
{
    if (_status)
    {
        _status->close();
        _status = 0;
    }

    if (_rtpRunnerManager)
    {
        _rtpRunnerManager->close();
        _rtpRunnerManager = 0;
    }

    if (_locator)
    {
        if (!_locator->close())
            Common::sleep(1500);
        _locator = 0;
    }

    if (_sipLine)
    {
        _sipLine->close();
        _sipLine = 0;
    }

    if (_eventCollector)
    {
        _eventCollector->close();
        _eventCollector = 0;
    }

    _configuratorManager = 0;
    Service::ServiceManagerI::onShutdown();
}

void MpCallSipGateway::onSchd()
{
    Service::ServiceManagerI::onSchd();

    if (_sipLine)
        _sipLine->schd();
}

void MpCallSipGateway::onUpdateConfigs()
{
    Service::ServiceManagerI::onUpdateConfigs();

    if (_rtpRunnerManager)
        _rtpRunnerManager->updateConfigs();

    if (_locator)
        _locator->updateConfigs();

    if (_sipLine)
        _sipLine->updateConfigs();

    initCategoryType();
}

bool MpCallSipGateway::getMainService(Common::ObjectServerPtr &service, Common::String &name, bool &famous)
{
    return false;
}

bool MpCallSipGateway::isMainServiceReady(Common::String &reason)
{
    if (!_locator)
    {
        reason = "InvalidLocator";
        return false;
    }

    if (!_rtpRunnerManager)
    {
        reason = "InvalidRunnerManager";
        return false;
    }

    if (!_sipLine)
    {
        reason = "InvalidSipLine";
        return false;
    }

    if (!_sipLine->isReady(reason))
    {
        _rtpRunnerManager->deactivate();
        _locator->deactivate(Common::getLastCallError());
        return false;
    }

    if (!_rtpRunnerManager->activate(_mainAdapter))
    {
        reason = "RtpRunnerManagerActivateFailed";
        _locator->deactivate(Error::RtpRunnerManagerActivatedFailed(ELOC));
        return false;
    }

    if (!_locator->activate(_mainAdapter))
    {
        reason = "LocatorActivateFailed";
        return false;
    }

    if (!_rtpRunnerManager->isServiceReady(reason))
        return false;

    _locator->updateFreePercent(_sipLine->getFreePercent());
    return true;
}

SessionLocator::SessionPtr MpCallSipGateway::onCreateSession(const Common::String &type, const Common::String &id, const Common::String &objectId)
{
    SipSessionConfiguratorPtr configurator = _configuratorManager->getConfigurator();
    if (!configurator)
    {
        UTIL_LOG_ERR("MpCallSipGateway", "content:create sip phone session type:" + type + " id:" + id);
        return nullptr;
    }

    if (_sessionType == SipPhone)
    {
        SipPhoneSessionPtr session;
        try
        {
            session = new SipPhoneSession(_application, _rtpRunnerManager, _sipLine, configurator, id, _eventCollector);
            session->_objectId = objectId;
        }
        catch (const std::exception &e)
        {
            UTIL_LOG_WRN("MpCallSipGateway", "content:create sip phone session failed, type:" + type + " id:" + id + " reason:" + e.what());
            return nullptr;
        }

        UTIL_LOG_IFO("MpCallSipGateway", "content:create sip phone session type:" + type + " id:" + id);
        return session.get();
    }

    return nullptr;
}

SipLineSessionListenerPtr MpCallSipGateway::onCreateSession(const SipLineSessionPtr &session)
{
    SipSessionConfiguratorPtr configurator = _configuratorManager->getConfigurator();
    if (!configurator)
    {
        UTIL_LOG_ERR("MpCallSipGateway", "content:create sip phone session failed, no configurator");
        return nullptr;
    }

    if (_sessionType == SipPhone)
    {
        SipPhoneSessionPtr phoneSession;
        try
        {
            phoneSession = new SipPhoneSession(_application, _rtpRunnerManager, _sipLine, configurator, session, this, _eventCollector);
        }
        catch (const std::exception &e)
        {
            UTIL_LOG_WRN("MpCallSipGateway", "content:create sip phone session for incoming call failed, reason:" + Common::String(e.what()));
            return nullptr;
        }

        UTIL_LOG_IFO("MpCallSipGateway", "content:create sip phone session for incoming call");
        return phoneSession.get();
    }

    return nullptr;
}

Common::String MpCallSipGateway::addSession(const Common::String &id, const SessionLocator::SessionPtr &session)
{
    SessionLocator::SessionLocatorPtr locator = _locator;
    if (!locator)
        return "";

    return locator->addSession(_categoryType, id, session);
}

bool MpCallSipGateway::setConfig(const Common::String &key, const Common::String &value)
{
    if (key == "KeepTermedSession")
    {
        SessionLocator::SessionLocatorPtr locator = _locator;
        SipLinePtr sipLine = _sipLine;
        if (!locator || !sipLine)
        {
            UTIL_LOG_WRN("MpCallSipGateway", "content:set config invalid state, key:" + key + " value:" + value);
            return false;
        }

        _keepTermedSession = value == "1";
        _locator->keepTermedSession(_keepTermedSession);
        _sipLine->setKeepTermedStatus(_keepTermedSession);
        UTIL_LOG_IFO("MpCallSipGateway", "content:set config, key:" + key + " value:" + value);
    }
    else
    {
        UTIL_LOG_WRN("MpCallSipGateway", "content:set config not supported, key:" + key + " value:" + value);
        return false;
    }

    return true;
}

bool MpCallSipGateway::getConfig(const Common::String &key, Common::String &value)
{
    if (key == "KeepTermedSession")
        value = _keepTermedSession ? "1" : "0";
    else
    {
        UTIL_LOG_WRN("MpCallSipGateway", "content:get config not supported, key:" + key);
        return false;
    }

    return true;
}

bool MpCallSipGateway::getCallStatusList(MpCallSip::ResponseAllCallStatus &status)
{
    SessionLocator::SessionLocatorPtr locator = _locator;
    if (!locator)
        return false;

    std::map<Common::String, SessionLocator::SessionPtr> sessions = locator->getSessions();
    for (const auto &kv : sessions)
    {
        SipSessionPtr sipSession = SipSessionPtr::dynamicCast(kv.second);
        if (sipSession)
        {
            MpCallSip::StatusSessionBriefStatus sessStatus;
            sessStatus.sessionId = kv.first;
            sipSession->getStatus(sessStatus.caller, sessStatus.callee, sessStatus.callId, sessStatus.direction, sessStatus.state, sessStatus.startTimeMs, sessStatus.durationMs, sessStatus.termedReason);
            status.sessions.push_back(sessStatus);
        }
    }

    status.count = sessions.size();
    return true;
}

bool MpCallSipGateway::getCallStatus(const Common::String &sessId, MpCallSip::ResponseStatus &status)
{
    SessionLocator::SessionLocatorPtr locator = _locator;
    if (!locator)
    {
        UTIL_LOG_WRN("MpCallSipGateway", "content:get call status invalid state, sessId:" + sessId);
        return false;
    }

    SessionLocator::SessionPtr session = locator->getSession(sessId);
    if (!session)
    {
        UTIL_LOG_WRN("MpCallSipGateway", "content:get call status not found, sessId:" + sessId);
        return false;
    }

    status.sessionId = sessId;
    SipSessionPtr sipSession = SipSessionPtr::dynamicCast(session);
    sipSession->getStatus(status.caller, status.callee, status.callId, status.direction, status.state, status.startTimeMs, status.durationMs, status.termedReason);
    if (!sipSession->getRtpStatus(status.rtpStats))
        UTIL_LOG_WRN("MpCallSipGateway", "content:get call status, sessId:" + sessId + " get rtp status failed");

    SipLinePtr sipLine = _sipLine;
    if (sipLine)
    {
        int pos = sessId.find("/");
        Common::String sipSessId = pos >= 0 ? sessId.substr(pos + 1) : sessId;
        std::map<std::string, SipLine::SessStatus> sessStatuses;
        if (!sipLine->getSessStatus(sipSessId.c_str(), sessStatuses))
        {
            UTIL_LOG_WRN("MpCallSipGateway", "content:get call status, sessId:" + sessId + " get sess status failed, sipSessId:" + sipSessId);
        }
        else
        {
            for (const auto &pair : sessStatuses)
            {
                status.sipStats[pair.first.c_str()].local = pair.second.localAddr.c_str();
                status.sipStats[pair.first.c_str()].remote = pair.second.remoteAddr.c_str();

                for (const auto &event : pair.second.events)
                    status.sipStats[pair.first.c_str()].events.push_back(MpCallSip::SipEvent(event.type.c_str(), event.time.c_str(), event.title.c_str(), event.detail.c_str(), event.timestamp));
            }
        }
    }

    UTIL_LOG_IFO("MpCallSipGateway", "content:get call status, sessId:" + sessId);
    return true;
}

void MpCallSipGateway::initCategoryType()
{
    if (!_categoryType.empty())
        return;

    SessionLocator::SessionLocatorPtr locator = _locator;
    if (!locator)
        return;

    Common::String coreNetId;
    if (!_application->getAppConfig("SipCall.CoreNetId", coreNetId))
    {
        UTIL_LOG_WRN("MpCallSipGateway", "content:no core net id configed");
        return;
    }
    _application->setStatistics("SipCall.CoreNetId", coreNetId);

    _serviceId = _application->getAppName().substr(_application->getPrefixName().size());
    Common::String dataCenter = _application->getDataCenter();
    if (_serviceId[0] == '.')
        _serviceId = _serviceId.substr(1);

    if (dataCenter.empty())
        dataCenter = "global";
    else
        _serviceId = _serviceId.substr(dataCenter.size() + 1);
    _application->setStatistics("SipCall.ServiceId", _serviceId);

    if (!_rtpRunnerManager || _rtpRunnerManager->totalRunnerCount() <= 0)
    {
        UTIL_LOG_WRN("MpCallSipGateway", "content:no rtp runner available");
        return;
    }

    Common::String categoryPrefix = "SipCall";
    if (_application->getPrefixName().find("Acd") >= 0)
        categoryPrefix = "AcdSipCall";

    Common::String sessionTypeLine = categoryPrefix + "." + dataCenter + "." + coreNetId;
    if (!locator->addSessionType(sessionTypeLine))
    {
        UTIL_LOG_ERR("MpCallSipGateway", "content:add session type:" + sessionTypeLine);
        return;
    }

    Common::String sessionTypeDc = categoryPrefix + "." + dataCenter;
    if (!locator->addSessionType(sessionTypeDc))
    {
        UTIL_LOG_ERR("MpCallSipGateway", "content:add session type:" + sessionTypeDc);
        locator->removeSessionType(sessionTypeLine);
        return;
    }

    if (!locator->addSessionType(categoryPrefix))
    {
        UTIL_LOG_ERR("MpCallSipGateway", "content:add session type:SipCall");
        locator->removeSessionType(sessionTypeLine);
        locator->removeSessionType(sessionTypeDc);
        return;
    }

    _categoryType = categoryPrefix;
    _application->setStatistics("SipCall.CategoryType", _categoryType);
}

void MpCallSipGateway::readRoomConfig()
{
    static const char *prefix = "MpCallRoomConfig.";
    static size_t prefixLen = strlen(prefix);

    Common::StrStrMap configs;
    _application->getAppConfigs(prefix, configs);
    for (auto kv : configs)
    {
        if (kv.first.size() <= prefixLen)
            continue;

        Common::String key = kv.first.substr(prefixLen);
        _roomConfig[key] = kv.second;
        _application->setStatistics(kv.first, kv.second);
    }
}

} // namespace SipMpCall
