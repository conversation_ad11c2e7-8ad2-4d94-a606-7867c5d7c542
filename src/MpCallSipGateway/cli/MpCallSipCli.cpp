//
// *****************************************************************************
// Copyright (c) 2023 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//
// Created on 2023/6/8 by <PERSON>
//

#include "CliMain/CliMain.h"
#include "Common/Common.h"
#include "Common/Util.h"
#include "MpCall/MpCallPub.h"
#include "clipp/clipp.hpp"
#include <cstdlib>
#include <iterator>
#include <sys/stat.h>
#include <sys/types.h>
#include <unistd.h>

#include "MpCall/MpCallSipAgent.h"
#include "MpCall/MpCallSessionServer.h"
#include "JSM/JSMEAgent.h"

class MpCallSipCli : public ::Cli::Cli, public CLIpp::Shell, public MpCall::SessionEndpointServer, public MpCall::SessionServer
{
public:
    MpCallSipCli()
        : _autoAnswer(true)
    {
    }

    virtual bool __ex(const Common::ServerCallPtr &__call, const Common::String &__cmd, const Common::IputStreamPtr &__iput)
    {
        if (MpCall::SessionEndpointServer::__ex(__call, __cmd, __iput))
            return true;
        if (MpCall::SessionServer::__ex(__call, __cmd, __iput))
            return true;
        return false;
    }

    virtual void call_begin(const Common::ServerCallPtr &__call, MpCall::UserType callerType, const Common::String &callerId, MpCall::UserType calleeType, const Common::String &calleeId, const MpCall::InviteConfig &inviteConfig, const Common::StrStrMap &params) override
    {
        Common::String sessId = "room" + callerId + calleeId;
        _agent = application->createAgent("SipCall/" + callerId + "/" + sessId);
        MpCall::SessionServer::call_end(__call, true, sessId, MpCall::RoomConfig());

        if (_autoAnswer)
        {
            std::string args[2];
            args[0] = "answer";
            args[1] = Common::randString().c_str();
            std::cout << "auto answer:" << args[1] << std::endl;
            answer(2, args);
        }
    }

    virtual void join_begin(const Common::ServerCallPtr &__call, MpCall::UserType userType, const Common::String &userId, const Common::StrStrMap &params) override
    {
        __call->throwException(Common::Exception("NotSupport"));
    }

    virtual void invite_begin(const Common::ServerCallPtr &__call, MpCall::UserType calleeType, const Common::String &calleeId, const MpCall::InviteConfig &inviteConfig) override
    {
        __call->throwException(Common::Exception("NotSupport"));
    }

    virtual void alert_begin(const Common::ServerCallPtr &__call, const Common::String &userId) override
    {
        __call->throwException(Common::Exception("NotSupport"));
    }

    virtual void cancel_begin(const Common::ServerCallPtr &__call, const Common::String &calleeId) override
    {
        __call->throwException(Common::Exception("NotSupport"));
    }

    virtual void accept_begin(const Common::ServerCallPtr &__call, const Common::String &callerId, bool isVideo, const Common::String &uui, const Common::StrStrMap &params) override
    {
        MpCall::SessionEndpointServer::accept_end(__call, true, MpCall::RoomConfig(), MpCall::MemberInfoList());
    }

    virtual void reject_begin(const Common::ServerCallPtr &__call, const Common::String &callerId, const Common::String &detail) override
    {
        __call->throwException(Common::Exception("NotSupport"));
    }

    virtual void leave_begin(const Common::ServerCallPtr &__call, bool terminate, const Common::String &detail) override
    {
        std::cout << "leaved detail:" << detail.c_str() << std::endl;
        _agent = 0;
        MpCall::SessionEndpointServer::leave_end(__call, true);
    }

    virtual void error_begin(const Common::ServerCallPtr &__call, const Common::String &reason, const Common::String &detail) override
    {
        std::cout << "error reason:" << reason.c_str() << " detail:" << detail.c_str() << std::endl;
        _agent = 0;
        __call->throwException(Common::Exception("NotSupport"));
    }

    virtual void event_begin(const Common::ServerCallPtr &__call, const Common::String &name, const Common::String &detail) override
    {
        std::cout << "event:" << name.c_str() << " detail:" << detail.c_str() << std::endl;
        MpCall::SessionEndpointServer::event_end(__call, true);
    }

    virtual void requestVideo_begin(const Common::ServerCallPtr &__call, MpCall::UserType userType, const Common::String &userId) override
    {
        __call->throwException(Common::Exception("NotSupport"));
    }

    virtual void reqProperty_begin(const Common::ServerCallPtr &__call, MpCall::UserType userType, const Common::String &userId, const Common::String &key, const Common::String &value, Common::Long version, int limitTime) override
    {
        __call->throwException(Common::Exception("NotSupport"));
    }

    virtual void repProperty_begin(const Common::ServerCallPtr &__call, const Common::String &key, const Common::String &value, bool agree, Common::Long version) override
    {
        __call->throwException(Common::Exception("NotSupport"));
    }

    virtual void sendMpMessage_begin(const Common::ServerCallPtr &__call, MpCall::UserType userType, const Common::String &userId, const Common::String &message, const Common::String &msgType) override
    {
        __call->throwException(Common::Exception("NotSupport"));
    }

    virtual void setProperty_begin(const Common::ServerCallPtr &__call, const Common::String &key, const Common::String &value, Common::Long version) override
    {
        __call->throwException(Common::Exception("NotSupport"));
    }

    bool setup() override
    {
        _caller = "tester";
        if (!application->getConfig("dc", _dc))
        {
            std::cerr << "starr program with dc=<datacenter_id> to specify datacenter" << std::endl;
            return false;
        }

        AddFunction("set", MpCallSipCli::set, "set <key> <value>");
        AddFunction("invite", MpCallSipCli::invite, "invite <sessid> <callee> [video]");
        AddFunction("call", MpCallSipCli::call, "call <sessid> <callee> [video]");
        AddFunction("callcancel", MpCallSipCli::callcancel, "callcancel <sessid> <callee> [video]");
        AddFunction("answer", MpCallSipCli::answer, "answer <roomId>");
        AddFunction("term", MpCallSipCli::term, "term");

        if (application->getAppConfig("Main.Endpoints").empty())
            application->setConfig("global.Main.Endpoints", "svarc;");
        _adapter = application->createAdapter("Main");
        _adapter->activate();
        _adapter->addServer("MpCallSipCli", this, true);
        while (_oid.empty())
        {
            Common::sleep(100);
            _oid = _adapter->getObjectId("MpCallSipCli");
        }

        std::cout << R"(
Welcome to MpCallSipCli program

This program used to test MpCallSipGateway service. The requirements as follow:

1. The host running this program must deployed with 'Router' service.
2. The program must be executed in the 'cube' directory, which contains 'server.cfg'
3. 'server.cfg' must contains 'global.Locators' config.
4. Add config 'MpCallSipGateway.TargetOid=MpCallSipCli' to MpCallSipGateway service.

Using 'invite' command to test SIP callout. Using 'call' command to create room then
perform SIP callout.  This program will auto answer incoming SIP call.

Usage:
        )" << std::endl;
        DisplayUsage();

        return true;
    }

    void teardown() override
    {
        _adapter->removeServer("MpCallSipCli");
        _adapter = 0;
    }

    int onRun() override
    {
        Run();
        return 0;
    }

    bool createRoom(const Common::String &roomId)
    {
        Common::String domainId = "100645";
        application->getConfig("domain", domainId);

        Common::StrStrMap params;
        params["domainId"] = domainId;
        params["appId"] = "0";
        params["accountId"] = _caller;
        params["sessionId"] = "test";

        Common::String ep;
        JSM::JSMEAgent jsme = application->createAgent("#JSME");
        if (!jsme.add2(roomId, params, ep, params))
        {
            std::cerr << "create room failed:" << Common::ObjectAgent::getLastReason().c_str() << std::endl;
            return false;
        }

        return true;
    }

#define SHELL_FUNCTION(__class, __name, __count, __usage)        \
    static void __name(CLIpp::Shell *s, int argc, string args[]) \
    {                                                            \
        if (argc < __count)                                      \
        {                                                        \
            std::cerr << __usage << std::endl;                   \
            return;                                              \
        }                                                        \
        dynamic_cast<__class *>(s)->__name(argc, args);          \
    }                                                            \
    void __name(int argc, string args[])

    SHELL_FUNCTION(MpCallSipCli, set, 3, "set <key> <value>")
    {
        if (args[1] == "autoanswer")
        {
            _autoAnswer = (std::stoi(args[2]) != 0);
        }
    }

    SHELL_FUNCTION(MpCallSipCli, invite, 3, "invite <sessid> <callee> [video=1] [line=xxx]")
    {
        MpCall::InviteConfig inviteConfig;
        inviteConfig.displayName = "MpCallCli";
        inviteConfig.uui = "CallFromMpCallCli";

        for (int i = 3; i < argc; i++)
        {
            if (args[i].find("video=") == 0)
                inviteConfig.isVideo = true;
            else if (args[i].find("line=") == 0)
                inviteConfig.routeId = args[i].substr(5).c_str();
        }

        _agent = application->createAgent("SipCall." + _dc + "/" + args[2].c_str() + "/" + args[1].c_str());
        if (!_agent.invitation(args[1].c_str(), _caller, args[2].c_str(), MpCall::RoomConfig(), inviteConfig))
        {
            std::cerr << "invite failed:" << Common::ObjectAgent::getLastReason().c_str() << std::endl;
            return;
        }
    }

    SHELL_FUNCTION(MpCallSipCli, call, 3, "call <sessid> <callee> [video=1] [line=xxx]")
    {
        MpCall::InviteConfig inviteConfig;
        inviteConfig.displayName = "MpCallCli";
        inviteConfig.uui = "CallFromMpCallCli";

        for (int i = 3; i < argc; i++)
        {
            if (args[i].find("video=") == 0)
                inviteConfig.isVideo = true;
            else if (args[i].find("line=") == 0)
                inviteConfig.routeId = args[i].substr(5).c_str();
        }

        MpCall::RoomConfig config;
        config.udId = args[1].c_str();

        if (!createRoom(config.udId))
            return;

        _agent = application->createAgent("SipCall." + _dc + "/" + config.udId);
        if (!_agent.invitation(config.udId, _caller, args[2].c_str(), config, inviteConfig))
        {
            std::cerr << "invite failed:" << Common::ObjectAgent::getLastReason().c_str() << std::endl;
            return;
        }
    }

    SHELL_FUNCTION(MpCallSipCli, callcancel, 3, "callcancel <sessid> <callee> [video=1] [line=xxx]")
    {
        MpCall::InviteConfig inviteConfig;
        inviteConfig.displayName = "MpCallCli";
        inviteConfig.uui = "CallFromMpCallCli";

        for (int i = 3; i < argc; i++)
        {
            if (args[i].find("video=") == 0)
                inviteConfig.isVideo = true;
            else if (args[i].find("line=") == 0)
                inviteConfig.routeId = args[i].substr(5).c_str();
        }

        MpCall::RoomConfig config;
        config.udId = args[1].c_str();

        if (!createRoom(config.udId))
            return;

        _agent = application->createAgent("SipCall." + _dc + "/" + config.udId);
        if (!_agent.invitation(config.udId, _caller, args[2].c_str(), config, inviteConfig))
        {
            std::cerr << "invite failed:" << Common::ObjectAgent::getLastReason().c_str() << std::endl;
            return;
        }

        if (!_agent.canceled(_caller, "Canceled"))
        {
            std::cerr << "cancel failed:" << Common::ObjectAgent::getLastReason().c_str() << std::endl;
            return;
        }
    }

    SHELL_FUNCTION(MpCallSipCli, answer, 2, "answer <roomId>")
    {
        if (!_agent)
            return;

        MpCall::RoomConfig config;
        config.udId = args[1].c_str();
        if (!createRoom(config.udId))
        {
            std::cerr << "answer create room failed" << std::endl;
            _agent.terminated("CreateRoomFailed");
            _agent = 0;
            return;
        }

        if (!_agent.accepted("MpCallSipCli", true, config, "AnswerFromMpCallCli"))
        {
            std::cerr << "answer accept failed:" << Common::ObjectAgent::getLastReason().c_str() << std::endl;
            _agent = 0;
            return;
        }
    }

    SHELL_FUNCTION(MpCallSipCli, term, 1, "term")
    {
        if (!_agent)
            return;

        if (!_agent.terminated("termed"))
        {
            std::cerr << "term failed:" << Common::ObjectAgent::getLastReason().c_str() << std::endl;
            return;
        }
    }

public:
    MpCall::SipGatewayAgent _agent;
    Common::String _caller;
    Common::String _dc;

    Common::AdapterPtr _adapter;
    Common::String _oid;

    int _autoAnswer;
};

REGISTER_CLI("MpCallSipCli", MpCallSipCli);
