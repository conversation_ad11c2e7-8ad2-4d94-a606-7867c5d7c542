//
// *****************************************************************************
// Copyright (c) 2025 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//

#pragma once

#include "Common/CommonEx.h"
#include "MpCall/MpCallPub.h"
#include "SipSessionConfigurator.h"

namespace SipMpCall
{

class CalleeTypeConfig
{
public:
    bool init(const Common::ApplicationExPtr &app);

    MpCall::UserType getCalleeType(const Common::String &domainId, const Common::String &appId, MpCall::UserType defaultType) const;

private:
    static MpCall::UserType parseCalleeType(const Common::String &config);

private:
    std::map<Common::String, MpCall::UserType> _configs;
};

class SipSessionConfiguratorBase : public SipSessionConfigurator
{
public:
    virtual bool onIncomingCall(const CallParams &inParams, CallParams &outParams, AppParams &appParams) override;
    virtual bool onOutgoingCall(const CallParams &inParams, CallParams &outParams) override;
    virtual bool onHolded(const Common::String &sessId, bool hold)  override;
    virtual bool onTermed(const Common::String &sessId) override;

public:
    CalleeTypeConfig _calleeTypeConfig;
};

typedef Common::Handle<SipSessionConfiguratorBase> SipSessionConfiguratorBasePtr;

class SipSessionConfiguratorDomain : public SipSessionConfiguratorBase
{
public:
    SipSessionConfiguratorDomain(const Common::String &domainId, const Common::String &appId)
        : _domainId(domainId)
        , _appId(appId)
    {
        if (_appId.empty())
            _appId = "0";
    }

    virtual bool onIncomingCall(const CallParams &inParams, CallParams &outParams, AppParams &appParams) override;

protected:
    Common::String _domainId;
    Common::String _appId;
};

class SipSessionConfiguratorCalleePrefix : public SipSessionConfiguratorBase
{
public:
    virtual bool onIncomingCall(const CallParams &inParams, CallParams &outParams, AppParams &appParams) override;
};

class SipSessionConfiguratorBaseManager : public SipSessionConfiguratorManager
{
public:
    explicit SipSessionConfiguratorBaseManager(const Common::ApplicationExPtr &app)
        : _app(app)
    {
    }

    virtual SipSessionConfiguratorPtr getConfigurator() override;

private:
    Common::ApplicationExPtr _app;
};

} // namespace SipMpCall
