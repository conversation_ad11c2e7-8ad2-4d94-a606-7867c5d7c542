//
// *****************************************************************************
// Copyright (c) 2025 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//

#include "MpCallSipDialer.h"
#include "ServiceUtil/Struct2Json.h"
#include "cpp-httplib/httplib.h"
#include "SipDialer/MpCallSipDialerPub.h"
#include "ServiceUtil/Struct2Json.h"
#include "MpCallSipDialer.html.h"
#include "MpCallSipDialer.css.h"
#include "JsmLog/JsmLog.h"


static void log_callback(const char *file, int file_size, const char *func, int func_size, int line,
    int level, const char *mod, const char *info)
{
    Common::log(file, file_size, func, func_size, line, level, mod, info);
}

namespace SipMpCall
{

bool DialerApiServer::open(Common::String &failReason)
{
    Common::String httpHost;
    int httpPort;
    if (!_app->getAppConfig("SipDialer.Http.ListenHost", httpHost) || httpHost == "0.0.0.0")
    {
        Common::NetSenderPtr sender = _app->getDriver()->listen("udp", "0.0.0.0", 0, 0);
        if (!sender)
        {
            UTIL_LOG_ERR("DialerApiServer", "content:activate get local ip failed.");
            failReason = "DialerApiServer.ListenFailed";
            return false;
        }

        sender->getLocal(httpHost, httpPort);
        sender->close();
    }

    httpPort = _app->getAppConfigAsInt("SipDialer.Http.ListenPort");
    if (httpPort < 0 || httpPort >= 65535)
    {
        UTIL_LOG_ERR("DialerApiServer", "content:activate invalid config SipDialer.Http.ListenPort");
        failReason = "DialerApiServer.InvaidConfig:SipDialer.Http.ListenPort";
        return false;
    }

    if (httpPort == 0)
    {
        httpPort = _httpServer.bind_to_any_port(httpHost.c_str());
        if (httpPort <= 0)
        {
            UTIL_LOG_ERR("DialerApiServer", "content:activate bind to any port failed:" + httpHost);
            failReason = "DialerApiServer.BindPortFailed:" + httpHost;
            return false;
        }
    }
    else
    {
        if (!_httpServer.bind_to_port(httpHost.c_str(), httpPort))
        {
            UTIL_LOG_ERR("DialerApiServer", "content:activate listen:" + httpHost + ":" + Common::String(httpPort) + " failed.");
            failReason = "DialerApiServer.BindPortFailed:" + httpHost + ":" + Common::String(httpPort);
            return false;
        }
    }

    if (!startRun(0, "DialerApiServer"))
    {
        UTIL_LOG_ERR("DialerApiServer", "content:activate start listen:" + httpHost + ":" + Common::String(httpPort) + " failed.");
        failReason = "DialerApiServer.StartListenFailed:" + httpHost + ":" + Common::String(httpPort);
        return false;
    }

    for (int i = 0; i < 5; i++)
    {
        if (_httpServerState == HttpServerError)
        {
            UTIL_LOG_ERR("DialerApiServer", "content:activate start listen:" + httpHost + ":" + Common::String(httpPort) + " failed.");
            failReason = "DialerApiServer.ListenFailed:" + httpHost + ":" + Common::String(httpPort);
            return false;
        }
        Common::sleep(10);
    }

    Common::String webSite = "http://" + httpHost + ":" + Common::String(httpPort);
    _apiUrl = (webSite + "/api/v1").c_str();
    _app->setStatistics("SipDialer.WebPortal", webSite);
    _app->setStatistics("SipDialer.ApiUrl", _apiUrl.c_str());
    UTIL_LOG_IFO("DialerApiServer", "content:activate start listen:" + webSite + " api:" + _apiUrl.c_str());
    return true;
}

void DialerApiServer::close()
{
    _listener = 0;
    _httpServer.stop();
}

void DialerApiServer::onRun()
{
    // get html
    _httpServer.Get("/", [&](const httplib::Request &req, httplib::Response &res) {
        // if local html file exists, use it
        if (access("MpCallSipDialer.html", F_OK) == 0)
        {
            std::ifstream file("MpCallSipDialer.html");
            std::string content((std::istreambuf_iterator<char>(file)), std::istreambuf_iterator<char>());
            res.set_content(content, "text/html");
        }
        else
        {
            res.set_content(std::string((const char *)MpCallSipDialer_html, MpCallSipDialer_html_len), "text/html");
        }
    });

    // get css
    _httpServer.Get("/MpCallSipDialer.css", [&](const httplib::Request &req, httplib::Response &res) {
        // if local css file exists, use it
        if (access("MpCallSipDialer.css", F_OK) == 0)
        {
            std::ifstream file("MpCallSipDialer.css");
            std::string content((std::istreambuf_iterator<char>(file)), std::istreambuf_iterator<char>());
            res.set_content(content, "text/css");
        }
        else
        {
            res.set_content(std::string((const char *)MpCallSipDialer_css, MpCallSipDialer_css_len), "text/css");
        }
    });

    // get config, path /api/v1/config/<key>
    _httpServer.Get("/api/v1/config/(.*)", [&](const httplib::Request &req, httplib::Response &res) {
        processGetConfig(req, res);
    });

    // set config, path /api/v1/config/<key>
    _httpServer.Post("/api/v1/config/(.*)", [&](const httplib::Request &req, httplib::Response &res) {
        processSetConfig(req, res);
    });

    // post call, path /api/v1/call
    _httpServer.Post("/api/v1/call", [&](const httplib::Request &req, httplib::Response &res) {
        processCall(req, res);
    });

    // get all call status, path /api/v1/call/status
    _httpServer.Get("/api/v1/call/status", [&](const httplib::Request &req, httplib::Response &res) {
        processAllCallStatus(req, res);
    });

    // get call status, path /api/v1/call/<sessId>/status
    _httpServer.Get("/api/v1/call/(.*)/status", [&](const httplib::Request &req, httplib::Response &res) {
        processCallStatus(req, res);
    });

    _httpServerState = HttpServerListened;

    if (!_httpServer.listen_after_bind())
    {
        UTIL_LOG_ERR("DialerApiServer", "content:http server thread listen failed.");
        _httpServerState = HttpServerError;
        return;
    }

    UTIL_LOG_IFO("DialerApiServer", "content:http server thread exit");
    _httpServerState = HttpServerDone;
}

void DialerApiServer::terminated(int sessionId, bool error, const std::string &reason)
{
    std::string host, path;
    int port;

    do
    {
        Common::RecLock lock(_mutex);

        auto it = _callbacks.find(sessionId);
        if (it == _callbacks.end())
            return;

        std::string callback = it->second;
        _callbacks.erase(it);

        if (callback.empty())
            return;

        if (!decodeHttpUri(callback, host, port, path))
            return;
    } while (0);

    httplib::Client client(host, port);
    if (!client.is_valid())
        return;

    SipDialer::ResponseTerminated response;
    response.sessionId = sessionId;
    response.error = error;
    response.reason = reason.c_str();
    Common::String body = ServiceUtil::to_json(response);
    client.Post(path, body.c_str(), "application/json");
}

void DialerApiServer::processGetConfig(const httplib::Request &req, httplib::Response &res)
{
    Common::RecLock lock(_mutex);

    DialerApiListenerPtr listener = _listener;
    if (!listener)
    {
        res.status = 500;
        res.set_content("NoListener", "text/plain");
        return;
    }

    std::string config;
    if (!listener->getConfig(req.matches[1], config))
    {
        res.status = 404;
        res.set_content("GetConfigFailed", "text/plain");
        return;
    }

    res.status = 200;
    res.set_content(config, "text/plain");
}

void DialerApiServer::processSetConfig(const httplib::Request &req, httplib::Response &res)
{
    Common::RecLock lock(_mutex);

    DialerApiListenerPtr listener = _listener;
    if (!listener)
    {
        res.status = 500;
        res.set_content("NoListener", "text/plain");
        return;
    }

    if (!listener->setConfig(req.matches[1], req.body))
    {
        res.status = 404;
        res.set_content("SetConfigFailed", "text/plain");
        return;
    }

    res.status = 200;
    res.set_content("OK", "text/plain");
}

void DialerApiServer::processCall(const httplib::Request &req, httplib::Response &res)
{
    Common::RecLock lock(_mutex);

    SipDialer::RequestCall request;
    ServiceUtil::from_json(request, req.body.c_str());

    if (request.caller.empty() || request.callee.empty() || request.timeout <= 0)
    {
        res.status = 400;
        res.set_content("InvalidRequest", "text/plain");
        return;
    }

    DialerApiListenerPtr listener = _listener;
    if (!listener)
    {
        res.status = 500;
        res.set_content("NoListener", "text/plain");
        return;
    }

    int sessionId;
    std::string callId;
    if (!listener->call(request.caller.c_str(), request.callee.c_str(), request.uui.c_str(), request.video, request.is3pcc, request.timeout, sessionId, callId))
    {
        res.status = 500;
        res.set_content("CallFailed", "text/plain");
        return;
    }

    if (!request.callback.empty())
        _callbacks[sessionId] = request.callback.c_str();

    res.status = 200;
    SipDialer::ResponseCall response;
    response.sessionId = sessionId;
    response.callId = callId.c_str();
    Common::String body = ServiceUtil::to_json(response);
    res.set_content(body.c_str(), "application/json");
}

void DialerApiServer::processAllCallStatus(const httplib::Request &req, httplib::Response &res)
{
    Common::RecLock lock(_mutex);

    std::string status;

    DialerApiListenerPtr listener = _listener;
    if (!listener)
    {
        res.status = 500;
        res.set_content("NoListener", "text/plain");
        return;
    }

    if (!listener->getAllCallStatus(status))
    {
        res.status = 500;
        res.set_content("GetAllCallStatusFailed", "text/plain");
        return;
    }

    res.status = 200;
    res.set_content(status, "application/json");
}

void DialerApiServer::processCallStatus(const httplib::Request &req, httplib::Response &res)
{
    Common::RecLock lock(_mutex);

    int sessionId;
    try
    {
        sessionId = std::stoi(req.matches[1]);
        if (sessionId < 0)
            throw std::exception();
    }
    catch (const std::exception &e)
    {
        UTIL_LOGFMT_ERR("DialerApiServer", "content:processCallStatus: exception:%s", e.what());
        res.status = 400;
        res.set_content("InvalidSessionId", "text/plain");
        return;
    }

    std::string status;
    if (!_listener->getCallStatus(sessionId, status))
    {
        res.status = 404;
        res.set_content("CallNotFound", "text/plain");
        return;
    }

    res.status = 200;
    res.set_content(status, "application/json");
}

bool DialerApiServer::decodeHttpUri(const std::string &uri, std::string &host, int &port, std::string &path)
{
    try
    {
        std::string parseUri = uri;
        size_t pos = parseUri.find("http://");
        if (pos == std::string::npos)
            return false;

        parseUri = parseUri.substr(pos + 7);

        pos = parseUri.find("/");
        if (pos == std::string::npos)
            return false;

        path = parseUri.substr(pos);
        parseUri = parseUri.substr(0, pos);

        pos = parseUri.find(":");
        if (pos == std::string::npos)
            port = 80;
        else
            port = std::stoi(parseUri.substr(pos + 1));

        host = parseUri.substr(0, pos);
        return true;
    }
    catch (...)
    {
    }

    return false;
}

MpCallSipDialer::MpCallSipDialer(const Common::ApplicationExPtr &application, const SipMpCall::SipLinePtr &sipLine)
    : Service::ServiceManagerI(application)
    , _sipLine(sipLine)
    , _lastSessionId(0)
    , _keepTermedSession(false)
    , _loadTestUpdating(false)
    , _loadTestLastUpdataTicks(Common::getCurTicks() - 20000)
    , _loadTestUpdateInterval(20000)
    , _echo(false)
    , _echoTimeoutSeconds(30)
{
    JsmClient::setLogCallback(log_callback);
}

bool MpCallSipDialer::onActivate()
{
    if (!Service::ServiceManagerI::onActivate())
    {
        UTIL_LOG_ERR("MpCallSipDialer", "content:service manager activate failed.");
        return false;
    }

    if (!_sipLine)
    {
        _sipLine = SipMpCall::SipLine::create(this, _application.get());
        if (!_sipLine)
        {
            UTIL_LOG_ERR("MpCallSipDialer", "content:create sip line failed.");
            return false;
        }
    }

    if (!_sipRegLine)
    {
        _sipRegLine = SipMpCall::SipRegLine::create(this, _application.get());
        if (!_sipRegLine)
        {
            UTIL_LOG_ERR("MpCallSipDialer", "content:create sip reg line failed.");
            return false;
        }
    }

    if (!_rtpManager)
    {
        int portMin, portMax;
        if (!_application->getAppConfigAsInt("SipDialer.RtpPorts.UpperBound", portMax) || !_application->getAppConfigAsInt("SipDialer.RtpPorts.LowerBound", portMin))
        {
            portMin = 22000;
            portMax = 22999;
        }
        else
        {
            if (portMin % 2 != 0)
                portMin++;
            if (portMax % 2 == 0)
                portMax--;
        }

        _application->setStatistics("SipDailer.RtpPorts", Common::String::formatString("%d-%d", portMin, portMax));

        _echo = _application->getAppConfigAsInt("SipDialer.EchoMode") != 0;
        if (_echo)
        {
            _echoTimeoutSeconds = _application->getAppConfigAsInt("SipDialer.EchoTimeoutSeconds");
            if (_echoTimeoutSeconds <= 0)
                _echoTimeoutSeconds = 30;
        }

        _rtpManager = SimpleRtpSession::SimpleRtpSessionManager::create(SimpleRtpSession::SimpleRtpSessionManager::PortRange(portMin, portMax), _echo);
        if (!_rtpManager)
        {
            UTIL_LOG_ERR("MpCallSipDialer", "content:create rtp manager failed.");
            return false;
        }
    }

    Common::addLogShieldCall("registerTestAgent.AgentGateway.Gateway", Common::LogWarn);
    Common::addLogShieldCall("heartbeat.AgentGateway.Gateway", Common::LogWarn);

    return true;
}

void MpCallSipDialer::onDeactivate()
{
    for (auto &kv : _sessions)
        kv.second->close();
    _sessions.clear();

    for (auto &kv : _b2bSessions)
        kv.second->close();
    _b2bSessions.clear();

    _rtpManager = 0;

    SipMpCall::SipLinePtr sipLine = _sipLine;
    _sipLine = 0;
    if (sipLine)
        sipLine->close();

    SipMpCall::SipRegLinePtr sipRegLine = _sipRegLine;
    _sipRegLine = 0;
    if (sipRegLine)
        sipRegLine->close();

    if (_apiServer)
    {
        _apiServer->close();
        _apiServer = 0;
    }

    Service::ServiceManagerI::onDeactivate();
}

void MpCallSipDialer::onShutdown()
{
    for (auto &kv : _sessions)
        kv.second->close();
    _sessions.clear();

    for (auto &kv : _b2bSessions)
        kv.second->close();
    _b2bSessions.clear();

    _rtpManager = 0;

    SipMpCall::SipLinePtr sipLine = _sipLine;
    _sipLine = 0;
    if (sipLine)
        sipLine->close();

    SipMpCall::SipRegLinePtr sipRegLine = _sipRegLine;
    _sipRegLine = 0;
    if (sipRegLine)
        sipRegLine->close();

    if (_apiServer)
    {
        _apiServer->close();
        _apiServer = 0;
    }

    Service::ServiceManagerI::onShutdown();
}

void MpCallSipDialer::onSchd()
{
    Service::ServiceManagerI::onSchd();

    SipMpCall::SipLinePtr sipLine = _sipLine;
    if (sipLine)
        sipLine->schd();

    SipMpCall::SipRegLinePtr sipRegLine = _sipRegLine;
    if (sipRegLine)
        sipRegLine->schd();
}

void MpCallSipDialer::onUpdateConfigs()
{
    Service::ServiceManagerI::onUpdateConfigs();

    SipMpCall::SipLinePtr sipLine = _sipLine;
    if (sipLine)
        sipLine->updateConfigs();

    SipMpCall::SipRegLinePtr sipRegLine = _sipRegLine;
    if (sipRegLine)
        sipRegLine->updateConfigs();

    Common::RecLock lock(_mutex);

    _statsTool.reset();
    for (auto it = _sessions.begin(); it != _sessions.end();)
    {
        if (it->second->isTermed())
        {
            if (it->second->inOutgoingCall())
                _apiServer->terminated(it->first, it->second->isError(), it->second->termedReason());

            _termedSessions[it->first] = it->second;
            it = _sessions.erase(it);
        }
        else
        {
            _statsTool.add(it->second->getState(), it->first);
            it++;
        }
    }

    for (auto it = _b2bSessions.begin(); it != _b2bSessions.end();)
    {
        if (it->second->isTermed())
            it = _b2bSessions.erase(it);
        else
            it++;
    }

    for (auto &kv : _termedSessions)
        _statsTool.add(kv.second->getState(), kv.first);

    if (!_keepTermedSession)
        _termedSessions.clear();
    setKeepTermedStatus(_keepTermedSession);

    for (auto &kv : _statsTool.getStats())
    {
        UTIL_LOGFMT_DBG("MpCallSipDialer", "content:onUpdateConfigs: SipDailer.Session.%s: %s", kv.first.c_str(), kv.second.c_str());
        _application->setStatistics(Common::String("SipDailer.Session.") + kv.first.c_str(), kv.second.c_str());
    }

    __updateTestAgent();
}

bool MpCallSipDialer::isMainServiceReady(Common::String &reason)
{
    SipMpCall::SipLinePtr sipLine = _sipLine;
    if (!sipLine)
    {
        reason = "InvalidSipLine";
        return false;
    }

    SipMpCall::SipRegLinePtr sipRegLine = _sipRegLine;
    if (!sipRegLine)
    {
        reason = "InvalidSipRegLine";
        return false;
    }

    if (!_apiServer)
    {
        _apiServer = new DialerApiServer(_application, this);
        if (!_apiServer->open(reason))
        {
            if (!reason.empty())
                UTIL_LOG_WRN("MpCallSipDialer", "content:open token dialer failed:" + reason);
            _apiServer = 0;
            return false;
        }
    }

    sipLine->isReady(reason);
    sipRegLine->isReady(reason);
    return true;
}

SipMpCall::SipLineSessionListenerPtr MpCallSipDialer::onCreateSession(const SipMpCall::SipLineSessionPtr &session)
{
    try
    {
        Common::RecLock lock(_mutex);

        int sessId = __genSessId();
        std::string dsipname, uri;
        session->GetCalledUri(dsipname, uri);
        UTIL_LOGFMT_IFO("MpCallSipDialer", "content:onCreateSession: uri:%s, user:%s", uri.c_str(), dsipname.c_str());
        if (_sipRegLine->isRegistered(dsipname))
        {
            DialerB2bSessionPtr dialerB2bSession = new DialerB2bSession(_sipRegLine, session, sessId, _echo ? _echoTimeoutSeconds : 0);
            _b2bSessions[sessId] = dialerB2bSession;
            UTIL_LOGFMT_IFO("MpCallSipDialer", "content:onCreateSession: dialerB2bSession: %d", sessId);
            return dialerB2bSession;
        }
        DialerSipSessionPtr dialerSession = new DialerSipSession(_rtpManager, session, sessId, _echo ? _echoTimeoutSeconds : 0);
        _sessions[sessId] = dialerSession;
        return dialerSession;
    }
    catch (const std::exception &e)
    {
        UTIL_LOGFMT_ERR("MpCallSipDialer", "content:onCreateSession: exception:%s", e.what());
        return 0;
    }
}

SipMpCall::SipRegLineSessionListenerPtr MpCallSipDialer::onCreateSession(const SipMpCall::SipRegLineSessionPtr &session)
{
    try
    {
        Common::RecLock lock(_mutex);

        int sessId = __genSessId();
        DialerB2bSessionPtr dialerB2bSession = new DialerB2bSession(_sipRegLine, session, sessId, _echo ? _echoTimeoutSeconds : 0);
        _b2bSessions[sessId] = dialerB2bSession;
        return dialerB2bSession;
    }
    catch(const std::exception& e)
    {
        UTIL_LOGFMT_ERR("MpCallSipDialer", "content:onCreateSession: exception:%s", e.what());
        return 0;
    }
}

bool MpCallSipDialer::getConfig(const std::string &key, std::string &value)
{
    Common::RecLock lock(_mutex);

    if (key == "KeepTermedSession")
    {
        value = _keepTermedSession ? "1" : "0";
        return true;
    }
    else if (key == "EarlyMedia")
    {
        value = _application->getAppConfigAsInt("SipCall.EarlyMedia") ? "1" : "0";
        return true;
    }
    else if (key == "Precondition")
    {
        value = _application->getAppConfigAsInt("SipCall.Precondition") ? "1" : "0";
        return true;
    }
    else if (key == "CallerUriFormat")
    {
        value = std::to_string(_application->getAppConfigAsInt("SipCall.CallerUriFormat"));
        return true;
    }
    else if (key == "CalleeUriFormat")
    {
        value = std::to_string(_application->getAppConfigAsInt("SipCall.CalleeUriFormat"));
        return true;
    }
    else if (key == "KeepAlive")
    {
        value = _application->getAppConfig("SipCall.KeepAlive").c_str();
        if (value.empty())
            value = "OFF";
        return true;
    }

    UTIL_LOGFMT_ERR("MpCallSipDialer", "content:getConfig: unknown key: %s", key.c_str());
    return false;
}

bool MpCallSipDialer::setConfig(const std::string &key, const std::string &value)
{
    Common::RecLock lock(_mutex);

    if (key == "KeepTermedSession")
    {
        _keepTermedSession = (value == "1");
        UTIL_LOGFMT_IFO("MpCallSipDialer", "content:setConfig: KeepTermedSession: %s", _keepTermedSession ? "true" : "false");
        return true;
    }
    else if (key == "EarlyMedia")
    {
        _application->setConfig("global.SipCall.EarlyMedia", Common::String(value.c_str()));
        UTIL_LOGFMT_IFO("MpCallSipDialer", "content:setConfig: EarlyMedia: %s", value.c_str());
        resetSipLine();
        return true;
    }
    else if (key == "Precondition")
    {
        _application->setConfig("global.SipCall.Precondition", Common::String(value.c_str()));
        UTIL_LOGFMT_IFO("MpCallSipDialer", "content:setConfig: Precondition: %s", value.c_str());
        resetSipLine();
        return true;
    }
    else if (key == "CallerUriFormat")
    {
        _application->setConfig("global.SipCall.CallerUriFormat", Common::String(value.c_str()));
        UTIL_LOGFMT_IFO("MpCallSipDialer", "content:setConfig: CallerUriFormat: %s", value.c_str());
        resetSipLine();
        return true;
    }
    else if (key == "CalleeUriFormat")
    {
        _application->setConfig("global.SipCall.CalleeUriFormat", Common::String(value.c_str()));
        UTIL_LOGFMT_IFO("MpCallSipDialer", "content:setConfig: CalleeUriFormat: %s", value.c_str());
        resetSipLine();
        return true;
    }
    else if (key == "KeepAlive")
    {
        _application->setConfig("global.SipCall.KeepAlive", Common::String(value.c_str()));
        UTIL_LOGFMT_IFO("MpCallSipDialer", "content:setConfig: KeepAlive: %s", value.c_str());
        resetSipLine();
        return true;
    }

    UTIL_LOGFMT_ERR("MpCallSipDialer", "content:setConfig: unknown key: %s", key.c_str());
    return false;
}

bool MpCallSipDialer::call(const std::string &caller, const std::string &callee, const std::string &uui, bool video, bool is3pcc, int timeoutSeconds, int &sessionId, std::string &callId)
{
    Common::RecLock lock(_mutex);

    sessionId = __genSessId();
    Common::Handle<DialerOutgoingSipSession> dialerSession = is3pcc ? new DialerOutgoing3pccSession(_rtpManager, sessionId, timeoutSeconds) : new DialerOutgoingSipSession(_rtpManager, sessionId, timeoutSeconds);
    if (!dialerSession->call(_sipLine, std::to_string(sessionId), caller, callee, uui, video))
    {
        UTIL_LOG_ERR("MpCallSipDialer", "content:call: create outgoing sip session failed.");
        return false;
    }

    UTIL_LOGFMT_IFO("MpCallSipDialer", "content:call: sessId:%d, caller:%s, callee:%s, video:%d, timeout:%d", sessionId, caller.c_str(), callee.c_str(), video, timeoutSeconds);
    _sessions[sessionId] = dialerSession;
    callId = dialerSession->getCallId();
    return true;
}

bool MpCallSipDialer::getAllCallStatus(std::string &status)
{
    Common::RecLock lock(_mutex);

    SipDialer::ResponseAllCallStatus response;
    response.count = _sessions.size() + _termedSessions.size();

    for (auto &kv : _sessions)
    {
        SipDialer::SessionBriefStatus status;
        status.sessionId = kv.first;
        status.state = kv.second->getState().c_str();
        status.direction = kv.second->inOutgoingCall() ? "outgoing" : "incoming";
        status.caller = kv.second->getCaller().c_str();
        status.callee = kv.second->getCallee().c_str();
        status.callId = kv.second->getCallId().c_str();
        status.uui = kv.second->getUui().c_str();
        status.duration = kv.second->getDurationMs();
        status.startTime = Common::getTimeStr("%04d-%02d-%02d %02d:%02d:%02d.%03d", kv.second->getStartTimeMs());
        status.error = kv.second->isError();
        status.reason = kv.second->termedReason().c_str();
        response.sessions.push_back(status);
    }

    for (auto &kv : _termedSessions)
    {
        SipDialer::SessionBriefStatus status;
        status.sessionId = kv.first;
        status.state = kv.second->getState().c_str();
        status.direction = kv.second->inOutgoingCall() ? "outgoing" : "incoming";
        status.caller = kv.second->getCaller().c_str();
        status.callee = kv.second->getCallee().c_str();
        status.callId = kv.second->getCallId().c_str();
        status.uui = kv.second->getUui().c_str();
        status.duration = kv.second->getDurationMs();
        status.startTime = Common::getTimeStr("%04d-%02d-%02d %02d:%02d:%02d.%03d", kv.second->getStartTimeMs());
        status.error = kv.second->isError();
        status.reason = kv.second->termedReason().c_str();
        response.sessions.push_back(status);
    }

    status = ServiceUtil::to_json(response).c_str();
    return true;
}

bool MpCallSipDialer::getCallStatus(int sessionId, std::string &status)
{
    Common::RecLock lock(_mutex);

    auto it = _sessions.find(sessionId);
    if (it == _sessions.end())
    {
        it = _termedSessions.find(sessionId);
        if (it == _termedSessions.end())
        {
            UTIL_LOGFMT_ERR("MpCallSipDialer", "content:getCallStatus: sessionId:%d not found", sessionId);
            return false;
        }
    }

    SipDialer::ResponseStatus statusData = it->second->getStatus();

    std::map<std::string, SipLine::SessStatus> sessStatuses;
    if (_sipLine->getSessStatus(std::to_string(sessionId), sessStatuses))
    {
        SipLine::SessStatus sessStatus = sessStatuses.begin()->second;
        statusData.sipStats.local = sessStatus.localAddr.c_str();
        statusData.sipStats.remote = sessStatus.remoteAddr.c_str();
        for (auto &event : sessStatus.events)
            statusData.sipStats.events.push_back(SipDialer::SipEvent(event.type.c_str(), event.time.c_str(), event.title.c_str(), event.detail.c_str(), event.timestamp));
    }

    status = ServiceUtil::to_json(statusData).c_str();
    return true;
}

void MpCallSipDialer::resetSipLine()
{
    SipMpCall::SipLinePtr sipLine = _sipLine;
    _sipLine = 0;
    if (sipLine)
        sipLine->close();

    _sipLine = SipMpCall::SipLine::create(this, _application.get());
    if (!_sipLine)
        UTIL_LOG_ERR("MpCallSipDialer", "content:create sip line failed.");
}

int MpCallSipDialer::__genSessId()
{
    if (_lastSessionId >= 0x7FFFFFFF)
        _lastSessionId = 0;

    return _lastSessionId++;
}

void MpCallSipDialer::setKeepTermedStatus(bool keepTermedSession)
{
    SipMpCall::SipLinePtr sipLine = _sipLine;
    if (sipLine)
        sipLine->setKeepTermedStatus(keepTermedSession);
}

void MpCallSipDialer::__updateTestAgent()
{
    if (!_application || !_apiServer)
        return;

    if (_loadTestUpdating || Common::getCurTicks() - _loadTestLastUpdataTicks < _loadTestUpdateInterval)
        return;

    _loadTestLastUpdataTicks = Common::getCurTicks();
    _loadTestUpdating = _loadTestServer ? __keepAliveTestAgent() : __registerTestAgent();
}

bool MpCallSipDialer::__registerTestAgent()
{
    class Async : public Common::AgentAsync
    {
    public:
        Async(MpCallSipDialer *dialer)
            : _dialer(dialer)
        {
        }

        void cmdResult(int rslt, const Common::IputStreamPtr &iput, const Common::ObjectPtr &userdata) override
        {
            Common::RecLock lock(_dialer->_mutex);

            _dialer->_loadTestUpdating = false;
            Common::String serverOid;
            if (!Gateway::AgentGatewayAgent::registerTestAgent_end(rslt, iput, serverOid))
            {
                if (Common::ObjectAgent::getRootReason().find("object-error:object not found:#LoadTestServer") >= 0)
                    _dialer->_loadTestUpdateInterval = 24 * 3600 * 1000;
                else
                    _dialer->_loadTestUpdateInterval = 60000;
                UTIL_LOG_WRN("MpCallSipDialer", "content:register test agent failed, reason:" + Common::ObjectAgent::getLogInfo(2));
                return;
            }

            _dialer->_loadTestUpdateInterval = 20000;
            _dialer->_loadTestServer = _dialer->_application->createAgent(serverOid);
            UTIL_LOG_IFO("MpCallSipDialer", "content:register test agent success, serverOid:" + serverOid);
        }

    private:
        Common::Handle<MpCallSipDialer> _dialer;
    };

    Gateway::AgentGatewayAgent agentGateway = _application->createAgent("#LoadTestServer");
    agentGateway.registerTestAgent_begin(new Async(this), Gateway::TestAgentType_toString(Gateway::TestAgentSipDialer), _application->getAppName(), _apiServer->getApiUrl().c_str());
    return true;
}

bool MpCallSipDialer::__keepAliveTestAgent()
{
    class Async : public Common::AgentAsync
    {
    public:
        Async(MpCallSipDialer *dialer)
            : _dialer(dialer)
        {
        }

        void cmdResult(int rslt, const Common::IputStreamPtr &iput, const Common::ObjectPtr &userdata) override
        {
            Common::RecLock lock(_dialer->_mutex);

            _dialer->_loadTestUpdating = false;
            if (!Gateway::AgentGatewayAgent::heartbeat_end(rslt, iput))
            {
                UTIL_LOG_WRN("MpCallSipDialer", "content:heartbeat test agent failed, reason:" + Common::ObjectAgent::getLogInfo(2));
                _dialer->_loadTestServer = 0;
                return;
            }
        }

    private:
        Common::Handle<MpCallSipDialer> _dialer;
    };

    _loadTestServer.heartbeat_begin(new Async(this), _application->getAppName(), Common::StrStrMap());
    return true;
}

} // namespace SipMpCall
