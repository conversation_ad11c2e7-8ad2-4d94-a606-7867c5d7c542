//
// *****************************************************************************
// Copyright (c) 2025 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//

#pragma once

#include "Common/CommonEx.h"
#include "SipLine/SipLine.h"
#include "Service/ServiceI.h"
#include "SipAdapter/SipCallInterface.h"
#include "cpp-httplib/httplib.h"
#include "SimpleRtpSession/SimpleRtpSession.h"
#include <map>
#include "Util/StatsTool.h"
#include "SipDialer/MpCallSipDialerPub.h"
#include "Gateway/AgentGatewayAgent.h"
#include "MpCallSipDialerSession.h"
#include "SipRegLine/SipRegLine.h"
#include "MpCallSipDialerB2bSession.h"

namespace SipMpCall
{

class DialerApiListener : virtual public Common::Shared
{
public:
    /**
     * @brief 获取配置
     *
     * @param key 配置项
     * @param value 配置值
     *
     * @return bool 是否成功, false 表示配置项不存在
     */
    virtual bool getConfig(const std::string &key, std::string &value) = 0;

    /**
     * @brief 设置配置
     *
     * @param key 配置项
     * @param value 配置值
     *
     * @return bool 是否成功, false 表示配置项不存在
     */
    virtual bool setConfig(const std::string &key, const std::string &value) = 0;

    /**
     * @brief 发起呼叫
     *
     * @param caller 主叫号码
     * @param callee 被叫号码
     * @param video 是否支持视频
     * @param is3pcc 是否3pcc
     * @param timeoutSeconds 超时时间
     * @param sessionId 会话ID
     * @param callId 呼叫ID
     *
     * @return bool 是否成功
     */
    virtual bool call(const std::string &caller, const std::string &callee, const std::string &uui, bool video, bool is3pcc, int timeoutSeconds, int &sessionId, std::string &callId) = 0;

    /**
     * @brief 获取所有会话状态
     *
     * @param status 会话状态
     *
     * @return bool 是否成功
     */
    virtual bool getAllCallStatus(std::string &status) = 0;

    /**
     * @brief 获取会话状态
     *
     * @param sessionId 会话ID
     * @param status 会话状态
     *
     * @return bool 是否成功
     */
    virtual bool getCallStatus(int sessionId, std::string &status) = 0;
};

typedef Common::Handle<DialerApiListener> DialerApiListenerPtr;

class DialerApiInterface : virtual public Common::Shared
{
public:
    /**
     * @brief 会话结束
     *
     * @param sessionId 会话ID
     * @param error 是否异常结束
     * @param reason 结束原因
     */
    virtual void terminated(int sessionId, bool error, const std::string &reason) = 0;
};

class DialerApiServer : public Common::Thread, public DialerApiInterface
{
    enum HttpServerState
    {
        HttpServerIdle,
        HttpServerListened,
        HttpServerDone,
        HttpServerError
    };

public:
    DialerApiServer(const Common::ApplicationExPtr application, const DialerApiListenerPtr &listener)
        : _app(application)
        , _httpServerState(HttpServerIdle)
        , _listener(listener)
    {
    }

    bool open(Common::String &failReason);
    void close();
    void onRun() override;
    std::string getApiUrl() const { return _apiUrl; }

    // implement DialerApiInterface
    void terminated(int sessionId, bool error, const std::string &reason) override;

private:
    void processGetConfig(const httplib::Request &req, httplib::Response &res);
    void processSetConfig(const httplib::Request &req, httplib::Response &res);
    void processCall(const httplib::Request &req, httplib::Response &res);
    void processAllCallStatus(const httplib::Request &req, httplib::Response &res);
    void processCallStatus(const httplib::Request &req, httplib::Response &res);
    static bool decodeHttpUri(const std::string &uri, std::string &host, int &port, std::string &path);

private:
    Common::ApplicationExPtr _app;
    DialerApiListenerPtr _listener;
    httplib::Server _httpServer;
    int _httpServerState;

    Common::RecMutex _mutex;
    std::map<int, std::string> _callbacks;
    std::string _apiUrl;
};

typedef Common::Handle<DialerApiServer> DialerApiServerPtr;

class MpCallSipDialer : public Service::ServiceManagerI, public SipMpCall::SipLineListner, public DialerApiListener, public SipMpCall::SipRegLineListner
{
public:
    explicit MpCallSipDialer(const Common::ApplicationExPtr &application, const SipMpCall::SipLinePtr &sipLine = 0);

    bool __ex(const Common::ServerCallPtr &call, const Common::String &cmd, const Common::IputStreamPtr &iput) override { return false; }

    // implement Service::ServiceManagerI
    bool onActivate() override;
    void onDeactivate() override;
    void onShutdown() override;
    void onSchd() override;
    void onUpdateConfigs() override;
    bool isMainServiceReady(Common::String &reason) override;

    // implement SipLineListener
    SipMpCall::SipLineSessionListenerPtr onCreateSession(const SipMpCall::SipLineSessionPtr &session) override;

    // implement SipRegLineListener
    SipMpCall::SipRegLineSessionListenerPtr onCreateSession(const SipMpCall::SipRegLineSessionPtr &session) override;

    // implement DialerApiListener
    bool getConfig(const std::string &key, std::string &value) override;
    bool setConfig(const std::string &key, const std::string &value) override;
    bool call(const std::string &caller, const std::string &callee, const std::string &uui, bool video, bool is3pcc, int timeoutSeconds, int &sessionId, std::string &callId) override;
    bool getAllCallStatus(std::string &status) override;
    bool getCallStatus(int sessionId, std::string &status) override;

private:
    void resetSipLine();
    int __genSessId();
    void setKeepTermedStatus(bool keepTermedSession);

    void __updateTestAgent();
    bool __registerTestAgent();
    bool __keepAliveTestAgent();

private:
    Common::RecMutex _mutex;
    bool _keepTermedSession;

    SipMpCall::SipLinePtr _sipLine;
    SipMpCall::SipRegLinePtr _sipRegLine;
    DialerApiServerPtr _apiServer;

    int _lastSessionId;
    std::map<int, DialerSipSessionPtr> _sessions;
    std::map<int, DialerSipSessionPtr> _termedSessions;
    std::map<int, DialerB2bSessionPtr> _b2bSessions;
    SimpleRtpSession::SimpleRtpSessionManagerPtr _rtpManager;
    StatsTool _statsTool;

    bool _loadTestUpdating;
    unsigned int _loadTestLastUpdataTicks;
    unsigned int _loadTestUpdateInterval;
    Gateway::AgentGatewayAgent _loadTestServer;

    bool _echo;
    int _echoTimeoutSeconds;
};

} // namespace SipMpCall
