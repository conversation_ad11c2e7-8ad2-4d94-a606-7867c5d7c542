//
// *****************************************************************************
// Copyright (c) 2025 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//

#include "MpCallSipDialerB2bSession.h"
#include "SimpleSipSession/SipMessage.h"
#include "SimpleSipSession/SipTypes.h"
#include <string>

namespace SipMpCall
{

static bool hasVideo(const std::string &sdp)
{
    size_t pos = sdp.find("\r\nm=video ");
    if (pos == std::string::npos)
        return false;

    return true;
}

#define CHECK_INVOKE_VOID(_obj, _func, _log)         \
    auto __obj = _obj;                               \
    if (!__obj)                                      \
    {                                                \
        UTIL_LOG_DBG("SipRegLine", "content:" _log); \
        return;                                      \
    }                                                \
    __obj->_func

void OutgoingSessionListener::onCallAlerted(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg)
{
    CHECK_INVOKE_VOID(_session, onCallAlerted(pcSdp, iStatCode, bSuptVideo, pcSipMsg), "content:onCallAlerted: session is null");
}

void OutgoingSessionListener::onCallAnswered(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg)
{
    CHECK_INVOKE_VOID(_session, onCallAnswered(pcSdp, iStatCode, bSuptVideo, pcSipMsg), "content:onCallAnswered: session is null");
}

void OutgoingSessionListener::onCallUpdate(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg)
{
    CHECK_INVOKE_VOID(_session, onCallUpdate(pcSdp, iStatCode, bSuptVideo, pcSipMsg), "content:onCallUpdate: session is null");
}

void OutgoingSessionListener::onCallRequestUpdate(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg)
{
    CHECK_INVOKE_VOID(_session, onCallRequestUpdate(pcSdp, iStatCode, bSuptVideo, pcSipMsg), "content:onCallRequestUpdate: session is null");
}

void OutgoingSessionListener::onCallResponseUdate(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg)
{
    CHECK_INVOKE_VOID(_session, onCallResponseUdate(pcSdp, iStatCode, bSuptVideo, pcSipMsg), "content:onCallResponseUdate: session is null");
}

void OutgoingSessionListener::onCallRequestModify(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg)
{
    CHECK_INVOKE_VOID(_session, onOutgoingCallRequestModify(pcSdp, iStatCode, bSuptVideo, pcSipMsg), "content:onOutgoingCallRequestModify: session is null");
}

void OutgoingSessionListener::onCallResponseModify(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg)
{
    CHECK_INVOKE_VOID(_session, onOutgoingCallResponseModify(pcSdp, iStatCode, bSuptVideo, pcSipMsg), "content:onOutgoingCallResponseModify: session is null");
}

void OutgoingSessionListener::onCallTerminated(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg)
{
    CHECK_INVOKE_VOID(_session, onOutgoingCallTerminated(pcSdp, iStatCode, bSuptVideo, pcSipMsg), "content:onOutgoingCallTerminated: session is null");
    _session = 0;
}

#undef CHECK_INVOKE_VOID

void DialerB2bSession::close()
{
    Common::RecLock lock(this);

    setState(StateTermed);

    if (_outgoingSess)
    {
        _outgoingSess->SipTerm();
        _outgoingSess = 0;
    }

    if (_incomingSess)
    {
        _incomingSess->SipTerm();
        _incomingSess = 0;
    }

    if (_outgoingListener)
        _outgoingListener = 0;

    _regLine = 0;

    // if (_sipSess)
    // {
    //     _sipSess->SipTerm();
    //     _sipSess = 0;
    // }

    // if (_rtpSession)
    // {
    //     SimpleRtpSession::RtpSessionStats rtpStats;
    //     _rtpSession->getStats(rtpStats);
    //     _status.rtpStats = rtpStats;
    //     _rtpSession->close();
    //     _rtpSession = 0;
    // }

    // _rtpManager = 0;
}

bool DialerB2bSession::isTermed()
{
    Common::RecLock lock(this);

    // if (_state == StateTermed || !_sipSess)
    //     return true;

    if (__checkTimeout())
    {
        UTIL_LOG_IFO("DialerB2bSession", "content:isTermed timeout");
        close();
        return true;
    }

    return false;
}

std::string DialerB2bSession::getCaller()
{
    Common::RecLock lock(this);
    return _caller;
}

std::string DialerB2bSession::getCallee()
{
    Common::RecLock lock(this);
    return _callee;
}

Common::Long DialerB2bSession::getDurationMs()
{
    Common::RecLock lock(this);

    if (_termedTimeMs > 0)
        return _termedTimeMs - _startTimeMs;

    return Common::getCurTimeMs() - _startTimeMs;
}

SipDialer::ResponseStatus DialerB2bSession::getStatus()
{
    Common::RecLock lock(this);
    return SipDialer::ResponseStatus();

    // _status.sessionId = _sessId;
    // _status.state = to_string(_state).c_str();
    // _status.startTime = Common::getTimeStr("%04d%02d%02d-%02d:%02d:%02d.%03d", _startTimeMs);
    // _status.duration = Common::getCurTimeMs() - _startTimeMs;
    // _status.direction = inOutgoingCall() ? "outgoing" : "incoming";
    // _status.caller = _caller.c_str();
    // _status.callee = _callee.c_str();

    // if (_rtpSession)
    // {
    //     SimpleRtpSession::RtpSessionStats rtpStats;
    //     _rtpSession->getStats(rtpStats);
    //     _status.rtpStats = rtpStats;
    // }

    // return _status;
}

void DialerB2bSession::onCallIncoming(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg)
{
    Common::RecLock lock(this);

    std::string name, uri;
    _incomingSess->GetPeerUri(name, uri);
    _caller = name;
    _incomingSess->GetCalledUri(name, uri);
    _callee = name;

    _logContext->setContext("sessId", Common::String(_sessId), Common::LogContext::LogMaskKeep);
    _logContext->setContext("caller", _caller.c_str(), Common::LogContext::LogMaskKeep);
    _logContext->setContext("callee", _callee.c_str(), Common::LogContext::LogMaskKeep);
    _logContext->setContext("video", bSuptVideo ? "true" : "false");

    setState(StateCalling);
    _outgoingListener = new OutgoingSessionListener(this);
    _outgoingSess = _regLine->SipCall(_outgoingListener, std::to_string(_sessId), _caller, _callee, pcSdp, std::map<std::string, std::string>());
    if (!_outgoingSess)
    {
        UTIL_LOG_CONTEXT_DBG("DialerB2bSession", _logContext, "content:onCallIncoming: callout failed");
        _incomingSess->SipTerm();
        _incomingSess = 0;
        close();
        return;
    }

    _incomingSess->setSessId(std::to_string(_sessId));
    UTIL_LOG_CONTEXT_IFO("DialerB2bSession", _logContext, "content:onCallIncoming");
}

void DialerB2bSession::onCallAlerted(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg)
{
    _incomingSess->SipAlert(pcSdp);
    UTIL_LOGFMT_CONTEXT_IFO("DialerB2bSession", _logContext, "content:onCallAlerted: iStatCode: %d, bSuptVideo: %d", iStatCode, bSuptVideo);
}

void DialerB2bSession::onCallAnswered(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg)
{
    UTIL_LOGFMT_CONTEXT_IFO("DialerB2bSession", _logContext, "content:onCallAnswered: iStatCode: %d, bSuptVideo: %d", iStatCode, bSuptVideo);

    Common::RecLock lock(this);
    setState(StateConnected);

    _incomingSess->SipAnswer(pcSdp, SipClient::SipCallExtHdrs());
}

void DialerB2bSession::onCallUpdate(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg)
{
    UTIL_LOGFMT_CONTEXT_IFO("DialerB2bSession", _logContext, "content:onCallUpdate: iStatCode: %d, bSuptVideo: %d", iStatCode, bSuptVideo);
}

void DialerB2bSession::onCallRequestUpdate(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg)
{
    UTIL_LOGFMT_CONTEXT_IFO("DialerB2bSession", _logContext, "content:onCallRequestUpdate: iStatCode: %d, bSuptVideo: %d", iStatCode, bSuptVideo);
}

void DialerB2bSession::onCallResponseUdate(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg)
{
    UTIL_LOGFMT_CONTEXT_IFO("DialerB2bSession", _logContext, "content:onCallResponseUdate: iStatCode: %d, bSuptVideo: %d", iStatCode, bSuptVideo);
}

void DialerB2bSession::onCallConnected(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg)
{
    UTIL_LOGFMT_CONTEXT_IFO("DialerB2bSession", _logContext, "content:onCallConnected: iStatCode: %d, bSuptVideo: %d", iStatCode, bSuptVideo);
    setState(StateConnected);
    _outgoingSess->SipAck(pcSdp);
}

void DialerB2bSession::onCallRequestModify(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg)
{
    UTIL_LOGFMT_CONTEXT_IFO("DialerB2bSession", _logContext, "content:onCallRequestModify: iStatCode: %d, bSuptVideo: %d", iStatCode, bSuptVideo);

    Common::RecLock lock(this);
    if (!_outgoingSess->SipUpdate(pcSdp))
    {
        UTIL_LOG_CONTEXT_WRN("DialerB2bSession", _logContext, "content:onIncomingCallRequestModify: update outgoing session failed");
        return;
    }
}

void DialerB2bSession::onCallResponseModify(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg)
{
    UTIL_LOGFMT_CONTEXT_IFO("DialerB2bSession", _logContext, "content:onIncomingCallResponseModify: iStatCode: %d, bSuptVideo: %d", iStatCode, bSuptVideo);

    Common::RecLock lock(this);
    if (!_outgoingSess->SipUpdateRsp(pcSdp))
    {
        UTIL_LOG_CONTEXT_WRN("DialerB2bSession", _logContext, "content:onIncomingCallResponseModify: update outgoing session failed");
        return;
    }
}

void DialerB2bSession::onCallTerminated(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg)
{
    Common::RecLock lock(this);

    if (!_incomingSess)
    {
        UTIL_LOG_CONTEXT_DBG("DialerB2bSession", _logContext, "content:onCallTerminated: incoming session is not exist");
        return;
    }

    if (!_outgoingSess)
    {
        UTIL_LOG_CONTEXT_DBG("DialerB2bSession", _logContext, "content:onCallTerminated: outgoing session is not exist");
        return;
    }

    std::string phase, reason;
    _incomingSess->GetCallTermedReason(phase, reason);
    setState(StateTermed);

    _outgoingSess->SipTerm();
    _outgoingSess = 0;
    _incomingSess = 0;
    _outgoingListener = 0;
    close();
    UTIL_LOGFMT_CONTEXT_IFO("DialerB2bSession", _logContext, "content:onCallTerminated: iStatCode: %d, bSuptVideo: %d, phase: %s, reason: %s", iStatCode, bSuptVideo, phase.c_str(), reason.c_str());
}

void DialerB2bSession::onOutgoingCallTerminated(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg)
{
    Common::RecLock lock(this);

    if (!_outgoingSess)
    {
        UTIL_LOG_CONTEXT_DBG("DialerB2bSession", _logContext, "content:onOutgoingCallTerminated: outgoing session is not exist");
        return;
    }

    if (!_incomingSess)
    {
        UTIL_LOG_CONTEXT_DBG("DialerB2bSession", _logContext, "content:onOutgoingCallTerminated: incoming session is not exist");
        return;
    }

    std::string phase, reason;
    _outgoingSess->GetCallTermedReason(phase, reason);
    setState(StateTermed);

    _incomingSess->SipTerm();
    _incomingSess = 0;
    _outgoingSess = 0;
    _outgoingListener = 0;
    close();
    UTIL_LOGFMT_CONTEXT_IFO("DialerB2bSession", _logContext, "content:onOutgoingCallTerminated: iStatCode: %d, bSuptVideo: %d, phase: %s, reason: %s", iStatCode, bSuptVideo, phase.c_str(), reason.c_str());
}

void DialerB2bSession::onOutgoingCallRequestModify(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg)
{
    UTIL_LOGFMT_CONTEXT_IFO("DialerB2bSession", _logContext, "content:onOutgoingCallRequestModify: iStatCode: %d, bSuptVideo: %d", iStatCode, bSuptVideo);

    Common::RecLock lock(this);
    if (!_incomingSess->SipUpdate(pcSdp))
    {
        UTIL_LOG_CONTEXT_WRN("DialerB2bSession", _logContext, "content:onOutgoingCallRequestModify: update incoming session failed");
        return;
    }
}

void DialerB2bSession::onOutgoingCallResponseModify(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg)
{
    UTIL_LOGFMT_CONTEXT_IFO("DialerB2bSession", _logContext, "content:onOutgoingCallResponseModify: iStatCode: %d, bSuptVideo: %d", iStatCode, bSuptVideo);

    Common::RecLock lock(this);
    if (!_incomingSess->SipUpdateRsp(pcSdp))
    {
        UTIL_LOG_CONTEXT_WRN("DialerB2bSession", _logContext, "content:onOutgoingCallResponseModify: update incoming session failed");
        return;
    }
}

std::string DialerB2bSession::to_string(enum State state)
{
    switch (state)
    {
    case StateIdle:
        return "Idle";
    case StateCalling:
        return "Calling";
    case StateConnected:
        return "Connected";
    case StateTermed:
        return "Termed";
    }

    return "Unknown";
}

void DialerB2bSession::setState(enum State state)
{
    Common::RecLock lock(this);
    if (_state == state)
        return;

    if (state == StateTermed)
        _termedTimeMs = Common::getCurTimeMs();

    UTIL_LOGFMT_CONTEXT_IFO("DialerB2bSession", _logContext, "content:setState: state:%s->%s", to_string(_state).c_str(), to_string(state).c_str());
    _state = state;
}

bool DialerB2bSession::__checkTimeout()
{
    return _timeoutMs > 0 && Common::getCurTimeMs() - _startTimeMs >= _timeoutMs;
}

} // namespace SipMpCall
