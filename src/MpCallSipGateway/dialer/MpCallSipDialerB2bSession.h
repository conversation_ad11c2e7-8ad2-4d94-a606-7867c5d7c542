//
// *****************************************************************************
// Copyright (c) 2025 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//

#pragma once

#include "Common/Util.h"
#include "SipLine/SipLineBase.h"
#include "SipDialer/MpCallSipDialerPub.h"
#include "SipRegLine/SipRegLine.h"

namespace SipMpCall
{
class DialerB2bSession;
typedef Common::Handle<DialerB2bSession> DialerB2bSessionPtr;

class OutgoingSessionListener : public SipLineSessionBaseListener
{
public:
    OutgoingSessionListener(const DialerB2bSessionPtr &session)
        : _session(session)
    {
    }

    virtual void onCallIncoming(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) override {}
    virtual void onCallAlerted(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) override;
    virtual void onCallAnswered(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) override;
    virtual void onCallUpdate(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) override;
    virtual void onCallRequestUpdate(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) override;
    virtual void onCallResponseUdate(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) override;
    virtual void onCallConnected(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) override {}
    virtual void onCallRequestModify(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) override;
    virtual void onCallResponseModify(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) override;
    virtual void onCallTerminated(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) override;

private:
    DialerB2bSessionPtr _session;
};

typedef Common::Handle<OutgoingSessionListener> OutgoingSessionListenerPtr;

class DialerB2bSession : public SipMpCall::SipLineSessionBaseListener, public Common::RecMutex
{
protected:
    enum State
    {
        StateIdle,
        StateCalling,
        StateConnected,
        StateTermed
    };

public:
    DialerB2bSession(const SipRegLinePtr &regLine, const SipLineSessionBasePtr &incomingSess, int sessId, int timeoutSeconds = 0)
        : _state(StateIdle)
        , _error(false)
        , _regLine(regLine)
        , _incomingSess(incomingSess)
        , _sessId(sessId)
        , _startTimeMs(Common::getCurTimeMs())
        , _termedTimeMs(0)
        , _timeoutMs(timeoutSeconds * 1000)
        , _logContext(Common::LogContext::create())
    {
    }

    void close();
    virtual bool inOutgoingCall() { return false; }
    virtual bool isTermed();
    std::string getCaller();
    std::string getCallee();
    Common::Long getStartTimeMs() { return _startTimeMs; }
    Common::Long getDurationMs();
    bool isError() { return _error; }
    std::string getState() { return to_string(_state); }
    const std::string &termedReason() { return _termedReason; }
    SipDialer::ResponseStatus getStatus();

    // implement SipLineSessionBaseListener
    virtual void onCallIncoming(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) override;
    virtual void onCallAlerted(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) override;
    virtual void onCallAnswered(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) override;
    virtual void onCallUpdate(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) override;
    virtual void onCallRequestUpdate(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) override;
    virtual void onCallResponseUdate(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) override;
    virtual void onCallConnected(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) override;
    virtual void onCallRequestModify(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) override;
    virtual void onCallResponseModify(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) override;
    virtual void onCallTerminated(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) override;

    void onOutgoingCallTerminated(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg);
    void onOutgoingCallRequestModify(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg);
    void onOutgoingCallResponseModify(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg);

protected:
    static std::string to_string(enum State state);
    void setState(enum State state);
    virtual bool __checkTimeout();

protected:
    const int _sessId;
    SipRegLinePtr _regLine;
    Common::Long _startTimeMs;
    Common::Long _termedTimeMs;
    Common::Long _timeoutMs;
    std::string _caller;
    std::string _callee;
    enum State _state;
    bool _error;
    std::string _termedReason;
    SipMpCall::SipLineSessionBasePtr _incomingSess;
    SipMpCall::SipLineSessionBasePtr _outgoingSess;
    Common::LogContextPtr _logContext;
    OutgoingSessionListenerPtr _outgoingListener;
};

typedef Common::Handle<DialerB2bSession> DialerB2bSessionPtr;

} // namespace SipMpCall
