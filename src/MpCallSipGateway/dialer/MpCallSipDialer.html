<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SIP 拨测工具</title>
    <link rel="stylesheet" href="MpCallSipDialer.css">
</head>

<body>
    <h1>SIP 拨测工具</h1>

    <div id="config">
        <h2>配置</h2>
        <div class="warning-message">
            注意：修改配置将重置SIP线路，在通话中修改将导致通话异常。
        </div>
        <div>
            <label for="EarlyMedia">开启支持早期媒体:</label>
            <select id="EarlyMedia" name="EarlyMedia">
                <option value="0">否</option>
                <option value="1">是</option>
            </select>
        </div>
        <div>
            <label for="Precondition">开启支持资源预留:</label>
            <select id="Precondition" name="Precondition">
                <option value="0">否</option>
                <option value="1">是</option>
            </select>
        </div>
        <div>
            <label for="CallerUriFormat">主叫URI格式:</label>
            <select id="CallerUriFormat" name="CallerUriFormat">
                <option value="0">SIP</option>
                <option value="1">SIPPHONE</option>
                <option value="2">TEL</option>
            </select>
        </div>
        <div>
            <label for="CalleeUriFormat">被叫URI格式:</label>
            <select id="CalleeUriFormat" name="CalleeUriFormat">
                <option value="0">SIP</option>
                <option value="1">SIPPHONE</option>
                <option value="2">TEL</option>
            </select>
        </div>
        <div>
            <label for="KeepAlive">SIP线路保活:</label>
            <select id="KeepAlive" name="KeepAlive">
                <option value="OFF">关闭</option>
                <option value="OPTIONS:30">OPTIONS 间隔30秒</option>
                <option value="OPTIONS:60">OPTIONS 间隔60秒</option>
                <option value="OPTIONS:300">OPTIONS 间隔5分钟</option>
                <option value="OPTIONS:600">OPTIONS 间隔10分钟</option>
                <option value="OPTIONS:1800">OPTIONS 间隔30分钟</option>
            </select>
        </div>
    </div>

    <div id="callForm">
        <h2>拨打电话</h2>
        <form id="dialForm">
            <div>
                <label for="caller">主叫号码:</label>
                <input type="text" id="caller" name="caller" required>
            </div>
            <div>
                <label for="callee">被叫号码:</label>
                <input type="text" id="callee" name="callee" required>
            </div>
            <div>
                <label for="uui">随路数据:</label>
                <input type="text" id="uui" name="uui">
            </div>
            <div>
                <label for="timeout">超时时间(秒):</label>
                <input type="number" id="timeout" name="timeout" value="30" min="1" required>
            </div>
            <div>
                <label for="video">视频通话:</label>
                <input type="checkbox" id="video" name="video">
            </div>
            <div>
                <label for="is3pcc">第三方呼叫控制（第一个INVITE不携带SDP）:</label>
                <input type="checkbox" id="is3pcc" name="is3pcc">
            </div>
            <button type="submit">发起呼叫</button>
        </form>
        <div id="callResult"></div>
    </div>

    <div id="callList">
        <h2>活跃的会话</h2>
        <div>
            <label for="KeepTermedSession">保持结束会话的信息:</label>
            <select id="KeepTermedSession" name="KeepTermedSession">
                <option value="0">否</option>
                <option value="1">是</option>
            </select>
        </div>

        <button id="refreshCallList">刷新列表</button>
        <div id="callListContent"></div>
    </div>

    <div id="callDetails">
        <h2>会话详情</h2>
        <div id="callDetailsContent">
            <p>选择一个会话查看详情</p>
        </div>
    </div>

    <script>
        let apiBaseUrl = window.location.protocol + '//' + window.location.host;
        let selectedSessionId = null;

        function showSipMessageDetail(message) {
            console.log('显示SIP消息详情, ID:', message.id);

            const detailPanel = document.getElementById('sip-message-detail-panel');
            const title = document.getElementById('detail-message-title');
            const content = document.getElementById('detail-message-content');

            title.textContent = `${message.method || message.type || 'SIP消息'} (${message.direction === 'outgoing' ? '发送' : '接收'})`;
            content.textContent = message.content || 'No content available';
            detailPanel.style.display = 'block';
        }

        function loadConfig(configName) {
            fetch('/api/v1/config/' + configName)
                .then(response => {
                    if (!response.ok) {
                        throw new Error('获取配置失败, 状态码: ' + response.status);
                    }
                    return response.text();
                })
                .then(value => {
                    const select = document.getElementById(configName);
                    select.value = value;
                })
                .catch(error => {
                    console.error('获取配置失败:', error);
                });
        }

        function setupConfigChangeListener(configName) {
            const element = document.getElementById(configName);
            element.addEventListener('change', function () {
                const value = this.value;

                fetch('/api/v1/config/' + configName, {
                method: 'POST',
                headers: {
                    'Content-Type': 'text/plain'
                },
                body: value
            })
                .then(response => {
                    if (!response.ok) {
                            throw new Error('配置更新失败, 状态码: ' + response.status);
                    }
                    return response.text();
                })
                .then(result => {
                        console.log('配置更新成功: ' + configName);
                })
                .catch(error => {
                        console.error('配置更新失败:', error);
                        // Revert select state on error
                        loadConfig(configName);
                });
        });
        }

        // Make a call
        document.getElementById('dialForm').addEventListener('submit', function (e) {
            e.preventDefault();

            const callData = {
                caller: document.getElementById('caller').value,
                callee: document.getElementById('callee').value,
                uui: document.getElementById('uui').value,
                timeout: parseInt(document.getElementById('timeout').value),
                video: document.getElementById('video').checked,
                is3pcc: document.getElementById('is3pcc').checked
            };

            fetch('/api/v1/call', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(callData)
            })
                .then(response => {
                    if (!response.ok) {
                        throw new Error('呼叫失败, 状态码: ' + response.status);
                    }
                    return response.json();
                })
                .then(data => {
                    document.getElementById('callResult').innerHTML =
                        `<p>呼叫已发起! 会话ID: ${data.sessionId}, Call-ID: ${data.callId}</p>`;
                    refreshCallList();
                })
                .catch(error => {
                    document.getElementById('callResult').innerHTML =
                        `<p style="color: red">Error: ${error.message}</p>`;
                });
        });

        // Get all calls
        function refreshCallList() {
            fetch('/api/v1/call/status')
                .then(response => {
                    if (!response.ok) {
                        throw new Error('获取会话列表失败, 状态码: ' + response.status);
                    }
                    return response.json();
                })
                .then(data => {
                    const listContent = document.getElementById('callListContent');
                    if (data.count === 0)
                    {
                        listContent.innerHTML = '<p>没有活跃的会话</p>';
                        return;
                    }

                    let html = '<table border="1"><tr><th>会话ID</th><th>开始时间</th><th>方向</th><th>主叫</th><th>被叫</th><th>Call-ID</th><th>随路数据</th><th>状态</th><th>时长(秒)</th><th>错误</th><th>原因</th><th>操作</th></tr>';
                    data.sessions.forEach(session => {
                        html += `<tr>
                        <td>${session.sessionId}</td>
                        <td>${session.startTime}</td>
                        <td>${session.direction}</td>
                        <td>${session.caller}</td>
                        <td>${session.callee}</td>
                        <td>${session.callId}</td>
                        <td>${session.uui}</td>
                        <td>${session.state}</td>
                        <td>${session.duration / 1000}</td>
                        <td>${session.error ? 'Yes' : 'No'}</td>
                        <td>${session.reason}</td>
                        <td><button onclick="getCallDetails(${session.sessionId})">查看详情</button></td>
                    </tr>`;
                    });
                    html += '</table>';
                    listContent.innerHTML = html;
                })
                .catch(error => {
                    document.getElementById('callListContent').innerHTML =
                        `<p style="color: red">获取会话列表失败: ${error.message}</p>`;
                });
        }

        // Get call details
        function getCallDetails(sessionId) {
            fetch(`/api/v1/call/${sessionId}/status`)
                .then(response => {
                    if (!response.ok) {
                        throw new Error('获取会话详情失败, 状态码: ' + response.status);
                    }
                    return response.json();
                })
                .then(data => {
                    const detailsContent = document.getElementById('callDetailsContent');
                    let html = `<h3>会话ID: ${data.sessionId}</h3>`;
                    html += `<p>状态: ${data.state}</p>`;

                    html += '<h4>SIP消息流程图</h4>';
                    html += '<div id="sip-flow-container" style="display: flex; flex-direction: row; margin-bottom: 20px;">';
                    html += '<div id="sip-flow" style="flex: 1;"></div>';
                    html += '<div id="sip-message-detail-panel" style="flex: 1; margin-left: 20px; display: none;">';
                    html += '<h5 id="detail-message-title">SIP消息详情</h5>';
                    html += '<pre id="detail-message-content" style="white-space: pre-wrap; overflow-x: auto; border: 1px solid #ccc; padding: 10px; background-color: #f9f9f9;"></pre>';
                    html += '</div></div>';

                    if (data.rtpStats) {
                        html += '<h4>RTP 统计</h4>';
                        html += '<div style="display: flex; gap: 20px;">';

                        html += '<div>';
                        html += '<table border="1">';
                        html += '<tr><th></th><th colspan="2">音频</th><th colspan="2">视频</th></tr>';
                        html += '<tr><th></th><th>当前</th><th>上一次</th><th>当前</th><th>上一次</th></tr>';
                        html += `<tr><td>Payload Type</td><td>${data.rtpStats.audio.payloadType}</td><td>${data.rtpStats.audio.lastPayloadType}</td><td>${data.rtpStats.video.payloadType}</td><td>${data.rtpStats.video.lastPayloadType}</td></tr>`;

                        // Current RTP
                        html += '<tr><th>RTP</th><th>当前</th><th>总计</th><th>当前</th><th>总计</th></tr>';
                        html += `<tr><td>包数量</td><td>${data.rtpStats.audio.currentRtp.count}</td><td>${data.rtpStats.audio.overallRtp.count}</td><td>${data.rtpStats.video.currentRtp.count}</td><td>${data.rtpStats.video.overallRtp.count}</td></tr>`;
                        html += `<tr><td>字节数量</td><td>${formatBytes(data.rtpStats.audio.currentRtp.bytes)}</td><td>${formatBytes(data.rtpStats.audio.overallRtp.bytes)}</td><td>${formatBytes(data.rtpStats.video.currentRtp.bytes)}</td><td>${formatBytes(data.rtpStats.video.overallRtp.bytes)}</td></tr>`;
                        html += `<tr><td>码率</td><td>${formatBitrate(data.rtpStats.audio.currentRtp.bps)}</td><td>${formatBitrate(data.rtpStats.audio.overallRtp.bps)}</td><td>${formatBitrate(data.rtpStats.video.currentRtp.bps)}</td><td>${formatBitrate(data.rtpStats.video.overallRtp.bps)}</td></tr>`;
                        html += `<tr><td>包速率/s</td><td>${data.rtpStats.audio.currentRtp.pps}</td><td>${data.rtpStats.audio.overallRtp.pps}</td><td>${data.rtpStats.video.currentRtp.pps}</td><td>${data.rtpStats.video.overallRtp.pps}</td></tr>`;
                        html += `<tr><td>丢包数量</td><td>${data.rtpStats.audio.currentRtp.lost}</td><td>${data.rtpStats.audio.overallRtp.lost}</td><td>${data.rtpStats.video.currentRtp.lost}</td><td>${data.rtpStats.video.overallRtp.lost}</td></tr>`;
                        html += `<tr><td>丢包率</td><td>${data.rtpStats.audio.currentRtp.lostRate}%</td><td>${data.rtpStats.audio.overallRtp.lostRate}%</td><td>${data.rtpStats.video.currentRtp.lostRate}%</td><td>${data.rtpStats.video.overallRtp.lostRate}%</td></tr>`;

                        // Current Network
                        html += '<tr><th>网络</th><th>当前</th><th>总计</th><th>当前</th><th>总计</th></tr>';
                        html += `<tr><td>包数量</td><td>${data.rtpStats.audio.currentNetwork.count}</td><td>${data.rtpStats.audio.overallNetwork.count}</td><td>${data.rtpStats.video.currentNetwork.count}</td><td>${data.rtpStats.video.overallNetwork.count}</td></tr>`;
                        html += `<tr><td>字节数量</td><td>${formatBytes(data.rtpStats.audio.currentNetwork.bytes)}</td><td>${formatBytes(data.rtpStats.audio.overallNetwork.bytes)}</td><td>${formatBytes(data.rtpStats.video.currentNetwork.bytes)}</td><td>${formatBytes(data.rtpStats.video.overallNetwork.bytes)}</td></tr>`;
                        html += `<tr><td>码率</td><td>${formatBitrate(data.rtpStats.audio.currentNetwork.bps)}</td><td>${formatBitrate(data.rtpStats.audio.overallNetwork.bps)}</td><td>${formatBitrate(data.rtpStats.video.currentNetwork.bps)}</td><td>${formatBitrate(data.rtpStats.video.overallNetwork.bps)}</td></tr>`;
                        html += `<tr><td>包速率/s</td><td>${data.rtpStats.audio.currentNetwork.pps}</td><td>${data.rtpStats.audio.overallNetwork.pps}</td><td>${data.rtpStats.video.currentNetwork.pps}</td><td>${data.rtpStats.video.overallNetwork.pps}</td></tr>`;
                        html += '</table>';
                        html += '</div>';

                        html += '</div>'; // End flex container
                    }

                    detailsContent.innerHTML = html;

                    // 在HTML渲染完成后加载SIP消息流程图
                    updateSipFlow(data.sessionId);
                })
                .catch(error => {
                    document.getElementById('callDetailsContent').innerHTML =
                        `<p style="color: red">获取会话详情失败: ${error.message}</p>`;
                });
        }

        // Format bytes to human-readable format
        function formatBytes(bytes) {
            if (bytes === 0) return '0 Bytes';

            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));

            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // Format bitrate to human-readable format
        function formatBitrate(bps) {
            if (bps === 0) return '0 bps';

            if (bps < 1000) return bps + ' bps';
            if (bps < 1000000) return (bps / 1000).toFixed(2) + ' Kbps';
            return (bps / 1000000).toFixed(2) + ' Mbps';
        }

        // Set up refresh button
        document.getElementById('refreshCallList').addEventListener('click', refreshCallList);

        // Initial load of call list
        loadConfig('KeepTermedSession');
        setupConfigChangeListener('KeepTermedSession');
        refreshCallList();

        // Initial load of configuration
        const configContainer = document.getElementById('config');
        const configSelects = configContainer.querySelectorAll('select');

        configSelects.forEach(select => {
            const configName = select.id;
            loadConfig(configName);
            setupConfigChangeListener(configName);
        });

        function updateSipFlow(sessionId) {
            const flowContainer = document.getElementById('sip-flow');

            if (!flowContainer) return;

            flowContainer.innerHTML = '';

            const detailPanel = document.getElementById('sip-message-detail-panel');
            if (detailPanel) {
                detailPanel.style.display = 'none';
            }

            fetch(`${apiBaseUrl}/api/v1/call/${sessionId}/status`)
                .then(response => {
                    if (!response.ok) {
                        throw new Error('获取会话详情失败, 状态码: ' + response.status);
                    }
                    return response.json();
                })
                .then(data => {
                    const sipData = {
                        localEndpoint: data.sipStats?.local || '本地端点',
                        remoteEndpoint: data.sipStats?.remote || '远程端点',
                        messages: []
                    };

                    if (data.sipStats && data.sipStats.events) {
                        data.sipStats.events.forEach(event => {
                            sipData.messages.push({
                                id: event.title,
                                method: event.title,
                                type: event.type,
                                direction: (event.type === 'send') ? 'outgoing' : 'incoming',
                                timestamp: event.timestamp,
                                content: event.detail
                            });
                        });
                    }

                    showSelectedSessionFlow(sessionId, sipData);
                })
                .catch(error => {
                    console.error('Error fetching SIP messages:', error);
                    flowContainer.innerHTML = '<div class="flow-no-messages">获取SIP消息出错: ' + error.message + '</div>';
                });
        }

        function showSelectedSessionFlow(sessionId, data) {
            const flowContainer = document.getElementById('sip-flow');
            flowContainer.innerHTML = '';

            if (!data || !data.messages || data.messages.length === 0) {
                flowContainer.innerHTML = '<div class="flow-no-messages">该会话没有SIP消息</div>';
                return;
            }

            const localEndpoint = data.localEndpoint || '本地端点';
            const remoteEndpoint = data.remoteEndpoint || '远程端点';

            const endpointDiv = document.createElement('div');
            endpointDiv.className = 'flow-endpoint';

            const localDiv = document.createElement('div');
            localDiv.className = 'endpoint';
            localDiv.textContent = localEndpoint;

            const remoteDiv = document.createElement('div');
            remoteDiv.className = 'endpoint';
            remoteDiv.textContent = remoteEndpoint;

            endpointDiv.appendChild(localDiv);
            endpointDiv.appendChild(remoteDiv);
            flowContainer.appendChild(endpointDiv);

            const leftLine = document.createElement('div');
            leftLine.className = 'endpoint-line endpoint-line-left';
            flowContainer.appendChild(leftLine);

            const rightLine = document.createElement('div');
            rightLine.className = 'endpoint-line endpoint-line-right';
            flowContainer.appendChild(rightLine);

            data.messages.forEach(message => {
                const isOutgoing = message.direction === 'outgoing';

                const messageDiv = document.createElement('div');
                messageDiv.className = `flow-message ${isOutgoing ? 'flow-outgoing' : 'flow-incoming'}`;

                const contentDiv = document.createElement('div');
                contentDiv.className = 'flow-content';

                const lineDiv = document.createElement('div');
                lineDiv.className = 'flow-line';
                contentDiv.appendChild(lineDiv);

                const arrowDiv = document.createElement('div');
                arrowDiv.className = `flow-arrow ${isOutgoing ? 'flow-arrow-right' : 'flow-arrow-left'}`;
                contentDiv.appendChild(arrowDiv);

                const labelDiv = document.createElement('div');
                labelDiv.className = `flow-label ${isOutgoing ? 'flow-label-right' : 'flow-label-left'}`;
                labelDiv.textContent = message.method || message.type || 'SIP消息';
                labelDiv.onclick = function() {
                    showSipMessageDetail(message);
                };
                contentDiv.appendChild(labelDiv);

                if (message.timestamp) {
                    const date = new Date(message.timestamp);
                    const options = {
                        fractionalSecondDigits: 3,
                        hour: '2-digit',
                        minute: '2-digit',
                        second: '2-digit',
                        hour12: false // 使用24小时制
                    };
                    const timeDiv = document.createElement('div');
                    timeDiv.className = `flow-timestamp ${isOutgoing ? 'flow-timestamp-right' : 'flow-timestamp-left'}`;
                    timeDiv.textContent = date.toLocaleTimeString('zh-CN', options);
                    contentDiv.appendChild(timeDiv);
                }

                messageDiv.appendChild(contentDiv);
                flowContainer.appendChild(messageDiv);
            });
        }

        window.getCallDetails = getCallDetails;
    </script>
</body>

</html>