#include "../../SimpleRtpSession/SimpleRtpSession.def"

module SipDialer
{
    struct RequestCall
    {
        string caller;
        string callee;
        string uui;
        bool video;
        bool is3pcc;
        int timeout;
        string callback;
    };

    struct ResponseCall
    {
        int sessionId;
        string callId;
    };

    struct ResponseTerminated
    {
        int sessionId;
        bool error;
        string reason;
    };

    struct SessionBriefStatus
    {
        int sessionId;
        string caller;
        string callee;
        string callId;
        string uui;
        string direction;
        string state;
        string startTime;
        int duration;
        bool error;
        string reason;
    };

    vector SessionBriefStatusVec<SessionBriefStatus>

    struct ResponseAllCallStatus
    {
        int count;
        SessionBriefStatusVec sessions;
    };

    struct SipEvent
    {
        string type;
        string time;
        string title;
        string detail;
        long timestamp;
    };

    vector SipEventVec<SipEvent>

    struct SipStats
    {
        string local;
        string remote;
        SipEventVec events;
    };

    struct ResponseStatus
    {
        int sessionId;
        string caller;
        string callee;
        string callId;
        string direction;
        string state;
        string startTime;
        int duration;
        SipStats sipStats;
        SimpleRtpSession.RtpSessionStats rtpStats;
    };
}
