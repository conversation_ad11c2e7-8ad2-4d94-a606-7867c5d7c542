//
// *****************************************************************************
// Copyright (c) 2025 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//

#include "MpCallSipDialerSession.h"
#include "SimpleSipSession/SipMessage.h"
#include "SimpleSipSession/SipTypes.h"

namespace SipMpCall
{

static bool hasVideo(const std::string &sdp)
{
    size_t pos = sdp.find("\r\nm=video ");
    if (pos == std::string::npos)
        return false;

    return true;
}

void DialerSipSession::close()
{
    Common::RecLock lock(this);

    setState(StateTermed);

    if (_sipSess)
    {
        _sipSess->SipTerm();
        _sipSess = 0;
    }

    if (_rtpSession)
    {
        SimpleRtpSession::RtpSessionStats rtpStats;
        _rtpSession->getStats(rtpStats);
        _status.rtpStats = rtpStats;
        _rtpSession->close();
        _rtpSession = 0;
    }

    _rtpManager = 0;
}

bool DialerSipSession::isTermed()
{
    Common::RecLock lock(this);

    if (_state == StateTermed || !_sipSess)
        return true;

    if (__checkTimeout())
    {
        UTIL_LOG_IFO("DialerSipSession", "content:isTermed timeout");
        close();
        return true;
    }

    return false;
}

std::string DialerSipSession::getCaller()
{
    Common::RecLock lock(this);
    return _caller;
}

std::string DialerSipSession::getCallee()
{
    Common::RecLock lock(this);
    return _callee;
}

std::string DialerSipSession::getCallId()
{
    Common::RecLock lock(this);
    return _callId;
}

std::string DialerSipSession::getUui()
{
    Common::RecLock lock(this);
    return _uui;
}

Common::Long DialerSipSession::getDurationMs()
{
    Common::RecLock lock(this);

    if (_termedTimeMs > 0)
        return _termedTimeMs - _startTimeMs;

    return Common::getCurTimeMs() - _startTimeMs;
}

SipDialer::ResponseStatus DialerSipSession::getStatus()
{
    Common::RecLock lock(this);

    _status.sessionId = _sessId;
    _status.state = to_string(_state).c_str();
    _status.startTime = Common::getTimeStr("%04d%02d%02d-%02d:%02d:%02d.%03d", _startTimeMs);
    _status.duration = Common::getCurTimeMs() - _startTimeMs;
    _status.direction = inOutgoingCall() ? "outgoing" : "incoming";
    _status.caller = _caller.c_str();
    _status.callee = _callee.c_str();

    if (_rtpSession)
    {
        SimpleRtpSession::RtpSessionStats rtpStats;
        _rtpSession->getStats(rtpStats);
        _status.rtpStats = rtpStats;
    }

    return _status;
}

void DialerSipSession::onCallIncoming(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg)
{
    Common::RecLock lock(this);

    SipMpCall::SipLineSessionPtr sipSess = _sipSess;
    if (!sipSess)
    {
        UTIL_LOG_WRN("DialerSipSession", "content:onCallIncoming: sip session is not exist");
        return;
    }

    sipSess->setSessId(std::to_string(_sessId));
    _logContext->setContext("sessId", Common::String(_sessId), Common::LogContext::LogMaskKeep);
    std::string name, uri;
    sipSess->GetPeerUri(name, uri);
    _caller = name;
    sipSess->GetCalledUri(name, uri);
    _callee = name;
    SimpleSipSession::SipMessagePtr msg = SimpleSipSession::SipMessage::create(pcSipMsg);
    _callId = msg->getHeader(SimpleSipSession::HeaderCName::CALL_ID);

    _logContext->setContext("caller", _caller.c_str(), Common::LogContext::LogMaskKeep);
    _logContext->setContext("callee", _callee.c_str(), Common::LogContext::LogMaskKeep);
    _logContext->setContext("callId", _callId.c_str(), Common::LogContext::LogMaskKeep);
    SimpleSipSession::SipMessagePtr sipMsg = SimpleSipSession::SipMessage::create(pcSipMsg);
    _uui = sipMsg->getHeader("User-to-User").c_str();

    _rtpSession = _rtpManager->createSession(bSuptVideo);
    if (!_rtpSession)
    {
        UTIL_LOG_CONTEXT_ERR("DialerSipSession", _logContext, "content:onCallIncoming: create rtp session failed.");
        close();
        return;
    }

    UTIL_LOGFMT_CONTEXT_IFO("DialerSipSession", _logContext, "content:onCallIncoming: process with video:%d", bSuptVideo);

    if (!sipSess->SipAlert("") || !sipSess->SipAnswer(_rtpSession->genAnswer(pcSdp), SipClient::SipCallExtHdrs()))
    {
        UTIL_LOG_CONTEXT_ERR("DialerSipSession", _logContext, "content:onCallIncoming: sip alert or answer failed.");
        close();
        return;
    }

    setState(StateCalling);
}

void DialerSipSession::onCallAlerted(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg)
{
    UTIL_LOGFMT_CONTEXT_IFO("DialerSipSession", _logContext, "content:onCallAlerted: iStatCode: %d, bSuptVideo: %d", iStatCode, bSuptVideo);
}

void DialerSipSession::onCallAnswered(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg)
{
    UTIL_LOGFMT_CONTEXT_IFO("DialerSipSession", _logContext, "content:onCallAnswered: iStatCode: %d, bSuptVideo: %d", iStatCode, bSuptVideo);

    Common::RecLock lock(this);
    if (!_rtpSession->setAnswer(pcSdp))
    {
        UTIL_LOG_CONTEXT_ERR("DialerSipSession", _logContext, "content:onCallAnswered: set answer failed.");
        close();
        return;
    }

    _sipSess->SipAck("");
    setState(StateConnected);
}

void DialerSipSession::onCallUpdate(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg)
{
    UTIL_LOGFMT_CONTEXT_IFO("DialerSipSession", _logContext, "content:onCallUpdate: iStatCode: %d, bSuptVideo: %d", iStatCode, bSuptVideo);
}

void DialerSipSession::onCallRequestUpdate(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg)
{
    UTIL_LOGFMT_CONTEXT_IFO("DialerSipSession", _logContext, "content:onCallRequestUpdate: iStatCode: %d, bSuptVideo: %d", iStatCode, bSuptVideo);
}

void DialerSipSession::onCallResponseUdate(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg)
{
    UTIL_LOGFMT_CONTEXT_IFO("DialerSipSession", _logContext, "content:onCallResponseUdate: iStatCode: %d, bSuptVideo: %d", iStatCode, bSuptVideo);
}

void DialerSipSession::onCallConnected(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg)
{
    UTIL_LOGFMT_CONTEXT_IFO("DialerSipSession", _logContext, "content:onCallConnected: iStatCode: %d, bSuptVideo: %d", iStatCode, bSuptVideo);
    setState(StateConnected);
}

void DialerSipSession::onCallRequestModify(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg)
{
    bSuptVideo = hasVideo(pcSdp);
    UTIL_LOGFMT_CONTEXT_IFO("DialerSipSession", _logContext, "content:onCallRequestModify: iStatCode: %d, bSuptVideo: %d", iStatCode, bSuptVideo);

    Common::RecLock lock(this);

    _rtpSession->enableVideo(bSuptVideo);
    std::string answerSdp = _rtpSession->genAnswer(pcSdp);
    if (answerSdp.empty())
    {
        UTIL_LOG_CONTEXT_WRN("DialerSipSession", _logContext, "content:onCallRequestModify: request modify sdp, terminate call");
        _sipSess->SipTerm();
        return;
    }

    if (!_sipSess->SipUpdateRsp(answerSdp))
    {
        UTIL_LOG_CONTEXT_WRN("DialerSipSession", _logContext, "content:onCallRequestModify: update rsp failed, terminate call");
        _sipSess->SipTerm();
        return;
    }
}

void DialerSipSession::onCallResponseModify(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg)
{
    UTIL_LOGFMT_CONTEXT_IFO("DialerSipSession", _logContext, "content:onCallResponseModify: iStatCode: %d, bSuptVideo: %d", iStatCode, bSuptVideo);
}

void DialerSipSession::onCallTerminated(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg)
{
    Common::RecLock lock(this);
    if (!_sipSess)
    {
        UTIL_LOG_CONTEXT_DBG("DialerSipSession", _logContext, "content:onCallTerminated: sip session is not exist");
        return;
    }

    std::string phase, reason;
    _sipSess->GetCallTermedReason(phase, reason);
    setState(StateTermed);
    _termedReason = "SipTermed:" + std::to_string(iStatCode);
    _sipSess = 0;

    close();
    UTIL_LOGFMT_CONTEXT_IFO("DialerSipSession", _logContext, "content:onCallTerminated: iStatCode: %d, bSuptVideo: %d, phase: %s, reason: %s", iStatCode, bSuptVideo, phase.c_str(), reason.c_str());
}

std::string DialerSipSession::to_string(enum State state)
{
    switch (state)
    {
    case StateIdle:
        return "Idle";
    case StateCalling:
        return "Calling";
    case StateConnected:
        return "Connected";
    case StateTermed:
        return "Termed";
    }

    return "Unknown";
}

void DialerSipSession::setState(enum State state)
{
    Common::RecLock lock(this);
    if (_state == state)
        return;

    if (state == StateTermed)
        _termedTimeMs = Common::getCurTimeMs();

    UTIL_LOGFMT_CONTEXT_IFO("DialerSipSession", _logContext, "content:setState: state:%s->%s", to_string(_state).c_str(), to_string(state).c_str());
    _state = state;
}

bool DialerSipSession::__checkTimeout()
{
    return _timeoutMs > 0 && Common::getCurTimeMs() - _startTimeMs >= _timeoutMs;
}

bool DialerOutgoingSipSession::call(const SipMpCall::SipLinePtr &sipLine, const std::string &sessId, const std::string &caller, const std::string &callee, const std::string &uui, bool video)
{
    Common::RecLock lock(this);
    _caller = caller;
    _callee = callee;
    _uui = uui;

    _logContext->setContext("caller", caller.c_str(), Common::LogContext::LogMaskKeep);
    _logContext->setContext("callee", callee.c_str(), Common::LogContext::LogMaskKeep);
    _logContext->setContext("video", video ? "true" : "false");

    _rtpSession = _rtpManager->createSession(video);
    if (!_rtpSession)
    {
        UTIL_LOG_CONTEXT_ERR("DialerOutgoingSipSession", _logContext, "content:call: create rtp session failed.");
        return false;
    }

    UTIL_LOGFMT_CONTEXT_IFO("DialerOutgoingSipSession", _logContext, "content:call caller:%s, callee:%s, uui:%s, video:%d", caller.c_str(), callee.c_str(), uui.c_str(), video);
    std::map<std::string, std::string> extHdrs;
    if (!uui.empty())
        extHdrs["User-to-User"] = uui;
    _sipSess = sipLine->SipCall(this, sessId, caller, callee, genOffer(), extHdrs);
    if (!_sipSess)
    {
        UTIL_LOG_CONTEXT_ERR("DialerOutgoingSipSession", _logContext, "content:call: create sip session failed.");
        close();
        return false;
    }

    _callId = _sipSess->getCallId();
    _logContext->setContext("sessId", sessId.c_str());
    _logContext->setContext("callId", _callId.c_str());

    setState(StateCalling);
    return true;
}

bool DialerOutgoingSipSession::__checkTimeout()
{
    if (DialerSipSession::__checkTimeout())
    {
        UTIL_LOG_IFO("DialerOutgoingSipSession", "content:__checkTimeout: timeout");
        if (_state != StateConnected)
            _error = true;
        _termedReason = "timeout";
        return true;
    }

    return false;
}

void DialerOutgoingSipSession::onCallTerminated(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg)
{
    if (!DialerSipSession::isTermed())
        _error = true;
    DialerSipSession::onCallTerminated(pcSdp, iStatCode, bSuptVideo, pcSipMsg);
}

void DialerOutgoing3pccSession::onCallAnswered(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg)
{
    UTIL_LOGFMT_CONTEXT_IFO("Dialer3pccSession", _logContext, "content:onCallAnswered: iStatCode: %d, bSuptVideo: %d", iStatCode, bSuptVideo);

    Common::RecLock lock(this);

    std::string answer = _rtpSession->genAnswer(pcSdp);
    if (answer.empty())
    {
        UTIL_LOG_CONTEXT_ERR("Dialer3pccSession", _logContext, "content:onCallAnswered: gen answer failed.");
        close();
        return;
    }

    _sipSess->SipAck(answer);
    setState(StateConnected);
}

} // namespace SipMpCall
