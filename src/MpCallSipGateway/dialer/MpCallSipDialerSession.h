//
// *****************************************************************************
// Copyright (c) 2025 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//

#pragma once

#include "Common/Util.h"
#include "SipLine/SipLine.h"
#include "SimpleRtpSession/SimpleRtpSession.h"
#include "SipDialer/MpCallSipDialerPub.h"

namespace SipMpCall
{

class DialerSipSession : public SipMpCall::SipLineSessionListener, public Common::RecMutex
{
protected:
    enum State
    {
        StateIdle,
        StateCalling,
        StateConnected,
        StateTermed
    };

public:
    DialerSipSession(const SimpleRtpSession::SimpleRtpSessionManagerPtr &rtpManager, const SipMpCall::SipLineSessionPtr &sipSess, int sessId, int timeoutSeconds = 0)
        : _state(StateIdle)
        , _error(false)
        , _sipSess(sipSess)
        , _rtpManager(rtpManager)
        , _sessId(sessId)
        , _startTimeMs(Common::getCurTimeMs())
        , _termedTimeMs(0)
        , _timeoutMs(timeoutSeconds * 1000)
        , _logContext(Common::LogContext::create())
    {
    }

    void close();
    virtual bool inOutgoingCall() { return false; }
    virtual bool isTermed();
    std::string getCaller();
    std::string getCallee();
    std::string getCallId();
    std::string getUui();
    Common::Long getStartTimeMs() { return _startTimeMs; }
    Common::Long getDurationMs();
    bool isError() { return _error; }
    std::string getState() { return to_string(_state); }
    const std::string &termedReason() { return _termedReason; }
    SipDialer::ResponseStatus getStatus();

    // implement SipLineSessionListener
    virtual void onCallIncoming(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) override;
    virtual void onCallAlerted(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) override;
    virtual void onCallAnswered(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) override;
    virtual void onCallUpdate(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) override;
    virtual void onCallRequestUpdate(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) override;
    virtual void onCallResponseUdate(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) override;
    virtual void onCallConnected(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) override;
    virtual void onCallRequestModify(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) override;
    virtual void onCallResponseModify(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) override;
    virtual void onCallTerminated(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) override;

protected:
    static std::string to_string(enum State state);
    void setState(enum State state);
    virtual bool __checkTimeout();

protected:
    const int _sessId;
    Common::Long _startTimeMs;
    Common::Long _termedTimeMs;
    Common::Long _timeoutMs;
    std::string _caller;
    std::string _callee;
    std::string _callId;
    std::string _uui;
    enum State _state;
    bool _error;
    std::string _termedReason;
    SipMpCall::SipLineSessionPtr _sipSess;
    SimpleRtpSession::SimpleRtpSessionManagerPtr _rtpManager;
    SimpleRtpSession::SimpleRtpSessionPtr _rtpSession;
    Common::LogContextPtr _logContext;
    SipDialer::ResponseStatus _status;
};

typedef Common::Handle<DialerSipSession> DialerSipSessionPtr;

class DialerOutgoingSipSession : public DialerSipSession
{
public:
    DialerOutgoingSipSession(const SimpleRtpSession::SimpleRtpSessionManagerPtr &rtpManager, int sessId, unsigned int timeoutSeconds)
        : DialerSipSession(rtpManager, 0, sessId, timeoutSeconds)
    {
    }

    bool call(const SipMpCall::SipLinePtr &sipLine, const std::string &sessId, const std::string &caller, const std::string &callee, const std::string &uui, bool video);
    virtual std::string genOffer() { return _rtpSession->genOffer(); }

    virtual bool inOutgoingCall() override { return true; }
    virtual bool __checkTimeout() override;

    virtual void onCallTerminated(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) override;
};

class DialerOutgoing3pccSession : public DialerOutgoingSipSession
{
public:
    DialerOutgoing3pccSession(const SimpleRtpSession::SimpleRtpSessionManagerPtr &rtpManager, int sessId, unsigned int timeoutSeconds)
        : DialerOutgoingSipSession(rtpManager, sessId, timeoutSeconds)
    {
    }

    virtual std::string genOffer() override { return ""; }

    virtual void onCallAnswered(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) override;
};

} // namespace SipMpCall
