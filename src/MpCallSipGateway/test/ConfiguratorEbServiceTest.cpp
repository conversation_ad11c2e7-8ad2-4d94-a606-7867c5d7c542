//
// *****************************************************************************
// Copyright (c) 2025 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//

#include "Mocks/MockApp.h"
#include "MpCall/MpCallPub.h"
#include "MpCall/MpCallSipServerMock.h"

#include "MpCallSipGateway/SipSessionConfigurator.h"
#include "MpCallSipGateway/SipSessionController.h"
#include "gmock/gmock-spec-builders.h"
#include "gtest/gtest.h"

using ::testing::_;

class SipSessionControllerTest : public ModuleTest::MockTest
{
public:
    void SetUp() override
    {
        ModuleTest::MockTest::SetUp();
        ebService = AddServer<ModuleTest::SipGatewayControllerServerMockBase>("EbService");
    }

    void TearDown() override
    {
        ModuleTest::MockTest::TearDown();
    }

    ModuleTest::SipGatewayControllerServerMockBasePtr ebService;
};

TEST_F(SipSessionControllerTest, Normal)
{
    SipMpCall::SipSessionConfiguratorPtr configurator = new SipMpCall::SipSessionController(_app->createAgent("EbService"));
    SipMpCall::SipSessionConfigurator::CallParams inParams, outParams;
    SipMpCall::SipSessionConfigurator::AppParams appParams;
    inParams.sessId = "12345";
    inParams.caller = "caller";
    inParams.callee = "callee";
    inParams.inviteConfig.uui = "test_uui";

    EXPECT_CALL(*(ebService.get()), onCallin_begin(_, _, _, _, _))
        .WillOnce([](const Common::ServerCallPtr &__call, const Common::String &sessId, const Common::String &caller, const Common::String &callee, const MpCall::InviteConfig &inviteConfig) {
            EXPECT_EQ(sessId, "12345");
            EXPECT_EQ(caller, "caller");
            EXPECT_EQ(callee, "callee");
            EXPECT_EQ(inviteConfig.uui, "test_uui");
            MpCall::InviteConfig config;
            config.uui = "out_uui";
            config.requireAlert = true;
            MpCall::SipGatewayControllerServer::onCallin_end(__call, true, "4568", "agent", config);
        });
    EXPECT_TRUE(configurator->onIncomingCall(inParams, outParams, appParams));
    EXPECT_TRUE(outParams.inviteConfig.requireAlert);
    EXPECT_EQ(outParams.inviteConfig.uui, "out_uui");
    EXPECT_EQ(outParams.sessId, "4568");
    EXPECT_EQ(outParams.callee, "agent");

    EXPECT_CALL(*(ebService.get()), onCallout_begin(_, _, _, _, _))
        .WillOnce([](const Common::ServerCallPtr &__call, const Common::String &sessId, const Common::String &caller, const Common::String &callee, const MpCall::InviteConfig &inviteConfig) {
            EXPECT_EQ(sessId, "12345");
            EXPECT_EQ(caller, "caller");
            EXPECT_EQ(callee, "callee");
            EXPECT_EQ(inviteConfig.uui, "test_uui");
            MpCall::InviteConfig config;
            config.uui = "out_uui";
            MpCall::SipGatewayControllerServer::onCallout_end(__call, true, "4568", config);
        });
    EXPECT_TRUE(configurator->onOutgoingCall(inParams, outParams));
    EXPECT_EQ(outParams.inviteConfig.uui, "out_uui");
    EXPECT_EQ(outParams.callee, "4568");

    EXPECT_CALL(*(ebService.get()), onHolded_begin(_, _, _))
        .WillOnce([](const Common::ServerCallPtr &__call, const Common::String &sessId, bool hold) {
            EXPECT_EQ(sessId, "12345");
            EXPECT_TRUE(hold);
            MpCall::SipGatewayControllerServer::onHolded_end(__call, true);
        });
    EXPECT_TRUE(configurator->onHolded("12345", true));

    EXPECT_CALL(*(ebService.get()), onTermed_begin(_, _))
        .WillOnce([](const Common::ServerCallPtr &__call, const Common::String &sessId) {
            EXPECT_EQ(sessId, "12345");
            MpCall::SipGatewayControllerServer::onTermed_end(__call, true);
        });
    EXPECT_TRUE(configurator->onTermed("12345"));
}
