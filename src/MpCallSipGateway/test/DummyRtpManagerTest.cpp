//
// *****************************************************************************
// Copyright (c) 2023 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//
// Created on 2023/12/9 by <PERSON>
//

#include "gtest/gtest.h"

#include "Common/Common.h"
#include "Common/TypesPub.h"
#include "Common/Util.h"
#include "MpCallSip/MpCallRtpAgent.h"
#include "MpCallSip/MpCallRtpPub.h"
#include "MpCallSipGateway/DummyRtpManager.h"

static const char *offerSdp = "\
v=0\r\n\
o=- 3455926086 0 IN IP4 ***********\r\n\
s=-\r\n\
c=IN IP4 ***********\r\n\
t=0 0\r\n\
m=audio 39478 RTP/AVP 0 8 101\r\n\
a=ptime:20\r\n\
a=maxptime:400\r\n\
a=rtpmap:101 telephone-event/8000\r\n\
m=video 39712 RTP/AVP 117 118\r\n\
i=3gppCvo\r\n\
a=rtpmap:117 H264-SVC/90000\r\n\
a=fmtp:117 profile-level-id=42800C; packetization-mode=1\r\n\
a=rtpmap:118 H264/90000\r\n\
a=fmtp:118 profile-level-id=42800C; packetization-mode=1\r\n\
a=extmap:1 urn:3gpp:video-orientation\r\n\
\r\n\
";

class DummyRtpManagerTest : public ::testing::Test
{
public:
    void SetUp()
    {
        Common::StrStrMap configs;
        configs["global.Log.Level"] = "3";
        configs["global.Log.Print"] = "1";
        configs["global.Test.Endpoints"] = "sudp -h 127.0.0.1";
        _app = Common::Application::create("Test", "", 0, configs);
        _app->activate();

        _adapter = _app->createAdapter("Test");
        _adapter->activate();
    }

    void TearDown()
    {
        if (_app)
        {
            _app->indShutdown();
            while (!_app->isShutdown())
                Common::sleep(10);
        }
    }

    Common::ApplicationPtr _app;
    Common::AdapterPtr _adapter;
};

TEST_F(DummyRtpManagerTest, Normal)
{
    ServiceRunner::RunnerManagerPtr mgr = SipMpCall::DummyRtpManager::create(_app);
    ASSERT_TRUE(mgr != nullptr);
    ASSERT_TRUE(mgr->activate(_adapter));
    ASSERT_EQ(mgr->totalRunnerCount(), 1);

    auto handle = mgr->allocRunner(ServiceRunner::RuleParams(), 0);
    ASSERT_TRUE(handle != nullptr);

    Common::String name, objectId;
    Common::Error error;
    ASSERT_TRUE(handle->getRunner(name, objectId, error));
    ASSERT_FALSE(name.empty());
    ASSERT_FALSE(objectId.empty());
    MpCallSip::RtpRunnerAgent agentIn = _app->createAgent(objectId);
    ASSERT_EQ(mgr->totalRunnerCount(), 2);

    handle = mgr->allocRunner(ServiceRunner::RuleParams(), 0);
    ASSERT_TRUE(handle != nullptr);
    ASSERT_TRUE(handle->getRunner(name, objectId, error));
    ASSERT_FALSE(name.empty());
    ASSERT_FALSE(objectId.empty());
    MpCallSip::RtpRunnerAgent agentOut = _app->createAgent(objectId);
    ASSERT_EQ(mgr->totalRunnerCount(), 3);

    MpCallSip::SdpParams sdpParams;

    Common::String sdp;
    ASSERT_TRUE(agentOut.callout("", "", MpCallSip::RtpRunnerConfig(), sdpParams, sdp));
    ASSERT_FALSE(sdp.empty());
    std::cout << sdp.c_str() << "-----------------" << std::endl;

    ASSERT_TRUE(agentIn.callinAndAnswer("", "", MpCallSip::RtpRunnerConfig(), sdp, sdpParams, sdp));
    ASSERT_FALSE(sdp.empty());
    std::cout << sdp.c_str() << "-----------------" << std::endl;

    ASSERT_TRUE(agentOut.setAnswer(sdp));

    // hold
    MpCallSip::SdpParams sdpParamsSendOnly = sdpParams;
    sdpParamsSendOnly.videoDirection = MpCallSip::MediaSendOnly;
    ASSERT_TRUE(agentIn.genOffer(sdpParamsSendOnly, sdp));
    std::cout << sdp.c_str() << "-----------------" << std::endl;
    ASSERT_TRUE(agentOut.setOfferAndAnswer(sdp, sdpParams, sdp));
    std::cout << sdp.c_str() << "-----------------" << std::endl;
    ASSERT_TRUE(agentIn.setAnswer(sdp));

    // unhold
    ASSERT_TRUE(agentIn.genOffer(sdpParams, sdp));
    std::cout << sdp.c_str() << "-----------------" << std::endl;
    ASSERT_TRUE(agentOut.setOfferAndAnswer(sdp, sdpParams, sdp));
    std::cout << sdp.c_str() << "-----------------" << std::endl;
    ASSERT_TRUE(agentIn.setAnswer(sdp));

    // another offer
    ASSERT_TRUE(agentOut.setOfferAndAnswer(offerSdp, sdpParams, sdp));
    std::cout << sdp.c_str() << "-----------------" << std::endl;

    Common::sleep(3000);
    ASSERT_EQ(mgr->totalRunnerCount(), 3);

    ASSERT_TRUE(agentIn.close());
    ASSERT_TRUE(agentOut.close());

    for (int i = 0; i < 10; i++)
    {
        mgr->updateConfigs();
        if (mgr->totalRunnerCount() == 1)
            break;
        Common::sleep(3000);
    }
    ASSERT_EQ(mgr->totalRunnerCount(), 1);

    mgr->close();
}