//
// *****************************************************************************
// Copyright (c) 2024 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//

#include "Mocks/MockApp.h"
#include "MpCall/MpCallSessionServerMock.h"
#include "JSM/JSMSServerMock.h"
#include "MpCall/MpCallEventPub.h"
#include "MpCall/MpCallPub.h"
#include "MpCall/MpCallSipAgent.h"
#include "DataCollection/DataCollectionServerMock.h"
#include "MpCallSip/MpCallRtpServerMock.h"
#include "SipGatewayEntry/SipGatewayEntryAgent.h"

#include "MpCallSipGateway/SipPhoneSession.h"
#include "MpCallSipGateway/MpCallSipGateway.h"
#include "JSM/JSMSAgent.h"
#include "JSM/JSMEAgent.h"
#include "SimpleSipLineMock.h"

using ::testing::_;
using ::testing::Eq;


class SipPhoneSessionEventTest : public ModuleTest::MockServiceTest<SipMpCall::MpCallSipGateway>
{
public:
    Common::StrStrMap appConfigs() override
    {
        return {
            {"global.Main.Endpoints", "sudp"},
            {"global.SessionLocator.Endpoints", "sudp"},
            {"global.SipCall.CoreNetId", "test_net"},
            {"global.SipGateway.DummyRtpRunner", "1"},
            {"global.Locators.MpCall", "MpCallLocator"},
            {"global.Locators.TpSipAgent", "MpCallLocator"},
            {"global.SipCall.IncomingDomainId", "100645/4"},
            {"global.SipCall.KeepAlive", "OFF"},
            {"global.SipCall.LocalAddress", "127.0.0.1:5080"},
            {"global.SipCall.PAIUriFormat", "TelUri"},
            {"global.SipCall.RemoteAddress", "127.0.0.1:5050"},
            {"global.SipCall.NoListening", "0"},
            {"global.MpCallSipGateway.TargetOid", "TpSipAgent/alice/1234"}
        };
    }

    virtual Common::AppSchedulerPtr createService(const Common::ApplicationExPtr &app) override
    {
        return new SipMpCall::MpCallSipGateway(_app, 0, _sipLine, 0, 0);
    }

    void SetUp() override
    {
        _sipLine = new ::testing::NiceMock<ModuleTest::SipLineMock>();
        _sipLine->deletgateToNice();

        ModuleTest::MockServiceTest<SipMpCall::MpCallSipGateway>::SetUp();
        // Initialize servers
        _dataCollectionServer = AddServer<ModuleTest::EventCollectionServiceServerMockBase>("#EventCollectionServer");
        _rtpRunnerServer = AddCategoryServer<ModuleTest::RtpRunnerServerMockBase>("RtpRunner");
        _tpSipServer = AddCategoryServer<ModuleTest::SessionEndpointServerMockBase>("TpSipAgent");

        // Wait for service initialization
        Common::sleep(3000);
    }

    void TearDown() override
    {
        // Clean up resources
        _rtpRunnerServer = nullptr;
        _dataCollectionServer = nullptr;
        _tpSipServer = nullptr;
        _sipLine = nullptr;
        ModuleTest::MockServiceTest<SipMpCall::MpCallSipGateway>::TearDown();
    }

    ModuleTest::EventCollectionServiceServerMockBasePtr _dataCollectionServer;
    ModuleTest::RtpRunnerServerMockBasePtr _rtpRunnerServer;
    ModuleTest::SessionEndpointServerMockBasePtr _tpSipServer;
    ModuleTest::SipLineMockPtr _sipLine;
};

TEST_F(SipPhoneSessionEventTest, outGoingCallFlowTest)
{
    EXPECT_CALL(*(_dataCollectionServer.get()), event_begin(_, _))
        .WillOnce(testing::Invoke([this](const Common::ServerCallPtr &__call, const DataCollection::EventList &eventList) {
            ASSERT_EQ(eventList.size(), 1);
            const DataCollection::Event &event = eventList[0];
            ASSERT_EQ(event.type, (DataCollection::EventType)15);  // SIP事件类型
            ASSERT_EQ(event.eventNumber, static_cast<int>(SipEvent::SipEventCall));  // 呼叫事件
            _dataCollectionServer->event_end(__call, true);
        }));

    Common::sleep(2000);

    MpCall::RoomConfig roomConfig;
    roomConfig.jsmiOid = "JSMI";
    roomConfig.domainId = 100645;
    roomConfig.appId = 4;
    roomConfig.udId = "1234100645_4";
    roomConfig.roomId = "9999999999";
    roomConfig.confNum = "10203040";
    MpCall::InviteConfig inviteConfig;
    inviteConfig.isVideo = false;
    inviteConfig.displayName = "1234567890";
    inviteConfig.uui = "some_uui";

    MpCall::SipGatewayAgent sipGwAgent = _app->createAgent("SipCall/alice/1234");
    ASSERT_TRUE(sipGwAgent.invitation(roomConfig.udId, "1234567890", "alice", roomConfig, inviteConfig));
    Common::sleep(100);

    EXPECT_CALL(*(_dataCollectionServer.get()), event_begin(_, _))
        .WillOnce(testing::Invoke([this](const Common::ServerCallPtr &__call, const DataCollection::EventList &eventList) {
            ASSERT_EQ(eventList.size(), 1);
            const DataCollection::Event &event = eventList[0];
            ASSERT_EQ(event.type, (DataCollection::EventType)15);  // SIP事件类型
            ASSERT_EQ(event.eventNumber, static_cast<int>(SipEvent::SipEventRtpNetQualityChanged));
            _dataCollectionServer->event_end(__call, true);
        }));

    MpCallSip::RtpRunnerMonitorAgent rtpMonitorAgent = _app->createAgent("SipCall/alice/1234");
    Common::StrIntMap jsmNetworkStatus;
    jsmNetworkStatus["RtpRunner"] = 3;
    rtpMonitorAgent.onNetworkStatusChanged(jsmNetworkStatus);
    Common::sleep(1000);

    EXPECT_CALL(*(_dataCollectionServer.get()), event_begin(_, _))
        .WillOnce(testing::Invoke([this](const Common::ServerCallPtr &__call, const DataCollection::EventList &eventList) {
            ASSERT_EQ(eventList.size(), 1);
            const DataCollection::Event &event = eventList[0];
            ASSERT_EQ(event.type, (DataCollection::EventType)15);  // SIP事件类型
            ASSERT_EQ(event.eventNumber, static_cast<int>(SipEvent::SipEventNetQualityChanged));
            _dataCollectionServer->event_end(__call, true);
        }));
    jsmNetworkStatus.clear();
    jsmNetworkStatus["[username:<EMAIL>]"] = 0;
    rtpMonitorAgent.onNetworkStatusChanged(jsmNetworkStatus);
    Common::sleep(1000);

    EXPECT_CALL(*(_dataCollectionServer.get()), event_begin(_, _))
     .WillOnce(testing::Invoke([this](const Common::ServerCallPtr &__call, const DataCollection::EventList &eventList) {
        ASSERT_EQ(eventList.size(), 1);
        const DataCollection::Event &event = eventList[0];
        ASSERT_EQ(event.type, (DataCollection::EventType)15);
        ASSERT_EQ(event.eventNumber, static_cast<int>(SipEvent::SipEventAlerted));
        _dataCollectionServer->event_end(__call, true);
    }));
    SipMpCall::SipLineSessionPtr sipCallSessionPtr = _sipLine->SipCall(0, "", "", "", "", {});
    ASSERT_TRUE(sipCallSessionPtr);
    ModuleTest::SipCallSessionMockPtr sipCallSessionMockPtr = ModuleTest::SipCallSessionMockPtr::dynamicCast(sipCallSessionPtr);
    SipMpCall::SipLineSessionListenerPtr sipCallSessionListenerPtr = sipCallSessionMockPtr->_listener;
    sipCallSessionListenerPtr->onCallAlerted("", 0, false, "");
    Common::sleep(1000);

    EXPECT_CALL(*(_dataCollectionServer.get()), event_begin(_, _))
     .WillOnce(testing::Invoke([this](const Common::ServerCallPtr &__call, const DataCollection::EventList &eventList) {
        ASSERT_EQ(eventList.size(), 1);
        const DataCollection::Event &event = eventList[0];
        ASSERT_EQ(event.type, (DataCollection::EventType)15);
        ASSERT_EQ(event.eventNumber, static_cast<int>(SipEvent::SipEventConnected));
        _dataCollectionServer->event_end(__call, true);
    }));

    std::string validSipMessage =
        "SIP/2.0 200 OK\r\n"
        "Via: SIP/2.0/UDP 127.0.0.1:5080;branch=z9hG4bK776asdhds\r\n"
        "To: <sip:1234567890@127.0.0.1>;tag=a6c85cf\r\n"
        "From: <sip:alice@127.0.0.1>;tag=1928301774\r\n"
        "Call-ID: a84b4c76e66710\r\n"
        "CSeq: 314159 INVITE\r\n"
        "Contact: <sip:1234567890@127.0.0.1:5060>\r\n"
        "Content-Type: application/sdp\r\n"
        "Content-Length: 0\r\n\r\n";

    sipCallSessionListenerPtr->onCallAnswered("", 0, false, validSipMessage);
    Common::sleep(1000);

    EXPECT_CALL(*(_dataCollectionServer.get()), event_begin(_, testing::Truly([](const DataCollection::EventList& eventList) {
        return eventList.size() == 1 && eventList[0].type == (DataCollection::EventType)15 &&
               eventList[0].eventNumber == static_cast<int>(SipEvent::SipEventTerminated);
    })))
    .WillOnce(testing::Invoke([this](const Common::ServerCallPtr &__call, const DataCollection::EventList &eventList) {
        _dataCollectionServer->event_end(__call, true);
    }));

    EXPECT_CALL(*(_dataCollectionServer.get()), event_begin(_, testing::Truly([](const DataCollection::EventList& eventList) {
        return eventList.size() == 1 && eventList[0].type == (DataCollection::EventType)14 && eventList[0].eventNumber == 1;
    })))
    .WillOnce(testing::Invoke([this](const Common::ServerCallPtr &__call, const DataCollection::EventList &eventList) {
        _dataCollectionServer->event_end(__call, true);
    }));

    sipCallSessionListenerPtr->onCallTerminated("", 0, false, "");
    Common::sleep(1000);
}