//
// *****************************************************************************
// Copyright (c) 2025 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//

#include "MpCallSipGateway/SipSessionConfiguratorBase.h"
#include "gtest/gtest.h"

TEST(ConfiguratorBaseTest, Normal)
{
    // Test the normal case of SipSessionConfiguratorBase
    SipMpCall::SipSessionConfiguratorBase configurator;
    SipMpCall::SipSessionConfigurator::CallParams inParams;
    inParams.sessId = "12345";
    inParams.caller = "caller";
    inParams.callee = "callee";
    inParams.inviteConfig.uui = "test_uui";

    SipMpCall::SipSessionConfigurator::CallParams outParams;
    SipMpCall::SipSessionConfigurator::AppParams appParams;
    EXPECT_TRUE(configurator.onIncomingCall(inParams, outParams, appParams));
    EXPECT_EQ(outParams.calleeType, MpCall::TypeApp);
    EXPECT_EQ(outParams.sessId, inParams.sessId);
    EXPECT_EQ(outParams.caller, inParams.caller);
    EXPECT_EQ(outParams.callee, inParams.callee);
    EXPECT_EQ(outParams.inviteConfig.uui, inParams.inviteConfig.uui);
    EXPECT_TRUE(appParams.domainId.empty());
    EXPECT_TRUE(appParams.appId.empty());

    EXPECT_TRUE(configurator.onOutgoingCall(inParams, outParams));
    EXPECT_EQ(outParams.calleeType, MpCall::TypeApp);
    EXPECT_EQ(outParams.sessId, inParams.sessId);
    EXPECT_EQ(outParams.caller, inParams.caller);
    EXPECT_EQ(outParams.callee, inParams.callee);
    EXPECT_EQ(outParams.inviteConfig.uui, inParams.inviteConfig.uui);

    EXPECT_TRUE(configurator.onHolded(inParams.sessId, true));
    EXPECT_TRUE(configurator.onHolded(inParams.sessId, false));

    EXPECT_TRUE(configurator.onTermed(inParams.sessId));
}

TEST(ConfiguratorBaseTest, Domain)
{
    // Test the domain and app ID parsing
    SipMpCall::SipSessionConfiguratorDomain configurator("100645", "4");
    SipMpCall::SipSessionConfigurator::CallParams inParams;
    inParams.sessId = "12345";
    inParams.caller = "caller";
    inParams.callee = "callee";
    inParams.inviteConfig.uui = "test_uui";

    SipMpCall::SipSessionConfigurator::CallParams outParams;
    SipMpCall::SipSessionConfigurator::AppParams appParams;
    EXPECT_TRUE(configurator.onIncomingCall(inParams, outParams, appParams));
    EXPECT_EQ(appParams.domainId, "100645");
    EXPECT_EQ(appParams.appId, "4");
}

TEST(ConfiguratorBaseTest, CalleePrefix)
{
    // Test the callee prefix handling
    SipMpCall::SipSessionConfiguratorCalleePrefix configurator;
    SipMpCall::SipSessionConfigurator::CallParams inParams;
    inParams.sessId = "12345";
    inParams.caller = "caller";
    inParams.callee = "123456callee";
    inParams.inviteConfig.uui = "test_uui";

    SipMpCall::SipSessionConfigurator::CallParams outParams;
    SipMpCall::SipSessionConfigurator::AppParams appParams;
    EXPECT_TRUE(configurator.onIncomingCall(inParams, outParams, appParams));
    EXPECT_EQ(appParams.domainId, "123456");
    EXPECT_EQ(appParams.appId, "0");
}

class ConfiguratorBaseCallTypeTest : public ::testing::Test
{
protected:
    void SetUp() override
    {
        _app = Common::ApplicationEx::create("ConfiguratorBaseCallTypeTest");
        _app->activate();
    }

    void TearDown() override
    {
        _app->indShutdown();
        while (!_app->isShutdown())
            Common::sleep(10);
    }

    Common::ApplicationExPtr _app;
    SipMpCall::SipSessionConfiguratorBase _configurator;
};

TEST_F(ConfiguratorBaseCallTypeTest, NoConfig)
{
    _configurator._calleeTypeConfig.init(_app);
    EXPECT_EQ(_configurator._calleeTypeConfig.getCalleeType("", "", MpCall::TypeUnknown), MpCall::TypeApp);
    EXPECT_EQ(_configurator._calleeTypeConfig.getCalleeType("", "", MpCall::TypeApp), MpCall::TypeApp);
    EXPECT_EQ(_configurator._calleeTypeConfig.getCalleeType("", "", MpCall::TypeSip), MpCall::TypeSip);
    EXPECT_EQ(_configurator._calleeTypeConfig.getCalleeType("", "", MpCall::TypeLegacyApp), MpCall::TypeLegacyApp);
    EXPECT_EQ(_configurator._calleeTypeConfig.getCalleeType("", "", MpCall::TypeVman), MpCall::TypeVman);
    EXPECT_EQ(_configurator._calleeTypeConfig.getCalleeType("", "", MpCall::TypeAcdApp), MpCall::TypeAcdApp);
    EXPECT_EQ(_configurator._calleeTypeConfig.getCalleeType("", "", MpCall::TypeH5), MpCall::TypeH5);
    EXPECT_EQ(_configurator._calleeTypeConfig.getCalleeType("", "", MpCall::TypeWechat), MpCall::TypeWechat);
    EXPECT_EQ(_configurator._calleeTypeConfig.getCalleeType("", "", MpCall::TypeYuv), MpCall::TypeYuv);
    EXPECT_EQ(_configurator._calleeTypeConfig.getCalleeType("100645", "", MpCall::TypeUnknown), MpCall::TypeApp);
    EXPECT_EQ(_configurator._calleeTypeConfig.getCalleeType("100645", "4", MpCall::TypeUnknown), MpCall::TypeApp);
}

TEST_F(ConfiguratorBaseCallTypeTest, DefaultLegacy)
{
    _app->setConfig("global.SipCall.IncomingCalleeType", "LegacyApp");
    _configurator._calleeTypeConfig.init(_app);
    EXPECT_EQ(_configurator._calleeTypeConfig.getCalleeType("", "", MpCall::TypeApp), MpCall::TypeLegacyApp);
    EXPECT_EQ(_configurator._calleeTypeConfig.getCalleeType("100645", "0", MpCall::TypeApp), MpCall::TypeLegacyApp);
    EXPECT_EQ(_configurator._calleeTypeConfig.getCalleeType("100645", "4", MpCall::TypeApp), MpCall::TypeLegacyApp);
    EXPECT_EQ(_configurator._calleeTypeConfig.getCalleeType("100000", "4", MpCall::TypeApp), MpCall::TypeLegacyApp);
}

TEST_F(ConfiguratorBaseCallTypeTest, DomainLegacy)
{
    _app->setConfig("global.SipCall.IncomingCalleeType.LegacyApp", "100645,200000");
    _configurator._calleeTypeConfig.init(_app);
    EXPECT_EQ(_configurator._calleeTypeConfig.getCalleeType("100645", "0", MpCall::TypeApp), MpCall::TypeLegacyApp);
    EXPECT_EQ(_configurator._calleeTypeConfig.getCalleeType("100645", "4", MpCall::TypeApp), MpCall::TypeLegacyApp);
    EXPECT_EQ(_configurator._calleeTypeConfig.getCalleeType("100000", "4", MpCall::TypeApp), MpCall::TypeApp);
}

TEST_F(ConfiguratorBaseCallTypeTest, DomainAppLegacy)
{
    _app->setConfig("global.SipCall.IncomingCalleeType.Sip", "200000,100645");
    _app->setConfig("global.SipCall.IncomingCalleeType.LegacyApp", "100645/4");
    _configurator._calleeTypeConfig.init(_app);
    EXPECT_EQ(_configurator._calleeTypeConfig.getCalleeType("100645", "0", MpCall::TypeApp), MpCall::TypeSip);
    EXPECT_EQ(_configurator._calleeTypeConfig.getCalleeType("100645", "4", MpCall::TypeApp), MpCall::TypeLegacyApp);
    EXPECT_EQ(_configurator._calleeTypeConfig.getCalleeType("100000", "4", MpCall::TypeApp), MpCall::TypeApp);
}
