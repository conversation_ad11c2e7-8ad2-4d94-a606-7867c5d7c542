//
// *****************************************************************************
// Copyright (c) 2025 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//

#include "gtest/gtest.h"
#include "Util/StatsTool.h"

TEST(StatsToolTest, add)
{
    SipMpCall::StatsTool stats;

    stats.add("test", -1);

    for (int i = 1; i <= 3; i++)
        stats.add("test", i);

    for (int i = 10; i <= 13; i++)
        stats.add("test", i);

    stats.add("test", 20);

    for (int i = 50; i <= 53; i++)
        stats.add("test", i);

    auto result = stats.getStats();
    EXPECT_EQ(result["test"], "count:13 ids:-1 1-3 10-13 20 50-53 ");
}
