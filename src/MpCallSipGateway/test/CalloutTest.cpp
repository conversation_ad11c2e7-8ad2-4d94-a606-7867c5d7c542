//
// *****************************************************************************
// Copyright (c) 2025 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//

#include "Mocks/MockApp.h"

#include "SessionLocator/SessionLocatorMock.h"
#include "ServiceRunner/RunnerManagerMock.h"
#include "SipLine/mock/SipLineMock.h"

#include "MpCallSip/MpCallRtpServerMock.h"

#include "MpCallSipGateway/MpCallSipGateway.h"
#include "MpCall/MpCallSipAgent.h"

using ::testing::_;
using ::testing::Eq;
using ::testing::Return;

class CalloutTestRunnerAllocHandle : public ServiceRunner::RunnerAllocHandle
{
public:
    bool getRunner(Common::String &name, Common::String &objectId, Common::Error &error) override
    {
        name = "RtpRunner";
        objectId = "RtpRunner";
        return true;
    }
};

class CalloutTest : public ModuleTest::MockServiceTest<SipMpCall::MpCallSipGateway>
{
public:
    Common::String appName() override
    {
        return "MpCallSipGateway.DC0.Main01";
    }

    Common::StrStrMap appConfigs() override
    {
        return {
            {"global.Main.Endpoints", "sudp"},
            {"global.SipCall.CoreNetId", "TestLine"},
        };
    }

    virtual Common::AppSchedulerPtr createService(const Common::ApplicationExPtr &app) override
    {
        _sipLine = new ::testing::NiceMock<SipMpCall::SipLineMock>();
        _sipLine->delegateToNice();
        _runnerManager = new ::testing::NiceMock<ServiceRunner::RunnerManagerMock>();
        _runnerManager->delegateToNice(new CalloutTestRunnerAllocHandle());

        return new SipMpCall::MpCallSipGateway(app, 0, _sipLine, _runnerManager);
    }

    void SetUp() override
    {
        ModuleTest::MockServiceTest<SipMpCall::MpCallSipGateway>::SetUp();
        _rtpRunner = AddServer<ModuleTest::RtpRunnerServerMockBase>("RtpRunner");
        Common::sleep(3000);
    }

    void TearDown() override
    {
        _rtpRunner = 0;
        ModuleTest::MockServiceTest<SipMpCall::MpCallSipGateway>::TearDown();
        _sipLine = 0;
        _runnerManager = 0;
    }

    SipMpCall::SipLineMockPtr _sipLine;
    ServiceRunner::RunnerManagerMockPtr _runnerManager;
    ModuleTest::RtpRunnerServerMockBasePtr _rtpRunner;
};

TEST_F(CalloutTest, Normal)
{
    SipMpCall::SipLineSessionListenerPtr _listener;

    EXPECT_CALL(*(_rtpRunner.get()), callout_begin(_, _, _, _, _)).WillOnce([](const Common::ServerCallPtr &__call, const Common::String &targetOid, const Common::String &monitorOid, const MpCallSip::RtpRunnerConfig &config, const MpCallSip::SdpParams &sdpParams) {
        EXPECT_STREQ(config.name.c_str(), "bob");
        EXPECT_EQ(config.domainId, 100645);
        EXPECT_EQ(config.appId, 0);
        EXPECT_STREQ(config.Udid.c_str(), "1234567890100645_0");
        EXPECT_STREQ(config.bindingUser.c_str(), "[username:<EMAIL>]");
        MpCallSip::RtpRunnerServer::callout_end(__call, true, "offer");
    });

    EXPECT_CALL(*_sipLine, SipCall(_, _, _, _, _, _)).WillOnce([&](const SipMpCall::SipLineSessionListenerPtr &listener, const std::string &sessId, const std::string &caller, const std::string &callee, const std::string &pcOfferSdp, const std::map<std::string, std::string> &extHdrs) {
        _listener = listener;
        return new ::testing::NiceMock<SipMpCall::SipLineSessionMock>();
    });

    MpCall::RoomConfig roomConfig;
    roomConfig.domainId = 100645;
    roomConfig.appId = 0;
    roomConfig.udId = "1234567890100645_0";

    MpCall::InviteConfig inviteConfig;
    inviteConfig.uui = "1234567890";
    inviteConfig.bindingUser = "[username:<EMAIL>]";

    MpCall::SipGatewayAgent agent = _app->createAgent("SipCall/1234567890");
    EXPECT_TRUE(agent.invitation("1234567890", "alice", "bob", roomConfig, inviteConfig));

    Common::sleep(1000);
}

TEST_F(CalloutTest, CompleteCallFlow)
{
    SipMpCall::SipLineSessionListenerPtr _listener;
    SipMpCall::SipLineSessionMockPtr _session;

    // 1. 呼叫发起阶段 - RTP Runner callout
    EXPECT_CALL(*(_rtpRunner.get()), callout_begin(_, _, _, _, _)).WillOnce([](const Common::ServerCallPtr &__call, const Common::String &targetOid, const Common::String &monitorOid, const MpCallSip::RtpRunnerConfig &config, const MpCallSip::SdpParams &sdpParams) {
        EXPECT_STREQ(config.name.c_str(), "bob");
        EXPECT_EQ(config.domainId, 100645);
        EXPECT_EQ(config.appId, 0);
        EXPECT_STREQ(config.Udid.c_str(), "1234567890100645_0");
        EXPECT_STREQ(config.bindingUser.c_str(), "[username:<EMAIL>]");
        MpCallSip::RtpRunnerServer::callout_end(__call, true, "v=0\r\no=- 1234567890 1234567890 IN IP4 *************\r\ns=-\r\nc=IN IP4 *************\r\nt=0 0\r\nm=audio 10000 RTP/AVP 0 8 101\r\na=rtpmap:0 PCMU/8000\r\na=rtpmap:8 PCMA/8000\r\na=rtpmap:101 telephone-event/8000\r\n");
    });

    // 2. SIP呼叫发起
    EXPECT_CALL(*_sipLine, SipCall(_, _, _, _, _, _)).WillOnce([&](const SipMpCall::SipLineSessionListenerPtr &listener, const std::string &sessId, const std::string &caller, const std::string &callee, const std::string &pcOfferSdp, const std::map<std::string, std::string> &extHdrs) {
        _listener = listener;
        _session = new ::testing::NiceMock<SipMpCall::SipLineSessionMock>();

        // 模拟SIP会话方法
        EXPECT_CALL(*_session, SipAlert(_)).WillRepeatedly(Return(true));
        EXPECT_CALL(*_session, SipAnswer(_, _)).WillRepeatedly(Return(true));
        EXPECT_CALL(*_session, SipAck(_)).WillRepeatedly(Return(true));
        EXPECT_CALL(*_session, SipTerm()).WillRepeatedly(Return(true));
        EXPECT_CALL(*_session, GetCallTermedReason(_, _)).WillRepeatedly([](std::string &phase, std::string &reason) {
            phase = "BYE";
            reason = "Normal Clearing";
            return true;
        });

        return _session;
    });

    MpCall::RoomConfig roomConfig;
    roomConfig.domainId = 100645;
    roomConfig.appId = 0;
    roomConfig.udId = "1234567890100645_0";

    MpCall::InviteConfig inviteConfig;
    inviteConfig.uui = "1234567890";
    inviteConfig.bindingUser = "[username:<EMAIL>]";

    MpCall::SipGatewayAgent agent = _app->createAgent("SipCall/1234567890");
    EXPECT_TRUE(agent.invitation("1234567890", "alice", "bob", roomConfig, inviteConfig));

    Common::sleep(500);

    // 3. 模拟呼叫振铃阶段
    if (_listener) {
        _listener->onCallAlerted("v=0\r\no=- 1234567890 1234567890 IN IP4 *************\r\ns=-\r\nc=IN IP4 *************\r\nt=0 0\r\nm=audio 10001 RTP/AVP 0 8 101\r\na=rtpmap:0 PCMU/8000\r\na=rtpmap:8 PCMA/8000\r\na=rtpmap:101 telephone-event/8000\r\n", 180, false, "SIP/2.0 180 Ringing");
    }

    Common::sleep(500);

    // 4. 模拟呼叫接听阶段
    if (_listener) {
        _listener->onCallAnswered("v=0\r\no=- 1234567890 1234567890 IN IP4 *************\r\ns=-\r\nc=IN IP4 *************\r\nt=0 0\r\nm=audio 10001 RTP/AVP 0 8 101\r\na=rtpmap:0 PCMU/8000\r\na=rtpmap:8 PCMA/8000\r\na=rtpmap:101 telephone-event/8000\r\n", 200, false, "SIP/2.0 200 OK");
    }

    Common::sleep(500);

    // 5. 模拟呼叫连接阶段
    if (_listener) {
        _listener->onCallConnected("v=0\r\no=- 1234567890 1234567890 IN IP4 *************\r\ns=-\r\nc=IN IP4 *************\r\nt=0 0\r\nm=audio 10001 RTP/AVP 0 8 101\r\na=rtpmap:0 PCMU/8000\r\na=rtpmap:8 PCMA/8000\r\na=rtpmap:101 telephone-event/8000\r\n", 200, false, "SIP/2.0 200 OK");
    }

    Common::sleep(1000);

    // 6. 模拟呼叫挂断阶段
    if (_listener) {
        _listener->onCallTerminated("", 200, false, "SIP/2.0 200 OK");
    }

    Common::sleep(500);
}

TEST_F(CalloutTest, CallRejected)
{
    SipMpCall::SipLineSessionListenerPtr _listener;
    SipMpCall::SipLineSessionMockPtr _session;

    // RTP Runner callout
    EXPECT_CALL(*(_rtpRunner.get()), callout_begin(_, _, _, _, _)).WillOnce([](const Common::ServerCallPtr &__call, const Common::String &targetOid, const Common::String &monitorOid, const MpCallSip::RtpRunnerConfig &config, const MpCallSip::SdpParams &sdpParams) {
        MpCallSip::RtpRunnerServer::callout_end(__call, true, "offer");
    });

    // SIP呼叫发起
    EXPECT_CALL(*_sipLine, SipCall(_, _, _, _, _, _)).WillOnce([&](const SipMpCall::SipLineSessionListenerPtr &listener, const std::string &sessId, const std::string &caller, const std::string &callee, const std::string &pcOfferSdp, const std::map<std::string, std::string> &extHdrs) {
        _listener = listener;
        _session = new ::testing::NiceMock<SipMpCall::SipLineSessionMock>();

        EXPECT_CALL(*_session, GetCallTermedReason(_, _)).WillRepeatedly([](std::string &phase, std::string &reason) {
            phase = "BYE";
            reason = "User Busy";
            return true;
        });

        return _session;
    });

    MpCall::RoomConfig roomConfig;
    roomConfig.domainId = 100645;
    roomConfig.appId = 0;
    roomConfig.udId = "1234567890100645_0";

    MpCall::InviteConfig inviteConfig;
    inviteConfig.uui = "1234567890";
    inviteConfig.bindingUser = "[username:<EMAIL>]";

    MpCall::SipGatewayAgent agent = _app->createAgent("SipCall/1234567890");
    EXPECT_TRUE(agent.invitation("1234567890", "alice", "bob", roomConfig, inviteConfig));

    Common::sleep(500);

    // 模拟呼叫被拒绝
    if (_listener) {
        _listener->onCallTerminated("", 486, false, "SIP/2.0 486 Busy Here");
    }

    Common::sleep(500);
}

TEST_F(CalloutTest, CallTimeout)
{
    SipMpCall::SipLineSessionListenerPtr _listener;
    SipMpCall::SipLineSessionMockPtr _session;

    // RTP Runner callout
    EXPECT_CALL(*(_rtpRunner.get()), callout_begin(_, _, _, _, _)).WillOnce([](const Common::ServerCallPtr &__call, const Common::String &targetOid, const Common::String &monitorOid, const MpCallSip::RtpRunnerConfig &config, const MpCallSip::SdpParams &sdpParams) {
        MpCallSip::RtpRunnerServer::callout_end(__call, true, "offer");
    });

    // SIP呼叫发起
    EXPECT_CALL(*_sipLine, SipCall(_, _, _, _, _, _)).WillOnce([&](const SipMpCall::SipLineSessionListenerPtr &listener, const std::string &sessId, const std::string &caller, const std::string &callee, const std::string &pcOfferSdp, const std::map<std::string, std::string> &extHdrs) {
        _listener = listener;
        _session = new ::testing::NiceMock<SipMpCall::SipLineSessionMock>();

        EXPECT_CALL(*_session, GetCallTermedReason(_, _)).WillRepeatedly([](std::string &phase, std::string &reason) {
            phase = "TIMEOUT";
            reason = "Request Timeout";
            return true;
        });

        return _session;
    });

    MpCall::RoomConfig roomConfig;
    roomConfig.domainId = 100645;
    roomConfig.appId = 0;
    roomConfig.udId = "1234567890100645_0";

    MpCall::InviteConfig inviteConfig;
    inviteConfig.uui = "1234567890";
    inviteConfig.bindingUser = "[username:<EMAIL>]";

    MpCall::SipGatewayAgent agent = _app->createAgent("SipCall/1234567890");
    EXPECT_TRUE(agent.invitation("1234567890", "alice", "bob", roomConfig, inviteConfig));

    Common::sleep(500);

    // 模拟呼叫超时
    if (_listener) {
        _listener->onCallTerminated("", 408, false, "SIP/2.0 408 Request Timeout");
    }

    Common::sleep(500);
}

TEST_F(CalloutTest, CallUpdate)
{
    SipMpCall::SipLineSessionListenerPtr _listener;
    SipMpCall::SipLineSessionMockPtr _session;

    // RTP Runner callout
    EXPECT_CALL(*(_rtpRunner.get()), callout_begin(_, _, _, _, _)).WillOnce([](const Common::ServerCallPtr &__call, const Common::String &targetOid, const Common::String &monitorOid, const MpCallSip::RtpRunnerConfig &config, const MpCallSip::SdpParams &sdpParams) {
        MpCallSip::RtpRunnerServer::callout_end(__call, true, "offer");
    });

    // SIP呼叫发起
    EXPECT_CALL(*_sipLine, SipCall(_, _, _, _, _, _)).WillOnce([&](const SipMpCall::SipLineSessionListenerPtr &listener, const std::string &sessId, const std::string &caller, const std::string &callee, const std::string &pcOfferSdp, const std::map<std::string, std::string> &extHdrs) {
        _listener = listener;
        _session = new ::testing::NiceMock<SipMpCall::SipLineSessionMock>();

        EXPECT_CALL(*_session, SipUpdateRsp(_)).WillRepeatedly(Return(true));
        EXPECT_CALL(*_session, SipAlert(_)).WillRepeatedly(Return(true));
        EXPECT_CALL(*_session, SipAnswer(_, _)).WillRepeatedly(Return(true));
        EXPECT_CALL(*_session, SipAck(_)).WillRepeatedly(Return(true));
        EXPECT_CALL(*_session, SipTerm()).WillRepeatedly(Return(true));
        EXPECT_CALL(*_session, GetCallTermedReason(_, _)).WillRepeatedly([](std::string &phase, std::string &reason) {
            phase = "BYE";
            reason = "Normal Clearing";
            return true;
        });

        return _session;
    });

    MpCall::RoomConfig roomConfig;
    roomConfig.domainId = 100645;
    roomConfig.appId = 0;
    roomConfig.udId = "1234567890100645_0";

    MpCall::InviteConfig inviteConfig;
    inviteConfig.uui = "1234567890";
    inviteConfig.bindingUser = "[username:<EMAIL>]";

    MpCall::SipGatewayAgent agent = _app->createAgent("SipCall/1234567890");
    EXPECT_TRUE(agent.invitation("1234567890", "alice", "bob", roomConfig, inviteConfig));

    Common::sleep(500);

    // 模拟呼叫振铃
    if (_listener) {
        _listener->onCallAlerted("", 180, false, "SIP/2.0 180 Ringing");
    }

    Common::sleep(500);

    // 模拟呼叫接听
    if (_listener) {
        _listener->onCallAnswered("", 200, false, "SIP/2.0 200 OK");
    }

    Common::sleep(500);

    // 模拟呼叫连接
    if (_listener) {
        _listener->onCallConnected("", 200, false, "SIP/2.0 200 OK");
    }

    Common::sleep(500);

    // 模拟呼叫更新请求
    if (_listener) {
        _listener->onCallRequestUpdate("v=0\r\no=- 1234567890 1234567890 IN IP4 *************\r\ns=-\r\nc=IN IP4 *************\r\nt=0 0\r\nm=audio 10001 RTP/AVP 0 8 101\r\na=rtpmap:0 PCMU/8000\r\na=rtpmap:8 PCMA/8000\r\na=rtpmap:101 telephone-event/8000\r\n", 200, false, "SIP/2.0 200 OK");
    }

    Common::sleep(500);

    // 模拟呼叫挂断
    if (_listener) {
        _listener->onCallTerminated("", 200, false, "SIP/2.0 200 OK");
    }

    Common::sleep(500);
}

TEST_F(CalloutTest, RtpRunnerError)
{
    // RTP Runner callout失败
    EXPECT_CALL(*(_rtpRunner.get()), callout_begin(_, _, _, _, _)).WillOnce([](const Common::ServerCallPtr &__call, const Common::String &targetOid, const Common::String &monitorOid, const MpCallSip::RtpRunnerConfig &config, const MpCallSip::SdpParams &sdpParams) {
        MpCallSip::RtpRunnerServer::callout_end(__call, false, "RTP Runner allocation failed");
    });

    MpCall::RoomConfig roomConfig;
    roomConfig.domainId = 100645;
    roomConfig.appId = 0;
    roomConfig.udId = "1234567890100645_0";

    MpCall::InviteConfig inviteConfig;
    inviteConfig.uui = "1234567890";
    inviteConfig.bindingUser = "[username:<EMAIL>]";

    MpCall::SipGatewayAgent agent = _app->createAgent("SipCall/1234567890");
    EXPECT_FALSE(agent.invitation("1234567890", "alice", "bob", roomConfig, inviteConfig));

    Common::sleep(500);
}

TEST_F(CalloutTest, SipCallFailed)
{
    // RTP Runner callout成功
    EXPECT_CALL(*(_rtpRunner.get()), callout_begin(_, _, _, _, _)).WillOnce([](const Common::ServerCallPtr &__call, const Common::String &targetOid, const Common::String &monitorOid, const MpCallSip::RtpRunnerConfig &config, const MpCallSip::SdpParams &sdpParams) {
        MpCallSip::RtpRunnerServer::callout_end(__call, true, "offer");
    });

    // SIP呼叫发起失败
    EXPECT_CALL(*_sipLine, SipCall(_, _, _, _, _, _)).WillOnce(Return(nullptr));

    MpCall::RoomConfig roomConfig;
    roomConfig.domainId = 100645;
    roomConfig.appId = 0;
    roomConfig.udId = "1234567890100645_0";

    MpCall::InviteConfig inviteConfig;
    inviteConfig.uui = "1234567890";
    inviteConfig.bindingUser = "[username:<EMAIL>]";

    MpCall::SipGatewayAgent agent = _app->createAgent("SipCall/1234567890");
    EXPECT_FALSE(agent.invitation("1234567890", "alice", "bob", roomConfig, inviteConfig));

    Common::sleep(500);
}
