#pragma once

#include "Common/CommonEx.h"
#include "SipLine/SipLine.h"
#include "gmock/gmock.h"
namespace ModuleTest
{

class SipLineMock;
class SipCallSessionMock;
typedef Common::Handle<SipLineMock> SipLineMockPtr;
typedef Common::Handle<SipCallSessionMock> SipCallSessionMockPtr;


class SipCallSessionMock : public SipMpCall::SipLineSession
{
public:
    SipCallSessionMock(const SipMpCall::SipLineSessionListenerPtr &listener = nullptr)
        : _listener(listener)
    {}

    MOCK_METHOD(void, setSessId, (const std::string &sessId), (override));
    MOCK_METHOD(vector<std::string>, getCallIds, (), (const override));
    MOCK_METHOD(std::string, getCallId, (), (const override));
    MOCK_METHOD(bool, SipSetContactUri, (const std::string &pcContactUri), (override));
    MOCK_METHOD(bool, SipAlert, (const std::string &pcAnswerSdp), (override));
    MOCK_METHOD(bool, SipAnswer, (const std::string &pcAnswerSdp, const SipClient::SipCallExtHdrs &pcExtHdrs), (override));
    MOCK_METHOD(bool, SipTerm, (), (override));
    MOCK_METHOD(bool, SipUpdate, (const std::string &pcOfferSdp), (override));
    MOCK_METHOD(bool, SipUpdateRsp, (const std::string &pcAnswerSdp), (override));
    MOCK_METHOD(bool, SipAck, (const std::string &pcAnswerSdp), (override));
    MOCK_METHOD(bool, GetCalledUri, (std::string &ppcDispName, std::string &ppcUri), (override));
    MOCK_METHOD(bool, GetPeerUri, (std::string &ppcDispName, std::string &ppcUri), (override));
    MOCK_METHOD(bool, GetCallTermedReason, (std::string &ppcSipPhase, std::string &ppcReason), (override));

public:
    SipMpCall::SipLineSessionListenerPtr _listener;
};

class SipLineMock : public SipMpCall::SipLine
{
public:
    static SipMpCall::SipLinePtr create(const SipMpCall::SipLineListnerPtr &listener, const Common::ApplicationPtr &app)
    {
        SipMpCall::SipLinePtr sipLine = new SipLineMock();
        return sipLine;
    }

    virtual SipMpCall::SipLineSessionPtr SipCall(const SipMpCall::SipLineSessionListenerPtr &listener, const std::string &sessId, const std::string &caller, const std::string &callee, const std::string &pcOfferSdp, const std::map<std::string, std::string> &extHdrs) override
    {
        if (!_sipCallSessionMockPtr)
            _sipCallSessionMockPtr = new SipCallSessionMock(listener);
        return _sipCallSessionMockPtr;
    }

    //MOCK_METHOD(bool, isReady, (Common::String &reason), (override));
    bool isReady(Common::String &reason) override  {return true;}
    MOCK_METHOD(void, close, (), (override));
    MOCK_METHOD(void, schd, (), (override));
    MOCK_METHOD(void, updateConfigs, (), (override));
    MOCK_METHOD(int, getFreePercent, (), (override));
    MOCK_METHOD(vector<std::string>, getCallId, (const std::string &sessId), (override));
    MOCK_METHOD(bool, getSessStatus, (const std::string &sessId, (std::map<std::string, SessStatus>) &statuses), (override));
    MOCK_METHOD(std::string, getContactUri, (const std::string &callId, const std::string &userpart), (override));
    MOCK_METHOD(void, setKeepTermedStatus, (bool keepTermedSession), (override));

    static std::string getNumber(const std::string &uri)
    {
        return "";
    }

    void deletgateToNice()
    {
        ON_CALL(*this, getFreePercent).WillByDefault([this]() { return 100; });
    }

public:
    SipCallSessionMockPtr _sipCallSessionMockPtr;
};
}