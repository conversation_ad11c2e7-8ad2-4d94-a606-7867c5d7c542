//
// *****************************************************************************
// Copyright (c) 2025 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//

#include "Common/CommonEx.h"
#include "MpCall/MpCallPub.h"
#include "MpCallSipGateway/SipSessionConfiguratorBase.h"
#include "gtest/gtest.h"

class CalleeTypeConfigTest : public ::testing::Test
{
protected:
    void SetUp() override
    {
        _app = Common::ApplicationEx::create("CalleeTypeConfigTest");
        _app->activate();
    }

    void TearDown() override
    {
        _app->indShutdown();
        while (!_app->isShutdown())
            Common::sleep(10);
    }

    Common::ApplicationExPtr _app;
    SipMpCall::SipSessionConfiguratorBase _configurator;
};

// 测试所有支持的用户类型配置
TEST_F(CalleeTypeConfigTest, AllUserTypesConfig)
{
    // 为每种用户类型设置配置
    _app->setConfig("global.SipCall.IncomingCalleeType.App", "100001");
    _app->setConfig("global.SipCall.IncomingCalleeType.Sip", "100002");
    _app->setConfig("global.SipCall.IncomingCalleeType.LegacyApp", "100003");
    _app->setConfig("global.SipCall.IncomingCalleeType.Vman", "100004");
    _app->setConfig("global.SipCall.IncomingCalleeType.AcdApp", "100005");
    _app->setConfig("global.SipCall.IncomingCalleeType.H5", "100006");
    _app->setConfig("global.SipCall.IncomingCalleeType.WeChat", "100007");
    _app->setConfig("global.SipCall.IncomingCalleeType.Yuv", "100008");
    _app->setConfig("global.SipCall.IncomingCalleeType.Ivvr", "100009");

    _configurator._calleeTypeConfig.init(_app);

    // 验证每种类型的配置
    EXPECT_EQ(_configurator._calleeTypeConfig.getCalleeType("100001", "", MpCall::TypeUnknown), MpCall::TypeApp);
    EXPECT_EQ(_configurator._calleeTypeConfig.getCalleeType("100002", "", MpCall::TypeUnknown), MpCall::TypeSip);
    EXPECT_EQ(_configurator._calleeTypeConfig.getCalleeType("100003", "", MpCall::TypeUnknown), MpCall::TypeLegacyApp);
    EXPECT_EQ(_configurator._calleeTypeConfig.getCalleeType("100004", "", MpCall::TypeUnknown), MpCall::TypeVman);
    EXPECT_EQ(_configurator._calleeTypeConfig.getCalleeType("100005", "", MpCall::TypeUnknown), MpCall::TypeAcdApp);
    EXPECT_EQ(_configurator._calleeTypeConfig.getCalleeType("100006", "", MpCall::TypeUnknown), MpCall::TypeH5);
    EXPECT_EQ(_configurator._calleeTypeConfig.getCalleeType("100007", "", MpCall::TypeUnknown), MpCall::TypeWechat);
    EXPECT_EQ(_configurator._calleeTypeConfig.getCalleeType("100008", "", MpCall::TypeUnknown), MpCall::TypeYuv);
    EXPECT_EQ(_configurator._calleeTypeConfig.getCalleeType("100009", "", MpCall::TypeUnknown), MpCall::TypeIvvr);
}

// 测试默认配置
TEST_F(CalleeTypeConfigTest, DefaultConfig)
{
    _configurator._calleeTypeConfig.init(_app);

    // 没有配置时，应该返回默认类型
    EXPECT_EQ(_configurator._calleeTypeConfig.getCalleeType("", "", MpCall::TypeUnknown), MpCall::TypeApp);
    EXPECT_EQ(_configurator._calleeTypeConfig.getCalleeType("100645", "", MpCall::TypeUnknown), MpCall::TypeApp);
    EXPECT_EQ(_configurator._calleeTypeConfig.getCalleeType("100645", "4", MpCall::TypeUnknown), MpCall::TypeApp);
}

// 测试全局默认配置
TEST_F(CalleeTypeConfigTest, GlobalDefaultConfig)
{
    // 设置全局默认配置为 Sip
    _app->setConfig("global.SipCall.IncomingCalleeType", "Sip");
    _configurator._calleeTypeConfig.init(_app);

    EXPECT_EQ(_configurator._calleeTypeConfig.getCalleeType("", "", MpCall::TypeUnknown), MpCall::TypeSip);
    EXPECT_EQ(_configurator._calleeTypeConfig.getCalleeType("100645", "", MpCall::TypeUnknown), MpCall::TypeSip);
    EXPECT_EQ(_configurator._calleeTypeConfig.getCalleeType("100645", "4", MpCall::TypeUnknown), MpCall::TypeSip);
}

// 测试域级别配置
TEST_F(CalleeTypeConfigTest, DomainLevelConfig)
{
    // 设置域级别配置
    _app->setConfig("global.SipCall.IncomingCalleeType.LegacyApp", "100645,200000");
    _configurator._calleeTypeConfig.init(_app);

    // 匹配的域应该返回配置的类型
    EXPECT_EQ(_configurator._calleeTypeConfig.getCalleeType("100645", "", MpCall::TypeUnknown), MpCall::TypeLegacyApp);
    EXPECT_EQ(_configurator._calleeTypeConfig.getCalleeType("100645", "4", MpCall::TypeUnknown), MpCall::TypeLegacyApp);
    EXPECT_EQ(_configurator._calleeTypeConfig.getCalleeType("200000", "", MpCall::TypeUnknown), MpCall::TypeLegacyApp);

    // 不匹配的域应该返回默认类型
    EXPECT_EQ(_configurator._calleeTypeConfig.getCalleeType("300000", "", MpCall::TypeUnknown), MpCall::TypeApp);
}

// 测试域/应用级别配置
TEST_F(CalleeTypeConfigTest, DomainAppLevelConfig)
{
    // 设置域/应用级别配置
    _app->setConfig("global.SipCall.IncomingCalleeType.Sip", "100645,200000");
    _app->setConfig("global.SipCall.IncomingCalleeType.LegacyApp", "100645/4");
    _configurator._calleeTypeConfig.init(_app);

    // 匹配的域/应用组合应该返回配置的类型
    EXPECT_EQ(_configurator._calleeTypeConfig.getCalleeType("100645", "4", MpCall::TypeUnknown), MpCall::TypeLegacyApp);

    // 匹配域但不匹配应用应该返回域级别的配置
    EXPECT_EQ(_configurator._calleeTypeConfig.getCalleeType("100645", "0", MpCall::TypeUnknown), MpCall::TypeSip);
    EXPECT_EQ(_configurator._calleeTypeConfig.getCalleeType("200000", "", MpCall::TypeUnknown), MpCall::TypeSip);

    // 不匹配的域应该返回默认类型
    EXPECT_EQ(_configurator._calleeTypeConfig.getCalleeType("300000", "", MpCall::TypeUnknown), MpCall::TypeApp);
}

// 测试无效配置
TEST_F(CalleeTypeConfigTest, InvalidConfig)
{
    // 设置无效的配置
    _app->setConfig("global.SipCall.IncomingCalleeType.InvalidType", "100645");
    _app->setConfig("global.SipCall.IncomingCalleeType", "InvalidType");
    _configurator._calleeTypeConfig.init(_app);

    // 应该返回默认类型
    EXPECT_EQ(_configurator._calleeTypeConfig.getCalleeType("100645", "", MpCall::TypeUnknown), MpCall::TypeApp);
    EXPECT_EQ(_configurator._calleeTypeConfig.getCalleeType("", "", MpCall::TypeUnknown), MpCall::TypeApp);
}

// 测试空配置
TEST_F(CalleeTypeConfigTest, EmptyConfig)
{
    // 设置空的配置
    _app->setConfig("global.SipCall.IncomingCalleeType", "");
    _app->setConfig("global.SipCall.IncomingCalleeType.Sip", "");
    _configurator._calleeTypeConfig.init(_app);

    // 应该返回默认类型
    EXPECT_EQ(_configurator._calleeTypeConfig.getCalleeType("100645", "", MpCall::TypeUnknown), MpCall::TypeApp);
}

// 测试配置优先级
TEST_F(CalleeTypeConfigTest, ConfigPriority)
{
    // 设置多层配置
    _app->setConfig("global.SipCall.IncomingCalleeType", "Sip");  // 全局默认
    _app->setConfig("global.SipCall.IncomingCalleeType.LegacyApp", "100645");  // 域级别
    _app->setConfig("global.SipCall.IncomingCalleeType.App", "100645/4");  // 域/应用级别

    _configurator._calleeTypeConfig.init(_app);

    // 域/应用级别优先级最高
    EXPECT_EQ(_configurator._calleeTypeConfig.getCalleeType("100645", "4", MpCall::TypeUnknown), MpCall::TypeApp);

    // 域级别次之
    EXPECT_EQ(_configurator._calleeTypeConfig.getCalleeType("100645", "0", MpCall::TypeUnknown), MpCall::TypeLegacyApp);

    // 全局默认最低
    EXPECT_EQ(_configurator._calleeTypeConfig.getCalleeType("200000", "", MpCall::TypeUnknown), MpCall::TypeSip);
}

// 测试大小写不敏感
TEST_F(CalleeTypeConfigTest, CaseInsensitive)
{
    // 使用小写配置
    _app->setConfig("global.SipCall.IncomingCalleeType", "sip");
    _app->setConfig("global.SipCall.IncomingCalleeType.legacyapp", "100645");
    _app->setConfig("global.SipCall.IncomingCalleeType.app", "100645/4");

    _configurator._calleeTypeConfig.init(_app);

    EXPECT_EQ(_configurator._calleeTypeConfig.getCalleeType("", "", MpCall::TypeUnknown), MpCall::TypeSip);
    EXPECT_EQ(_configurator._calleeTypeConfig.getCalleeType("100645", "", MpCall::TypeUnknown), MpCall::TypeLegacyApp);
    EXPECT_EQ(_configurator._calleeTypeConfig.getCalleeType("100645", "4", MpCall::TypeUnknown), MpCall::TypeApp);
}

// 测试多个域配置
TEST_F(CalleeTypeConfigTest, MultipleDomains)
{
    // 设置多个域配置
    _app->setConfig("global.SipCall.IncomingCalleeType.Sip", "100645,200000,300000");
    _app->setConfig("global.SipCall.IncomingCalleeType.LegacyApp", "400000,500000");

    _configurator._calleeTypeConfig.init(_app);

    // 验证所有配置的域
    EXPECT_EQ(_configurator._calleeTypeConfig.getCalleeType("100645", "", MpCall::TypeUnknown), MpCall::TypeSip);
    EXPECT_EQ(_configurator._calleeTypeConfig.getCalleeType("200000", "", MpCall::TypeUnknown), MpCall::TypeSip);
    EXPECT_EQ(_configurator._calleeTypeConfig.getCalleeType("300000", "", MpCall::TypeUnknown), MpCall::TypeSip);
    EXPECT_EQ(_configurator._calleeTypeConfig.getCalleeType("400000", "", MpCall::TypeUnknown), MpCall::TypeLegacyApp);
    EXPECT_EQ(_configurator._calleeTypeConfig.getCalleeType("500000", "", MpCall::TypeUnknown), MpCall::TypeLegacyApp);

    // 未配置的域应该返回默认类型
    EXPECT_EQ(_configurator._calleeTypeConfig.getCalleeType("600000", "", MpCall::TypeUnknown), MpCall::TypeApp);
}

// 测试配置更新
TEST_F(CalleeTypeConfigTest, ConfigUpdate)
{
    // 初始配置
    _app->setConfig("global.SipCall.IncomingCalleeType", "Sip");
    _configurator._calleeTypeConfig.init(_app);
    EXPECT_EQ(_configurator._calleeTypeConfig.getCalleeType("100645", "", MpCall::TypeUnknown), MpCall::TypeSip);

    // 更新配置
    _app->setConfig("global.SipCall.IncomingCalleeType", "LegacyApp");
    _configurator._calleeTypeConfig.init(_app);
    EXPECT_EQ(_configurator._calleeTypeConfig.getCalleeType("100645", "", MpCall::TypeUnknown), MpCall::TypeLegacyApp);
}

// 测试所有用户类型的大小写变体
TEST_F(CalleeTypeConfigTest, AllUserTypesCaseVariants)
{
    // 测试所有用户类型的大小写变体（只使用parseCalleeType支持的大小写格式）
    _app->setConfig("global.SipCall.IncomingCalleeType.App", "100001");
    _app->setConfig("global.SipCall.IncomingCalleeType.app", "100002");

    _app->setConfig("global.SipCall.IncomingCalleeType.Sip", "100003");
    _app->setConfig("global.SipCall.IncomingCalleeType.sip", "100004");

    _app->setConfig("global.SipCall.IncomingCalleeType.LegacyApp", "100005");
    _app->setConfig("global.SipCall.IncomingCalleeType.legacyapp", "100006");

    _app->setConfig("global.SipCall.IncomingCalleeType.Vman", "100007");
    _app->setConfig("global.SipCall.IncomingCalleeType.vman", "100008");

    _app->setConfig("global.SipCall.IncomingCalleeType.AcdApp", "100009");
    _app->setConfig("global.SipCall.IncomingCalleeType.acdapp", "100010");

    _app->setConfig("global.SipCall.IncomingCalleeType.H5", "100011");
    _app->setConfig("global.SipCall.IncomingCalleeType.h5", "100012");

    _app->setConfig("global.SipCall.IncomingCalleeType.WeChat", "100013");
    _app->setConfig("global.SipCall.IncomingCalleeType.wechat", "100014");

    _app->setConfig("global.SipCall.IncomingCalleeType.Yuv", "100015");
    _app->setConfig("global.SipCall.IncomingCalleeType.yuv", "100016");

    _app->setConfig("global.SipCall.IncomingCalleeType.Ivvr", "100017");
    _app->setConfig("global.SipCall.IncomingCalleeType.ivvr", "100018");

    _configurator._calleeTypeConfig.init(_app);

    // 验证所有大小写变体都能正确解析
    EXPECT_EQ(_configurator._calleeTypeConfig.getCalleeType("100001", "", MpCall::TypeUnknown), MpCall::TypeApp);
    EXPECT_EQ(_configurator._calleeTypeConfig.getCalleeType("100002", "", MpCall::TypeUnknown), MpCall::TypeApp);

    EXPECT_EQ(_configurator._calleeTypeConfig.getCalleeType("100003", "", MpCall::TypeUnknown), MpCall::TypeSip);
    EXPECT_EQ(_configurator._calleeTypeConfig.getCalleeType("100004", "", MpCall::TypeUnknown), MpCall::TypeSip);

    EXPECT_EQ(_configurator._calleeTypeConfig.getCalleeType("100005", "", MpCall::TypeUnknown), MpCall::TypeLegacyApp);
    EXPECT_EQ(_configurator._calleeTypeConfig.getCalleeType("100006", "", MpCall::TypeUnknown), MpCall::TypeLegacyApp);

    EXPECT_EQ(_configurator._calleeTypeConfig.getCalleeType("100007", "", MpCall::TypeUnknown), MpCall::TypeVman);
    EXPECT_EQ(_configurator._calleeTypeConfig.getCalleeType("100008", "", MpCall::TypeUnknown), MpCall::TypeVman);

    EXPECT_EQ(_configurator._calleeTypeConfig.getCalleeType("100009", "", MpCall::TypeUnknown), MpCall::TypeAcdApp);
    EXPECT_EQ(_configurator._calleeTypeConfig.getCalleeType("100010", "", MpCall::TypeUnknown), MpCall::TypeAcdApp);

    EXPECT_EQ(_configurator._calleeTypeConfig.getCalleeType("100011", "", MpCall::TypeUnknown), MpCall::TypeH5);
    EXPECT_EQ(_configurator._calleeTypeConfig.getCalleeType("100012", "", MpCall::TypeUnknown), MpCall::TypeH5);

    EXPECT_EQ(_configurator._calleeTypeConfig.getCalleeType("100013", "", MpCall::TypeUnknown), MpCall::TypeWechat);
    EXPECT_EQ(_configurator._calleeTypeConfig.getCalleeType("100014", "", MpCall::TypeUnknown), MpCall::TypeWechat);

    EXPECT_EQ(_configurator._calleeTypeConfig.getCalleeType("100015", "", MpCall::TypeUnknown), MpCall::TypeYuv);
    EXPECT_EQ(_configurator._calleeTypeConfig.getCalleeType("100016", "", MpCall::TypeUnknown), MpCall::TypeYuv);

    EXPECT_EQ(_configurator._calleeTypeConfig.getCalleeType("100017", "", MpCall::TypeUnknown), MpCall::TypeIvvr);
    EXPECT_EQ(_configurator._calleeTypeConfig.getCalleeType("100018", "", MpCall::TypeUnknown), MpCall::TypeIvvr);
}

// 测试不支持的大小写格式
TEST_F(CalleeTypeConfigTest, UnsupportedCaseVariants)
{
    // 测试不支持的大小写格式（应该被忽略，返回默认类型）
    _app->setConfig("global.SipCall.IncomingCalleeType.APP", "100001");
    _app->setConfig("global.SipCall.IncomingCalleeType.SIP", "100002");
    _app->setConfig("global.SipCall.IncomingCalleeType.LEGACYAPP", "100003");
    _app->setConfig("global.SipCall.IncomingCalleeType.VMAN", "100004");
    _app->setConfig("global.SipCall.IncomingCalleeType.ACDAPP", "100005");
    _app->setConfig("global.SipCall.IncomingCalleeType.WECHAT", "100007");
    _app->setConfig("global.SipCall.IncomingCalleeType.YUV", "100008");
    _app->setConfig("global.SipCall.IncomingCalleeType.IVVR", "100009");

    _configurator._calleeTypeConfig.init(_app);

    // 不支持的大小写格式应该返回默认类型
    EXPECT_EQ(_configurator._calleeTypeConfig.getCalleeType("100001", "", MpCall::TypeUnknown), MpCall::TypeApp);
    EXPECT_EQ(_configurator._calleeTypeConfig.getCalleeType("100002", "", MpCall::TypeUnknown), MpCall::TypeApp);
    EXPECT_EQ(_configurator._calleeTypeConfig.getCalleeType("100003", "", MpCall::TypeUnknown), MpCall::TypeApp);
    EXPECT_EQ(_configurator._calleeTypeConfig.getCalleeType("100004", "", MpCall::TypeUnknown), MpCall::TypeApp);
    EXPECT_EQ(_configurator._calleeTypeConfig.getCalleeType("100005", "", MpCall::TypeUnknown), MpCall::TypeApp);
    EXPECT_EQ(_configurator._calleeTypeConfig.getCalleeType("100007", "", MpCall::TypeUnknown), MpCall::TypeApp);
    EXPECT_EQ(_configurator._calleeTypeConfig.getCalleeType("100008", "", MpCall::TypeUnknown), MpCall::TypeApp);
    EXPECT_EQ(_configurator._calleeTypeConfig.getCalleeType("100009", "", MpCall::TypeUnknown), MpCall::TypeApp);
}