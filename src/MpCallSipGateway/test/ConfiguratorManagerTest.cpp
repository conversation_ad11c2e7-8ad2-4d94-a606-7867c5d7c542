//
// *****************************************************************************
// Copyright (c) 2025 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//

#include "Common/CommonEx.h"
#include "Common/TypesPub.h"
#include "MpCallSipGateway/SipSessionConfigurator.h"
#include "MpCallSipGateway/SipSessionConfiguratorBase.h"
#include "MpCallSipGateway/SipSessionController.h"
#include "gtest/gtest.h"

class ConfiguratorManagerTest : public ::testing::Test
{
protected:
    void SetUp(const Common::String &appname, const Common::StrStrMap &configs)
    {
        app = Common::ApplicationEx::create(appname, "", 0, configs);
        app->activate();
    }

    void TearDown()
    {
        if (!app)
            return;

        app->indShutdown();
        while (!app->isShutdown())
            Common::sleep(100);
    }

    Common::ApplicationExPtr app;
};

TEST_F(ConfiguratorManagerTest, Base)
{
    SetUp("MpCallSipGateway", Common::StrStrMap());
    auto configuratorManager = SipMpCall::SipSessionConfiguratorManager::create(app);
    ASSERT_NE(configuratorManager, nullptr);
    auto configurator = configuratorManager->getConfigurator();
    ASSERT_NE(configurator, nullptr);
    ASSERT_NE(Common::Handle<SipMpCall::SipSessionConfiguratorBase>::dynamicCast(configurator), nullptr);
}

TEST_F(ConfiguratorManagerTest, IncomingDomainIdDomain)
{
    Common::StrStrMap configs = {
        {"global.SipCall.IncomingDomainId", "100645"},
    };
    SetUp("MpCallSipGateway", configs);
    auto configuratorManager = SipMpCall::SipSessionConfiguratorManager::create(app);
    ASSERT_NE(configuratorManager, nullptr);
    auto configurator = configuratorManager->getConfigurator();
    ASSERT_NE(configurator, nullptr);
    ASSERT_NE(Common::Handle<SipMpCall::SipSessionConfiguratorDomain>::dynamicCast(configurator), nullptr);

    SipMpCall::SipSessionConfigurator::CallParams inParams;
    inParams.sessId = "12345";
    inParams.caller = "caller";
    inParams.callee = "callee";
    inParams.inviteConfig.uui = "test_uui";

    SipMpCall::SipSessionConfigurator::CallParams outParams;
    SipMpCall::SipSessionConfigurator::AppParams appParams;
    EXPECT_TRUE(configurator->onIncomingCall(inParams, outParams, appParams));
    EXPECT_EQ(appParams.domainId, "100645");
    EXPECT_EQ(appParams.appId, "0");
}

TEST_F(ConfiguratorManagerTest, IncomingDomainIdDomainApp)
{
    Common::StrStrMap configs = {
        {"global.SipCall.IncomingDomainId", "100645/4"},
    };
    SetUp("MpCallSipGateway", configs);
    auto configuratorManager = SipMpCall::SipSessionConfiguratorManager::create(app);
    ASSERT_NE(configuratorManager, nullptr);
    auto configurator = configuratorManager->getConfigurator();
    ASSERT_NE(configurator, nullptr);
    ASSERT_NE(Common::Handle<SipMpCall::SipSessionConfiguratorDomain>::dynamicCast(configurator), nullptr);

    SipMpCall::SipSessionConfigurator::CallParams inParams;
    inParams.sessId = "12345";
    inParams.caller = "caller";
    inParams.callee = "callee";
    inParams.inviteConfig.uui = "test_uui";

    SipMpCall::SipSessionConfigurator::CallParams outParams;
    SipMpCall::SipSessionConfigurator::AppParams appParams;
    EXPECT_TRUE(configurator->onIncomingCall(inParams, outParams, appParams));
    EXPECT_EQ(appParams.domainId, "100645");
    EXPECT_EQ(appParams.appId, "4");
}

TEST_F(ConfiguratorManagerTest, IncomingDomainIdCalleePrefix)
{
    Common::StrStrMap configs = {
        {"global.SipCall.IncomingDomainId", "CalleePrefix"},
    };
    SetUp("MpCallSipGateway", configs);
    auto configuratorManager = SipMpCall::SipSessionConfiguratorManager::create(app);
    ASSERT_NE(configuratorManager, nullptr);
    auto configurator = configuratorManager->getConfigurator();
    ASSERT_NE(configurator, nullptr);
    ASSERT_NE(Common::Handle<SipMpCall::SipSessionConfiguratorCalleePrefix>::dynamicCast(configurator), nullptr);

    SipMpCall::SipSessionConfigurator::CallParams inParams;
    inParams.sessId = "12345";
    inParams.caller = "caller";
    inParams.callee = "123456callee";
    inParams.inviteConfig.uui = "test_uui";

    SipMpCall::SipSessionConfigurator::CallParams outParams;
    SipMpCall::SipSessionConfigurator::AppParams appParams;
    EXPECT_TRUE(configurator->onIncomingCall(inParams, outParams, appParams));
    EXPECT_STREQ(appParams.domainId.c_str(), "123456");
    EXPECT_STREQ(appParams.appId.c_str(), "0");
    EXPECT_STREQ(outParams.callee.c_str(), "callee");
}

TEST_F(ConfiguratorManagerTest, IncomingDomainIdAbnormal)
{
    Common::StrStrMap configs = {
        {"global.SipCall.IncomingDomainId", "Hello"},
    };
    SetUp("MpCallSipGateway", configs);
    auto configuratorManager = SipMpCall::SipSessionConfiguratorManager::create(app);
    ASSERT_NE(configuratorManager, nullptr);
    auto configurator = configuratorManager->getConfigurator();
    ASSERT_NE(configurator, nullptr);
    ASSERT_NE(Common::Handle<SipMpCall::SipSessionConfiguratorBase>::dynamicCast(configurator), nullptr);
}

TEST_F(ConfiguratorManagerTest, IncomingDomainIdAbnormal2)
{
    Common::StrStrMap configs = {
        {"global.SipCall.IncomingDomainId", "100645/-2"},
    };
    SetUp("MpCallSipGateway", configs);
    auto configuratorManager = SipMpCall::SipSessionConfiguratorManager::create(app);
    ASSERT_NE(configuratorManager, nullptr);
    auto configurator = configuratorManager->getConfigurator();
    ASSERT_NE(configurator, nullptr);
    ASSERT_NE(Common::Handle<SipMpCall::SipSessionConfiguratorBase>::dynamicCast(configurator), nullptr);
}

TEST_F(ConfiguratorManagerTest, EbService)
{
    SetUp("jrtcMpCallEbSipGateway", Common::StrStrMap());
    auto configuratorManager = SipMpCall::SipSessionConfiguratorManager::create(app);
    ASSERT_NE(configuratorManager, nullptr);
    auto configurator = configuratorManager->getConfigurator();
    ASSERT_NE(configurator, nullptr);
    ASSERT_NE(Common::Handle<SipMpCall::SipSessionController>::dynamicCast(configurator), nullptr);
}
