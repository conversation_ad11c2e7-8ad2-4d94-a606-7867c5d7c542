//
// *****************************************************************************
// Copyright (c) 2023 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//
// Created on 2023/5/22 by <PERSON>
//

#pragma once

#include <condition_variable>
#include <mutex>

#include "Common/Common.h"
#include "MpCall/MpCallPub.h"
#include "SipSessionConfigurator.h"
#include "SipAdapter/SipCallInterface.h"
#include "SessionLocator/SessionLocator.h"
#include "MpCall/MpCallSipServer.h"
#include "MpCall/MpCallSessionAgent.h"
#include "MpCallSip/MpCallRtpAgent.h"
#include "MpCallSip/MpCallRtpServer.h"
#include "ServiceRunner/RunnerManager.h"
#include "SipLine/SipLine.h"
#include "SipSession.h"
#include "EventCollector/EventCollector.h"
#include "SipEvent/EventSipPub.h"
#include "SipEventNotify.h"

namespace SipMpCall
{

class SipPhoneSession : public SipSession, public SipLineSessionListener, public SessionLocator::Session, public MpCall::SipGatewayServer, public MpCallSip::RtpRunnerMonitorServer, public ServiceRunner::RunnerManagerItemListener
{
public:
    enum SessionState
    {
        StateIdle = 0,
        StateIncoming,
        StateOutgoing,
        StateConnecting,
        StateTalking,
        StateTerming,
        StateTermed
    };

    static Common::String to_string(enum SessionState state)
    {
        Common::String __stateMain[] = {"Idle", "Incoming", "Outgoing", "Connecting", "Talking", "Terming", "Termed"};
        return __stateMain[state];
    }

    enum UpdateState
    {
        UpdateStateIdle = 0,
        UpdateStateIncoming,
        UpdateStateOutgoing
    };

    static Common::String to_string(enum UpdateState state)
    {
        Common::String __stateUpdate[] = {"UpdateIdle", "UpdateIncoming", "UpdateOutgoing"};
        return __stateUpdate[state];
    }

    enum RtpState
    {
        RtpStateIdle = 0,
        RtpStateStandby,
        RtpStateTalking,
        RtpStateFailed
    };

    static Common::String to_string(enum RtpState state)
    {
        Common::String __stateDesc[] = {"Idle", "Standby", "Talking", "Failed"};
        return __stateDesc[state];
    }

public:
    SipPhoneSession(const Common::ApplicationPtr &app, const ServiceRunner::RunnerManagerPtr &runnerManager, const SipLinePtr &sipLine, const SipSessionConfiguratorPtr &configurator, const Common::String &id, const JsmClient::EventCollectorPtr &eventCollector = nullptr)
        : _startTimeMs(Common::getCurTimeMs())
        , _termedTimeMs(0)
        , _callout(true)
        , _sessState(StateIdle)
        , _updateState(UpdateStateIdle)
        , _rtpState(RtpStateIdle)
        , _app(app)
        , _sessId(id)
        , _sipLine(sipLine)
        , _runnerManager(runnerManager)
        , _configurator(configurator)
        , _eventCollector(eventCollector)
        , _logCtx(Common::LogContext::create())
        , _requireAlert(false)
        , _isVideo(false)
        , _holded(false)
        , _ringingTimeMs(0)
        , _endType(0)
        , _networkStatus(0)
        , _sipEventNotify(eventCollector)
        , _sipTermCalled(false)
        , _domainId(0)
        , _appId(0)
    {
        if (!_app || !_sipLine || !_runnerManager)
            throw Common::Exception("MissingDependency");
        if (!_logCtx)
            throw Common::Exception("CreateLogContextFailed");
        _logCtx->setContext("sessId", _sessId, Common::LogContext::LogMaskKeep);
    }

    SipPhoneSession(const Common::ApplicationPtr &app, const ServiceRunner::RunnerManagerPtr &runnerManager, const SipLinePtr &sipLine, const SipSessionConfiguratorPtr &configurator, const SipLineSessionPtr &sipSess, const SessionManagerPtr &sessionManager, const JsmClient::EventCollectorPtr &eventCollector = nullptr)
        : _startTimeMs(Common::getCurTimeMs())
        , _termedTimeMs(0)
        , _callout(false)
        , _sessState(StateIdle)
        , _rtpState(RtpStateIdle)
        , _updateState(UpdateStateIdle)
        , _app(app)
        , _sipSess(sipSess)
        , _sipLine(sipLine)
        , _runnerManager(runnerManager)
        , _configurator(configurator)
        , _sessionManager(sessionManager)
        , _eventCollector(eventCollector)
        , _requireAlert(false)
        , _isVideo(false)
        , _holded(false)
        , _lastKeepAliveTicks(Common::getCurTicks())
        , _logCtx(Common::LogContext::create())
        , _ringingTimeMs(0)
        , _endType(0)
        , _networkStatus(0)
        , _sipEventNotify(eventCollector)
        , _sipTermCalled(false)
        , _domainId(0)
        , _appId(0)
    {
        if (!_app || !_sipSess || !_sipLine || !_runnerManager || !_sessionManager)
            throw Common::Exception("MissingDependency");
        if (!_logCtx)
            throw Common::Exception("CreateLogContextFailed");
    }

    bool __ex(const Common::ServerCallPtr &__call, const Common::String &__cmd, const Common::IputStreamPtr &__iput) override
    {
        if (MpCall::SipGatewayServer::__ex(__call, __cmd, __iput))
            return true;
        if (MpCallSip::RtpRunnerMonitorServer::__ex(__call, __cmd, __iput))
            return true;
        return false;
    }

    void setState(enum SessionState state, const Common::String &termedReason = "", EndType endtype = EndType::Normal);
    bool waitState(std::unique_lock<std::mutex> &lock, enum SessionState state, int timeoutMs = 3000);
    void setState(enum UpdateState state);
    void setState(enum RtpState state);

    // implement SipSession
    bool getStatus(Common::String &caller, Common::String &callee, Common::String &callId, Common::String &direction, Common::String &state, Common::Long &startTimeMs, Common::Long &durationMs, Common::String &termedReason) override;
    bool getRtpStatus(MpCallSip::RtpStats &rtpStats) override;

    // implement SessionLocator::Session
    virtual Common::String checkState(bool &closed) override;
    virtual void onError(const Common::CallError &error) override;

    // implement MpCall::SipGatewayServer
    virtual bool invitation(const Common::ServerCallPtr &__call, const Common::String &sessId, const Common::String &caller, const Common::String &callee, const MpCall::RoomConfig &roomConfig, const MpCall::InviteConfig &inviteConfig) override;
    virtual bool canceled(const Common::ServerCallPtr &__call, const Common::String &userId, const Common::String &reason) override;
    virtual bool alerted(const Common::ServerCallPtr &__call, const Common::String &userId) override;
    virtual bool accepted(const Common::ServerCallPtr &__call, const Common::String &userId, bool isVideo, const MpCall::RoomConfig &roomConfig, const Common::String &uui) override;
    virtual bool rejected(const Common::ServerCallPtr &__call, const Common::String &userId, const Common::String &reason) override;
    virtual bool leaved(const Common::ServerCallPtr &__call, const Common::String &userId) override;
    virtual bool terminated(const Common::ServerCallPtr &__call, const Common::String &reason) override;
    virtual bool requestVideo(const Common::ServerCallPtr &__call) override;

    // implement SipLineSessionListener
    virtual void onCallIncoming(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) override;
    virtual void onCallAlerted(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) override;
    virtual void onCallAnswered(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) override;
    virtual void onCallUpdate(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) override;
    virtual void onCallRequestUpdate(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) override;
    virtual void onCallResponseUdate(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) override;
    virtual void onCallConnected(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) override;
    virtual void onCallRequestModify(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) override;
    virtual void onCallResponseModify(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) override;
    virtual void onCallTerminated(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) override;

    // implement MpCallSip::RtpRunnerMonitorServer
    virtual void onError(const Common::ServerCallPtr &__call, const Common::String &reason) override;
    virtual void onNetworkStatusChanged(const Common::ServerCallPtr& __call,const Common::StrIntMap& networkStatus) override;

    // implement ServiceRunner::RunnerManagerItemListener
    virtual void onRunnerTermed() override;

private:
    Common::String getMpCallServerOid(const Common::String &sessId = "");
    bool hasVideo(const std::string &sdp);
    bool isHolded(const std::string &sdp, bool local);
    bool allocRtpAgent(const MpCall::RoomConfig &roomConfig);
    bool createRtpAgent(Common::Error &error);
    bool configRoom2Rtp(MpCallSip::RtpRunnerConfig &rtpConfig, const MpCall::RoomConfig &roomConfig);
    void configInvite2Rtp(MpCallSip::RtpRunnerConfig &rtpConfig, const MpCall::InviteConfig &inviteConfig);
    void getAudioExcludeConfig(MpCallSip::RtpRunnerConfig &rtpConfig);
    MpCallSip::SdpParams configInvite2Sdp(const MpCall::InviteConfig &inviteConfig);
    MpCallSip::SdpParams configInvite2Sdp(MpCall::MediaDirection direction);
    Common::CallParamsPtr configApp2CallParams(const SipSessionConfigurator::AppParams &appParams);
    void updateLogContext(const MpCall::InviteConfig &inviteConfig);

    void __releaseBySip(const Common::String &reason, const Common::String &detail, EndType endType = EndType::Normal);
    void __releaseByMpCall(const Common::String &reason, EndType endType = EndType::Normal);
    void __releaseByRtp(const Common::String &reason, const Common::String &detail, EndType endType = EndType::Normal);
    void __release(const Common::String &reason, EndType endType = EndType::Normal);

    void __notifyHolded(bool hold);
    void __notifyTermed();
    void __updateStateBySdp(const std::string &sdp, bool local);
    Common::String __getSipCallId();

private:
    Common::ApplicationPtr _app;
    SessionManagerPtr _sessionManager;
    std::mutex _mutex;
    std::condition_variable _stateChanged;
    Common::Long _startTimeMs;
    bool _callout;
    Common::String _termedReason;
    Common::Long _termedTimeMs;
    enum SessionState _sessState;
    enum UpdateState _updateState;
    Common::String _sessId;
    Common::String _objectId;
    SipLinePtr _sipLine;
    SipLineSessionPtr _sipSess;
    Common::LogContextPtr _logCtx;

    Common::String _caller;
    Common::String _callee;
    bool _requireAlert;

    enum RtpState _rtpState;
    ServiceRunner::RunnerManagerPtr _runnerManager;
    ServiceRunner::RunnerAllocHandlePtr _allocHandle;
    Common::String _runnerName;
    MpCallSip::RtpRunnerAgent _rtpAgent;
    MpCallSip::RtpStats _rtpStats;

    Common::String _peerSdp;
    bool _isVideo;
    bool _holded;
    bool _sipTermCalled;

    Common::String _targetOid;
    MpCall::SessionEndpointAgent _mpcallAgent;

    SipSessionConfiguratorPtr _configurator;

    JsmClient::EventCollectorPtr _eventCollector;
    Common::Long _ringingTimeMs;
    Common::String _callId;
    Common::Long _domainId;
    Common::Long _appId;
    int _endType;
    int _networkStatus;
    SipEventNotify _sipEventNotify;
    Common::String _jsmSipUser;

    friend class MpCallSipGateway;

private:
    unsigned int _lastKeepAliveTicks;
    void onNotifyEventFailed(const Common::String &name, const Common::String &logInfo);

    class EventAsync : public Common::AgentAsync
    {
    public:
        explicit EventAsync(const Common::String &eventName)
            : _eventName(eventName)
        {
        }

        virtual void cmdResult(int rslt, const Common::IputStreamPtr &iput, const Common::ObjectPtr &userdata) override;

    private:
        Common::String _eventName;
    };
};

typedef Common::Handle<SipPhoneSession> SipPhoneSessionPtr;

} // namespace SipMpCall
