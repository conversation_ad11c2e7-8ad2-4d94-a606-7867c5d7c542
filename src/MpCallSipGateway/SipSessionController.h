//
// *****************************************************************************
// Copyright (c) 2025 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//

#pragma once

#include "Common/Common.h"
#include "Common/CommonEx.h"
#include "SipSessionConfigurator.h"
#include "MpCall/MpCallSipAgent.h"

namespace SipMpCall
{

class SipSessionController : public SipSessionConfigurator
{
public:
    explicit SipSessionController(const Common::ObjectAgentPtr &agent);

    virtual bool onIncomingCall(const CallParams &inParams, CallParams &outParams, AppParams &appParams) override;
    virtual bool onOutgoingCall(const CallParams &inParams, CallParams &outParams) override;
    virtual bool onHolded(const Common::String &sessId, bool hold) override;
    virtual bool onTermed(const Common::String &sessId) override;

private:
    MpCall::SipGatewayControllerAgent _agent;
};

class SipSessionControllerManager : public SipSessionConfiguratorManager
{
public:
    SipSessionControllerManager(const Common::ApplicationExPtr &app, const Common::String &controllerOid);
    virtual SipSessionConfiguratorPtr getConfigurator() override;

private:
    Common::ApplicationExPtr _app;
    Common::String _controllerOid;
};

class SipSessionEbServiceManager : public SipSessionControllerManager
{
public:
    explicit SipSessionEbServiceManager(const Common::ApplicationExPtr &app);
};

} // namespace SipMpCall
