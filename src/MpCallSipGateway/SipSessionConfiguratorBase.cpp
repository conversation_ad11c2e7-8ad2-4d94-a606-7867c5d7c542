//
// *****************************************************************************
// Copyright (c) 2025 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//

#include "SipSessionConfiguratorBase.h"

static bool parseDomainAppId(const Common::String &config, Common::String &domainId, Common::String &appId)
{
    if (config.size() < 3)
        return false;

    int pos = config.find('/');
    if (pos == 0 || pos == config.size() - 1)
        return false;

    if (pos < 0)
    {
        domainId = config;
        appId = "0";
    }
    else
    {
        domainId = config.substr(0, pos);
        appId = config.substr(pos + 1);
    }

    return domainId.toInt(-1) > 0 && appId.toInt(-1) >= 0;
}

namespace SipMpCall
{

bool CalleeTypeConfig::init(const Common::ApplicationExPtr &app)
{
    static const char *key = "SipCall.IncomingCalleeType";
    static size_t keyLen = sizeof("SipCall.IncomingCalleeType") - 1;

    Common::StrStrMap configs;
    app->getAppConfigs(key, configs);

    for (const auto &config : configs)
    {
        if (config.first.size() == keyLen)
        {
            MpCall::UserType userType = parseCalleeType(config.second);
            if (userType == MpCall::TypeUnknown)
            {
                UTIL_LOG_WRN("SipSessionConfiguratorBase", "content:invalid config, key:" + config.first + " value:" + config.second);
                continue;
            }

            UTIL_LOG_IFO("SipSessionConfiguratorBase", "content:add config, default value:" + Common::String(userType) + " config:" + config.second);
            _configs[""] = userType;
        }
        else if (config.first.size() > keyLen + 2)
        {
            MpCall::UserType userType = parseCalleeType(config.first.substr(keyLen + 1));
            if (userType == MpCall::TypeUnknown)
            {
                UTIL_LOG_WRN("SipSessionConfiguratorBase", "content:invalid config, key:" + config.first + " value:" + config.second);
                continue;
            }

            Common::StrVec domainAppIds;
            config.second.split(domainAppIds, ",");
            for (const auto &domainAppId : domainAppIds)
            {
                Common::String key = domainAppId;
                key.trim();
                if (key.empty())
                    continue;

                UTIL_LOG_IFO("SipSessionConfiguratorBase", "content:add config, domain:" + key + " value:" + Common::String(userType) + " config:" + config.second);
                _configs[key] = userType;
            }
        }
        else
        {
            UTIL_LOG_WRN("SipSessionConfiguratorBase", "content:invalid config, key:" + config.first + " value:" + config.second);
        }
    }

    return true;
}

MpCall::UserType CalleeTypeConfig::getCalleeType(const Common::String &domainId, const Common::String &appId, MpCall::UserType defaultType) const
{
    if (!domainId.empty() && !appId.empty())
    {
        auto it = _configs.find(domainId + "/" + appId);
        if (it != _configs.end())
        {
            UTIL_LOG_DBG("SipSessionConfiguratorBase", "content:get callee type from domain:" + domainId + " app:" + appId + " type:" + Common::String(it->second));
            return it->second;
        }
    }

    if (!domainId.empty())
    {
        auto it = _configs.find(domainId);
        if (it != _configs.end())
        {
            UTIL_LOG_DBG("SipSessionConfiguratorBase", "content:get callee type from domain:" + domainId + " type:" + Common::String(it->second));
            return it->second;
        }
    }

    auto it = _configs.find("");
    if (it != _configs.end())
    {
        UTIL_LOG_DBG("SipSessionConfiguratorBase", "content:get callee type from default type:" + Common::String(it->second));
        return it->second;
    }

    if (defaultType != MpCall::TypeUnknown)
        return defaultType;

    UTIL_LOG_DBG("SipSessionConfiguratorBase", "content:get callee type:" + Common::String(MpCall::TypeApp));
    return MpCall::TypeApp;
}

MpCall::UserType CalleeTypeConfig::parseCalleeType(const Common::String &config)
{
    if (config == "App" || config == "app")
        return MpCall::TypeApp;
    else if (config == "Sip" || config == "sip")
        return MpCall::TypeSip;
    else if (config == "LegacyApp" || config == "legacyapp")
        return MpCall::TypeLegacyApp;
    else if (config == "Vman" || config == "vman")
        return MpCall::TypeVman;
    else if (config == "AcdApp" || config == "acdapp")
        return MpCall::TypeAcdApp;
    else if (config == "H5" || config == "h5")
        return MpCall::TypeH5;
    else if (config == "WeChat" || config == "wechat")
        return MpCall::TypeWechat;
    else if (config == "Yuv" || config == "yuv")
        return MpCall::TypeYuv;
    else if (config == "Ivvr" || config == "ivvr")
        return MpCall::TypeIvvr;

    return MpCall::TypeUnknown;
}

bool SipSessionConfiguratorBase::onIncomingCall(const CallParams &inParams, CallParams &outParams, AppParams &appParams)
{
    outParams = inParams;
    outParams.calleeType = _calleeTypeConfig.getCalleeType(appParams.domainId, appParams.appId, inParams.calleeType);
    return true;
}

bool SipSessionConfiguratorBase::onOutgoingCall(const CallParams &inParams, CallParams &outParams)
{
    outParams = inParams;
    return true;
}

bool SipSessionConfiguratorBase::onHolded(const Common::String &sessId, bool hold)
{
    return true;
}

bool SipSessionConfiguratorBase::onTermed(const Common::String &sessId)
{
    return true;
}

bool SipSessionConfiguratorDomain::onIncomingCall(const CallParams &inParams, CallParams &outParams, AppParams &appParams)
{
    SipSessionConfiguratorBase::onIncomingCall(inParams, outParams, appParams);
    appParams.domainId = _domainId;
    appParams.appId = _appId;
    UTIL_LOG_IFO("SipSessionConfiguratorDomain", "content:callee " + outParams.callee + " domain:" + appParams.domainId + " app:" + appParams.appId);
    return true;
}

bool SipSessionConfiguratorCalleePrefix::onIncomingCall(const CallParams &inParams, CallParams &outParams, AppParams &appParams)
{
    SipSessionConfiguratorBase::onIncomingCall(inParams, outParams, appParams);

    if (outParams.callee.size() <= 6)
    {
        UTIL_LOG_WRN("SipSessionConfiguratorCalleePrefix", "content:callee prefix invalid, callee:" + outParams.callee);
        return false;
    }

    appParams.domainId = outParams.callee.substr(0, 6);
    appParams.appId = "0";
    outParams.callee = outParams.callee.substr(6);
    UTIL_LOG_IFO("SipSessionConfiguratorCalleePrefix", "content:onIncomingCall callee " + outParams.callee + " domain:" + appParams.domainId + " app:" + appParams.appId);
    return true;
}

SipSessionConfiguratorPtr SipSessionConfiguratorBaseManager::getConfigurator()
{
    try
    {
        SipSessionConfiguratorBasePtr configurator;

        do
        {
            Common::String incomingDomainIdConfig = _app->getAppConfig("SipCall.IncomingDomainId");
            if (incomingDomainIdConfig == "CalleePrefix")
            {
                configurator = new SipSessionConfiguratorCalleePrefix();
                break;
            }

            Common::String domainId, appId;
            if (parseDomainAppId(incomingDomainIdConfig, domainId, appId))
            {
                configurator = new SipSessionConfiguratorDomain(domainId, appId);
                break;
            }

            configurator = new SipSessionConfiguratorBase();
        } while (false);

        if (configurator)
            configurator->_calleeTypeConfig.init(_app);

        return configurator;
    }
    catch (std::exception &e)
    {
        UTIL_LOG_ERR("SipSessionConfiguratorBaseManager", "content:create sip session configurator failed, reason:" + Common::String(e.what()));
        return nullptr;
    }
}

} // namespace SipMpCall
