//
// *****************************************************************************
// Copyright (c) 2023 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//
// Created on 2023/5/29 by <PERSON>
//

#pragma once

#include "Common/Error.h"

namespace SipMpCall
{

namespace Error
{
    static const char *const DomainCode = "M30";

    ERROR_DECLARE_F(7300, SipSessionExists);
    ERROR_DECLARE_F(7301, SipSessionNotExists);
    ERROR_DECLARE_F(7302, CreateAgentFailed);
    ERROR_DECLARE_F(7303, RtpRunnerError);
    ERROR_DECLARE_F(7304, Sip<PERSON>alloutFailed);
    ERROR_DECLARE_F(7305, AllocRtpRunnerFailed);
    ERROR_DECLARE_F(7306, SipAnswerFailed);
    ERROR_DECLARE_F(7307, NoValidRoomConfig);
    ERROR_DECLARE_F(7308, InvalidState);
    ERROR_DECLARE_F(7312, RtpRunnerManagerActivatedFailed);
    ERROR_DECLARE_F(7313, RtpRunnerManagerNotReady);
    ERROR_DECLARE_F(7400, InvalidControllerOid);
    ERROR_DECLARE_F(7401, ControllerFailed);
} // namespace Error

} // namespace SipMpCall
