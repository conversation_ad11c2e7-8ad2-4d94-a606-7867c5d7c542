//
// *****************************************************************************
// Copyright (c) 2023 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//
// Created on 2023/5/23 by <PERSON>
//

#pragma once

#include "Service/ServiceI.h"
#include "SessionLocator/SessionLocator.h"
#include "SipLine/SipLine.h"
#include "ServiceRunner/RunnerManager.h"
#include "SipSession.h"
#include "SipSessionConfigurator.h"
#include "SipGatewayStatus.h"
#include "EventCollector/EventCollector.h"

namespace SipMpCall
{

class MpCallSipGateway : public Service::ServiceManagerI, public SessionLocator::SessionLocatorListener, public SipLineListner, public SessionManager, public SipGatewayStatusInterface
{
    enum SessionType
    {
        SipPhone,
        SipVolte
    };

public:
    explicit MpCallSipGateway(const Common::ApplicationExPtr &application, const SessionLocator::SessionLocatorPtr &locator = 0, const SipLinePtr &sipLine = 0, const ServiceRunner::RunnerManagerPtr &runnerManager = 0, const SipSessionConfiguratorManagerPtr &configuratorManager = 0);

    bool __ex(const Common::ServerCallPtr &call, const Common::String &cmd, const Common::IputStreamPtr &iput) override { return false; }

    // implement Service::ServiceManagerI
    bool onActivate() override;
    void onDeactivate() override;
    void onShutdown() override;
    void onSchd() override;
    void onUpdateConfigs() override;
    bool getMainService(Common::ObjectServerPtr &service, Common::String &name, bool &famous) override;
    bool isMainServiceReady(Common::String &reason) override;

    // implement SessionLocator::SessionLocatorListener
    SessionLocator::SessionPtr onCreateSession(const Common::String &type, const Common::String &id, const Common::String &objectId) override;

    // implement SipLineListener
    SipLineSessionListenerPtr onCreateSession(const SipLineSessionPtr &session) override;

    // implement SessionManager
    Common::String addSession(const Common::String &id, const SessionLocator::SessionPtr &session) override;
    Common::StrStrMap getRoomConfig() override { return _roomConfig; }

    // implement SipGatewayStatusInterface
    bool setConfig(const Common::String &key, const Common::String &value) override;
    bool getConfig(const Common::String &key, Common::String &value) override;
    bool getCallStatusList(MpCallSip::ResponseAllCallStatus &status) override;
    bool getCallStatus(const Common::String &sessId, MpCallSip::ResponseStatus &status) override;

private:
    void initCategoryType();
    void readRoomConfig();

private:
    enum SessionType _sessionType;
    Common::String _categoryType;
    Common::String _serviceId;
    SessionLocator::SessionLocatorPtr _locator;
    SipLinePtr _sipLine;
    ServiceRunner::RunnerManagerPtr _rtpRunnerManager;
    SipSessionConfiguratorManagerPtr _configuratorManager;
    SipGatewayStatusPtr _status;
    bool _keepTermedSession;
    JsmClient::EventCollectorPtr _eventCollector;
    Common::StrStrMap _roomConfig;
};

} // namespace SipMpCall