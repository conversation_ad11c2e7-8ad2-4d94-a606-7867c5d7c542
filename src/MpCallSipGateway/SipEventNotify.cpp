#include "SipEventNotify.h"
#include "Common/Common.h"

namespace SipMpCall
{
void SipEventNotify::notifySipEvent(const EventBaseInfo &baseInfo, enum SipEvent::SipEventType eventType, Common::String sipCallId)
{
    if (!_eventCollector)
    {
        UTIL_LOG_WRN("SipEventManager", "content:report event failed, event collector is null");
        return;
    }
    SipEvent::EventSipDetail sipEvent = getSipEvent(baseInfo, sipCallId);
    sendDataCollectionEvent(sipEvent, (DataCollection::EventType)15, static_cast<int>(eventType));
    UTIL_LOG_IFO("SipEventManager", "content:report sip event success, type:" + Common::String(15) + " eventNumber:" + Common::String(static_cast<int>(eventType)));
}

void SipEventNotify::notifySipNetworkEvent(const EventBaseInfo &baseInfo, enum SipEvent::SipEventType eventType, int networkStatus, Common::String sipCallId)
{
    if (!_eventCollector)
    {
        UTIL_LOG_WRN("SipEventManager", "content:report network event failed, event collector is null");
        return;
    }
    SipEvent::EventSipNetworkChanged networkEvent = getSipNetworkEvent(baseInfo, networkStatus, sipCallId);
    sendDataCollectionEvent(networkEvent, (DataCollection::EventType)15, static_cast<int>(eventType));
    UTIL_LOG_IFO("SipPhoneSession", "content:report network event success, networkStatus:" + Common::String(networkEvent.networkStatus));
}

void SipEventNotify::notifySipCallRecord(const EventBaseInfo &baseInfo, Common::Long ringingTimeMs, Common::String source)
{
    if (!_eventCollector)
    {
        UTIL_LOG_WRN("SipEventManager", "content:report call record event failed, event collector is null");
        return;
    }
    SipEvent::SipCallRecord callRecord = getSipCallRecord(baseInfo, ringingTimeMs, source);
    sendDataCollectionEvent(callRecord, (DataCollection::EventType)14, 1);
    UTIL_LOG_IFO("SipPhoneSession", "content:report call record success");
}

SipEvent::EventSipDetail SipEventNotify::getSipEvent(const EventBaseInfo &baseInfo, Common::String sipCallId)
{
    SipEvent::EventSipDetail sipEvent;
    setCommonFields(sipEvent, baseInfo);

    sipEvent.duration = sipEvent.endTimestamp > 0 ? sipEvent.endTimestamp - sipEvent.beginTimestamp : Common::getCurTimeMs() - sipEvent.beginTimestamp;
    sipEvent.endType = baseInfo.endType;
    sipEvent.sipCallId = sipCallId;

    return sipEvent;
}

SipEvent::EventSipNetworkChanged SipEventNotify::getSipNetworkEvent(const EventBaseInfo &baseInfo, int networkStatus, Common::String sipCallId)
{
    SipEvent::EventSipNetworkChanged sipNetworkEvent;
    setCommonFields(sipNetworkEvent, baseInfo);

    sipNetworkEvent.duration = sipNetworkEvent.endTimestamp > 0 ? sipNetworkEvent.endTimestamp - sipNetworkEvent.beginTimestamp : Common::getCurTimeMs() - sipNetworkEvent.beginTimestamp;
    sipNetworkEvent.networkStatus = networkStatus;
    sipNetworkEvent.sipCallId = sipCallId;

    return sipNetworkEvent;
}

SipEvent::SipCallRecord SipEventNotify::getSipCallRecord(const EventBaseInfo &baseInfo, Common::Long ringingTimeMs, Common::String source)
{
    SipEvent::SipCallRecord callRecord;
    setCommonFields(callRecord, baseInfo);

    callRecord.endType = baseInfo.endType;
    callRecord.bizId = baseInfo.callId;
    callRecord.ringingTimeStamp = ringingTimeMs;
    callRecord.source = source;
    callRecord.gmtCreate = Common::getTimeStr("%04d-%02d-%02d %02d:%02d:%02d.%03d", Common::getCurTimeMs());

    return callRecord;
}
} // namespace SipMpCall
