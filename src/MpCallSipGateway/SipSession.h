//
// *****************************************************************************
// Copyright (c) 2023 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//
// Created on 2023/6/10 by <PERSON>
//

#pragma once

#include "SessionLocator/SessionLocator.h"
#include "MpCallSip/MpCallSipStatusPub.h"

namespace SipMpCall
{

class SipSession : virtual public Common::Shared
{
public:
    virtual bool getStatus(Common::String &caller, Common::String &callee, Common::String &callId, Common::String &direction, Common::String &state, Common::Long &startTimeMs, Common::Long &durationMs, Common::String &termedReason) = 0;
    virtual bool getRtpStatus(MpCallSip::RtpStats &rtpStats) = 0;
};

typedef Common::Handle<SipSession> SipSessionPtr;

class SessionManager : virtual public Common::Shared
{
public:
    virtual Common::String addSession(const Common::String &id, const SessionLocator::SessionPtr &session) = 0;
    virtual Common::StrStrMap getRoomConfig() = 0;
};

typedef Common::Handle<SessionManager> SessionManagerPtr;

enum class EndType
{
    Normal = 0,

    Canceled = 100301,                   // 未接通主叫挂断（直呼）
    Rejected = 100304,                   // 被叫拒接（直呼）

    SipInviteConfigFailed = 120101,      // SIP邀请参数配置失败
    SipCalloutFailed = 120102,           // SIP外呼失败
    SipAlertFailed = 120103,             // SIP振铃失败
    InvalidSessionState = 120104,        // SIP服务状态异常
    SipUserLeave = 120105,               // 用户离开
    SipAnswerFailed = 120106,            // SIP应答失败
    SipUpdateFailed = 120107,            // SIP更新失败

    MpCallAlertFailed = 3002101,         // 服务振铃失败
    MpCallAcceptFailed = 3002102,        // 服务应答失败
    MpCallIncomingFailed = 3002103,      // 处理呼入失败
    MpCallEventFailed = 3002104,         // 发送事件通知失败

    RtpAgentError = 4000101,             // 分配资源异常
    RtpCallinFailed = 4000102,           // 资源接通失败
    RtpConfigError = 4000103,            // 配置异常
    RtpNegotiate = 4000104,              // 资源协商 SDP 失败
    RtpAbnormalTermed = 4000105,         // 资源异常结束
    RtpInternalError = 4000106,          // 资源内部异常
    RtpCalloutFailed = 4000107           // 外呼失败
};

} // namespace SipMpCall