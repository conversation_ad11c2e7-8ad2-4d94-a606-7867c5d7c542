//
// *****************************************************************************
// Copyright (c) 2023 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//
// Created on 2023/12/8 by <PERSON>
//

#pragma once

#include "ServiceRunner/RunnerManager.h"
#include "MpCallSip/MpCallRtpServer.h"
#include "SimpleSipSession/OfferAnswerModel.h"

namespace SipMpCall
{

class DummyRtpRunner;
class DummyRtpAllocHandle;
class DummyRtpManager;

typedef Common::Handle<DummyRtpRunner> DummyRtpRunnerPtr;
typedef Common::Handle<DummyRtpAllocHandle> DummyRtpAllocHandlePtr;
typedef Common::Handle<DummyRtpManager> DummyRtpManagerPtr;

class DummyRtpRunner : public MpCallSip::RtpRunnerServer
{
public:
    DummyRtpRunner(const Common::String &name, const Common::String &localIp, int port, const ServiceRunner::RunnerManagerItemListenerPtr &listener);

    virtual void callout_begin(const Common::ServerCallPtr &__call, const Common::String &targetOid, const Common::String &monitorOid, const MpCallSip::RtpRunnerConfig &config, const MpCallSip::SdpParams &sdpParams) override;
    virtual void callin_begin(const Common::ServerCallPtr &__call, const Common::String &targetOid, const Common::String &monitorOid, const MpCallSip::RtpRunnerConfig &config, const Common::String &peerSdp) override;
    virtual void callinAndOffer_begin(const Common::ServerCallPtr &__call, const Common::String &targetOid, const Common::String &monitorOid, const MpCallSip::RtpRunnerConfig &config, const MpCallSip::SdpParams &sdpParams) override;
    virtual void callinAndAnswer_begin(const Common::ServerCallPtr &__call, const Common::String &targetOid, const Common::String &monitorOid, const MpCallSip::RtpRunnerConfig &config, const Common::String &peerSdp, const MpCallSip::SdpParams &sdpParams) override;
    virtual bool close(const Common::ServerCallPtr &__call) override;
    virtual bool genOffer(const Common::ServerCallPtr &__call, const MpCallSip::SdpParams &sdpParams, Common::String &offerSdp) override;
    virtual bool setOffer(const Common::ServerCallPtr &__call, const Common::String &offerSdp) override;
    virtual bool setOfferAndAnswer(const Common::ServerCallPtr &__call, const Common::String &offerSdp, const MpCallSip::SdpParams &sdpParams, Common::String &answerSdp) override;
    virtual bool genAnswer(const Common::ServerCallPtr &__call, const MpCallSip::SdpParams &sdpParams, Common::String &answerSdp) override;
    virtual bool setAnswer(const Common::ServerCallPtr &__call, const Common::String &answerSdp) override;
    virtual bool setTalking(const Common::ServerCallPtr &__call, bool talking) override;
    virtual bool getRtpStatus(const Common::ServerCallPtr &__call, MpCallSip::RtpStats &stats) override;

private:
    void initOfferAnswer();

private:
    ServiceRunner::RunnerManagerItemListenerPtr _listener;
    Common::String _name;
    Common::String _localIp;
    int _port;
    Common::String _objectId;
    bool _closed;

    SimpleSipSession::OfferAnswerModel _offerAnswer;
    Common::String _peerOffer;

    friend class DummyRtpAllocHandle;
    friend class DummyRtpManager;
};

class DummyRtpAllocHandle : public ServiceRunner::RunnerAllocHandle
{
public:
    explicit DummyRtpAllocHandle(const DummyRtpRunnerPtr &runner)
        : _runner(runner)
    {
    }

    virtual bool getRunner(Common::String &name, Common::String &objectId, Common::Error &error) override;

private:
    DummyRtpRunnerPtr _runner;
};

class DummyRtpManager : public ServiceRunner::RunnerManager
{
public:
    explicit DummyRtpManager(const Common::ApplicationPtr &app)
        : _app(app)
    {
    }

    static ServiceRunner::RunnerManagerPtr create(const Common::ApplicationPtr &app, const Common::AdapterPtr &adapter = 0);

    virtual bool activate(const Common::AdapterPtr &adapter = 0) override;
    virtual void deactivate() override {};
    virtual void close() override;
    virtual void updateConfigs() override;
    virtual bool isServiceReady(Common::String &abnormalInfo) override { return true; };

    virtual int totalRunnerCount() override;
    virtual ServiceRunner::RunnerAllocHandlePtr allocRunner(const ServiceRunner::RuleParams &rules, const ServiceRunner::RunnerManagerItemListenerPtr &listener) override;
    virtual void releaseRunner(const Common::String &name) override;

private:
    void getLocalIp();

private:
    Common::ApplicationPtr _app;
    Common::AdapterPtr _adapter;
    Common::RecMutex _mutex;
    Common::String _ipv4;
    std::map<Common::String, DummyRtpRunnerPtr> _runners;
};

} // namespace SipMpCall