//
// *****************************************************************************
// Copyright (c) 2025 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//

#include "SipGatewayStatusI.h"
#include "ServiceUtil/Struct2Json.h"
#include "html/index.html.h"
#include "html/index.css.h"

namespace SipMpCall
{

bool SipGatewayStatusI::open(const Common::String &host, int &port, Common::String &failReason)
{
    if (port == 0)
    {
        port = _httpServer.bind_to_any_port(host.c_str());
        if (port <= 0)
        {
            UTIL_LOG_ERR("SipGatewayStatus", "content:activate listen:" + host + " failed.");
            failReason = "SipGatewayStatus.BindPortFailed:" + host;
            return false;
        }
    }
    else
    {
        if (!_httpServer.bind_to_port(host.c_str(), port))
        {
            UTIL_LOG_ERR("SipGatewayStatus", "content:activate listen:" + host + ":" + Common::String(port) + " failed.");
            failReason = "SipGatewayStatus.BindPortFailed:" + host + ":" + Common::String(port);
            return false;
        }
    }

    if (!startRun(0, "SipGatewayStatus"))
    {
        UTIL_LOG_ERR("SipGatewayStatus", "content:activate start listen:" + host + ":" + Common::String(port) + " failed.");
        failReason = "SipGatewayStatus.StartListenFailed:" + host + ":" + Common::String(port);
        return false;
    }

    for (int i = 0; i < 5; i++)
    {
        if (_httpServerState == HttpServerError)
        {
            UTIL_LOG_ERR("SipGatewayStatus", "content:activate start listen:" + host + ":" + Common::String(port) + " failed.");
            failReason = "SipGatewayStatus.ListenFailed:" + host + ":" + Common::String(port);
            return false;
        }
        Common::sleep(10);
    }

    UTIL_LOG_IFO("SipGatewayStatus", "content:activate start listen:" + host + ":" + Common::String(port));
    return true;
}

// implement SipGatewayStatus
void SipGatewayStatusI::close()
{
    _listener = nullptr;
    _httpServer.stop();
}

// implement Common::Thread
void SipGatewayStatusI::onRun()
{
    // Helper function to serve static content
    auto serveStaticContent = [](const std::string &filePath, const char *embeddedContent, size_t contentLength, const char *contentType) {
        if (access(filePath.c_str(), F_OK) == 0) {
            std::ifstream file(filePath);
            std::string content((std::istreambuf_iterator<char>(file)), std::istreambuf_iterator<char>());
            return content;
        } else {
            return std::string(embeddedContent, contentLength);
        }
    };

    // get html
    _httpServer.Get("/", [&, serveStaticContent](const httplib::Request &req, httplib::Response &res) {
        res.set_content(serveStaticContent("html/index.html", (const char *)index_html, index_html_len, "text/html"), "text/html");
    });

    // get css
    _httpServer.Get("/index.css", [&, serveStaticContent](const httplib::Request &req, httplib::Response &res) {
        res.set_content(serveStaticContent("html/index.css", (const char *)index_css, index_css_len, "text/css"), "text/css");
    });

    // get all call status, path /api/v1/call/status
    _httpServer.Get("/api/v1/call/status", [&](const httplib::Request &req, httplib::Response &res) {
        SipGatewayStatusInterfacePtr listener = _listener;
        if (!listener)
        {
            res.status = 500;
            res.set_content("Listener is empty", "text/plain");
            return;
        }

        MpCallSip::ResponseAllCallStatus status;
        if (!listener->getCallStatusList(status))
        {
            res.status = 500;
            res.set_content("Get call status list failed", "text/plain");
            return;
        }

        res.status = 200;
        res.set_content(ServiceUtil::to_json(status).c_str(), "application/json");
    });

    // get call status, path /api/v1/call/<sessId>/status
    _httpServer.Get("/api/v1/call/(.*)/status", [&](const httplib::Request &req, httplib::Response &res) {
        SipGatewayStatusInterfacePtr listener = _listener;
        if (!listener)
        {
            res.status = 500;
            res.set_content("Listener is empty", "text/plain");
            return;
        }

        MpCallSip::ResponseStatus status;
        if (!listener->getCallStatus(req.matches[1].str().c_str(), status))
        {
            res.status = 500;
            res.set_content("Get call " + req.matches[1].str() + " status failed", "text/plain");
            return;
        }

        res.status = 200;
        res.set_content(ServiceUtil::to_json(status).c_str(), "application/json");
    });

    // get config, path /api/v1/config/<key>
    _httpServer.Get("/api/v1/config/(.*)", [&](const httplib::Request &req, httplib::Response &res) {
        SipGatewayStatusInterfacePtr listener = _listener;
        if (!listener)
        {
            res.status = 500;
            res.set_content("Listener is empty", "text/plain");
            return;
        }

        Common::String value;
        if (!listener->getConfig(req.matches[1].str().c_str(), value))
        {
            res.status = 500;
            res.set_content("Get config " + req.matches[1].str() + " failed", "text/plain");
            return;
        }

        res.status = 200;
        res.set_content(value.c_str(), "text/plain");
    });

    // set config, path /api/v1/config/<key>
    _httpServer.Post("/api/v1/config/(.*)", [&](const httplib::Request &req, httplib::Response &res) {
        SipGatewayStatusInterfacePtr listener = _listener;
        if (!listener)
        {
            res.status = 500;
            res.set_content("Listener is empty", "text/plain");
            return;
        }

        if (!listener->setConfig(req.matches[1].str().c_str(), req.body.c_str()))
        {
            res.status = 500;
            res.set_content("Failed", "text/plain");
            return;
        }

        res.status = 200;
        res.set_content("Success", "text/plain");
    });

    _httpServerState = HttpServerListened;

    if (!_httpServer.listen_after_bind())
    {
        UTIL_LOG_ERR("SipGatewayStatus", "content:http server thread listen failed.");
        _httpServerState = HttpServerError;
        return;
    }

    UTIL_LOG_IFO("SipGatewayStatus", "content:http server thread exit");
    _httpServerState = HttpServerDone;
}

SipGatewayStatusPtr SipGatewayStatus::create(const Common::ApplicationPtr &application, const SipGatewayStatusInterfacePtr &listener)
{
    try
    {

        Common::String httpHost;
        int httpPort;
        if (!application->getAppConfig("SipGateway.Http.ListenHost", httpHost) || httpHost == "0.0.0.0")
        {
            Common::NetSenderPtr sender = application->getDriver()->listen("udp", "0.0.0.0", 0, 0);
            if (!sender)
            {
                UTIL_LOG_ERR("SipGatewayStatus", "content:activate get local ip failed.");
                return nullptr;
            }

            sender->getLocal(httpHost, httpPort);
            sender->close();
        }

        httpPort = application->getAppConfigAsInt("SipGateway.Http.ListenPort");
        if (httpPort < 0 || httpPort >= 65535)
        {
            UTIL_LOG_ERR("SipGatewayStatus", "content:activate invalid config SipGateway.Http.ListenPort");
            return nullptr;
        }

        Common::Handle<SipGatewayStatusI> status = new SipGatewayStatusI(listener);
        Common::String failReason;
        if (!status->open(httpHost, httpPort, failReason))
        {
            UTIL_LOG_ERR("SipGatewayStatus", "content:open failed:" + failReason);
            return nullptr;
        }

        Common::String webSite = "http://" + httpHost + ":" + Common::String(httpPort);
        application->setStatistics("SipGateway.WebPortal", webSite);
        UTIL_LOG_IFO("SipGatewayStatus", "content:open " + webSite + " success");
        return status;
    }
    catch (const std::exception &e)
    {
        UTIL_LOG_ERR("SipGatewayStatus", "content:create failed:" + Common::String(e.what()));
        return nullptr;
    }
}

} // namespace SipMpCall
