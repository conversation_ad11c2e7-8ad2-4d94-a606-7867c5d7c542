//
// *****************************************************************************
// Copyright (c) 2025 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//

#pragma once

#include "Common/CommonEx.h"
#include "MpCall/MpCallPub.h"

namespace SipMpCall
{

class SipSessionConfigurator;
class SipSessionConfiguratorManager;
typedef Common::Handle<SipSessionConfigurator> SipSessionConfiguratorPtr;
typedef Common::Handle<SipSessionConfiguratorManager> SipSessionConfiguratorManagerPtr;

class SipSessionConfigurator : virtual public Common::Shared
{
public:
    struct CallParams
    {
        CallParams()
            : callerType(MpCall::TypeApp)
            , calleeType(MpCall::TypeApp)
        {
        }

        CallParams(const Common::String &_sessId, const Common::String &_caller, const Common::String &_callee, const MpCall::InviteConfig &_inviteConfig)
            : sessId(_sessId)
            , callerType(MpCall::TypeApp)
            , caller(_caller)
            , calleeType(MpCall::TypeApp)
            , callee(_callee)
            , inviteConfig(_inviteConfig)
        {
        }

        Common::String sessId;
        MpCall::UserType callerType;
        Common::String caller;
        MpCall::UserType calleeType;
        Common::String callee;
        MpCall::InviteConfig inviteConfig;
    };

    struct AppParams
    {
        Common::String domainId;
        Common::String appId;
    };

    virtual bool onIncomingCall(const CallParams &inParams, CallParams &outParams, AppParams &appParams) = 0;
    virtual bool onOutgoingCall(const CallParams &inParams, CallParams &outParams) = 0;
    virtual bool onHolded(const Common::String &sessId, bool hold) = 0;
    virtual bool onTermed(const Common::String &sessId) = 0;
};

class SipSessionConfiguratorManager : virtual public Common::Shared
{
public:
    static SipSessionConfiguratorManagerPtr create(const Common::ApplicationExPtr &app);
    virtual SipSessionConfiguratorPtr getConfigurator() = 0;
};

} // namespace SipMpCall
