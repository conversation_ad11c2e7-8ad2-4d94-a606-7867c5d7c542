﻿#ifndef __PenglCommon_h
#define __PenglCommon_h

#include "Common/Reason.h"
#include "Common/TypesPub.h"
#include "Common/CommonEx.h"
#include "Common/Error.h"

#ifdef USERSDB
#include "UserDb/Error.h"
#endif
#ifdef USER
#endif
#ifdef ACCOUNTDB_LOCATOR
#include "Account/Error.h"
#endif
#ifdef ACCOUNT_RESIDENTRDB
#include "Account/Error.h"
#endif

namespace Common
{
#define UTIL_LOG_ERR_LOCATE(key,errorCode,info) Common::log(UTIL_LOG_LOC, Common::LogError, key, Common::String::formatString("rccode:%s\tlocate:%s\tcontent:", errorCode, __FUNCTION__) + info)
#define UTIL_LOG_WRN_LOCATE(key,errorCode,info) Common::log(UTIL_LOG_LOC, Common::LogWarn, key, Common::String::formatString("rccode:%s\tlocate:%s\tcontent:", errorCode, __FUNCTION__) + info)

#ifdef USERSDB
    #define ERROR(_error) USERSDB_ERROR(_error)
    #define ERROR_ARG(_error, _arg) USERSDB_ERROR_ARG(_error, _arg)
#endif
#ifdef USER
    #define ERROR(_error) USER_ERROR(_error)
    #define ERROR_ARG(_error, _arg) USER_ERROR_ARG(_error, _arg)
#endif
#ifdef ACCOUNTDB_LOCATOR
    #define ERROR(_error) ACCOUNTDB_ERROR(_error)
    #define ERROR_ARG(_error, _arg) ACCOUNTDB_ERROR_ARG(_error, _arg)
#endif
#ifdef ACCOUNT_RESIDENTRDB
    #define ERROR(_error) ACCOUNT_RESIDENTRDB_ERROR(_error)
    #define ERROR_ARG(_error, _arg) ACCOUNT_RESIDENTRDB_ERROR_ARG(_error, _arg)
#endif

#define ERR_BREAK_IF(condition, e) \
    { \
        if (condition) \
        { \
            error = e; \
            break; \
        } \
    }

#define ERR_RET_IF(condition, e) \
    { \
        if (condition) \
        { \
            error = e; \
            return false; \
        } \
    }

#define ERR_CONTINUE_IF(condition, e) \
    { \
        if (condition) \
        { \
            error = e; \
            continue; \
        } \
    }

#define REASON_BREAK_IF(condition, r) \
    { \
        if (condition) \
        { \
            reason = r; \
            break; \
        } \
    }

#define REASON_RET_IF(condition, r) \
    { \
        if (condition) \
        { \
            reason = r; \
            return false; \
        } \
    }

#define REASON_CONTINUE_IF(condition, r) \
    { \
        if (condition) \
        { \
            reason = r; \
            continue; \
        } \
    }


#define ALERT(application) \
    application->addStatisticsLong(INNER_ERROR + ":" __FILE__ "_" + Common::String(__LINE__), 1);


#define ALERT_ERROR(application, error) \
    { \
        error = ERROR(INNER_ERROR); \
        application->addStatisticsLong("inner-error:" __FILE__ "_" + Common::String(__LINE__), 1); \
    }

// 这里为求简单,不再考虑为debugStrs的读写加锁.
#define ALERT_ERROR_PRO(debugStr, application, error) \
    { \
        error = ERROR(INNER_ERROR); \
        application->addStatisticsLong("inner-error:" __FILE__ "_" + Common::String(__LINE__), 1); \
        Common::String debugKey = "debug:" __FILE__ "_" + Common::String(__LINE__); \
        Common::String debugStrs = application->getStatistic(debugKey); \
        if (debugStrs.size() < 128 && debugStrs.find("|" + debugStr + "|") == -1) \
        { \
            if (debugStrs.empty()) \
                debugStrs = "|"; \
            application->setStatistics(debugKey, debugStrs + debugStr + "|"); \
        } \
    }

#define ASSERT_ERROR_BREAK(cond, application, error) \
    { \
        if (!(cond)) \
        { \
            ALERT_ERROR(application, error) \
            break; \
        } \
    }

#define ASSERT_ERROR_PRO_BREAK(cond, debugStr, application, error) \
    { \
        if (!(cond)) \
        { \
            ALERT_REASON_PRO(debugStr, application, error) \
            break; \
        } \
    }

#define ASSERT_ERROR_CONTINUE(cond, application, error) \
    { \
        if (!(cond)) \
        { \
            ALERT_REASON(application, error) \
            continue; \
        } \
    }

#define ASSERT_ERROR_RET(cond, application, error) \
    { \
        if (!(cond)) \
        { \
            ALERT_ERROR(application, error) \
            return false; \
        } \
    }

#define ALERT_REASON(application, reason) \
    { \
        reason = Common::String(REASON_INNER_ERROR) + ":" __FILE__ "_" + Common::String(__LINE__); \
        application->addStatisticsLong(reason, 1); \
    }

// 这里为求简单,不再考虑为debugStrs的读写加锁.
#define ALERT_REASON_PRO(debugStr, application, reason) \
    { \
        reason = Common::String(REASON_INNER_ERROR) + ":" __FILE__ "_" + Common::String(__LINE__); \
        application->addStatisticsLong(reason, 1); \
        Common::String debugKey = "debug:" __FILE__ "_" + Common::String(__LINE__); \
        Common::String debugStrs = application->getStatistic(debugKey); \
        if (debugStrs.size() < 128 && debugStrs.find("|" + debugStr + "|") == -1) \
        { \
            if (debugStrs.empty()) \
                debugStrs = "|"; \
            application->setStatistics(debugKey, debugStrs + debugStr + "|"); \
        } \
    }

#define ASSERT_REASON_BREAK(cond, application, reason) \
    { \
        if (!(cond)) \
        { \
            ALERT_REASON(application, reason) \
            break; \
        } \
    }

#define ASSERT_REASON_PRO_BREAK(cond, debugStr, application, reason) \
    { \
        if (!(cond)) \
        { \
            ALERT_REASON_PRO(debugStr, application, reason) \
            break; \
        } \
    }

#define ASSERT_REASON_CONTINUE(cond, application, reason) \
    { \
        if (!(cond)) \
        { \
            ALERT_REASON(application, reason) \
            continue; \
        } \
    }

#define ASSERT_REASON_RET(cond, application, reason) \
    { \
        if (!(cond)) \
        { \
            ALERT_REASON(application, reason) \
            return false; \
        } \
    }

    template<typename K, typename V>
    void applyDiff(map<K, V>& base, const map<K, V>& diff, bool eraseEmpty = false) // If accumulate diffs, let eraseEmpty be false.
    {
        typename map<K, V>::const_iterator it = diff.begin();
        for (; it != diff.end(); ++it)
        {
            if (eraseEmpty && it->second == V())
                base.erase(it->first);
            else
                base[it->first] = it->second;
        }
    }

    template<typename T>
    void mergeMap(map<Common::String, T>& src, const map<Common::String, T>& diff)
    {
        typename map<Common::String, T>::const_iterator it = diff.begin();
        for (; it != diff.end(); ++it)
            src[it->first] = it->second;
    }

    // 依据prefixs对props进行过滤,只留下前缀匹配的项.
    template<typename T>
    void filterMap(const Common::StrSet& prefixs, map<Common::String, T>& props)
    {
        if (prefixs.find("") != prefixs.end())
            return;

        typename map<Common::String, T>::const_iterator propIt = props.begin();
        for (; propIt != props.end();)
        {
            Common::StrSet::const_iterator prefixIt = prefixs.begin();
            for (; prefixIt != prefixs.end(); ++prefixIt)
            {
                if (propIt->first.subequ(0, prefixIt->c_str(), prefixIt->size()))
                {
                    ++propIt;
                    break;
                }
            }
            if (prefixIt == prefixs.end())
                props.erase((propIt++)->first);
        }
    }

    template<typename T>
    void filterMapEx(const Common::StrSet& prefixs, const map<Common::String, T>& props, map<Common::String, T>& rslt)
    {
        if (prefixs.find("") != prefixs.end())
        {
            rslt = props;
            return;
        }

        typename map<Common::String, T>::const_iterator propIt = props.begin();
        for (; propIt != props.end(); ++propIt)
        {
            Common::StrSet::const_iterator prefixIt = prefixs.begin();
            for (; prefixIt != prefixs.end(); ++prefixIt)
            {
                if (propIt->first.subequ(0, prefixIt->c_str(), prefixIt->size()))
                {
                    rslt.insert(make_pair(propIt->first, propIt->second));
                    break;
                }
            }
        }
    }

    static Common::String extractNodeAddr(const Common::String& src)
    {
        int pos = src.rfind(":");
        if (pos <= 0 || pos >= src.size() - 1)
            return src;
        Common::String ip = src.substr(0, pos);
        Common::String port = src.substr(pos + 1);
        if (port.toInt(-1) == -1)
        {
            Common::String key;
            int i = 0;
            do
            {
                char c = port[i];
                if(c < '0' || c > '9')
                    break;
                key.append(&c, 1);
            } while (++i < port.size());
            port = key;
        }
        return ip + ":" + port;
    }

    class RWMutex
    {
    public:
        RWMutex()
            : _lock(0) {};
        void rlock()
        {
            while (1)
            {
                if (Common::atomAdd(_lock, 2) % 2 == 0)
                    break;
                Common::atomAdd(_lock, -2);
                while (_lock % 2 != 0)
                    Common::schd_release();
            }
        }
        void runlock()
        {
            Common::atomAdd(_lock, -2);
        }
        void wlock()
        {
            while (1)
            {
                if (Common::atomAdd(_lock, 1) == 0)
                    break;
                Common::atomAdd(_lock, -1);
                while (_lock != 0)
                    Common::schd_release();
            }
        }
        void wunlock()
        {
            Common::atomAdd(_lock, -1);
        }
    private:
        Common::Aint _lock;
        friend class ReadLock;
        friend class WriteLock;
    };

    class ReadLock
    {
    public:
        explicit ReadLock(RWMutex* mutex)
            : _mutex(mutex)
        {
            _mutex->rlock();
        }
        explicit ReadLock(RWMutex& mutex)
            : _mutex(&mutex)
        {
            _mutex->rlock();
        }
        ~ReadLock()
        {
            _mutex->runlock();
        }
    private:
        RWMutex* _mutex;
    };

    class WriteLock
    {
    public:
        explicit WriteLock(RWMutex* mutex)
            : _mutex(mutex)
        {
            _mutex->wlock();
        }
        explicit WriteLock(RWMutex& mutex)
            : _mutex(&mutex)
        {
            _mutex->wlock();
        }
        ~WriteLock()
        {
            _mutex->wunlock();
        }
    private:
        RWMutex* _mutex;
    };
}

#endif
