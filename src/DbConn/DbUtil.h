﻿#ifndef __DbUtil_h
#define __DbUtil_h


#include "Common/TypesPub.h"

namespace DbConn
{
    bool parseMonoUri(const Common::String& mongoUri, Common::String& parseUri);

    class URI
    {
    public:
        URI(const Common::String& uri);
        bool valid() const;
        Common::String proto() const;
        Common::String host() const;
        Common::String port() const;
        void addrs(list<Common::String>& hosts, list<Common::String>& ports) const;
        Common::String path() const;
        Common::String param(const Common::String& key) const;
        const Common::StrStrMap& params() const;
    private:
        bool _valid;
        Common::String _proto;
        Common::String _path;
        list<Common::String> _hosts, _ports;
        Common::StrStrMap _params;
    };

}

#endif
