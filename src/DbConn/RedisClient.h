﻿#ifndef __RedisClient_h
#define __RedisClient_h

#include "Common/Common.h"
#include "Common/TypesPub.h"

namespace Redis
{
    using namespace Common;

    class Cmd;
    class Rpl;
    class Cli;

    typedef Handle<Cmd>                 CmdPtr;
    typedef Handle<Rpl>                 RplPtr;
    typedef Handle<Cli>                 CliPtr;

    class Key
    {
    public:
        Key(const String &_key)
            : key(_namespace.empty() ? "" : (_namespace + ":"))
        {
            for (int i = 0; i < _key.size(); i++)
            {
                if ((_key[i] >= '0' && _key[i] <= '9') || (_key[i] >= 'a' && _key[i] <= 'z') || (_key[i] >= 'A' && _key[i] <= 'Z') || _key[i] == '.' || _key[i] == ':' || _key[i] == '_' || _key[i] == '-' || _key[i] == '#')
                    key += _key.substr(i, 1);
                else
                    key += "_";
            }
        }

        operator String() const
        {
            return key;
        }

        String key;

    public:
        static void setNamespace(const String &ns) { _namespace = ns; }
        static String getNamespace() {return _namespace; }

    private:
        static String _namespace;
    };

    typedef vector<Key> KeyVec;

    class Cmd : public Shared
    {
    public:
        static CmdPtr cmd(const String& cmd);
        virtual CmdPtr p(const void* data, int len) = 0;
        virtual CmdPtr p(const Stream& val) = 0;
        virtual CmdPtr p(Long val) = 0;
        virtual CmdPtr p(const String& val) = 0;
        // 有key,请务必调用key()函数.
        virtual CmdPtr key(const Key &key) = 0;
    };


    class Rpl : public Shared
    {
    public:
        virtual bool isStat() const = 0;
        virtual bool isErr() const = 0;
        virtual bool isInt() const = 0;
        virtual bool isNil() const = 0;
        virtual bool isStr() const = 0;
        virtual bool isArray() const = 0;
        virtual String getStat() const = 0;
        virtual String getErr() const = 0;
        virtual Long getInt() const = 0;
        virtual String getStr() const = 0;
        virtual int arraySize() const = 0;
        virtual RplPtr getArray(int idx) const = 0;
    };


    class Cli : public Shared
    {
    public:
        static const String SCAN_BEGIN;
        static const int SCAN_DFLT_CNT;
        static CliPtr create(const ApplicationPtr& application, const String& uri);
        virtual void onUpdateConfigs() = 0;
        virtual void onDeactivate() = 0;
        virtual bool exec(const CmdPtr& cmd, RplPtr& reply, String& reason) = 0;
        virtual bool exec(const vector<CmdPtr>& cmds, vector<RplPtr>& rpls, String& reason) = 0;    // 对cluster调用批量exec()此接口时,必须保证这些Cmd的key属于同一个slot.
        virtual bool evalSha(const String &script, const Key &key, const StrVec &argv, RplPtr &rpl, String &reason) = 0;
        virtual bool evalSha(const String &script, const KeyVec &keys, const StrVec &argv, RplPtr &rpl, String &reason) = 0;                    // 对cluster调用此接口时,必须保证所有key属于同一个slot.
        virtual bool scan(const String& cursor, const String& pattern, int preferCnt, String& nextCursor, StrSet& keys, String& reason) = 0;    // cluster之上的SCAN命令实现.各出入参注意事项参见SCAN命令介绍.使用前务必仔细阅读.
    };
}

#endif
