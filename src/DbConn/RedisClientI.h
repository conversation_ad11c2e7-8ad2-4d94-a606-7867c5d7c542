﻿#ifndef __RedisConnI_h
#define __RedisConnI_h

#include "RedisClient.h"
#include "PenglCommon.h"

namespace Redis
{
    class Command;
    class Reply;
    class Conn;
    class ConnPool;
    class Client;
    class SentinelClient;
    class ClusterNode;

    typedef Handle<Command>                 CommandPtr;
    typedef Handle<Reply>                   ReplyPtr;
    typedef Handle<Conn>                    ConnPtr;
    typedef Handle<ConnPool>                ConnPoolPtr;
    typedef Handle<Client>                  ClientPtr;
    typedef Handle<SentinelClient>          SentinelClientPtr;
    typedef Handle<ClusterNode>             ClusterNodePtr;

    const char STATUS_TYPE                  = '+';
    const char ERROR_TYPE                   = '-';
    const char INTEGER_TYPE                 = ':';
    const char BULK_TYPE                    = '$';
    const char MULTI_BULK_TYPE              = '*';
    const int TYPE_LEN                      = 1;
    const char* LINE_BREAK                  = "\r\n";
    const int LINE_BREAK_LEN                = 2;
    const int NIL_LEN                       = -1;   // 参见redis协议.
    const int INVALID_BULK_REPLY_LEN        = -2;

    const int CLUSTER_SLOTS                 = 16384;

    struct Credential
    {
        Credential() = default;
        Credential(const Credential &rhs)
            : user(rhs.user)
            , pwd(rhs.pwd)
        {
        }
        Credential(const String &_user, const String &_pwd)
            : user(_user)
            , pwd(_pwd)
        {
        }

        bool operator==(const Credential &rhs) const
        {
            return user == rhs.user && pwd == rhs.pwd;
        }

        String user;
        String pwd;
    };

    class Command : public Cmd
    {
    public:
        Command(const String& cmd);
        CmdPtr p(const void* data, int len) { return p(String((const char*)data, len)); }
        CmdPtr p(const Stream& val) { return p(val.toString()); }
        CmdPtr p(Long val) { return p(String(val)); }
        CmdPtr p(const String& val);
        CmdPtr key(const Key &key) { _keys.push_back(key); return p(key.key); }
        const KeyVec& getKeys() { return _keys; }
        String getData();
        String logStr() { return _logStr; }
    private:
        KeyVec _keys;
        mutable int _argc;
        mutable String _cmdStr;
        String _logStr;
    };


    class Reply : public Rpl
    {
        enum DeserializationStatus
        {
            ReplyTypeRead,
            SingleLineReplyIncomplete,
            BulkReplyLengthIncomplete,
            BulkReplyContentIncomplete,
            MultiBulkReplyLengthIncomplete,
            MultiBulkReplyBulkIncomplete,
            ReplyComplete
        };
    public:
        Reply();
        bool isStat() const { return _buff[0] == STATUS_TYPE; }
        bool isErr() const { return _buff[0] == ERROR_TYPE; }
        bool isInt() const { return _buff[0] == INTEGER_TYPE; }
        bool isNil() const { return _valLen == NIL_LEN && (_buff[0] == BULK_TYPE || _buff[0] == MULTI_BULK_TYPE); }
        bool isStr() const { return _valLen != NIL_LEN && _buff[0] == BULK_TYPE; }
        bool isArray() const { return _valLen != NIL_LEN && _buff[0] == MULTI_BULK_TYPE; }
        String getStat() const { return _buff.substr(_valOffset, _valLen); }
        String getErr() const { return _buff.substr(_valOffset, _valLen); }
        Long getInt() const { return _buff.substr(_valOffset, _valLen).toLong(0xffffffff); }
        String getStr() const { return _buff.substr(_valOffset, _valLen); }
        int arraySize() const { return _valLen; }
        RplPtr getArray(int idx) const{ if (idx >= 0 && idx < (int)_multiBulk.size()) return _multiBulk[idx]; else return 0; }
        void append(const unsigned char* data, int dataLen);
        int read(const unsigned char* data, int dataLen);
        bool complete() { return _status == ReplyComplete; }
    private:
        String _buff;
        int _curLen, _valLen, _valOffset;
        vector<ReplyPtr> _multiBulk;
        DeserializationStatus _status;
    };


    class Conn : public NetReceiver,
        public RecMutex
    {
        enum ConnStatus
        {
            IDLE,
            EXECUTING,
            SUCC,
            CLOSED,
            FAIL
        };
    public:
        Conn(const ApplicationPtr& application);
        ~Conn();
        bool init(const String &hostport, const Credential &credential, String &reason);
        void close();
        bool closed();
        bool usable(const String& hostport);
        bool exec(const CmdPtr& cmd, RplPtr& rpl, String& reason);
        bool exec(const vector<CmdPtr>& cmds, vector<RplPtr>& rpls, String& reason);
    private:
        void recv(const unsigned char* data,int dataLen);
        void onConnClose();
        bool sendCmds(const String& cmdStr, unsigned int cmdCnt);
        bool _exec(const String& cmdStr, unsigned int cmdCnt, vector<RplPtr>& rpls, String& reason);
        void _recv(const unsigned char *data, int dataLen);
        void _markExecuted();
    private:
        const ApplicationPtr _application;
        ConnStatus _status;
        void* _event;
        String _hostport;
        NetSenderPtr _sender;   //  ConnPtr和NetSenderPtr形成循环引用.必须依赖手动close()来打破循环.
        unsigned int _replyIdx;
        vector<ReplyPtr> _replies;
        String _reason; // 代码多处用_reason.empty()判断是否异常,所以每个异常流程都必须赋非空值.
    };


    class ConnPool : public Shared
    {
    public:
        ConnPool(const ApplicationPtr& application);
        ~ConnPool();
        void setRemote(const String& hostport, const Credential &credential, const ConnPtr& conn = 0, bool idle = false);
        ConnPtr getConn(String& reason);
        void releaseConn(const ConnPtr& conn);
        void __setStat();
    protected:
        const ApplicationPtr _application;
    private:
        RecMutex _poolMutex;
        String _hostport;
        Credential _credential;
        list<ConnPtr> _idleConns;
        set<ConnPtr> _busyConns;
    };


    class Client : public Cli
    {
    public:
        Client(const ApplicationPtr& application);
        virtual bool init(String& reason) = 0;
        virtual void onUpdateConfigs() override;
        virtual void onDeactivate() override;
        bool exec(const CmdPtr& cmd, RplPtr& reply, String& reason);
        virtual bool exec(const vector<CmdPtr>& cmds, vector<RplPtr>& rpls, String& reason) = 0;
        bool evalSha(const String& script, const Key& key, const StrVec& argv, RplPtr& rpl, String& reason);
        virtual bool evalSha(const String& script, const KeyVec& keys, const StrVec& argv, RplPtr& rpl, String& reason) = 0;
    protected:
        String getSha(const String& script, const ConnPtr& coon, String& reason);
    protected:
        const ApplicationPtr _application;
    private:
        RWMutex _scriptMutex;
        StrStrMap _scriptShas;
    };


    class NonslotClient : public Client
    {
    public:
        NonslotClient(const ApplicationPtr& application);
        bool exec(const vector<CmdPtr>& cmds, vector<RplPtr>& rpls, String& reason);
        bool evalSha(const String& script, const KeyVec& keys, const StrVec& argv, RplPtr& rpl, String& reason);
        bool scan(const String& cursor, const String& pattern, int preferCnt, String& nextCursor, StrSet& keys, String& reason);
    protected:
        ConnPoolPtr _pool;
    };


    class SimpleClient : public NonslotClient
    {
    public:
        SimpleClient(const ApplicationPtr& application, const String& hostport, const String &user, const String &pwd);
        virtual bool init(String& reason) override;
    private:
        const String _hostport;
        const Credential _credential;
    };


    class SentinelClient : public NonslotClient
    {
    private:
        static void* resolveThreadFunc(void* params);
    public:
        SentinelClient(const ApplicationPtr &application, const String &masterName, const list<String> &sentinelHostPorts, const String &user, const String &pwd, const String &sentinelUser, const String &sendtinelPwd);
        virtual bool init(String& reason) override;
        virtual void onDeactivate() override;
    private:
        bool _resolveMaster();
        void _resolveThreadFunc();
    private:
        const String _masterName;
        list<String> _sentinelHostPorts;
        const Credential _credential;
        const Credential _sentinelCredential;
        void* _initEvent;
        ConnPtr _verifyConn;
        bool _deactivate;
    };


    // redis & cluster
    class ClusterNode : public ConnPool
    {
    public:
        ClusterNode(const ApplicationPtr &application, const Credential &credential, const String &master = "", const StrSet &slaves = StrSet());
        bool testSlot(int slot);
        void setSlot(int slot);
        void clearSlot(int slot);
        ConnPtr getConn(String &reason);
        // 返回true表示跳转命中,无需再检查其他ClusterNode.若命中后获取新连接成功,为conn赋非空值.若获取失败,原因见reason.
        bool checkSlotMigrate(const String &migrateRemote, ConnPtr &conn, String &reason);
        bool setup(const String& migrateRemote, ConnPtr& conn, String& reason);
    public:
        String returnMaster(StrSet slaves) { slaves = _slaves; return _master; }
    private:
        RecMutex _mutex;
        Credential _credential;
        unsigned char _bitMap[CLUSTER_SLOTS/8];
        String _master;
        StrSet _slaves;
        Long _lastRoleTime;
    };


    class ClusterClient : public Client
    {
    private:
        static void* reloadThreadFunc(void* params);
    public:
        ClusterClient(const ApplicationPtr &application, const list<String> &clusterHostPorts, const String &user, const String &pwd);
        virtual bool init(String& reason) override;
        virtual void onDeactivate() override;
        bool exec(const vector<CmdPtr>& cmds, vector<RplPtr>& rpls, String& reason);
        bool evalSha(const String& script, const KeyVec& keys, const StrVec& argv, RplPtr& rpl, String& reason);
        bool scan(const String& cursor, const String& pattern, int preferCnt, String& nextCursor, StrSet& keys, String& reason);
    private:
        // 发生转移时的err格式'MOVED 3999 127.0.0.1:7379'/'ASK 3999 127.0.0.1:7379'.
        // err为跳转返回true,并尝试跳转检测.若检测成功,为conn赋非空值,并更新node为跳转目的node.若失败,原因存入reason.
        // err非跳转返回false.
        bool checkMovedAsk(const String& err, ClusterNodePtr& node, ConnPtr& conn, String& reason);
        ConnPtr getConn(const KeyVec& keys, ClusterNodePtr& node, String& reason);
        static unsigned int getSlot(const String& key);
        static unsigned int crc16(char *data_p, unsigned short length);
    private:
        bool _reloadCluster(String& reason);
        void reloadClusterEntry();
    private:
        const list<String> _clusterHostPorts;
        const Credential _credential;
        RWMutex _nodeMutex;
        vector<ClusterNodePtr> _nodes;
        bool _deactivate;
    };
}
#endif
