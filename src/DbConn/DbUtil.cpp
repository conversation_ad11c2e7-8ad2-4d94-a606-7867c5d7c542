﻿#include "DbUtil.h"
#include "PBEWithMD5AndDES.h"


namespace DbConn
{
    bool parseMonoUri(const Common::String& mongoUri, Common::String& parseUri)
    {
        do
        {
            if (mongoUri.find("mongodb://") == -1)
                break;

            Common::String user,pwd,host;
            int pos = mongoUri.find('@');
            if (pos != -1)
            {
                host = mongoUri.substr(pos+1);
                Common::String subUri = mongoUri.substr(10, pos - 10);
                pos = subUri.find(':');
                if (pos != -1)
                {
                    user = subUri.substr(0,pos);
                    pwd = subUri.substr(pos+1);
                    unsigned char* decrypt;
                    size_t outlen;
                    Common::String salt = "juphoon419708";
                    if (PBEWithMD5AndDES_decrypt((char*)pwd.c_str(), (unsigned char*)salt.c_str(), salt.size(), &decrypt, &outlen) != NULL)
                    {
                        pwd = Common::String((char*)decrypt, (int)outlen);
                        free(decrypt);
                    }
                    parseUri = "mongodb://" + user + ":" + pwd + "@" + host;
                }
                else
                    break;
            }
            else
                parseUri = mongoUri;

            return true;
        } while (0);

        return false;
    }

    URI::URI(const Common::String& uri)
    {
        do
        {
            int i = uri.find("://"), k;
            Common::String addrs;
            if (i == -1)
                break;
            k = uri.find('/', i + 3);
            _proto = uri.substr(0, i);
            addrs = uri.substr(i + 3, k - i - 3);

            Common::StrVec addrs_;
            addrs.split(addrs_, ";");
            if (addrs_.empty())
                break;
            Common::StrVec::const_iterator it = addrs_.begin();
            for (; it != addrs_.end(); ++it)
            {
                Common::String host, port;
                int j = it->find_last_of("]:");
                if (j == -1 || (*it)[j] == ']')
                {
                    host = *it;
                }
                else
                {
                    host = it->substr(0, j);
                    port = it->substr(j + 1);
                    int port_ = port.toInt(-1);
                    if (port_ < 1024 || port_ > 65535)
                        break;
                }
                if (host.size() > 2 && host[0] == '[' && host[host.size() - 1] == ']')
                    host = host.substr(1, host.size() - 2);
                if (host.empty())
                    break;

                _hosts.push_back(host);
                _ports.push_back(port);
            }
            if (it != addrs_.end())
                break;

            if (k != -1)
            {
                int l = uri.find('?', k + 1);
                if (l == -1)
                {
                    _path = uri.substr(k);
                }
                else
                {
                    _path = uri.substr(k, l - k);
                    Common::StrVec params;
                    uri.substr(l + 1).split(params, "&");
                    Common::StrVec::const_iterator it = params.begin();
                    for (; it != params.end(); ++it)
                    {
                        int o = it->find('=');
                        // 如果不含=,略过.
                        if (o != -1)
                        {
                            // 为简单起见,如果params含重复key,以排在前面的为准.由于Common::StrSet的排序特性,目前实际上是以最短的为准.
                            Common::String key = it->substr(0, o), value = it->substr(o + 1);
                            if (key == "pwd" || key == "spwd")
                            {
                                unsigned char* decrypt;
                                size_t outlen;
                                Common::String salt = "juphoon419708";
                                if (PBEWithMD5AndDES_decrypt((char*)value.c_str(), (unsigned char*)salt.c_str(), salt.size(), &decrypt, &outlen) != NULL)
                                {
                                    value = Common::String((char*)decrypt, (int)outlen);
                                    free(decrypt);
                                }
                                else
                                    UTIL_LOG_IFO("DbConn", "PBEWithMD5AndDES_decrypt error");
                            }
                            _params.insert(make_pair(key, value));
                        }
                    }
                }
            }
            _valid = true;
            return;
        } while (0);
        _valid = false;
        _proto.clear();
        _path.clear();
        _hosts.clear();
        _ports.clear();
        _params.clear();
    }

    bool URI::valid() const
    {
        return _valid;
    }

    Common::String URI::proto() const
    {
        return _proto;
    }

    Common::String URI::host() const
    {
        return *_hosts.begin();
    }

    Common::String URI::port() const
    {
        return *_ports.begin();
    }

    void URI::addrs(list<Common::String>& hosts, list<Common::String>& ports) const
    {
        hosts = _hosts;
        ports = _ports;
    }

    Common::String URI::path() const
    {
        return _path;
    }

    Common::String URI::param(const Common::String& key) const
    {
        Common::StrStrMap::const_iterator it = _params.find(key);
        return it == _params.end() ? "" : it->second;
    }

    const Common::StrStrMap& URI::params() const
    {
        return _params;
    }
}
