﻿#include "RedisClientI.h"
#include "DbReason.h"
#include "DbUtil.h"

namespace Redis
{
    const int DFLT_MAX_CONN_CNT             = 512;
    const unsigned int MAX_REPLY_TIME_OUT   = 4000;     // ms
    const Common::String INNER_ERROR = "inner-error";
    const Common::String NOT_SUPPORT_ERROR = "not_support_error";

    const unsigned int crc16tab[256] = {
        0x0000,0x1021,0x2042,0x3063,0x4084,0x50a5,0x60c6,0x70e7,
        0x8108,0x9129,0xa14a,0xb16b,0xc18c,0xd1ad,0xe1ce,0xf1ef,
        0x1231,0x0210,0x3273,0x2252,0x52b5,0x4294,0x72f7,0x62d6,
        0x9339,0x8318,0xb37b,0xa35a,0xd3bd,0xc39c,0xf3ff,0xe3de,
        0x2462,0x3443,0x0420,0x1401,0x64e6,0x74c7,0x44a4,0x5485,
        0xa56a,0xb54b,0x8528,0x9509,0xe5ee,0xf5cf,0xc5ac,0xd58d,
        0x3653,0x2672,0x1611,0x0630,0x76d7,0x66f6,0x5695,0x46b4,
        0xb75b,0xa77a,0x9719,0x8738,0xf7df,0xe7fe,0xd79d,0xc7bc,
        0x48c4,0x58e5,0x6886,0x78a7,0x0840,0x1861,0x2802,0x3823,
        0xc9cc,0xd9ed,0xe98e,0xf9af,0x8948,0x9969,0xa90a,0xb92b,
        0x5af5,0x4ad4,0x7ab7,0x6a96,0x1a71,0x0a50,0x3a33,0x2a12,
        0xdbfd,0xcbdc,0xfbbf,0xeb9e,0x9b79,0x8b58,0xbb3b,0xab1a,
        0x6ca6,0x7c87,0x4ce4,0x5cc5,0x2c22,0x3c03,0x0c60,0x1c41,
        0xedae,0xfd8f,0xcdec,0xddcd,0xad2a,0xbd0b,0x8d68,0x9d49,
        0x7e97,0x6eb6,0x5ed5,0x4ef4,0x3e13,0x2e32,0x1e51,0x0e70,
        0xff9f,0xefbe,0xdfdd,0xcffc,0xbf1b,0xaf3a,0x9f59,0x8f78,
        0x9188,0x81a9,0xb1ca,0xa1eb,0xd10c,0xc12d,0xf14e,0xe16f,
        0x1080,0x00a1,0x30c2,0x20e3,0x5004,0x4025,0x7046,0x6067,
        0x83b9,0x9398,0xa3fb,0xb3da,0xc33d,0xd31c,0xe37f,0xf35e,
        0x02b1,0x1290,0x22f3,0x32d2,0x4235,0x5214,0x6277,0x7256,
        0xb5ea,0xa5cb,0x95a8,0x8589,0xf56e,0xe54f,0xd52c,0xc50d,
        0x34e2,0x24c3,0x14a0,0x0481,0x7466,0x6447,0x5424,0x4405,
        0xa7db,0xb7fa,0x8799,0x97b8,0xe75f,0xf77e,0xc71d,0xd73c,
        0x26d3,0x36f2,0x0691,0x16b0,0x6657,0x7676,0x4615,0x5634,
        0xd94c,0xc96d,0xf90e,0xe92f,0x99c8,0x89e9,0xb98a,0xa9ab,
        0x5844,0x4865,0x7806,0x6827,0x18c0,0x08e1,0x3882,0x28a3,
        0xcb7d,0xdb5c,0xeb3f,0xfb1e,0x8bf9,0x9bd8,0xabbb,0xbb9a,
        0x4a75,0x5a54,0x6a37,0x7a16,0x0af1,0x1ad0,0x2ab3,0x3a92,
        0xfd2e,0xed0f,0xdd6c,0xcd4d,0xbdaa,0xad8b,0x9de8,0x8dc9,
        0x7c26,0x6c07,0x5c64,0x4c45,0x3ca2,0x2c83,0x1ce0,0x0cc1,
        0xef1f,0xff3e,0xcf5d,0xdf7c,0xaf9b,0xbfba,0x8fd9,0x9ff8,
        0x6e17,0x7e36,0x4e55,0x5e74,0x2e93,0x3eb2,0x0ed1,0x1ef0};

    int g_maxConnCnt;
    bool g_debug = false;

    static String to_hostport(const String &host, const String &port, int defaultPort)
    {
        String __port = port.empty() ? String(defaultPort) : port;
        if (host.find(":") >= 0)
            return "[" + host + "]:" + __port;
        return host + ":" + __port;
    }

    static list<String> to_hostport(const list<String> &hosts, const list<String> &ports, int defaultPort)
    {
        list<String> hostports;
        list<String>::const_iterator itHost = hosts.begin();
        list<String>::const_iterator itPort = ports.begin();
        for (; itHost != hosts.end() && itPort != ports.end(); itHost++, itPort++)
        {
            hostports.push_back(to_hostport(*itHost, *itPort, defaultPort));
        }

        return hostports;
    }

    static bool from_hostport(const String &hostport, String &host, String &port)
    {
        int pos = hostport.rfind(':');
        if (pos <= 0 || pos >= hostport.size() - 1)
            return false;

        host = hostport.substr(0, pos);
        port = hostport.substr(pos + 1);

        if (host.size() > 2 && host[0] == '[' && host[host.size() - 1] == ']')
            host = host.substr(1, host.size() - 2);

        if (host.empty() || port.empty())
            return false;

        int portNo = port.toInt(-1);
        if (portNo <= 0 || portNo >= 65535)
            return false;

        return true;
    }

    String Key::_namespace;

    CmdPtr Cmd::cmd(const String& cmd)
    {
        return new Command(cmd);
    }

    Command::Command(const String& cmd)
    {
        if (cmd.empty())
            throw Exception(INNER_ERROR);
        _argc = 1;
        _cmdStr = String(&BULK_TYPE, TYPE_LEN) + String(cmd.size()) + LINE_BREAK + cmd + LINE_BREAK;
        _logStr = cmd;
    }

    CmdPtr Command::p(const String& val)
    {
        if (_argc == -1)
            throw Exception(INNER_ERROR);
        ++_argc;
        _cmdStr += String(&BULK_TYPE, TYPE_LEN) + String(val.size()) + LINE_BREAK + val + LINE_BREAK;
        _logStr += " " + val;
        return this;
    }

    String Command::getData()
    {
        if (_argc > 0)
        {
            _cmdStr = String(&MULTI_BULK_TYPE, TYPE_LEN) + String(_argc) + LINE_BREAK + _cmdStr;
            _argc = -1;
        }
        return _cmdStr;
    }


    Reply::Reply()
    {
        // 若TYPE_LEN不为1,还需要加一种状态 ReplyTypeIncomplete且初始化为它.这里省略.
        _status = ReplyTypeRead;
        _curLen = 0;
        _valOffset = _valLen = -1;
    }

    void Reply::append(const unsigned char* data, int dataLen)
    {
        if (_multiBulk.empty())
            _buff += String((const char*)data, dataLen);
        else
            (*_multiBulk.rbegin())->append(data, dataLen);
    }

    int Reply::read(const unsigned char* data, int dataLen)
    {
        int lastCurLen = _curLen;
        bool multiBulkAppend = false;
        switch (_status)
        {
        case ReplyTypeRead:
            {
                _curLen = TYPE_LEN;
                switch (_buff[0])
                {
                case STATUS_TYPE:
                case ERROR_TYPE:
                case INTEGER_TYPE:
                    {
                        _valOffset = TYPE_LEN;
                        _status = SingleLineReplyIncomplete;
                        break;
                    }
                case BULK_TYPE:
                    {
                        _status = BulkReplyLengthIncomplete;
                        break;
                    }
                case MULTI_BULK_TYPE:
                    {
                        _status = MultiBulkReplyLengthIncomplete;
                        break;
                    }
                default:
                    {
                        throw Exception(DESERIALIZE_ERROR);
                    }
                }
                break;
            }
        case SingleLineReplyIncomplete:
            {
                int i = _buff.find(LINE_BREAK, _curLen - LINE_BREAK_LEN + 1);
                if (i == -1)
                {
                    _curLen += dataLen;
                }
                else
                {
                    _valLen = i - _valOffset;
                    _curLen = i + LINE_BREAK_LEN;
                    _status = ReplyComplete;
                }
                break;
            }
        case BulkReplyLengthIncomplete:
            {
                int i = _buff.find(LINE_BREAK, _curLen - LINE_BREAK_LEN + 1);
                if (i == -1)
                {
                    _curLen += dataLen;
                }
                else
                {
                    _curLen = i + LINE_BREAK_LEN;
                    _valLen = _buff.substr(TYPE_LEN, i - TYPE_LEN).toInt(INVALID_BULK_REPLY_LEN);
                    if (_valLen == NIL_LEN)
                    {
                        // 返回nil的批量回复协议格式是$-1\r\n
                        _status = ReplyComplete;
                    }
                    else if (_valLen >= 0)
                    {
                        // 返回空串的批量回复协议格式是$0\r\n\r\n
                        _valOffset = _curLen;
                        _status = BulkReplyContentIncomplete;
                    }
                    else
                    {
                        throw Exception(DESERIALIZE_ERROR);
                    }
                }
                break;
            }
        case BulkReplyContentIncomplete:
            {
                if (_buff.size() < _valOffset + _valLen + LINE_BREAK_LEN)
                {
                    _curLen = _buff.size();
                }
                else if (_buff.subequ(_valOffset + _valLen, LINE_BREAK, LINE_BREAK_LEN))
                {
                    _curLen = _valOffset + _valLen + LINE_BREAK_LEN;
                    _status = ReplyComplete;
                }
                else
                {
                    throw Exception(DESERIALIZE_ERROR);
                }
                break;
            }
        case MultiBulkReplyLengthIncomplete:
            {
                int i = _buff.find(LINE_BREAK, _curLen - LINE_BREAK_LEN + 1);
                if (i == -1)
                {
                    _curLen += dataLen;
                }
                else
                {
                    _curLen = i + LINE_BREAK_LEN;
                    _valLen = _buff.substr(TYPE_LEN, i - TYPE_LEN).toInt(INVALID_BULK_REPLY_LEN);
                    if (_valLen == NIL_LEN  // 返回nil的多批量回复协议格式是*-1\r\n
                        || _valLen == 0)    // 返回size为0的多批量回复协议格式是*0\r\n
                    {
                        _status = ReplyComplete;
                    }
                    else if (_valLen > 0)
                    {
                        _multiBulk.push_back(new Reply());
                        multiBulkAppend = true;
                        _status = MultiBulkReplyBulkIncomplete;
                    }
                    else
                    {
                        throw Exception(DESERIALIZE_ERROR);
                    }
                }
                break;
            }
        case MultiBulkReplyBulkIncomplete:
            {
                _curLen += (*_multiBulk.rbegin())->read(data, dataLen);
                if ((*_multiBulk.rbegin())->complete())
                {
                    if (_multiBulk.size() == _valLen)
                    {
                        _status = ReplyComplete;
                    }
                    else
                    {
                        _multiBulk.push_back(new Reply());
                        multiBulkAppend = true;
                    }
                }
                break;
            }
        }
        int readLen = _curLen - lastCurLen;
        if (multiBulkAppend)
            (*_multiBulk.rbegin())->append(data + readLen, dataLen - readLen);
        return readLen;
    }


    // 不考虑支持异步连接.一次redis连接的速度通常是很快的,占用线程的时间很短,因此不考虑线程池的占用问题.同步连接足以支持所有的需求.
    // 实现异步连接的难点在于超时.势必需要保存所有的异步连接,并在onSchd内对他们一一做超时判断,频繁的加解锁必不可少,最终性能会因此受到很大影响.
    Conn::Conn(const ApplicationPtr& application)
        : _application(application)
    {

    }

    Conn::~Conn()
    {
        if (_event != NULL)
        {
            destroyEvent(_event);
            _event = NULL;
        }
        // 不要试图在析构中_sender->close();
        // 这是因为Conn自身作为NetReciver也有ref存在于NetDriver中,必须调_sender->close();才能让这些ref也释放掉,才有可能触发~Conn().
    }

    bool Conn::init(const String &hostport, const Credential &credential, String &reason)
    {
        do
        {
            _hostport = hostport;
            String host, port;
            if (!from_hostport(hostport, host, port))
            {
                _reason = "invalid_hostport:" + hostport;
                break;
            }

            _event = createEvent();
            // 如果_event创建成功但后续init失败,将依赖于~Conn()释放.
            if (_event == NULL)
            {
                _reason = "create_event_error";
                break;
            }
            _sender = _application->getDriver()->connect("tcp", "", 0, host, port.toInt(0), this);
            if (!_sender)
            {
                _reason = "redis_connect_error:" + hostport;
                break;
            }

            _status = IDLE; // 复用exec做初始化,而exec的前提是conn处于IDLE状态.
            RplPtr rpl;
            if (credential.pwd.empty())
            {
                UTIL_LOG_IFO("RedisClient", "Redis connection init without AUTH, hostport:" + hostport);
                return true;
            }

            bool ret;
            if (credential.user.empty())
                ret = exec(Cmd::cmd("AUTH")->p(credential.pwd), rpl, _reason);
            else
                ret = exec(Cmd::cmd("AUTH")->p(credential.user)->p(credential.pwd), rpl, _reason);
            if (ret)
            {
                if (!rpl->isErr())
                {
                    UTIL_LOG_IFO("RedisClient", "Redis connection init ok, hostport:" + hostport + (credential.user.empty() ? Common::String() : ", user:" + credential.user));
                    return true;
                }

                _reason = rpl->getErr();
                if (_reason == "ERR Client sent AUTH, but no password is set")
                {
                    _reason.clear();
                    UTIL_LOG_IFO("RedisClient", "Redis connection init ok, no password needed, hostport:" + hostport + (credential.user.empty() ? Common::String() : ", user:" + credential.user));
                    return true;
                }
            }
            close();
        } while (0);
        _application->addStatisticsLong("RedisClient.ConnInitFail", 1);
        reason = GET_CONN_ERROR ":" + _reason;
        UTIL_LOG_ERR("RedisClient", "Redis connection init fail:" + reason + ", hostport:" + hostport + (credential.user.empty() ? Common::String() : ", user:" + credential.user));
        return false;
    }

    void Conn::close()
    {
        RecLock lock(this);
        if (_status == EXECUTING)
        {
            _markExecuted();
            _reason = "redis_conn_closed";
        }
        _status = CLOSED;
        if (_sender)
        {
            _sender->close();
            _sender = 0;
        }
    }

    bool Conn::closed()
    {
        return _status == CLOSED;
    }

    bool Conn::usable(const String& hostport)
    {
        RecLock lock(this);
        if (_status == IDLE && _hostport == hostport)
            return true;
        _status = CLOSED;
        if (_sender)
        {
            _sender->close();
            _sender = 0;
        }
        return false;
    }

    bool Conn::exec(const CmdPtr& cmd, RplPtr& rpl, String& reason)
    {
        CommandPtr command = CommandPtr::dynamicCast(cmd);
        ASSERT_REASON_RET(command, _application, reason);
        vector<RplPtr> rpls;
        UTIL_LOG_DBG("RedisClient", "content:exec command:" + command->logStr());
        if (!_exec(command->getData(), 1, rpls, reason))
            return false;
        rpl = rpls[0];
        return true;
    }

    bool Conn::exec(const vector<CmdPtr>& cmds, vector<RplPtr>& rpls, String& reason)
    {
        ASSERT_REASON_RET(!cmds.empty(), _application, reason);
        String cmdStrs;
        vector<CmdPtr>::const_iterator it = cmds.begin();
        do
        {
            CommandPtr command = CommandPtr::dynamicCast(*it);
            ASSERT_REASON_RET(command, _application, reason);
            cmdStrs += command->getData();
            UTIL_LOG_DBG("RedisClient", "content:exec batch command:" + command->logStr());
        } while (++it != cmds.end());
        return _exec(cmdStrs, cmds.size(), rpls, reason);
    }

    void Conn::recv(const unsigned char* data,int dataLen)
    {
        if (dataLen <= 0)
            return;
        if (g_debug)
            UTIL_LOG_DBG("RedisClient", "RedisClient recv:\n" + String((const char*)data, dataLen));
        bool executed = false;
        {
            RecLock lock(this);
            if (_status == EXECUTING)
            {
                try
                {
                    if (_replyIdx < _replies.size())
                        _recv(data, dataLen);
                    else
                        throw Exception(UNRECOGNIZED_DATA);
                }
                catch (const Exception& ex)
                {
                    _reason = String(ex.what());
                    _status = FAIL;
                }
                if (_status != EXECUTING)
                    executed = true;
            }
            else if (_status == FAIL)
            {
                _application->addStatisticsLong("RedisClient.RecvWhenError", 1);
            }
            else
            {
                _application->addStatisticsLong("RedisClient.RecvWhenNeitherExecNorError", 1);
                _status = FAIL;
            }
        }
        // markExecuted脱离锁的范围出于这样的考量:
        // 同步调用时markExecuted往往(当然不一定,取决于OS)会由setEvent立即触发线程切换,如果此时锁未释放,被点亮的线程又得被迫等待.
        if (executed)
            _markExecuted();
    }

    void Conn::onConnClose()
    {
        RecLock lock(this);
        if (_status == CLOSED)
            return;
        _application->addStatisticsLong("RedisClient.ConnClosed", 1);
        if (_status == EXECUTING)
        {
            _markExecuted();
            _reason = "redis_conn_closed";
        }
        _status = CLOSED;
        _sender = 0;
    }

    bool Conn::sendCmds(const String& cmdStr, unsigned int cmdCnt)
    {
        int dataLen;
        const unsigned char* data = cmdStr.getData(dataLen);

        // 单个conn加锁后不支持边发边收.几乎不影响性能,但能极大简化程序逻辑,避免异常时的崩溃.
        RecLock lock(this);
        // 即使在exec之前加了usable,也不能保证在usable和此处之间一定不会发生onConnClose.
        // 另外,如果调用者连续使用同一个Conn,在多次exec之间仍然有可能onConnClose.
        if (_status == CLOSED)
        {
            _reason = "redis_send_but_closed";
            return false;
        }
        int sendLenSum = 0;
        do
        {
            int sendLen = _sender->send(data + sendLenSum, dataLen - sendLenSum);
            if (sendLen < 0)
            {
                _reason = "redis_send_error";
                _status = FAIL;
                _application->addStatisticsLong("RedisClient.SendError", 1);
                return false;
            }
            sendLenSum += sendLen;
        } while (sendLenSum < dataLen);
        if (g_debug)
            UTIL_LOG_DBG("RedisClient", "RedisClient send:\n" + cmdStr);

        _reason.clear();
        _status = EXECUTING;
        _replyIdx = 0;
        _replies.clear();
        unsigned int i = 0;
        do
        {
            _replies.push_back(new Reply());
        } while (++i < cmdCnt);
        return true;
    }

    bool Conn::_exec(const String& cmdStr, unsigned int cmdCnt, vector<RplPtr>& rpls, String& reason)
    {
        rpls.clear();
        reason.clear();
        if (sendCmds(cmdStr, cmdCnt))
        {
            bool waited = waitEvent(_event, MAX_REPLY_TIME_OUT);
            RecLock lock(this);
            if (waited)
            {
                if (_status == SUCC)
                {
                    rpls.insert(rpls.begin(), _replies.begin(), _replies.end());
                    _replies.clear();
                    _status = IDLE;
                    return true;
                }
            }
            else
            {
                _reason = EXEC_TIMEOUT_ERROR;
                _status = FAIL;
            }
        }
        reason = _reason;
        _application->addStatisticsLong("RedisClient.ExecFail." + reason.substr(reason.find(':') + 1), 1);
        return false;
    }

    void Conn::_recv(const unsigned char *data, int dataLen)
    {
        ReplyPtr reply = _replies[_replyIdx];
        reply->append(data, dataLen);
        int readLen = 0;
        bool replyComplete = false;
        do
        {
            readLen += reply->read(data + readLen, dataLen - readLen);
            replyComplete = reply->complete();
        } while (!replyComplete && readLen < dataLen);
        if (replyComplete)
        {
            if (++_replyIdx >= _replies.size())
            {
                if (readLen == dataLen)
                    _status = SUCC;
                else
                    throw Exception(UNRECOGNIZED_DATA);
            }
            else if (readLen < dataLen)
            {
                _recv(data + readLen, dataLen - readLen);
            }
        }
    }

    void Conn::_markExecuted()
    {
        setEvent(_event);
    }


    ConnPool::ConnPool(const ApplicationPtr& application)
        : _application(application)
    {

    }

    ConnPool::~ConnPool()
    {
        setRemote("", Credential());
    }

    void ConnPool::setRemote(const String &hostport, const Credential &credential, const ConnPtr &conn, bool idle)
    {
        RecLock lock(_poolMutex);
        if (_hostport == hostport && _credential == credential)
            return;

        if (!_hostport.empty())
        {
            // Conn本身是NetReceiver,它和Conn::_sender构成实质上的循环引用,需手动打破以触发析构.
            list<ConnPtr>::iterator it1 = _idleConns.begin();
            for (; it1 != _idleConns.end(); ++it1)
                (*it1)->close();
            _idleConns.clear();
            set<ConnPtr>::iterator it2 = _busyConns.begin();
            for (; it2 != _busyConns.end(); ++it2)
                (*it2)->close();
            _busyConns.clear();
            __setStat();
        }
        _hostport = hostport;
        _credential = credential;
        if (conn)
        {
            if (idle)
                _idleConns.push_back(conn);
            else
                _busyConns.insert(conn);
            __setStat();
        }
    }

    ConnPtr ConnPool::getConn(String& reason)
    {
        bool retry = true;
        do
        {
            String hostport;
            ConnPtr conn;
            {
                RecLock lock(_poolMutex);
                if (_hostport.empty())
                {
                    ALERT_REASON(_application, reason);
                    return 0;
                }
                hostport = _hostport;
                list<ConnPtr>::iterator idleConnIt = _idleConns.begin();
                while (idleConnIt != _idleConns.end())
                {
                    conn = *idleConnIt;
                    _idleConns.erase(idleConnIt++);
                    // 如果在身处idle队列中时变为了CLOSED,让其在此处析构掉
                    if (conn->usable(_hostport))
                    {
                        _busyConns.insert(conn);
                        __setStat();
                        return conn;
                    }
                    conn = 0;
                }
                if ((int)_busyConns.size() >= g_maxConnCnt)
                {
                    reason = GET_CONN_ERROR ":redis_reach_max_conn_cnt";
                    return 0;
                }
            }
            conn = new Conn(_application);
            if (!conn->init(hostport, _credential, reason)) // 锁外运行init,提升性能.
                return 0;
            RecLock lock(_poolMutex);
            if (hostport == _hostport)
            {
                _busyConns.insert(conn);
                __setStat();
                return conn;
            }
            conn->close();
            if (retry)
            {
                retry = false;
                continue;
            }
            reason = GET_CONN_ERROR ":redis_remote_unsteady";
            return 0;
        } while (1);
    }

    void ConnPool::releaseConn(const ConnPtr& conn)
    {
        RecLock lock(_poolMutex);
        _busyConns.erase(conn);
        if (conn->usable(_hostport))
            _idleConns.push_back(conn);
        __setStat();
    }

    void ConnPool::__setStat()
    {
        _application->setStatistics("RedisClient." + _hostport + ".Cnt", String((int)_idleConns.size()) + "|" + String((int)_busyConns.size()));
    }


    Client::Client(const ApplicationPtr& application)
        : _application(application)
    {
        onUpdateConfigs();
    }

    void Client::onUpdateConfigs()
    {
        int maxConnCnt;
        if (!_application->getAppConfigAsInt("Redis.MaxConnCnt", maxConnCnt) || maxConnCnt <= 0 || maxConnCnt > 1024)
            maxConnCnt = DFLT_MAX_CONN_CNT;
        g_maxConnCnt = maxConnCnt;
        int debug;
        g_debug = _application->getAppConfigAsInt("Redis.Debug", debug) && debug > 0;
    }

    void Client::onDeactivate()
    {

    }

    bool Client::exec(const CmdPtr& cmd, RplPtr& rpl, String& reason)
    {
        vector<RplPtr> rpls;
        if (!exec(vector<CmdPtr>(1, cmd), rpls, reason))
            return false;
        rpl = rpls[0];
        return true;
    }

    bool Client::evalSha(const String& script, const Key& key, const StrVec& argv, RplPtr& rpl, String& reason)
    {
        return evalSha(script, KeyVec(1, key), argv, rpl, reason);
    }

    String Client::getSha(const String& script, const ConnPtr& conn, String& reason)
    {
        {
            ReadLock rl(_scriptMutex);
            StrStrMap::const_iterator it = _scriptShas.find(script);
            if (it != _scriptShas.end())
                return it->second;
        }

        RplPtr rpl;
        if (!conn->exec(Cmd::cmd("SCRIPT")->p("LOAD")->p(script), rpl, reason))
            return "";
        if (rpl->isErr())
        {
            reason = rpl->getErr();
            return "";
        }

        String sha = rpl->getStr();
        WriteLock wl(_scriptMutex);
        _scriptShas.insert(make_pair(script, sha));   // 退读锁加写锁中间有可能已经插入了该sha,导致插入失败,但并不影响逻辑.这是因为同样的一个lua,总是会生成固定的sha.
        UTIL_LOG_DBG("RedisClient", "content:load script sha:" + sha);
        return sha;
    }


    NonslotClient::NonslotClient(const ApplicationPtr& application)
        : Client(application)
    {

    }

    bool NonslotClient::exec(const vector<CmdPtr>& cmds, vector<RplPtr>& rpls, String& reason)
    {
        ConnPtr conn = _pool->getConn(reason);
        if (!conn)
            return false;
        bool rslt = conn->exec(cmds, rpls, reason);
        _pool->releaseConn(conn);
        return rslt;
    }

    bool NonslotClient::evalSha(const String& script, const KeyVec& keys, const StrVec& argv, RplPtr& rpl, String& reason)
    {
        ConnPtr conn = _pool->getConn(reason);
        if (!conn)
            return false;

        String sha = getSha(script, conn, reason);
        if (!sha.empty())
        {
            CmdPtr cmd = Cmd::cmd("EVALSHA")->p(sha)->p(keys.size());
            for (KeyVec::const_iterator it = keys.begin(); it != keys.end(); ++it)
                cmd->key(*it);
            for (StrVec::const_iterator it = argv.begin(); it != argv.end(); ++it)
                cmd->p(*it);
            conn->exec(cmd, rpl, reason)
            && rpl->isErr()
            && rpl->getErr() == "NOSCRIPT No matching script. Please use EVAL."
            && conn->exec(Cmd::cmd("SCRIPT")->p("LOAD")->p(script), rpl, reason)
            && !rpl->isErr()
            && conn->exec(cmd, rpl, reason);
        }
        _pool->releaseConn(conn);
        return reason.empty();
    }

    bool NonslotClient::scan(const String& cursor, const String& pattern, int preferCnt, String& nextCursor, StrSet& keys, String& reason)
    {
        reason = NOT_SUPPORT_ERROR;
        return false;
    }

    SimpleClient::SimpleClient(const ApplicationPtr &application, const String &hostport, const String &user, const String &pwd)
        : NonslotClient(application)
        , _hostport(hostport)
        , _credential(user, pwd)
    {
    }

    bool SimpleClient::init(String& reason)
    {
        ConnPtr conn = new Conn(_application);
        if (!conn->init(_hostport, _credential, reason))
            return false;
        _pool = new ConnPool(_application);
        _pool->setRemote(_hostport, _credential, conn, true);
        return true;
    }


    void* SentinelClient::resolveThreadFunc(void* params)
    {
        SentinelClientPtr client = (SentinelClient*)params; // SentinelClientPtr引用计数+1.
        client->_resolveThreadFunc();
        return NULL;
    }

    SentinelClient::SentinelClient(const ApplicationPtr &application, const String &masterName, const list<String> &sentinelHostPorts, const String &user, const String &pwd, const String &sentinelUser, const String &sendtinelPwd)
        : NonslotClient(application)
        , _masterName(masterName)
        , _sentinelHostPorts(sentinelHostPorts)
        , _credential(user, pwd)
        , _sentinelCredential(sentinelUser, sendtinelPwd)
        , _deactivate(false)
    {

    }

    bool SentinelClient::init(String& reason)
    {
        _initEvent = createEvent();
        if (_initEvent == NULL)
        {
            reason = "create_event_err";
            return false;
        }
        createThread(-1, &resolveThreadFunc, (void*)this);
        bool waited = waitEvent(_initEvent, 16000);
        destroyEvent(_initEvent);
        if (waited && _pool)
            return true;
        reason = "resolve_master_fail";
        _deactivate = true;
        return false;
    }

    void SentinelClient::onDeactivate()
    {
        NonslotClient::onDeactivate();
        _deactivate = true;
    }

    bool SentinelClient::_resolveMaster()
    {
        int i = (int)_sentinelHostPorts.size();
        do
        {
            ConnPtr resolveConn = new Conn(_application);
            RplPtr resolveRpl;
            String reason, hostport = *_sentinelHostPorts.begin();
            if (resolveConn->init(hostport, _sentinelCredential, reason))
            {
                bool resolveRslt = resolveConn->exec(Cmd::cmd("SENTINEL")->p("GET-MASTER-ADDR-BY-NAME")->p(_masterName), resolveRpl, reason);
                resolveConn->close();
                if (resolveRslt)
                {
                    if (resolveRpl->isArray() && resolveRpl->arraySize() == 2)
                    {
                        String masterHostPort = to_hostport(resolveRpl->getArray(0)->getStr(), resolveRpl->getArray(1)->getStr(), 26379);
                        _verifyConn = new Conn(_application);
                        if (_verifyConn->init(masterHostPort, _credential, reason))
                        {
                            RplPtr verifyRpl;
                            if (_verifyConn->exec(Cmd::cmd("ROLE"), verifyRpl, reason))
                            {
                                if (verifyRpl->isErr())
                                {
                                    reason = verifyRpl->getErr();
                                }
                                else if (verifyRpl->isArray() && verifyRpl->arraySize() > 0)
                                {
                                    if (verifyRpl->getArray(0)->getStr() == "master")
                                    {
                                        if (!_pool)
                                            _pool = new ConnPool(_application);
                                        _pool->setRemote(masterHostPort, _credential);
                                        return true;    // verify成功之后并不立即close _verifyConn,并依赖此连接继续监听master.
                                    }
                                }
                                else
                                {
                                    ALERT_REASON(_application, reason);
                                }
                            }
                            _verifyConn->close();
                        }
                        _verifyConn = 0;
                    }
                    else
                    {
                        ALERT_REASON(_application, reason);
                    }
                }
            }
            _sentinelHostPorts.push_back(hostport);
            _sentinelHostPorts.pop_front();
        } while (--i > 0);
        _application->addStatisticsLong("RedisClient.ResolveMasterFail", 1);
        return false;
    }

    void SentinelClient::_resolveThreadFunc()
    {
        bool rslt = _resolveMaster();
        setEvent(_initEvent);
        if (!rslt)
            return;

        while (!_deactivate)
        {
            sleep(3000);
            if (_verifyConn
                && !_verifyConn->closed())
            {
                RplPtr rpl;
                String reason;
                if (_verifyConn->exec(Cmd::cmd("ROLE"), rpl, reason)
                    && rpl->isArray()
                    && rpl->arraySize() > 0
                    && rpl->getArray(0)->getStr() == "master")
                    continue;
                _verifyConn->close();
                _verifyConn = 0;
            }
            _resolveMaster();
        }
    }

    ClusterNode::ClusterNode(const ApplicationPtr &application, const Credential &credential, const String &master, const StrSet &slaves)
        : ConnPool(application)
        , _credential(credential)
        , _master(master)
        , _slaves(slaves)
        , _lastRoleTime(-1)
    {
        if (!master.empty())
            setRemote(master, credential);
        memset(_bitMap, 0, CLUSTER_SLOTS/8);
    }

    bool ClusterNode::testSlot(int slot)
    {
        if (slot == -1)
            return true;
        if (slot < -1 || slot >= CLUSTER_SLOTS)
            return false;
        off_t byte = slot / 8;
        int bit = slot & 7;
        return (_bitMap[byte] & (1<<bit)) != 0;
    }

    void ClusterNode::setSlot(int slot)
    {
        if (slot < 0 || slot >= CLUSTER_SLOTS)
            return;
        off_t byte = slot / 8;
        int bit = slot & 7;
        if ((_bitMap[byte] & (1<<bit)) == 0)
            _bitMap[byte] |= 1<<bit;
    }

    void ClusterNode::clearSlot(int slot)
    {
        if (slot < 0 || slot >= CLUSTER_SLOTS)
            return;
        off_t byte = slot / 8;
        int bit = slot & 7;
        if ((_bitMap[byte] & (1<<bit)) != 0)
            _bitMap[byte] &= ~(1<<bit);
    }

    ConnPtr ClusterNode::getConn(String& reason)
    {
        ConnPtr conn = ConnPool::getConn(reason);
        if (conn)
            return conn;

        if (!reason.subequ(0, GET_CONN_ERROR))
            return 0;

        Long curTime = getCurTimeMs();
        RecLock lock(_mutex);
        if (curTime < _lastRoleTime + 3000)
            return 0;
        _lastRoleTime = curTime;
        // 遍历所有slave直至确认其中一台为master,不再对ROLE命令结果做详细解析.
        StrSet::const_iterator slaveIt = _slaves.begin();
        for (; slaveIt != _slaves.end(); ++slaveIt)
        {
            conn = new Conn(_application);
            if (!conn->init(*slaveIt, _credential, reason))
                continue;
            RplPtr rpl;
            if (conn->exec(Cmd::cmd("ROLE"), rpl, reason))
            {
                if (rpl->isArray()
                    && rpl->arraySize() == 3)
                {
                    if (rpl->getArray(0)->getStr() == "master")
                        break;
                }
                else
                    ALERT(_application);
            }
            conn->close();
        }
        if (slaveIt == _slaves.end())
        {
            reason = GET_CONN_ERROR ":all_node_init_fail";
            return 0;
        }
        setRemote(*slaveIt, _credential, conn);
        _slaves.insert(_master);
        _master = *slaveIt;
        _slaves.erase(slaveIt);
        return conn;
    }

    // 返回true表示跳转命中,无需再检查其他ClusterNode.若命中后获取新连接成功,为conn赋非空值.若获取失败,原因见reason.
    bool ClusterNode::checkSlotMigrate(const String &migrateRemote, ConnPtr &conn, String &reason)
    {
        StrSet::const_iterator slaveIt;
        RecLock lock(_mutex);
        if (migrateRemote != _master
            && (slaveIt = _slaves.find(migrateRemote)) == _slaves.end())
            return false;
        if (migrateRemote == _master)
        {
            conn = ConnPool::getConn(reason);
        }
        else
        {
            conn = new Conn(_application);
            if (conn->init(migrateRemote, _credential, reason)) // 简化逻辑,直接运行在锁内.
            {
                RplPtr rpl;
                if (conn->exec(Cmd::cmd("ROLE"), rpl, reason))
                {
                    if (rpl->isArray()
                        && rpl->arraySize() == 3)
                    {
                        if (rpl->getArray(0)->getStr() == "master")
                        {
                            setRemote(migrateRemote, _credential, conn);
                            _slaves.erase(slaveIt);
                            _slaves.insert(_master);
                            _master = migrateRemote;
                            return true;
                        }
                    }
                    else
                        ALERT(_application);
                }
                conn->close();
            }
            conn = 0;
        }
        return true;
    }

    bool ClusterNode::setup(const String &migrateRemote, ConnPtr &conn, String &reason)
    {
        conn = new Conn(_application);
        if (conn->init(migrateRemote, _credential, reason))
        {
            RplPtr r1, r2;
            if (conn->exec(Cmd::cmd("ROLE"), r1, reason))
            {
                if (r1->isArray()
                    && r1->arraySize() == 3
                    && (r2 = r1->getArray(2))->isArray())
                {
                    if (r1->getArray(0)->getStr() == "master")
                    {
                        RecLock lock(_mutex);
                        setRemote(migrateRemote, _credential, conn);
                        _master = migrateRemote;
                        int slaveCnt = r2->arraySize();
                        int i = 0;
                        for (; i < slaveCnt; ++i)
                            _slaves.insert(r2->getArray(i)->getArray(0)->getStr() + ":" + r2->getArray(i)->getArray(1)->getStr());
                        return true;
                    }
                }
                else
                    ALERT(_application);
            }
            conn->close();
        }
        conn = 0;
        return false;
    }


    void* ClusterClient::reloadThreadFunc(void* params)
    {
        ClusterClient* client = static_cast<ClusterClient *>(params);
        client->reloadClusterEntry();
        return NULL;
    }

    ClusterClient::ClusterClient(const ApplicationPtr &application, const list<String> &clusterHostPorts, const String &user, const String &pwd)
        : Client(application)
        , _clusterHostPorts(clusterHostPorts)
        , _credential(user, pwd)
        , _deactivate(false)
    {

    }

    bool ClusterClient::init(String& reason)
    {
        if (!_reloadCluster(reason))
            return false;

        createThread(-1, &reloadThreadFunc, (void*)this);
        return true;
    }

    void ClusterClient::onDeactivate()
    {
        _deactivate = true;
    }

    bool ClusterClient::exec(const vector<CmdPtr>& cmds, vector<RplPtr>& rpls, String& reason)
    {
        KeyVec keys;
        vector<CmdPtr>::const_iterator it = cmds.begin();
        for (; it != cmds.end(); ++it)
        {
            CommandPtr command = CommandPtr::dynamicCast(*it);
            ASSERT_REASON_RET(command, _application, reason);
            keys.insert(keys.end(), command->getKeys().begin(), command->getKeys().end());  // 即使有重复也没关系.
        }
        ClusterNodePtr master;
        ConnPtr conn = getConn(keys, master, reason);
        if (!conn)
            return false;
        bool retry = false;
        do
        {
            bool rslt = conn->exec(cmds, rpls, reason);
            master->releaseConn(conn);
            conn = 0;
            if (!rslt)
                return false;
            if (retry   // 只重试一遍.
                || !rpls[0]->isErr()
                || !checkMovedAsk(rpls[0]->getErr(), master, conn, reason)) // Conn::exec实现保证rpls.size()==cmds.size(). 而cmds为空进而keys为空时getConn会报错.
                return true;
            if (!conn)
                return false;
            retry = true;
        } while (1);
    }

    bool ClusterClient::evalSha(const String& script, const KeyVec& keys, const StrVec& argv, RplPtr& rpl, String& reason)
    {
        ClusterNodePtr master;
        ConnPtr conn = getConn(keys, master, reason);   // 不支持keys为空的eval.
        if (!conn)
            return false;

        String sha = getSha(script, conn, reason);
        if (sha.empty())
        {
            master->releaseConn(conn);
            return false;
        }
        CmdPtr cmd = Cmd::cmd("EVALSHA")->p(sha)->p(keys.size());
        for (KeyVec::const_iterator it = keys.begin(); it != keys.end(); ++it)
            cmd->key(*it);
        for (StrVec::const_iterator it = argv.begin(); it != argv.end(); ++it)
            cmd->p(*it);
        bool moveAskRetry = false;
        do
        {
            String err;
            bool rslt = conn->exec(cmd, rpl, reason);
            if (rslt
                && rpl->isErr()
                && (err = rpl->getErr()) == "NOSCRIPT No matching script. Please use EVAL."
                && conn->exec(Cmd::cmd("SCRIPT")->p("LOAD")->p(script), rpl, reason)
                && !rpl->isErr())
                continue;
            master->releaseConn(conn);
            conn = 0;
            if (!rslt)
                return false;
            if (moveAskRetry
                || err.empty()
                || !checkMovedAsk(err, master, conn, reason))
                return true;
            if (!conn)
                return false;
            moveAskRetry = true;
        } while (1);
    }

    bool ClusterClient::scan(const String& cursor, const String& pattern, int preferCnt, String& nextCursor, StrSet& keys, String& reason)
    {
        bool beginScan = cursor == SCAN_BEGIN;
        LongVec cursors;
        LongVec::iterator it1;
        vector<ClusterNodePtr> nodes;
        vector<ClusterNodePtr>::const_iterator it2;
        {
            ReadLock rl(_nodeMutex);
            // _nodes由Cli::create保证非空.
            if (beginScan)
            {
                cursors = LongVec(_nodes.size(), 0);
                nodes = _nodes;
            }
            else
            {
                // for example, "0;5;10;"
                int i = 0, j;
                it2 = _nodes.begin();
                do
                {
                    Long cr;
                    if (it2 == _nodes.end()
                        || (j = cursor.find(';', i)) <= i
                        || (cr = cursor.substr(i, j - i).toLong(-1)) < 0)
                    {
                        reason = "cursor_invalid";
                        return false;
                    }
                    cursors.push_back(cr);
                    nodes.push_back(*(it2++));
                    i = j + 1;
                } while (i < cursor.size());
            }
        }

        if (preferCnt <= 0)
            preferCnt = 1000;
        nextCursor.clear();
        keys.clear();
        ASSERT_REASON_RET(cursors.size() == nodes.size(), _application, reason);
        it1 = cursors.begin();
        it2 = nodes.begin();
        do
        {
            if (!beginScan && *it1 == 0)
                continue;
            ConnPtr conn = (*it2)->getConn(reason);
            CmdPtr cmd = Cmd::cmd("SCAN")->p(*it1);
            if (!pattern.empty())
                cmd->p("MATCH")->p(pattern);
            cmd->p("COUNT")->p(preferCnt);
            RplPtr rpl0, rpl1;
            bool rslt = conn->exec(cmd, rpl0, reason);
            (*it2)->releaseConn(conn);
            if (!rslt || (rpl0->isErr() && !(reason = rpl0->getErr()).empty()))
                return false;
            ASSERT_REASON_RET(rpl0->isArray() && rpl0->arraySize() == 2 && rpl0->getArray(0)->isInt(), _application, reason);
            *it1 = rpl0->getArray(0)->getInt();
            rpl1 = rpl0->getArray(1);
            ASSERT_REASON_RET(rpl1->isArray(), _application, reason);
            int i = rpl1->arraySize(), j = 0;
            while (j <= i - 1)
                keys.insert(rpl1->getArray(j++)->getStr());
            if ((int)keys.size() >= preferCnt)
                break;
        } while (++it1, ++it2 != nodes.end());
        it1 = cursors.begin();
        nextCursor.clear();
        bool endScan = true;
        do
        {
            if (*it1 != 0)
                endScan = false;
            nextCursor += String(*it1) + ";";
        } while (++it1 != cursors.end());
        if (endScan)
            nextCursor = SCAN_BEGIN;
        return true;
    }

    bool ClusterClient::checkMovedAsk(const String& err, ClusterNodePtr& node, ConnPtr& conn, String& reason)
    {
        bool moved;
        if (!(moved = err.subequ(0, "MOVED "))
            && !err.subequ(0, "ASK "))
            return false;

        do
        {
            ASSERT_REASON_BREAK((node && !conn) || (conn = 0), _application, reason);
            int i = moved ? 6 : 4;
            int j = err.find(' ', i);
            ASSERT_REASON_BREAK(j > i, _application, reason);
            int slot = err.substr(i, j - i).toInt(-1);
            String migrateRemote = err.substr(j + 1);
            // 直接锁住所有访问.等待_nodes更新完毕.
            WriteLock wl(_nodeMutex);
            vector<ClusterNodePtr>::const_iterator it = _nodes.begin();
            do
            {
                if (*it == node)
                {
                    if ((*it)->checkSlotMigrate(migrateRemote, conn, reason))
                    {
                        if (!moved)
                        {
                            ALERT_REASON(_application, reason); // master内部跳转一定是发生了主从切换,不可能是ASK.
                            conn = 0;
                        }
                        break;
                    }
                }
                else
                {
                    if ((*it)->checkSlotMigrate(migrateRemote, conn, reason))
                    {
                        if (moved)
                        {
                            // 发生了槽迁移
                            node->clearSlot(slot);
                            (*it)->setSlot(slot);
                        }
                        node = *it;
                        break;
                    }
                }
            }
            while (++it != _nodes.end());
            if (it != _nodes.end())
                break;
            // 槽迁移到了新增的ClusterNode.
            ClusterNodePtr newNode = new ClusterNode(_application, _credential);
            if (newNode->setup(migrateRemote, conn, reason))
            {
                if (moved)
                {
                    node->clearSlot(slot);
                    newNode->setSlot(slot);
                }
                node = newNode;
                _nodes.push_back(newNode);
            }
        } while (0);
        return true;
    }

    ConnPtr ClusterClient::getConn(const KeyVec& keys, ClusterNodePtr& node, String& reason)
    {
        if (keys.empty())
        {
            ReadLock rl(_nodeMutex);
            node = *_nodes.begin();
        }
        else
        {
            int slot = getSlot(keys[0]);
            unsigned int i = 1;
            for (; i < keys.size(); ++i)
            {
                if (slot != getSlot(keys[i]))
                {
                    reason = "keys_on_diff_slot";
                    return 0;
                }
            }

            ReadLock rl(_nodeMutex);
            vector<ClusterNodePtr>::const_iterator nodeIt = _nodes.begin();
            do
            {
                // 没有为testSlot加ClusterNode锁.testSlot函数的实现不需要在锁下运行.
                if ((*nodeIt)->testSlot(slot))
                {
                    node = *nodeIt;
                    break;
                }
            } while (++nodeIt != _nodes.end());
            if (nodeIt == _nodes.end())
            {
                ALERT_REASON(_application, reason);
                return 0;
            }
        }

        return node->getConn(reason);
    }

    unsigned int ClusterClient::getSlot(const String& key)
    {
        char* k;
        int len = key.size(), s, e;
        k = const_cast<char*>(key.c_str());
        for (s = 0; s < len; ++s)
        {
            if (k[s] == '{')
                break;
        }
        if (s == len)
            return crc16(k, len) & 0x3FFF;
        for (e = s + 1; e < len; ++e)
        {
            if (k[e] == '}')
                break;
        }
        if (e == len || e == s + 1)
            return crc16(k, len) & 0x3FFF;
        return crc16(k + s + 1, e - s - 1) & 0x3FFF;
    }

    unsigned int ClusterClient::crc16(char *data_p, unsigned short length)
    {
        int counter;
        unsigned int crc = 0;
        for (counter = 0; counter < length; ++counter)
            crc = (crc<<8) ^ crc16tab[((crc>>8) ^ *data_p++)&0x00FF];
        return crc;
    }

    bool ClusterClient::_reloadCluster(String& reason)
    {
        for (list<String>::const_iterator it = _clusterHostPorts.begin(); it != _clusterHostPorts.end(); it++)
        {
            ConnPtr conn = new Conn(_application);
            REASON_CONTINUE_IF(!conn->init(*it, _credential, reason), "cluster_node_init_fail");
            RplPtr r0;
            bool rslt = conn->exec(Cmd::cmd("CLUSTER")->p("NODES"), r0, reason);
            conn->close();
            conn = 0;
            if (!rslt)
                continue;
            REASON_CONTINUE_IF(r0->isErr(), r0->getErr());
            ASSERT_REASON_CONTINUE(r0->isStr(), _application, reason);
            StrVec result;
            r0->getStr().split(result, "\n");
            StrStrStrMap nodes;
            StrStrStrMap::iterator resultIt;
            for (int i = 0; i < result.size(); ++i)
            {
                StrVec strs;
                String masterId, slot;
                result[i].split(strs, " ");
                REASON_BREAK_IF(strs.size() < 8, "cluster_node_params_invalid");
                if (strs[2].find("master") > -1)
                {
                    if (strs.size() < 9) // 已发生主从切换，未上线，没有槽节点信息
                        continue;
                    REASON_BREAK_IF(strs[7] == "disconnected", "cluster_master_offline");
                        masterId = strs[0];
                    slot = strs[8];
                }
                else if (strs[2].find("slave") > -1)
                    masterId = strs[3];
                else
                    REASON_BREAK_IF(true, "cluster_node_flags_error");
                resultIt = nodes.find(masterId);
                if (resultIt == nodes.end())
                    resultIt = nodes.insert(make_pair(masterId, StrStrMap())).first;
                resultIt->second.insert(make_pair(extractNodeAddr(strs[1]), slot)); // 可能会出现这种格式192.168.2.23:7002@17002
            }
            REASON_BREAK_IF(!reason.empty(), reason);
            REASON_BREAK_IF(nodes.size() < 3, "cluster_at_least_3_nodes");
            vector<ClusterNodePtr> clusterNodes;
            int slotCount = 0;
            for (resultIt = nodes.begin(); resultIt != nodes.end(); ++resultIt)
            {
                String master;
                StrSet slaves;
                int start = 0, stop = 0;
                StrStrMap::const_iterator nodeIt = resultIt->second.begin();
                for (; nodeIt != resultIt->second.end(); ++nodeIt)
                {
                    if (!nodeIt->second.empty())
                    {
                        master = nodeIt->first;
                        int pos = nodeIt->second.find("-");
                        start = nodeIt->second.substr(0, pos).toInt(-1);
                        stop = nodeIt->second.substr(pos + 1).toInt(-1);
                        REASON_RET_IF((start < 0 || stop < 0), "cluster_reshard"); // 处于槽迁移状态  slot-<-slot
                    }
                    else
                    {
                        slaves.insert(nodeIt->first);
                    }
                }
                ClusterNodePtr node = new ClusterNode(_application, _credential, master, slaves);
                slotCount += stop - start + 1;
                while (start <= stop)
                    node->setSlot(start++);
                clusterNodes.push_back(node);
            }
            REASON_CONTINUE_IF(slotCount != CLUSTER_SLOTS, "slot_uncovered_err");
            WriteLock wl(_nodeMutex);
            for (int i = 0; i < clusterNodes.size(); i++)
            {
                for (int j = 0; j < _nodes.size(); j++)
                {
                    StrSet set1,set2;
                    if (clusterNodes[i]->returnMaster(set1) == _nodes[j]->returnMaster(set2))
                    {
                        if (set1.size() != set2.size())
                            break;
                        set1.insert(set2.begin(),set2.end());
                        if (set1.size() == set2.size())
                            clusterNodes[i] = _nodes[j];
                    }
                }
            }
            _nodes.swap(clusterNodes);
            return true;
        }
        return false;
    }

    void ClusterClient::reloadClusterEntry()
    {
        Common::sleep(3000);
        while (!_deactivate)
        {
            ApplicationPtr app = _application;
            if (!app)
                break;
            String reason;
            _reloadCluster(reason);
            app->setStatistics("RedisClient.ResolveClusterError", reason);
            Common::sleep(3000);
        }
    }

    const String Cli::SCAN_BEGIN    = "";
    const int Cli::SCAN_DFLT_CNT    = 10;

    CliPtr Cli::create(const ApplicationPtr& application, const String& uri)
    {
        ClientPtr c;
        String reason;
        DbConn::URI u(uri);
        if (u.valid())
        {
            if (u.proto() == "redis")
            {
                // redis://************:6379/?pwd=juphoon419708
                c = new SimpleClient(application, to_hostport(u.host(), u.port(), 6379), u.param("user"), u.param("pwd"));
            }
            else if (u.proto() == "sentinel")
            {
                // sentinel://************:26379;************:26379;************:26379/?master_name=pengl&pwd=juphoon419708
                list<String> hosts, ports;
                u.addrs(hosts, ports);
                c = new SentinelClient(application, u.param("master_name"), to_hostport(hosts, ports, 26379), u.param("user"), u.param("pwd"), u.param("suser"), u.param("spwd"));
            }
            else
            {
                // cluster://************:26379;************:26379;************:26379;.../?pwd=juphoon419708
                list<String> hosts, ports;
                u.addrs(hosts, ports);
                c = new ClusterClient(application, to_hostport(hosts, ports, 6379), u.param("user"), u.param("pwd"));
            }
            if (!c->init(reason))
                c = 0;

            String value;
            if (application->getAppConfig("redis.namespace", value) || application->getAppConfig("Redis.Namespace", value))
                Key::setNamespace(value);
            application->setStatistics("Redis.Namespace", value);
            application->setStatistics("Redis.Uri", uri);
        }
        else
        {
            reason = "redis_uri_invalid";
        }

        application->setStatistics("RedisClient.CreateClientErr", reason);
        return c;
    }
}