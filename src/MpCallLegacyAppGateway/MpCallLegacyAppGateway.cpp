//
// *****************************************************************************
// Copyright (c) 2023-2025 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//

#include "MpCallLegacyAppGateway.h"
#include "AppSession.h"
#include "Common/Common.h"
#include "Common/Error.h"
#include "Common/Property.h"
#include "Common/TypesPub.h"
#include "Common/Util.h"
#include "JSM/JSMEAgent.h"
#include "JSM/JSMEServer.h"
#include "MpCall/MpCallLegacyAppAgent.h"
#include "MpCallLegacyAppGateway/Error.h"
#include "SipGatewayEntry/SipGatewayEntryServer.h"
#include <exception>

static Common::String getValue(const Common::StrStrMap &params, const Common::String &key, const Common::String &defaultValue = "")
{
    auto it = params.find(key);
    return it != params.end() ? it->second : defaultValue;
}

static Common::String username(const Common::String &uri)
{
    size_t pos = uri.find(':');
    if (pos == std::string::npos)
        return uri;

    size_t pos1 = uri.find('@');
    if (pos1 == std::string::npos)
        return uri.substr(pos + 1);

    return uri.substr(pos + 1, pos1 - pos - 1);
}

Common::StrStrMap processConfigMap(const Common::ApplicationExPtr &application, const Common::String &configPrefix)
{
    Common::StrStrMap configs;
    application->getAppConfigs(configPrefix, configs);

    Common::StrStrMap result;
    for (auto it = configs.begin(); it != configs.end(); ++it)
    {
        Common::String coreNetId = it->first.substr(configPrefix.size() + 1);
        if (coreNetId.empty())
        {
            UTIL_LOG_WRN("LegacyAppGateway", "content:onUpdateConfigs invalid coreNetId:" + it->first);
            continue;
        }

        Common::StrVec domains;
        it->second.split(domains, ",");
        for (auto it2 = domains.begin(); it2 != domains.end(); ++it2)
        {
            Common::String key = *it2;
            key.trim();
            if (key.empty())
                continue;
            result[key] = coreNetId;
        }
    }

    Common::String stats;
    for (auto &kv : result)
        stats += kv.first + "=" + kv.second + " ";
    application->setStatistics(configPrefix, stats);

    return result;
}

namespace MpCall
{

void SipGatewayEntryServer::onDeactivate()
{
    Common::RecLock lock(_mutex);
    _app = 0;
}

void SipGatewayEntryServer::onUpdateConfigs()
{
    Common::RecLock lock(_mutex);

    if (!_app)
        return;

    _domainCoreNetIdMap = processConfigMap(_app, "LegacyAppGateway.SipCoreNetDomains");
    _coreNetIdMap = processConfigMap(_app, "LegacyAppGateway.SipCoreNetMap");
}

void SipGatewayEntryServer::onInvited_begin(const Common::ServerCallPtr &__call, const Common::String &conferenceId, const Common::String &callerAccount, const Common::String &calleeNumber, const Common::String &domainId, const Common::String &appId, const Common::String &deviceIp, const Common::StrStrMap &params)
{
    Common::String accountId = __call->getParam(CALL_PARAMS_ACCOUNT);
    if (accountId.empty())
    {
        UTIL_LOG_WRN("LegacyAppGateway", "content:onInvited no account id, conferenceId:" + conferenceId + " callee:" + calleeNumber);
        SipGatewayEntry::SipGatewayEntryServer::onInvited_end(__call, LegacyAppGatewayError::NoAccountId(EARGS("conferenceId", conferenceId, "callee", calleeNumber)));
        return;
    }

    Common::String privateRoomNumber;
    if (!queryPrivateRoomNumber(privateRoomNumber, conferenceId.toLong(0), __call->getParams()))
    {
        SipGatewayEntry::SipGatewayEntryServer::onInvited_end(__call, LegacyAppGatewayError::QueryFailed(EARGSWRAP("conferenceId", conferenceId, "callee", calleeNumber)));
        return;
    }
    if (privateRoomNumber.empty())
    {
        UTIL_LOG_WRN("LegacyAppGateway", "content:onInvited no private room number, account:" + accountId + " conferenceId:" + conferenceId + " callee:" + calleeNumber);
        SipGatewayEntry::SipGatewayEntryServer::onInvited_end(__call, LegacyAppGatewayError::NoPrivateRoomNumber(EARGS("conferenceId", conferenceId, "callee", calleeNumber)));
        return;
    }

    Common::String uui = getValue(params, "userToUserInfo");
    Common::String routeId = getRouteId(__call, domainId, params);
    Common::String bindingUser = getValue(params, "srcAudioUri");

    MpCall::LegacyAppEntryAgent agent = _app->createAgent("LegacyAppEntry/" + accountId + "/" + privateRoomNumber);
    Common::String caller = username(callerAccount);
    if (caller.empty())
        caller = callerAccount;
    if (!agent.invite(caller, calleeNumber, uui, routeId, bindingUser, __call->getParams()))
    {
        UTIL_LOG_WRN("LegacyAppGateway", "content:onInvited account:" + accountId + " udid:" + privateRoomNumber + " caller:" + caller + " callee:" + calleeNumber + " failed:" + Common::ObjectAgent::getLogInfo(2));
        SipGatewayEntry::SipGatewayEntryServer::onInvited_end(__call, LegacyAppGatewayError::InviteFailed(EARGSWRAP("udid", privateRoomNumber, "routeId", routeId)));
        return;
    }

    UTIL_LOG_IFO("LegacyAppGateway", "content:onInvited account:" + accountId + " udid:" + privateRoomNumber + " caller:" + caller + " callee:" + calleeNumber + " routeId:" + routeId + " uui:" + uui);
    SipGatewayEntry::SipGatewayEntryServer::onInvited_end(__call, true);
}

bool SipGatewayEntryServer::queryPrivateRoomNumber(Common::String &privateRoomNumber, Common::Long confNumber, const Common::CallParamsPtr &callParams)
{
    Common::String roomId;
    Common::String jsmiId;
    Common::StrStrMap params;
    Common::StrVec memberList;

    JSM::JSMEAgent agent = _app->createAgent("#JSME");
    if (!agent.query(confNumber, roomId, jsmiId, params, memberList, callParams))
    {
        UTIL_LOG_WRN("LegacyAppGateway", "content:queryPrivateRoomNumber query failed, confNum:" + Common::String(confNumber) + " reason:" + Common::ObjectAgent::getLogInfo(2));
        return false;
    }

    privateRoomNumber = params["userdefinedId"];
    UTIL_LOG_IFO("LegacyAppGateway", "content:queryPrivateRoomNumber confNum:" + Common::String(confNumber) + " udid:" + privateRoomNumber);
    return true;
}

Common::String SipGatewayEntryServer::getRouteId(const Common::ServerCallPtr &__call, const Common::String &domainId, const Common::StrStrMap &params)
{
    Common::RecLock lock(_mutex);

    Common::String routeId = getValue(params, "coreNetId");
    if (!routeId.empty())
    {
        auto it = _coreNetIdMap.find(routeId);
        if (it != _coreNetIdMap.end())
        {
            UTIL_LOG_DBG("LegacyAppGateway", "content:getRouteId from call params coreNetId:" + routeId + "->" + it->second);
            return it->second;
        }

        UTIL_LOG_DBG("LegacyAppGateway", "content:getRouteId from call params coreNetId:" + routeId);
        return routeId;
    }

    Common::String domain = domainId.empty() ? __call->getParam(CALL_PARAMS_DOMAIN) : domainId;
    auto it = _domainCoreNetIdMap.find(domain);
    if (it != _domainCoreNetIdMap.end())
    {
        UTIL_LOG_DBG("LegacyAppGateway", "content:getRouteId from call params domain:" + domain + "->" + it->second);
        return it->second;
    }

    UTIL_LOG_WRN("LegacyAppGateway", "content:getRouteId from call params domain:" + domain + " coreNetId not found");
    return "";
}

LegacyAppGateway::LegacyAppGateway(const Common::ApplicationExPtr &application, const SessionLocator::SessionLocatorPtr &locator)
    : Service::ServiceManagerI(application)
    , SipGatewayEntryServer(application)
    , _locator(locator)
{
    Common::String value;
    if (!application->getAppConfig("Locators.MpCall", value))
        application->setConfig("global.Locators.MpCall", "MpCallLocator");
    if (!application->getAppConfig("Locators.TpAppAgent", value))
        application->setConfig("global.Locators.TpAppAgent", "MpCallLocator");

    if (!application->getAppConfig("EventManager.MaxProcessors", value))
        application->setConfig("global.EventManager.MaxProcessors", "10");
}

bool LegacyAppGateway::onActivate()
{
    if (!_locator)
    {
        _locator = SessionLocator::SessionLocator::create(_application.get(), this, "MpCallLegacyAppGateway");
        if (!_locator)
        {
            UTIL_LOG_ERR("LegacyAppGateway", "content:create session locator failed.");
            return false;
        }
    }

    readRoomConfig();

    return Service::ServiceManagerI::onActivate();
}

void LegacyAppGateway::onDeactivate()
{
    if (_locator)
    {
        _locator->close();
        _locator = 0;
    }

    SipGatewayEntryServer::onDeactivate();
    Service::ServiceManagerI::onDeactivate();
}

void LegacyAppGateway::onShutdown()
{
    if (_locator)
    {
        _locator->close();
        _locator = 0;
    }

    SipGatewayEntryServer::onDeactivate();
    Service::ServiceManagerI::onShutdown();
}

void LegacyAppGateway::onSchd()
{
    Service::ServiceManagerI::onSchd();
}

void LegacyAppGateway::onUpdateConfigs()
{
    Service::ServiceManagerI::onUpdateConfigs();
    SipGatewayEntryServer::onUpdateConfigs();

    if (_locator)
        _locator->updateConfigs();

    initCategoryType();
}

bool LegacyAppGateway::getMainService(Common::ObjectServerPtr &service, Common::String &name, bool &famous)
{
    if (!_locator->activate(_mainAdapter))
    {
        UTIL_LOG_ERR("LegacyAppGateway", "content:init session locator failed.");
        return false;
    }

    service = this;
    name = "#JSME";
    famous = false;

    _mainAdapter->addServer("#SipGatewayEntry", this, false);
    return true;
}

bool LegacyAppGateway::isMainServiceReady(Common::String &reason)
{
    if (!_locator)
    {
        reason = "InvalidLocator";
        return false;
    }

    return true;
}

SessionLocator::SessionPtr LegacyAppGateway::onCreateSession(const Common::String &type, const Common::String &id, const Common::String &objectId)
{
    AppSessionPtr session;
    try
    {
        session = new AppSession(_application, id, this);
        session->_objectId = objectId;
    }
    catch (const std::exception &e)
    {
        UTIL_LOG_WRN("LegacyAppGateway", "content:create app session failed, type:" + type + " id:" + id + " reason:" + e.what());
        return nullptr;
    }

    UTIL_LOG_IFO("LegacyAppGateway", "content:create app session type:" + type + " id:" + id);
    return session.get();
}

Common::String LegacyAppGateway::addSession(const Common::String &id, const SessionLocator::SessionPtr &session)
{
    SessionLocator::SessionLocatorPtr locator = _locator;
    if (!locator)
        return "";

    return locator->addSession(_categoryType, id, session);
}

Common::String LegacyAppGateway::addEntrySession(const Common::String &id, const SessionLocator::SessionPtr &session)
{
    SessionLocator::SessionLocatorPtr locator = _locator;
    if (!locator)
        return "";

    return locator->addSession("LegacyAppEntry", id, session);
}

void LegacyAppGateway::loadRegionConfigs_begin(const Common::ServerCallPtr &__call)
{
    JSM::RegionConfig defaultConfig;
    JSM::RegionConfigMap otherConfigs;

    JSM::JSMEAgent agent = _application->createAgent("#JSME");
    if (!agent.loadRegionConfigs(defaultConfig, otherConfigs, __call->getParams()))
    {
        JSM::JSMEServer::loadRegionConfigs_end(__call, LegacyAppGatewayError::ProxyRpcFailed(EWRAP));
        return;
    }

    JSM::JSMEServer::loadRegionConfigs_end(__call, true, defaultConfig, otherConfigs);
}

void LegacyAppGateway::cancelReserve_begin(const Common::ServerCallPtr &__call, Common::Long confNumber, const Common::StrStrMap &params)
{
    Common::String accountId = __call->getParam(CALL_PARAMS_ACCOUNT);
    if (accountId.empty())
    {
        UTIL_LOG_WRN("LegacyAppGateway", "content:cancelReserve2 no account id, confNum:" + Common::String(confNumber));
        JSM::JSMEServer::cancelReserve2_end(__call, LegacyAppGatewayError::NoAccountId(ELOC));
        return;
    }

    Common::String roomId;
    Common::String jsmiId;
    Common::StrStrMap qparams;
    Common::StrVec memberList;

    JSM::JSMEAgent agentJsme = _application->createAgent("#JSME");
    if (!agentJsme.query(confNumber, roomId, jsmiId, qparams, memberList, __call->getParams()))
    {
        JSM::JSMEServer::cancelReserve_end(__call, LegacyAppGatewayError::QueryFailed(EWRAP));
        return;
    }

    auto it = qparams.find("userdefinedId");
    if (it == qparams.end())
    {
        JSM::JSMEServer::cancelReserve_end(__call, LegacyAppGatewayError::QueryFailed(ELOC));
        return;
    }

    Common::String privateRoomNumber = it->second;
    MpCall::LegacyAppEntryAgent agent = _application->createAgent("LegacyAppEntry/" + accountId + "/" + privateRoomNumber);
    if (!agent.cease(__call->getParams()))
    {
        UTIL_LOG_WRN("LegacyAppGateway", "content:cancelReserve failed, account:" + accountId + " confNum:" + Common::String(confNumber) + " udid:" + privateRoomNumber + " reason:" + Common::ObjectAgent::getLogInfo(2));
        JSM::JSMEServer::cancelReserve2_end(__call, LegacyAppGatewayError::JoinFailed(EARGSWRAP("udid", privateRoomNumber)));
        return;
    }

    UTIL_LOG_IFO("LegacyAppGateway", "content:cancelReserve account:" + accountId + " confNum:" + Common::String(confNumber) + " udid:" + privateRoomNumber);
    JSM::JSMEServer::cancelReserve2_end(__call, true);
}

void LegacyAppGateway::cancelReserve2_begin(const Common::ServerCallPtr &__call, const Common::String &privateRoomNumber, const Common::StrStrMap &params)
{
    Common::String accountId = __call->getParam(CALL_PARAMS_ACCOUNT);
    if (accountId.empty())
    {
        UTIL_LOG_WRN("LegacyAppGateway", "content:cancelReserve2 no account id, udid:" + privateRoomNumber);
        JSM::JSMEServer::cancelReserve2_end(__call, LegacyAppGatewayError::NoAccountId(ELOC));
        return;
    }

    MpCall::LegacyAppEntryAgent agent = _application->createAgent("LegacyAppEntry/" + accountId + "/" + privateRoomNumber);
    if (!agent.cease(__call->getParams()))
    {
        UTIL_LOG_WRN("LegacyAppGateway", "content:cancelReserve2 failed, account:" + accountId + " udid:" + privateRoomNumber + " reason:" + Common::ObjectAgent::getLogInfo(2));
        JSM::JSMEServer::cancelReserve2_end(__call, LegacyAppGatewayError::JoinFailed(EARGSWRAP("udid", privateRoomNumber)));
        return;
    }

    UTIL_LOG_IFO("LegacyAppGateway", "content:cancelReserve2 account:" + accountId + " udid:" + privateRoomNumber);
    JSM::JSMEServer::cancelReserve2_end(__call, true);
}

void LegacyAppGateway::query_begin(const Common::ServerCallPtr &__call, Common::Long confNumber)
{
    Common::String roomId;
    Common::String jsmiId;
    Common::StrStrMap params;
    Common::StrVec memberList;

    JSM::JSMEAgent agent = _application->createAgent("#JSME");
    if (!agent.query(confNumber, roomId, jsmiId, params, memberList, __call->getParams()))
    {
        JSM::JSMEServer::query_end(__call, LegacyAppGatewayError::ProxyRpcFailed(EWRAP));
        return;
    }

    JSM::JSMEServer::query_end(__call, true, roomId, jsmiId, params, memberList);
}

void LegacyAppGateway::query2_begin(const Common::ServerCallPtr &__call, const Common::String &privateRoomNumber, const Common::StrStrMap &params)
{
    Common::String roomId;
    Common::String jsmiId;
    Common::StrStrMap outParams;
    Common::StrVec memberList;

    JSM::JSMEAgent agent = _application->createAgent("#JSME");
    if (!agent.query2(privateRoomNumber, params, roomId, jsmiId, outParams, memberList, __call->getParams()))
    {
        JSM::JSMEServer::query2_end(__call, LegacyAppGatewayError::ProxyRpcFailed(EWRAP));
        return;
    }

    JSM::JSMEServer::query2_end(__call, true, roomId, jsmiId, params, memberList);
}

void LegacyAppGateway::add2_begin(const Common::ServerCallPtr &__call, const Common::String &privateRoomNumber, const Common::StrStrMap &params)
{
    Common::String accountId = __call->getParam(CALL_PARAMS_ACCOUNT);
    if (accountId.empty())
    {
        UTIL_LOG_WRN("LegacyAppGateway", "content:add2 no account id, udid:" + privateRoomNumber);
        JSM::JSMEServer::add2_end(__call, LegacyAppGatewayError::NoAccountId(ELOC));
        return;
    }

    Common::String ep;
    Common::StrStrMap outParams;
    MpCall::LegacyAppEntryAgent agent = _application->createAgent("LegacyAppEntry/" + accountId + "/" + privateRoomNumber);
    if (!agent.join(accountId, privateRoomNumber, _roomConfig, ep, outParams, __call->getParams()))
    {
        UTIL_LOG_WRN("LegacyAppGateway", "content:add2 failed, account:" + accountId + " udid:" + privateRoomNumber + " reason:" + Common::ObjectAgent::getLogInfo(2));
        JSM::JSMEServer::add2_end(__call, LegacyAppGatewayError::JoinFailed(EARGSWRAP("udid", privateRoomNumber)));
        return;
    }

    UTIL_LOG_IFO("LegacyAppGateway", "content:add2 account:" + accountId + " udid:" + privateRoomNumber + " ep:" + ep);
    JSM::JSMEServer::add2_end(__call, true, ep, outParams);
}

void LegacyAppGateway::createAgent_begin(const Common::ServerCallPtr &__call, const Common::String &domainId, const Common::String &appId, const Common::String &roomId, const Common::String &type, const Common::StrStrMap &params)
{
    Common::StrStrMap outParams;

    JSM::JSMEAgent agent = _application->createAgent("#JSME");
    if (!agent.createAgent(domainId, appId, roomId, type, params, outParams, __call->getParams()))
    {
        JSM::JSMEServer::createAgent_end(__call, LegacyAppGatewayError::ProxyRpcFailed(EWRAP));
        return;
    }

    JSM::JSMEServer::createAgent_end(__call, true, outParams);
}

void LegacyAppGateway::add3_begin(const Common::ServerCallPtr &__call, const Common::String &privateRoomNumber, const Common::StrStrMap &params)
{
    JSM::JSMEServer::add3_end(__call, LegacyAppGatewayError::NotSupport(ELOC));
}

void LegacyAppGateway::initCategoryType()
{
    if (!_categoryType.empty())
        return;

    SessionLocator::SessionLocatorPtr locator = _locator;
    if (!locator)
        return;

    if (!locator->addSessionType("LegacyAppEntry"))
    {
        UTIL_LOG_ERR("LegacyAppGateway", "content:add session type:LegacyAppEntry");
        return;
    }

    if (!locator->addSessionType("LegacyAppCall"))
    {
        UTIL_LOG_ERR("LegacyAppGateway", "content:add session type:LegacyAppCall");
        locator->removeSessionType("LegacyAppEntry");
        return;
    }

    _categoryType = "LegacyAppCall";
    _application->setStatistics("LegacyAppGateway.CategoryType", _categoryType);
}

void LegacyAppGateway::readRoomConfig()
{
    static const char *prefix = "MpCallRoomConfig.";
    static size_t prefixLen = strlen(prefix);

    Common::StrStrMap configs;
    _application->getAppConfigs(prefix, configs);

    for (auto kv : configs)
    {
        if (kv.first.size() <= prefixLen)
            continue;

        Common::String key = kv.first.substr(prefixLen);
        _roomConfig[key] = kv.second;
        _application->setStatistics(kv.first, kv.second);
    }
}

} // namespace MpCall
