//
// *****************************************************************************
// Copyright (c) 2025 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//

#include "Mocks/MockApp.h"
#include "MpCall/MpCallSessionServerMock.h"
#include "JSM/JSMSServerMock.h"
#include "JSM/JSMEServerMock.h"

#include "MpCall/MpCallLegacyAppAgent.h"
#include "MpCallLegacyAppGateway/MpCallLegacyAppGateway.h"
#include "JSM/JSMSAgent.h"
#include "JSM/JSMEAgent.h"
#include "SipGatewayEntry/SipGatewayEntryAgent.h"
#include "Common/Property.h"

using ::testing::_;
using ::testing::Eq;

class JoinTest : public ModuleTest::MockServiceTest<MpCall::LegacyAppGateway>
{
public:
    Common::StrStrMap appConfigs() override
    {
        return {
            {"global.Main.Endpoints", "sudp"},
            {"global.MpCallRoomConfig.ConfigKeyRecordLayout", "TestRecordLayout"},
            {"global.MpCallRoomConfig.ConfigKeyOrdinaryRecord", "0"},
        };
    }

    void SetUp() override
    {
        ModuleTest::MockServiceTest<MpCall::LegacyAppGateway>::SetUp();
        _mpcallServer = AddCategoryServer<ModuleTest::SessionServerMockBase>("MpCall");
        _tpAppServer = AddCategoryServer<ModuleTest::SessionEndpointServerMockBase>("TpAppAgent");
        _jsmiServer = AddServer<ModuleTest::JSMIServerMockBase>("JSMI");
        _jsmeServer = AddServer<ModuleTest::JSMEServerMockBase>("#JSME");

        _callParams = Common::CallParams::create();
        _callParams->setParam(CALL_PARAMS_ACCOUNT, "[username:<EMAIL>]");
        _callParams->setParam(CALL_PARAMS_DOMAIN, "100645");
        _callParams->setParam(CALL_PARAMS_APP, "4");
        Common::sleep(3000);
    }

    void TearDown() override
    {
        _jsmeServer = 0;
        _jsmiServer = 0;
        _tpAppServer = 0;
        _mpcallServer = 0;
        ModuleTest::MockServiceTest<MpCall::LegacyAppGateway>::TearDown();
    }

    ModuleTest::SessionServerMockBasePtr _mpcallServer;
    ModuleTest::SessionEndpointServerMockBasePtr _tpAppServer;
    ModuleTest::JSMIServerMockBasePtr _jsmiServer;
    ModuleTest::JSMEServerMockBasePtr _jsmeServer;
    Common::String _listenerOid;
    Common::CallParamsPtr _callParams;
};

TEST_F(JoinTest, Normal)
{
    // start join room
    EXPECT_CALL(*(_mpcallServer.get()), join_begin(_, _, _, _))
        .WillOnce([](const Common::ServerCallPtr &__call, MpCall::UserType userType, const Common::String &userId, const Common::StrStrMap &params) {
            Common::StrStrMap expectedParams = params;
            std::cout << "======================== expectedParams: " << expectedParams.size() << std::endl;
            for (auto &kv : expectedParams)
                std::cout << kv.first.c_str() << ":" << kv.second.c_str() << std::endl;
            EXPECT_TRUE(expectedParams["ConfigKeyRecordLayout"] == "TestRecordLayout");
            EXPECT_TRUE(expectedParams["ConfigKeyOrdinaryRecord"] == "0");
            MpCall::RoomConfig roomConfig;
            roomConfig.jsmiOid = "JSMI";
            MpCall::SessionServer::join_end(__call, true, "1234", roomConfig, MpCall::MemberInfoList());
        });

    EXPECT_CALL(*(_jsmiServer.get()), addListener(_, _, _))
        .WillOnce([this](const Common::ServerCallPtr &__call, const Common::String &oid, const Common::StrStrMap &params) {
            _listenerOid = oid;
            return true;
        });

    JSM::JSMEAgent jsmeAgent = _app->createAgent("#JSME", false);
    ASSERT_TRUE(jsmeAgent->setRouter(_app->createAgent("MpCallLegacyAppGateway")));
    Common::String ep;
    Common::StrStrMap outParams;
    ASSERT_TRUE(jsmeAgent.add2("1234", Common::StrStrMap(), ep, outParams, _callParams));

    // notify join event
    EXPECT_CALL(*(_tpAppServer.get()), event_begin(_, _, _))
        .WillRepeatedly([](const Common::ServerCallPtr &__call, const Common::String &name, const Common::String &detail) {
            MpCall::SessionEndpointServer::event_end(__call, true);
        });

    JSM::ConferenceListenerAgent listenerAgent = _app->createAgent(_listenerOid);
    ASSERT_TRUE(listenerAgent);
    Common::sleep(100);
    Common::String content = R"({"pub":"join", "actor":{"[username:<EMAIL>]":{"role":7,"state":7,"idx":0,"name":"alice"}}})";
    ASSERT_TRUE(listenerAgent.onEvent("1234", content, Common::StrStrMap()));
    Common::sleep(100);
}
