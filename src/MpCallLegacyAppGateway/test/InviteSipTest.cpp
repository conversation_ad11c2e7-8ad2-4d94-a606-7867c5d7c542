//
// *****************************************************************************
// Copyright (c) 2025 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//

#include "Mocks/MockApp.h"
#include "MpCall/MpCallSessionServerMock.h"
#include "JSM/JSMSServerMock.h"
#include "JSM/JSMEServerMock.h"

#include "MpCall/MpCallLegacyAppAgent.h"
#include "MpCallLegacyAppGateway/MpCallLegacyAppGateway.h"
#include "JSM/JSMSAgent.h"
#include "JSM/JSMEAgent.h"
#include "SipGatewayEntry/SipGatewayEntryAgent.h"
#include "Common/Property.h"

using ::testing::_;
using ::testing::Eq;

class InviteSipTest : public ModuleTest::MockServiceTest<MpCall::LegacyAppGateway>
{
public:
    Common::StrStrMap appConfigs() override
    {
        return {
            {"global.Main.Endpoints", "sudp"},
            {"global.LegacyAppGateway.SipCoreNetDomains.TestLine", "100645,100646"},
            {"global.LegacyAppGateway.SipCoreNetMap.MapedLine", "legacyline"}
        };
    }

    void SetUp() override
    {
        ModuleTest::MockServiceTest<MpCall::LegacyAppGateway>::SetUp();
        _mpcallServer = AddCategoryServer<ModuleTest::SessionServerMockBase>("MpCall");
        _tpAppServer = AddCategoryServer<ModuleTest::SessionEndpointServerMockBase>("TpAppAgent");
        _jsmiServer = AddServer<ModuleTest::JSMIServerMockBase>("JSMI");
        _jsmeServer = AddServer<ModuleTest::JSMEServerMockBase>("#JSME");

        _callParams = Common::CallParams::create();
        _callParams->setParam(CALL_PARAMS_ACCOUNT, "[username:<EMAIL>]");
        _callParams->setParam(CALL_PARAMS_DOMAIN, "100645");
        _callParams->setParam(CALL_PARAMS_APP, "4");
        Common::sleep(3000);

        // start join room
        EXPECT_CALL(*(_mpcallServer.get()), join_begin(_, _, _, _))
            .WillOnce([](const Common::ServerCallPtr &__call, MpCall::UserType userType, const Common::String &userId, const Common::StrStrMap &params) {
                MpCall::RoomConfig roomConfig;
                roomConfig.jsmiOid = "JSMI";
                MpCall::SessionServer::join_end(__call, true, "1234", roomConfig, MpCall::MemberInfoList());
            });

        EXPECT_CALL(*(_jsmiServer.get()), addListener(_, _, _))
            .WillOnce([this](const Common::ServerCallPtr &__call, const Common::String &oid, const Common::StrStrMap &params) {
                _listenerOid = oid;
                return true;
            });

        JSM::JSMEAgent jsmeAgent = _app->createAgent("#JSME", false);
        ASSERT_TRUE(jsmeAgent->setRouter(_app->createAgent("MpCallLegacyAppGateway")));
        Common::String ep;
        Common::StrStrMap outParams;
        ASSERT_TRUE(jsmeAgent.add2("1234", Common::StrStrMap(), ep, outParams, _callParams));

        // notify join event
        EXPECT_CALL(*(_tpAppServer.get()), event_begin(_, _, _))
            .WillRepeatedly([](const Common::ServerCallPtr &__call, const Common::String &name, const Common::String &detail) {
                MpCall::SessionEndpointServer::event_end(__call, true);
            });

        JSM::ConferenceListenerAgent listenerAgent = _app->createAgent(_listenerOid);
        ASSERT_TRUE(listenerAgent);
        Common::sleep(100);
        Common::String content = R"({"pub":"join", "actor":{"[username:<EMAIL>]":{"role":7,"state":7,"idx":0,"name":"alice"}}})";
        ASSERT_TRUE(listenerAgent.onEvent("1234", content, Common::StrStrMap()));
        Common::sleep(100);
    }

    void TearDown() override
    {
        _jsmeServer = 0;
        _jsmiServer = 0;
        _tpAppServer = 0;
        _mpcallServer = 0;
        ModuleTest::MockServiceTest<MpCall::LegacyAppGateway>::TearDown();
    }

    ModuleTest::SessionServerMockBasePtr _mpcallServer;
    ModuleTest::SessionEndpointServerMockBasePtr _tpAppServer;
    ModuleTest::JSMIServerMockBasePtr _jsmiServer;
    ModuleTest::JSMEServerMockBasePtr _jsmeServer;
    Common::String _listenerOid;
    Common::CallParamsPtr _callParams;
};

TEST_F(InviteSipTest, InviteAcceptSelectRouteByDomain)
{
    // invite sip
    EXPECT_CALL(*(_jsmeServer.get()), query_begin(_, _))
        .WillOnce([this](const Common::ServerCallPtr &__call, const Common::Long confNumber) {
            JSM::JSMEServer::query_end(__call, true, "1234", "1234", {{"userdefinedId", "1234"}}, Common::StrVec());
        });

    EXPECT_CALL(*(_tpAppServer.get()), invite_begin(_, Eq(MpCall::TypeSip), Eq("1234567890"), _))
        .WillOnce([](const Common::ServerCallPtr &__call, MpCall::UserType calleeType, const Common::String &calleeId, const MpCall::InviteConfig &inviteConfig) {
            std::cout << "inviteConfig.routeId: " << inviteConfig.routeId.c_str() << std::endl;
            std::cout << "inviteConfig.callerNumber: " << inviteConfig.callerNumber.c_str() << std::endl;
            std::cout << "inviteConfig.bindingUser: " << inviteConfig.bindingUser.c_str() << std::endl;
            EXPECT_STREQ(inviteConfig.routeId.c_str(), "TestLine");
            EXPECT_STREQ(inviteConfig.callerNumber.c_str(), "alice");
            EXPECT_STREQ(inviteConfig.bindingUser.c_str(), "[username:<EMAIL>]");
            MpCall::SessionEndpointServer::invite_end(__call, true);
        });

    Common::StrStrMap params = {
        {"srcAudioUri", "[username:<EMAIL>]"}
    };
    SipGatewayEntry::SipGatewayEntryAgent sgAgent = _app->createAgent("#SipGatewayEntry", false);
    ASSERT_TRUE(sgAgent->setRouter(_app->createAgent("MpCallLegacyAppGateway")));
    ASSERT_TRUE(sgAgent.onInvited("1234", "[username:<EMAIL>]", "1234567890", "100645", "4", "", params, _callParams));
    Common::sleep(100);

    // notify alerted
    MpCall::LegacyAppGatewayAgent appGwAgent = _app->createAgent("LegacyAppCall/alice/1234");
    ASSERT_TRUE(appGwAgent.alerted("1234567890"));
    Common::sleep(100);

    // notify accept
    appGwAgent = _app->createAgent("LegacyAppCall/alice/1234");
    ASSERT_TRUE(appGwAgent.accepted("1234567890", false, MpCall::RoomConfig(), ""));
    Common::sleep(100);

    // notify terminated
    EXPECT_CALL(*(_jsmiServer.get()), removeListener(_, Eq(_listenerOid), _))
        .WillOnce([](const Common::ServerCallPtr &__call, const Common::String &objectId, const Common::StrStrMap &params) {
            return true;
        });

    EXPECT_CALL(*(_jsmiServer.get()), command_begin(_, _, _, _))
        .WillRepeatedly([](const Common::ServerCallPtr &__call, const Common::String &fromId, const Common::String &commandJson, const Common::StrStrMap &params) {
            return true;
        });

    ASSERT_TRUE(appGwAgent.terminated("TestOver"));
    Common::sleep(100);
}

TEST_F(InviteSipTest, InviteAcceptSelectRouteByCallParamsDomain)
{
    // invite sip
    EXPECT_CALL(*(_jsmeServer.get()), query_begin(_, _))
        .WillOnce([this](const Common::ServerCallPtr &__call, const Common::Long confNumber) {
            JSM::JSMEServer::query_end(__call, true, "1234", "1234", {{"userdefinedId", "1234"}}, Common::StrVec());
        });

    EXPECT_CALL(*(_tpAppServer.get()), invite_begin(_, Eq(MpCall::TypeSip), Eq("1234567890"), _))
        .WillOnce([](const Common::ServerCallPtr &__call, MpCall::UserType calleeType, const Common::String &calleeId, const MpCall::InviteConfig &inviteConfig) {
            EXPECT_EQ(inviteConfig.routeId, "TestLine");
            MpCall::SessionEndpointServer::invite_end(__call, true);
        });

    SipGatewayEntry::SipGatewayEntryAgent sgAgent = _app->createAgent("#SipGatewayEntry", false);
    ASSERT_TRUE(sgAgent->setRouter(_app->createAgent("MpCallLegacyAppGateway")));
    ASSERT_TRUE(sgAgent.onInvited("1234", "[username:<EMAIL>]", "1234567890", "", "", "", Common::StrStrMap(), _callParams));
    Common::sleep(100);

    // notify alerted
    MpCall::LegacyAppGatewayAgent appGwAgent = _app->createAgent("LegacyAppCall/alice/1234");
    ASSERT_TRUE(appGwAgent.alerted("1234567890"));
    Common::sleep(100);

    // notify accept
    appGwAgent = _app->createAgent("LegacyAppCall/alice/1234");
    ASSERT_TRUE(appGwAgent.accepted("1234567890", false, MpCall::RoomConfig(), ""));
    Common::sleep(100);

    // notify terminated
    EXPECT_CALL(*(_jsmiServer.get()), removeListener(_, Eq(_listenerOid), _))
        .WillOnce([](const Common::ServerCallPtr &__call, const Common::String &objectId, const Common::StrStrMap &params) {
            return true;
        });

    EXPECT_CALL(*(_jsmiServer.get()), command_begin(_, _, _, _))
        .WillRepeatedly([](const Common::ServerCallPtr &__call, const Common::String &fromId, const Common::String &commandJson, const Common::StrStrMap &params) {
            return true;
        });

    ASSERT_TRUE(appGwAgent.terminated("TestOver"));
    Common::sleep(100);
}

TEST_F(InviteSipTest, InviteAcceptSelectRouteByCoreNetId)
{
    // invite sip
    EXPECT_CALL(*(_jsmeServer.get()), query_begin(_, _))
        .WillOnce([this](const Common::ServerCallPtr &__call, const Common::Long confNumber) {
            JSM::JSMEServer::query_end(__call, true, "1234", "1234", {{"userdefinedId", "1234"}}, Common::StrVec());
        });

    EXPECT_CALL(*(_tpAppServer.get()), invite_begin(_, Eq(MpCall::TypeSip), Eq("1234567890"), _))
        .WillOnce([](const Common::ServerCallPtr &__call, MpCall::UserType calleeType, const Common::String &calleeId, const MpCall::InviteConfig &inviteConfig) {
            EXPECT_EQ(inviteConfig.routeId, "MapedLine");
            MpCall::SessionEndpointServer::invite_end(__call, true);
        });

    SipGatewayEntry::SipGatewayEntryAgent sgAgent = _app->createAgent("#SipGatewayEntry", false);
    ASSERT_TRUE(sgAgent->setRouter(_app->createAgent("MpCallLegacyAppGateway")));
    Common::StrStrMap params = {{"coreNetId", "legacyline"}};
    ASSERT_TRUE(sgAgent.onInvited("1234", "[username:<EMAIL>]", "1234567890", "", "", "", params, _callParams));
    Common::sleep(100);

    // notify alerted
    MpCall::LegacyAppGatewayAgent appGwAgent = _app->createAgent("LegacyAppCall/alice/1234");
    ASSERT_TRUE(appGwAgent.alerted("1234567890"));
    Common::sleep(100);

    // notify accept
    appGwAgent = _app->createAgent("LegacyAppCall/alice/1234");
    ASSERT_TRUE(appGwAgent.accepted("1234567890", false, MpCall::RoomConfig(), ""));
    Common::sleep(100);

    // notify terminated
    EXPECT_CALL(*(_jsmiServer.get()), removeListener(_, Eq(_listenerOid), _))
        .WillOnce([](const Common::ServerCallPtr &__call, const Common::String &objectId, const Common::StrStrMap &params) {
            return true;
        });

    EXPECT_CALL(*(_jsmiServer.get()), command_begin(_, _, _, _))
        .WillRepeatedly([](const Common::ServerCallPtr &__call, const Common::String &fromId, const Common::String &commandJson, const Common::StrStrMap &params) {
            return true;
        });

    ASSERT_TRUE(appGwAgent.terminated("TestOver"));
    Common::sleep(100);
}