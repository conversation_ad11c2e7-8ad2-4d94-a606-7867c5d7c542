//
// *****************************************************************************
// Copyright (c) 2025 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//

#include "Mocks/MockApp.h"
#include "MpCall/MpCallSessionServerMock.h"
#include "JSM/JSMSServerMock.h"
#include "Account/AccountPub2ServerMock.h"

#include "MpCall/MpCallLegacyAppAgent.h"
#include "MpCallLegacyAppGateway/MpCallLegacyAppGateway.h"
#include "JSM/JSMSAgent.h"
#include "JSM/JSMEAgent.h"

using ::testing::_;
using ::testing::Eq;

class InviteAppTest : public ModuleTest::MockServiceTest<MpCall::LegacyAppGateway>
{
public:
    Common::StrStrMap appConfigs() override
    {
        return {
            {"global.Main.Endpoints", "sudp"}
        };
    }

    void SetUp() override
    {
        ModuleTest::MockServiceTest<MpCall::LegacyAppGateway>::SetUp();
        _tpAppServer = AddCategoryServer<ModuleTest::SessionEndpointServerMockBase>("TpAppAgent");
        _jsmiServer = AddServer<ModuleTest::JSMIServerMockBase>("JSMI");
        _accountServer = AddCategoryServer<ModuleTest::AccountServerMockBase>("Account");
    }

    void TearDown() override
    {
        _accountServer = 0;
        _jsmiServer = 0;
        _tpAppServer = 0;
        ModuleTest::MockServiceTest<MpCall::LegacyAppGateway>::TearDown();
    }

    ModuleTest::SessionEndpointServerMockBasePtr _tpAppServer;
    ModuleTest::JSMIServerMockBasePtr _jsmiServer;
    ModuleTest::AccountServerMockBasePtr _accountServer;
    Common::String _listenerOid;
};

TEST_F(InviteAppTest, InviteAcceptVideo)
{
    Common::sleep(3000);

    // invite app
    EXPECT_CALL(*(_jsmiServer.get()), addListener(_, _, _))
        .WillOnce([this](const Common::ServerCallPtr &__call, const Common::String &oid, const Common::StrStrMap &params) {
            _listenerOid = oid;
            return true;
        });
    EXPECT_CALL(*(_accountServer.get()), sendOnlineMessage_begin(_, Eq("kMtcImOnlineMessage"), _, _))
        .WillOnce([this](const Common::ServerCallPtr &__call, const Common::String &type, const Common::StrStrMap &params, const Common::Stream &message) {
            Common::StrStrMap expectedParams = params;
            EXPECT_TRUE(expectedParams["MtcImSenderUriKey"] == "[username:<EMAIL>]");
            EXPECT_TRUE(expectedParams["MtcImTextKey"].find("SipConfInvite") > 0);

            Account::AccountServer::sendOnlineMessage_end(__call, true);
        });
    EXPECT_CALL(*(_tpAppServer.get()), accept_begin(_, Eq("**********"), Eq(true), _, _))
        .WillOnce([](const Common::ServerCallPtr &__call, const Common::String &callerId, bool isVideo, const Common::String &uui, const Common::StrStrMap &params) {
            MpCall::SessionEndpointServer::accept_end(__call, true, MpCall::RoomConfig(), MpCall::MemberInfoList());
        });

    MpCall::LegacyAppGatewayAgent appGwAgent = _app->createAgent("LegacyAppCall/alice/1234");
    MpCall::RoomConfig roomConfig;
    roomConfig.jsmiOid = "JSMI";
    roomConfig.domainId = 100645;
    roomConfig.appId = 4;
    roomConfig.udId = "1234100645_4";
    roomConfig.roomId = "**********";
    roomConfig.confNum = "********";
    MpCall::InviteConfig inviteConfig;
    inviteConfig.isVideo = true;
    inviteConfig.displayName = "**********";
    inviteConfig.uui = "some_uui";
    ASSERT_TRUE(appGwAgent.invitation(roomConfig.udId, "**********", "alice", roomConfig, inviteConfig));

    // notify join event
    EXPECT_CALL(*(_tpAppServer.get()), event_begin(_, _, _))
        .WillRepeatedly([](const Common::ServerCallPtr &__call, const Common::String &name, const Common::String &detail) {
            MpCall::SessionEndpointServer::event_end(__call, true);
        });

    JSM::ConferenceListenerAgent listenerAgent = _app->createAgent(_listenerOid);
    ASSERT_TRUE(listenerAgent);
    Common::sleep(100);
    Common::String content = R"({"pub":"join", "actor":{"[username:<EMAIL>]":{"role":7,"state":12,"idx":0,"name":"alice"}}})";
    ASSERT_TRUE(listenerAgent.onEvent("1234100645_4", content, Common::StrStrMap()));
    Common::sleep(100);

    // cancel room
    EXPECT_CALL(*(_tpAppServer.get()), leave_begin(_, Eq(true), _))
        .WillOnce([](const Common::ServerCallPtr &__call, bool terminate, const Common::String &detail) {
            MpCall::SessionEndpointServer::leave_end(__call, true);
        });

    EXPECT_CALL(*(_jsmiServer.get()), removeListener(_, Eq(_listenerOid), _))
        .WillOnce([](const Common::ServerCallPtr &__call, const Common::String &objectId, const Common::StrStrMap &params) {
            return true;
        });

    EXPECT_CALL(*(_jsmiServer.get()), command_begin(_, _, _, _))
        .WillRepeatedly([](const Common::ServerCallPtr &__call, const Common::String &fromId, const Common::String &commandJson, const Common::StrStrMap &params) {
            return true;
        });

    JSM::JSMEAgent jsmeAgent = _app->createAgent("#JSME", false);
    ASSERT_TRUE(jsmeAgent->setRouter(_app->createAgent("MpCallLegacyAppGateway")));
    Common::String ep;
    Common::StrStrMap outParams;
    Common::CallParamsPtr callParams = Common::CallParams::create();
    callParams->setParam("account", "[username:<EMAIL>]");
    ASSERT_TRUE(jsmeAgent.cancelReserve2("1234100645_4", Common::StrStrMap(), callParams));
    Common::sleep(100);
}

TEST_F(InviteAppTest, InviteFailed)
{
    Common::sleep(3000);

    // invite app
    EXPECT_CALL(*(_jsmiServer.get()), addListener(_, _, _))
        .WillOnce([this](const Common::ServerCallPtr &__call, const Common::String &oid, const Common::StrStrMap &params) {
            _listenerOid = oid;
            return true;
        });
    EXPECT_CALL(*(_accountServer.get()), sendOnlineMessage_begin(_, Eq("kMtcImOnlineMessage"), _, _))
        .WillOnce([this](const Common::ServerCallPtr &__call, const Common::String &type, const Common::StrStrMap &params, const Common::Stream &message) {
            Common::StrStrMap expectedParams = params;
            EXPECT_TRUE(expectedParams["MtcImSenderUriKey"] == "[username:<EMAIL>]");
            EXPECT_TRUE(expectedParams["MtcImTextKey"].find("SipConfInvite") > 0);

            Account::AccountServer::sendOnlineMessage_end(__call, false);
        });
    EXPECT_CALL(*(_tpAppServer.get()), reject_begin(_, Eq("**********"), _))
        .WillOnce([](const Common::ServerCallPtr &__call, const Common::String &callerId, const Common::String &reason) {
            MpCall::SessionEndpointServer::reject_end(__call, true);
        });

    MpCall::LegacyAppGatewayAgent appGwAgent = _app->createAgent("LegacyAppCall/alice/1234");
    MpCall::RoomConfig roomConfig;
    roomConfig.jsmiOid = "JSMI";
    roomConfig.domainId = 100645;
    roomConfig.appId = 4;
    roomConfig.udId = "1234100645_4";
    roomConfig.roomId = "**********";
    roomConfig.confNum = "********";
    MpCall::InviteConfig inviteConfig;
    inviteConfig.isVideo = false;
    inviteConfig.displayName = "**********";
    inviteConfig.uui = "some_uui";
    ASSERT_TRUE(appGwAgent.invitation(roomConfig.udId, "**********", "alice", roomConfig, inviteConfig));

    Common::sleep(100);
}

TEST_F(InviteAppTest, InviteCancel)
{
    Common::sleep(3000);

    // invite app
    EXPECT_CALL(*(_jsmiServer.get()), addListener(_, _, _))
        .WillOnce([this](const Common::ServerCallPtr &__call, const Common::String &oid, const Common::StrStrMap &params) {
            _listenerOid = oid;
            return true;
        });
    EXPECT_CALL(*(_accountServer.get()), sendOnlineMessage_begin(_, Eq("kMtcImOnlineMessage"), _, _))
        .WillOnce([this](const Common::ServerCallPtr &__call, const Common::String &type, const Common::StrStrMap &params, const Common::Stream &message) {
            Common::StrStrMap expectedParams = params;
            EXPECT_TRUE(expectedParams["MtcImSenderUriKey"] == "[username:<EMAIL>]");
            EXPECT_TRUE(expectedParams["MtcImTextKey"].find("SipConfInvite") > 0);

            Account::AccountServer::sendOnlineMessage_end(__call, true);
        });
    EXPECT_CALL(*(_tpAppServer.get()), accept_begin(_, Eq("**********"), Eq(false), _, _))
        .WillOnce([](const Common::ServerCallPtr &__call, const Common::String &callerId, bool isVideo, const Common::String &uui, const Common::StrStrMap &params) {
            MpCall::SessionEndpointServer::accept_end(__call, true, MpCall::RoomConfig(), MpCall::MemberInfoList());
        });

    MpCall::LegacyAppGatewayAgent appGwAgent = _app->createAgent("LegacyAppCall/alice/1234");
    MpCall::RoomConfig roomConfig;
    roomConfig.jsmiOid = "JSMI";
    roomConfig.domainId = 100645;
    roomConfig.appId = 4;
    roomConfig.udId = "1234100645_4";
    roomConfig.roomId = "**********";
    roomConfig.confNum = "********";
    MpCall::InviteConfig inviteConfig;
    inviteConfig.isVideo = false;
    inviteConfig.displayName = "**********";
    inviteConfig.uui = "some_uui";
    ASSERT_TRUE(appGwAgent.invitation(roomConfig.udId, "**********", "alice", roomConfig, inviteConfig));
    Common::sleep(1000);

    // cancel
    EXPECT_CALL(*(_accountServer.get()), sendOnlineMessage_begin(_, Eq("kMtcImOnlineMessage"), _, _))
        .WillOnce([this](const Common::ServerCallPtr &__call, const Common::String &type, const Common::StrStrMap &params, const Common::Stream &message) {
            Common::StrStrMap expectedParams = params;
            EXPECT_TRUE(expectedParams["MtcImSenderUriKey"] == "[username:<EMAIL>]");
            EXPECT_TRUE(expectedParams["MtcImTextKey"].find("SipConfCancel") > 0);

            Account::AccountServer::sendOnlineMessage_end(__call, true);
        });
    EXPECT_CALL(*(_jsmiServer.get()), removeListener(_, Eq(_listenerOid), _))
        .WillOnce([](const Common::ServerCallPtr &__call, const Common::String &objectId, const Common::StrStrMap &params) {
            return true;
        });
    EXPECT_CALL(*(_jsmiServer.get()), command_begin(_, _, _, _))
        .WillRepeatedly([](const Common::ServerCallPtr &__call, const Common::String &fromId, const Common::String &commandJson, const Common::StrStrMap &params) {
            return true;
        });

    ASSERT_TRUE(appGwAgent.canceled("alice", "some_reason"));
    Common::sleep(100);
}