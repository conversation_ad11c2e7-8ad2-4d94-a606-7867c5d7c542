//
// *****************************************************************************
// Copyright (c) 2023-2025 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//

#include "Common/Common.h"
#include "Common/Error.h"
#include "Common/Property.h"
#include "Common/TypesPub.h"
#include "Common/Util.h"
#include "Error.h"
#include "JSM/JSMSServer.h"
#include "MpCall/MpCallErrorPub.h"
#include "MpCall/MpCallEventPub.h"
#include "MpCall/MpCallPub.h"
#include "AppSession.h"
#include "JSMD/JsmdPub.h"
#include "ServiceUtil/Struct2Json.h"
#include "Account/AccountPub2Agent.h"

namespace MpCall
{

Common::String to_json(const Common::StrStrMap &map)
{
    Common::OputStreamPtr oput = Common::OputStream::create(Common::StreamJSON);
    __textWrite_StrStrMap(oput, "event", map);
    Common::String text = oput->saveText();
    return text.substr(9, text.size() - 10);
}

static Common::String getConfUri(const Common::String &roomId, const Common::String &jsmiId, const Common::String &confNum, const Common::String &title, bool isVideo)
{
    return "jsmi://" + roomId + "?jsmiId=" + jsmiId + "&directJsmiId=" + jsmiId + "&confNum=" + confNum + "&title=" + title + "&video=" + (isVideo ? "1" : "0") + "&viewmode=1&vidquality=0&vidsquare=0&security=0&privateToken=";
}

Common::String AppSession::checkState(bool &closed)
{
    enum State state = _state;
    closed = (state == StateTermed);

    MpCall::SessionEndpointAgent mpcall = _mpcallAgent;
    if (mpcall && !closed)
    {
        class Async : public Common::AgentAsync
        {
        public:
            virtual void cmdResult(int rslt, const Common::IputStreamPtr &iput, const Common::ObjectPtr &userdata) override
            {
                if (!MpCall::SessionEndpointAgent::event_end(rslt, iput))
                {
                    AppSessionPtr session = AppSessionPtr::dynamicCast(userdata);
                    if (session)
                        session->onNotifyEventFailed(Common::ObjectAgent::getLogInfo(2));
                }
            }
        };

        if (Common::getCurTicks() - _lastKeepAliveTicks > 30000)
        {
            mpcall.event_begin(new Async(), MpCall::Event_toString(MpCall::EventKeepAlive), "", 0, this);
            _lastKeepAliveTicks = Common::getCurTicks();
        }
    }

    return to_string(state);
}

bool AppSession::invitation(const Common::ServerCallPtr &__call, const Common::String &sessId, const Common::String &caller, const Common::String &callee, const MpCall::RoomConfig &roomConfig, const MpCall::InviteConfig &inviteConfig)
{
    class Async : public Common::AgentAsync
    {
    public:
        virtual void cmdResult(int rslt, const Common::IputStreamPtr &iput, const Common::ObjectPtr &userdata) override
        {
            bool success = Account::AccountAgent::sendOnlineMessage_end(rslt, iput);
            AppSessionPtr session = AppSessionPtr::dynamicCast(userdata);
            if (session)
                session->onSendInviteResult(success, Common::ObjectAgent::getLogInfo(2));
        }
    };

    std::lock_guard<std::mutex> lock(_mutex);

    if (_state != StateIdle)
    {
        UTIL_LOG_CONTEXT_WRN("AppSession", _logContext, "content:session invitation invalid state:" + to_string(_state));
        __call->setError(LegacyAppGatewayError::InvalidState(EARGS("state", _state)));
        __release();
        return false;
    }

    _sessId = sessId;
    _roomConfig = roomConfig;
    _peerUserId = caller;
    Common::String callerAccountId = "[username:" + caller + "@" + Common::String(_roomConfig.domainId) + ".cloud.justalk.com]";
    _userId = callee;
    _accountId = "[username:" + callee + "@" + Common::String(_roomConfig.domainId) + ".cloud.justalk.com]";

    if (!_clientParams)
    {
        _clientParams = Common::CallParams::create();
        _clientParams->setParam(CALL_PARAMS_ACCOUNT, _accountId);
        _clientParams->setParam(CALL_PARAMS_DOMAIN, Common::String(_roomConfig.domainId));
        _clientParams->setParam(CALL_PARAMS_APP, Common::String(_roomConfig.appId));
    }

    _logContext->setContext("user", _userId, Common::LogContext::LogMaskKeep);
    _logContext->setContext("sessId", _sessId, Common::LogContext::LogMaskKeep);
    _logContext->setContext("udid", _roomConfig.udId, Common::LogContext::LogMaskKeep);

    if (_sessionManager->addEntrySession(_accountId + "/" + _sessId, this).empty())
    {
        UTIL_LOG_CONTEXT_WRN("AppSession", _logContext, "content:session invitation add entry session failed, caller:" + caller);
        __call->setError(LegacyAppGatewayError::AddSessionFailed(EARGSWRAP("sessId", _sessId)));
        __release();
        return false;
    }

    _mpcallAgent = _app->createAgent("TpAppAgent/" + _userId + "/" + _sessId, false);
    _mpcallAgent->setParams(Common::CallParams::create()->enableTrace("MPCALL" + _sessId));
    JSM::JSMIAgent jsmi = _app->createAgent(_roomConfig.jsmiOid, false);
    if (!jsmi.addListener(_objectId, Common::StrStrMap(), _clientParams))
    {
        UTIL_LOG_CONTEXT_WRN("AppSession", _logContext, "content:session add listener failed:" + Common::ObjectAgent::getLogInfo(2));
        __call->setError(LegacyAppGatewayError::AddListenerFailed(EARGSWRAP("sessId", _sessId)));
        __release();
        return false;
    }
    _jsmiAgent = jsmi;

    _inviteMessage.Type = "SipConfInvite";
    _inviteMessage.ConfUri = getConfUri(_roomConfig.roomId, _roomConfig.jsmiOid, _roomConfig.confNum, _roomConfig.roomId, inviteConfig.isVideo);
    _inviteMessage.From = callerAccountId;
    _inviteMessage.UserToUserInfo = inviteConfig.uui;
    _inviteMessage.Video = inviteConfig.isVideo;

    Common::StrStrMap params;
    params["MtcImSenderUidKey"] = "<username:" + caller + "@" + Common::String(_roomConfig.domainId) + ">";
    params["MtcImSenderUriKey"] = callerAccountId;
    params["MtcImTextKey"] = ServiceUtil::to_json(_inviteMessage);
    params["notify"] = "kMtcImOnlineMessage";

    Account::AccountAgent accountAgent = _app->createAgent("Account/" + _accountId);
    accountAgent.sendOnlineMessage_begin(new Async(), Common::String("kMtcImOnlineMessage"), params, Common::Stream(), 0, this);

    setState(StateInviting);
    UTIL_LOG_CONTEXT_IFO("AppSession", _logContext, "content:invitation sessId:" + sessId + " caller:" + caller + " caller:" + caller + " params:" + to_json(params));
    return true;
}

bool AppSession::canceled(const Common::ServerCallPtr &__call, const Common::String &userId, const Common::String &reason)
{
    std::lock_guard<std::mutex> lock(_mutex);

    if (_inviteMessage.Type == "SipConfInvite")
    {
        _inviteMessage.Type = "SipConfCancel";
        Common::StrStrMap params;
        params["MtcImSenderUidKey"] = "<username:" + _peerUserId + "@" + Common::String(_roomConfig.domainId) + ">";
        params["MtcImSenderUriKey"] = "[username:" + _peerUserId + "@" + Common::String(_roomConfig.domainId) + ".cloud.justalk.com]";
        params["MtcImTextKey"] = ServiceUtil::to_json(_inviteMessage);
        params["notify"] = "kMtcImOnlineMessage";

        Account::AccountAgent accountAgent = _app->createAgent("Account/" + _accountId);
        if (!accountAgent.sendOnlineMessage(Common::String("kMtcImOnlineMessage"), params, Common::Stream()))
        {
            UTIL_LOG_CONTEXT_WRN("AppSession", _logContext, "content:session invitation failed, caller:" + _peerUserId + " error:" + Common::ObjectAgent::getLogInfo(2));
            __call->setError(LegacyAppGatewayError::InviteFailed(EARGSWRAP("sessId", _sessId)));
        }
    }

    if (_state == StateInviting || _state == StateInvited || _state == StateJoining)
        UTIL_LOG_CONTEXT_IFO("AppSession", _logContext, "content:canceled session caller:" + userId + " reason:" + reason);
    else
        UTIL_LOG_CONTEXT_WRN("AppSession", _logContext, "content:canceled session caller:" + userId + " reason:" + reason + " invalid state:" + to_string(_state));

    _mpcallAgent = 0;
    __release();

    return true;
}

bool AppSession::alerted(const Common::ServerCallPtr &__call, const Common::String &userId)
{
    UTIL_LOG_CONTEXT_IFO("AppSession", _logContext, "content:alerted callee:" + userId);
    return true;
}

bool AppSession::accepted(const Common::ServerCallPtr &__call, const Common::String &userId, bool isVideo, const MpCall::RoomConfig &roomConfig, const Common::String &uui)
{
    std::lock_guard<std::mutex> lock(_mutex);

    if (_state != StateJoined)
    {
        UTIL_LOG_CONTEXT_WRN("AppSession", _logContext, "content:session accepted invalid state, udid:" + _udid + " sessId:" + _sessId + " state:" + to_string(_state));
        __call->setError(LegacyAppGatewayError::InvalidState(EARGS("state", _state)));
        __release();
        return false;
    }

    UTIL_LOG_CONTEXT_IFO("AppSession", _logContext, "content:accepted userId:" + userId + " isVideo:" + Common::String(isVideo) + " uui:" + uui);
    return true;
}

bool AppSession::rejected(const Common::ServerCallPtr &__call, const Common::String &userId, const Common::String &reason)
{
    std::lock_guard<std::mutex> lock(_mutex);

    if (_state != StateJoined)
    {
        UTIL_LOG_CONTEXT_WRN("AppSession", _logContext, "content:session rejected invalid state, udid:" + _udid + " sessId:" + _sessId + " state:" + to_string(_state));
        __call->setError(LegacyAppGatewayError::InvalidState(EARGS("state", _state)));
        __release();
        return false;
    }

    UTIL_LOG_CONTEXT_IFO("AppSession", _logContext, "content:rejected userId:" + userId + " reason:" + reason);
    return true;
}

bool AppSession::leaved(const Common::ServerCallPtr &__call, const Common::String &userId)
{
    UTIL_LOG_CONTEXT_IFO("AppSession", _logContext, "content:leaved userId:" + userId);
    return true;
}

bool AppSession::terminated(const Common::ServerCallPtr &__call, const Common::String &reason)
{
    std::lock_guard<std::mutex> lock(_mutex);

    _mpcallAgent = 0;

    if (_state == StateIdle || _state == StateTermed)
    {
        UTIL_LOG_CONTEXT_WRN("AppSession", _logContext, "content:session terminated invalid state, udid:" + _udid + " sessId:" + _sessId + " state:" + to_string(_state));
        __call->setError(LegacyAppGatewayError::InvalidState(EARGS("state", _state)));
        __release();
        return false;
    }

    if (_state == StateJoining)
    {
        UTIL_LOG_CONTEXT_IFO("AppSession", _logContext, "content:terminated wait joining call, udid:" + _udid + " sessId:" + _sessId);
        setState(StateTerming);
        return true;
    }

    if (_jsmiAgent)
        _jsmiAgent.command_begin(0, "AppSession", kickCommand(_accountId), Common::StrStrMap(), _clientParams);

    __release();
    UTIL_LOG_CONTEXT_IFO("AppSession", _logContext, "content:terminated term call, udid:" + _udid + " sessId:" + _sessId);
    return true;
}

bool AppSession::join(const Common::ServerCallPtr &__call, const Common::String &accountId, const Common::String &privateRoomNumber, const Common::StrStrMap &params, Common::String &ep, Common::StrStrMap &outParams)
{
    std::lock_guard<std::mutex> lock(_mutex);

    if (_state == StateJoining || _state == StateJoined)
    {
        ep = _roomConfig.jsmiOid;
        outParams = _roomConfig.params;

        UTIL_LOG_CONTEXT_IFO("AppSession", _logContext, "content:session rejoin udid: " + _udid + " sessId:" + _sessId + " jsmi:" + ep + " state:" + to_string(_state));
        return true;
    }

    if (_state != StateIdle)
    {
        UTIL_LOG_CONTEXT_WRN("AppSession", _logContext, "content:session join invalid state, udid:" + privateRoomNumber + " state:" + to_string(_state));
        __call->setError(LegacyAppGatewayError::InvalidState(EARGS("state", _state)));
        __release();
        return false;
    }

    _accountId = accountId;
    _userId = getUserId(accountId);
    _clientParams = __call->getParams();
    _logContext->setContext("user", _userId, Common::LogContext::LogMaskKeep);

    MpCall::SessionAgent mpcall = _app->createAgent("MpCall/" + privateRoomNumber, false);
    MpCall::MemberInfoList memberInfo;
    if (!mpcall.join(MpCall::TypeLegacyApp, _userId, params, _sessId, _roomConfig, memberInfo, _clientParams))
    {
        UTIL_LOG_CONTEXT_WRN("AppSession", _logContext, "content:session join invalid state, udid:" + privateRoomNumber);
        __call->setError(LegacyAppGatewayError::JoinFailed(EARGSWRAP("udid", _udid)));
        __release();
        return false;
    }

    _logContext->setContext("sessId", _sessId, Common::LogContext::LogMaskKeep);
    _logContext->setContext("udid", _udid, Common::LogContext::LogMaskKeep);

    _objectId = _sessionManager->addSession(_userId + "/" + _sessId, this);
    if (_objectId.empty())
    {
        UTIL_LOG_CONTEXT_WRN("AppSession", _logContext, "content:session join add session failed");
        __call->setError(LegacyAppGatewayError::AddSessionFailed(EARGS("sessId", _sessId)));
        __release(ErrorType_toString(ErrorAppJoinRoom));
        return false;
    }

    _mpcallAgent = _app->createAgent("TpAppAgent/" + _userId + "/" + _sessId, false);
    _mpcallAgent->setParams(Common::CallParams::create()->enableTrace("MPCALL" + _sessId));
    JSM::JSMIAgent jsmi = _app->createAgent(_roomConfig.jsmiOid, false);
    if (!jsmi.addListener(_objectId, Common::StrStrMap(), _clientParams))
    {
        UTIL_LOG_CONTEXT_WRN("AppSession", _logContext, "content:session join add listener failed:" + Common::ObjectAgent::getLogInfo(2));
        __call->setError(LegacyAppGatewayError::AddListenerFailed(EARGSWRAP("sessId", _sessId)));
        __release(ErrorType_toString(ErrorAppJoinRoom));
        return false;
    }
    _jsmiAgent = jsmi;

    ep = _roomConfig.jsmiOid;
    outParams = _roomConfig.params;

    setState(StateJoining);
    UTIL_LOG_CONTEXT_IFO("AppSession", _logContext, "content:session join ok, jsmi:" + ep);

    return true;
}

bool AppSession::invite(const Common::ServerCallPtr &__call, const Common::String &caller, const Common::String &callee, const Common::String &uui, const Common::String &routeId, const Common::String &bindingUser)
{
    std::unique_lock<std::mutex> lock(_mutex);

    if (_state == StateJoining && !waitState(lock, StateJoined, 3000))
    {
        UTIL_LOG_CONTEXT_IFO("AppSession", _logContext, "content:session invite caller:" + caller + " callee:" + callee + " routerId:" + routeId +  " wait joining call");
        __call->setError(LegacyAppGatewayError::InvalidState(EARGS("callee", callee, "state", _state)));
        return false;
    }

    if (_state != StateJoined)
    {
        UTIL_LOG_CONTEXT_WRN("AppSession", _logContext, "content:session invite caller:" + caller + " callee:" + callee + " routerId:" + routeId + " invalid state:" + to_string(_state));
        __call->setError(LegacyAppGatewayError::InvalidState(EARGS("callee", callee, "state", _state)));
        return false;
    }

    if (!_mpcallAgent)
    {
        UTIL_LOG_CONTEXT_WRN("AppSession", _logContext, "content:session invite caller:" + caller + " callee:" + callee + " routerId:" + routeId + " no mpcall agent.");
        __call->setError(LegacyAppGatewayError::InvalidState(EARGS("callee", callee, "detail", "NoMpCallAgent")));
        return false;
    }

    if (!_clientParams)
        _clientParams = __call->getParams();

    _peerUserId = callee;
    MpCall::InviteConfig inviteConfig;
    inviteConfig.uui = uui;
    inviteConfig.routeId = routeId;
    inviteConfig.callerNumber = caller;
    inviteConfig.bindingUser = bindingUser;
    if (!_mpcallAgent.invite(MpCall::TypeSip, callee, inviteConfig, _clientParams))
    {
        UTIL_LOG_CONTEXT_WRN("AppSession", _logContext, "content:session invite caller:" + caller + " callee:" + callee + " routerId:" + routeId + " failed:" + Common::ObjectAgent::getLogInfo(2));
        __call->setError(LegacyAppGatewayError::InviteFailed(EARGSWRAP("callee", callee)));
        return false;
    }

    UTIL_LOG_CONTEXT_IFO("AppSession", _logContext, "content:session invited caller:" + caller + " callee:" + callee + " routerId:" + routeId + " uui:" + uui + " bindingUser:" + bindingUser);
    return true;
}

bool AppSession::cease(const Common::ServerCallPtr &__call)
{
    std::lock_guard<std::mutex> lock(_mutex);

    if (!_mpcallAgent)
    {
        UTIL_LOG_CONTEXT_WRN("AppSession", _logContext, "content:session no mpcall agent.");
        __call->setError(LegacyAppGatewayError::InvalidState(ELOC));
        return false;
    }

    UTIL_LOG_CONTEXT_IFO("AppSession", _logContext, "content:session ceased.");
    _mpcallAgent.leave_begin(0, true, "");
    _mpcallAgent = 0;
    __release();
    return true;
}

void AppSession::onEvent_begin(const Common::ServerCallPtr &__call, const Common::String &jsmiId, const Common::String &content, const Common::StrStrMap &params)
{
    std::unique_lock<std::mutex> lock(_mutex);

    if (_state == StateIdle || _state == StateTermed)
    {
        UTIL_LOG_CONTEXT_WRN("AppSession", _logContext, "content:session event invalid state:" + to_string(_state));
        __call->setError(LegacyAppGatewayError::InvalidState(EARGS("state", _state)));
        __release();
        return;
    }

    if (_state == StateInviting && !waitState(lock, StateInvited, 3000))
    {
        UTIL_LOG_CONTEXT_WRN("AppSession", _logContext, "content:session event " + jsmiId + " wait invited call");
        __call->setError(LegacyAppGatewayError::InvalidState(EARGS("state", _state)));
        return;
    }

    do
    {
        if (procEventJoin(content))
            break;
        if (procEventLeave(content))
            break;
        if (procEventPub(content))
            break;

        UTIL_LOG_CONTEXT_DBG("AppSession", _logContext, "content:unknown event from:" + jsmiId + " data:" + content);
    } while (0);

    JSM::ConferenceListenerServer::onEvent_end(__call, true);
}

Common::String AppSession::getUserId(const Common::String &accountId)
{
    int pos = accountId.find("username:");
    if (pos < 0)
        return accountId;
    pos += 9;

    int endPos = accountId.find_first_of("@]", pos);
    if (endPos <= pos)
        return accountId.substr(pos);

    return accountId.substr(pos, endPos - pos);
}

bool AppSession::procEventJoin(const Common::String &content)
{
    JSMD::EventJoin event;
    if (!ServiceUtil::from_json(event, content) || event.pub != "join")
        return false;

    for (auto &kv : event.actor)
    {
        if (kv.first == _accountId)
        {
            UTIL_LOG_CONTEXT_IFO("AppSession", _logContext, "content:process jsmd join event.");
            if (_state == StateInvited || _state == StateJoining)
            {
                if (_mpcallAgent)
                    _mpcallAgent.event(MpCall::Event_toString(MpCall::EventRoomJoined), "");
                setState(StateJoined);
            }
            else if (_state != StateJoined)
            {
                if (_jsmiAgent)
                    _jsmiAgent.command_begin(0, "AppSession", kickCommand(_accountId), Common::StrStrMap(), _clientParams);
                __release();
            }

            return true;
        }
    }

    UTIL_LOG_CONTEXT_IFO("AppSession", _logContext, "content:received jsmd join event.");
    return true;
}

bool AppSession::procEventLeave(const Common::String &content)
{
    JSMD::EventLeave event;
    JSMD::EventActorLeave actorEvent;

    if (ServiceUtil::from_json(event, content) && event.pub == "leave")
    {
        for (auto &kv : event.actor)
        {
            if (kv.first == _accountId)
            {
                UTIL_LOG_CONTEXT_IFO("AppSession", _logContext, "content:process jsmd leave event.");
                __release();
                return true;
            }
        }
    }
    else if (ServiceUtil::from_json(actorEvent, content) && actorEvent.pub == "actorleave")
    {
        if (actorEvent.actorleave.actorid == _accountId)
        {
            UTIL_LOG_CONTEXT_IFO("AppSession", _logContext, "content:process jsmd leave event.");
            __release();
            return true;
        }
    }
    else
        return false;

    UTIL_LOG_CONTEXT_IFO("AppSession", _logContext, "content:received jsmd leave event.");
    return true;
}
bool AppSession::procEventPub(const Common::String &content)
{
    JSMD::Event event;

    if (!ServiceUtil::from_json(event, content))
        return false;

    if (event.pub == "delete")
    {
        UTIL_LOG_CONTEXT_IFO("AppSession", _logContext, "content:process jsmd delete event");
        __release(ErrorType_toString(ErrorAppRoomTermed));
        return true;
    }

    UTIL_LOG_CONTEXT_IFO("AppSession", _logContext, "content:receive jsmd event:" + event.pub + " detail:" + content);
    return true;
}

Common::String AppSession::kickCommand(const Common::String &accountId)
{
    return "{\"req\":\"kick\",\"inverse\":\"f\",\"actorId\":[\"" + accountId + "\"]}";
}

void AppSession::__release(const Common::String &reason, const Common::String &detail)
{
    if (_jsmiAgent)
    {
        UTIL_LOG_CONTEXT_IFO("AppSession", _logContext, "content:kick self when release");
        _jsmiAgent.command_begin(0, "AppSession", kickCommand(_accountId), Common::StrStrMap(), _clientParams);
        _jsmiAgent.removeListener_begin(0, _objectId, Common::StrStrMap(), _clientParams);
        _jsmiAgent = 0;
    }

    if (_mpcallAgent)
    {
        if (!reason.empty())
            _mpcallAgent.error_begin(0, reason, detail);
        else
            _mpcallAgent.leave_begin(0, false, detail);
        _mpcallAgent = 0;
    }

    _sessionManager = 0;
    setState(StateTermed);
}

void AppSession::onNotifyEventFailed(const Common::String &logInfo)
{
    std::lock_guard<std::mutex> lock(_mutex);

    _mpcallAgent = 0;

    if (_state == StateIdle || _state == StateTermed)
    {
        UTIL_LOG_CONTEXT_WRN("AppSession", _logContext, "content:session event failed:" + logInfo + " invalid state, state:" + to_string(_state));
        __release();
        return;
    }

    if (_state == StateJoining)
    {
        UTIL_LOG_CONTEXT_IFO("AppSession", _logContext, "content:event failed:" + logInfo + " wait terming call");
        setState(StateTerming);
        return;
    }

    if (_jsmiAgent)
        _jsmiAgent.command_begin(0, "AppSession", kickCommand(_accountId), Common::StrStrMap(), _clientParams);

    __release();
    UTIL_LOG_CONTEXT_IFO("AppSession", _logContext, "content:event failed:" + logInfo + " term call");
}

void AppSession::onSendInviteResult(bool success, const Common::String &failReason)
{
    std::lock_guard<std::mutex> lock(_mutex);

    if (success)
    {
        if (_state != StateInviting)
        {
            UTIL_LOG_CONTEXT_IFO("AppSession", _logContext, "content:session invitation success result invalid state:" + to_string(_state));
            return;
        }

        MpCall::RoomConfig outRoomConfig;
        MpCall::MemberInfoList memberInfo;
        if (!_mpcallAgent.accept(_peerUserId, _inviteMessage.Video, "", Common::StrStrMap(), outRoomConfig, memberInfo))
        {
            UTIL_LOG_CONTEXT_WRN("AppSession", _logContext, "content:session invitation accept failed:" + Common::ObjectAgent::getLogInfo(2));
            __release();
            return;
        }

        setState(StateInvited);
        UTIL_LOG_CONTEXT_IFO("AppSession", _logContext, "content:session invitation success");
    }
    else
    {
        UTIL_LOG_CONTEXT_WRN("AppSession", _logContext, "content:session invitation failed:" + failReason);

        if (_state != StateTermed)
        {
            if (!_mpcallAgent.reject(_peerUserId, failReason))
                UTIL_LOG_CONTEXT_WRN("AppSession", _logContext, "content:session invitation reject failed:" + Common::ObjectAgent::getLogInfo(2));
        }

        __release();
    }
}

void AppSession::setState(enum State state)
{
    if (_state == state)
        return;

    UTIL_LOG_CONTEXT_IFO("AppSession", _logContext, "content:update state:" + to_string(_state) + "->" + to_string(state));
    _state = state;
    _stateChangeEvent.notify_all();
}

bool AppSession::waitState(std::unique_lock<std::mutex> &lock, enum State state, int timeout)
{
    std::chrono::system_clock::time_point toTime = std::chrono::system_clock::now() + std::chrono::milliseconds(timeout);
    enum State oldState = _state;
    while (_state == oldState)
    {
        if (_stateChangeEvent.wait_until(lock, toTime) == std::cv_status::timeout)
            return false;
    }

    return _state == state;
}

} // namespace MpCall
