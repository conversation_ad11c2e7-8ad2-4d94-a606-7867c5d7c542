//
// *****************************************************************************
// Copyright (c) 2023-2025 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//

#pragma once

#include "Common/Error.h"

namespace MpCall
{

namespace LegacyAppGatewayError
{
    static const char *const DomainCode = "M29";

    ERROR_DECLARE_F(7290, NotSupport);
    ERROR_DECLARE_F(7291, InvalidState);
    ERROR_DECLARE_F(7292, JoinFailed);
    ERROR_DECLARE_F(7293, AddL<PERSON><PERSON>Failed);
    ERROR_DECLARE_F(7294, AddSess<PERSON>Failed);
    ERROR_DECLARE_F(7295, NoAccountId);
    ERROR_DECLARE_F(7296, ProxyRpcFailed);
    ERROR_DECLARE_F(7297, QueryFailed);
    ERROR_DECLARE_F(7298, InviteFailed);
    ERROR_DECLARE_F(7299, NoPrivateRoomNumber);
} // namespace LegacyAppGatewayError

} // namespace MpCall