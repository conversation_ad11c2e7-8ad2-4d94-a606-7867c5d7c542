//
// *****************************************************************************
// Copyright (c) 2023-2025 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//

#pragma once

#include "Service/ServiceI.h"
#include "SessionLocator/SessionLocator.h"
#include "AppSession.h"
#include "JSM/JSMEServer.h"
#include "SipGatewayEntry/SipGatewayEntryServer.h"
#include "Error.h"

namespace MpCall
{

class SipGatewayEntryServer : public SipGatewayEntry::SipGatewayEntryServer
{
public:
    SipGatewayEntryServer(const Common::ApplicationExPtr &application)
        : _app(application)
    {
    }

    void onDeactivate();
    void onUpdateConfigs();

    // implement SipGatewayEntry::SipGatewayEntryServer
    virtual void onInvited_begin(const Common::ServerCallPtr &__call, const Common::String &conferenceId, const Common::String &callerAccount, const Common::String &calleeNumber, const Common::String &domainId, const Common::String &appId, const Common::String &deviceIp, const Common::StrStrMap &params) override;

private:
    bool queryPrivateRoomNumber(Common::String &privateRoomNumber, Common::Long confNumber, const Common::CallParamsPtr &callParams);
    Common::String getRouteId(const Common::ServerCallPtr &__call, const Common::String &domainId, const Common::StrStrMap &params);

private:
    Common::RecMutex _mutex;
    Common::ApplicationExPtr _app;
    Common::StrStrMap _domainCoreNetIdMap;
    Common::StrStrMap _coreNetIdMap;
};

class LegacyAppGateway : public Service::ServiceManagerI, public SessionLocator::SessionLocatorListener, public AppSessionManager, public JSM::JSMEServer, public SipGatewayEntryServer
{
public:
    LegacyAppGateway(const Common::ApplicationExPtr &application, const SessionLocator::SessionLocatorPtr &locator = 0);
    DEF_SERVER_FUNCS2(JSM::JSMEServer, SipGatewayEntryServer)

    // implement Service::ServiceManagerI
    bool onActivate() override;
    void onDeactivate() override;
    void onShutdown() override;
    void onSchd() override;
    void onUpdateConfigs() override;
    bool getMainService(Common::ObjectServerPtr &service, Common::String &name, bool &famous) override;
    bool isMainServiceReady(Common::String &reason) override;

    // implement SessionLocator::SessionLocatorListener
    SessionLocator::SessionPtr onCreateSession(const Common::String &type, const Common::String &id, const Common::String &objectId) override;

    // implement AppSessionManager
    Common::String addSession(const Common::String &id, const SessionLocator::SessionPtr &session) override;
    Common::String addEntrySession(const Common::String &id, const SessionLocator::SessionPtr &session) override;

#define MPCALL_APPGATEWAY_NOT_SUPPORT_IMPL(_file, _line)                                                           \
    {                                                                                                              \
        __call->throwException(LegacyAppGatewayError::NotSupport(_file, _line, Common::ErrorStackPtr(), nullptr)); \
    }

#define MPCALL_APPGATEWAY_NOT_SUPPORT_IMP2(_file, _line)                                                           \
    {                                                                                                              \
        __call->throwException(LegacyAppGatewayError::NotSupport(_file, _line, Common::ErrorStackPtr(), nullptr)); \
        return false;                                                                                              \
    }

    // implement JSM::JSMEServer
    void loadRegionConfigs_begin(const Common::ServerCallPtr &__call) override;
    void create_begin(const Common::ServerCallPtr &__call, const Common::StrSet &wantCaps, const Common::StrStrMap &params) override MPCALL_APPGATEWAY_NOT_SUPPORT_IMPL(__FILE__, __LINE__);
    void reserve_begin(const Common::ServerCallPtr &__call, const Common::StrSet &wantCaps, const Common::StrStrMap &params, Common::Long start, Common::Long duration) override MPCALL_APPGATEWAY_NOT_SUPPORT_IMPL(__FILE__, __LINE__);
    void cancelReserve_begin(const Common::ServerCallPtr &__call, Common::Long confNumber, const Common::StrStrMap &params) override;
    void cancelReserve2_begin(const Common::ServerCallPtr &__call, const Common::String &privateRoomNumber, const Common::StrStrMap &params) override;
    void query_begin(const Common::ServerCallPtr &__call, Common::Long confNumber) override;
    void query2_begin(const Common::ServerCallPtr &__call, const Common::String &privateRoomNumber, const Common::StrStrMap &params) override;
    void destroy_begin(const Common::ServerCallPtr &__call, const Common::String &roomId) override MPCALL_APPGATEWAY_NOT_SUPPORT_IMPL(__FILE__, __LINE__);
    bool update(const Common::ServerCallPtr &__call, const Common::String &serviceType, const Common::String &serviceId, int expires, const Common::StrStrMap &confs, const Common::StrStrMap &infos, Common::StrStrMap &params) override MPCALL_APPGATEWAY_NOT_SUPPORT_IMP2(__FILE__, __LINE__);
    void statusNotify_begin(const Common::ServerCallPtr &__call, const Common::String &roomId, const Common::String &confNumber, const Common::String &userData, const Common::String &status, const Common::StrStrMap &params) override MPCALL_APPGATEWAY_NOT_SUPPORT_IMPL(__FILE__, __LINE__);
    void add_begin(const Common::ServerCallPtr &__call, const Common::String &privateRoomNumber, const Common::StrStrMap &params) override MPCALL_APPGATEWAY_NOT_SUPPORT_IMPL(__FILE__, __LINE__);
    void add2_begin(const Common::ServerCallPtr &__call, const Common::String &privateRoomNumber, const Common::StrStrMap &params) override;
    void delivery_create_begin(const Common::ServerCallPtr &__call, const Common::String &confNumber, const Common::StrSet &wantCaps, const Common::StrStrMap &params) override MPCALL_APPGATEWAY_NOT_SUPPORT_IMPL(__FILE__, __LINE__);
    void addRelay_begin(const Common::ServerCallPtr &__call, Common::Long confNumber, const Common::StrStrMap &params) override MPCALL_APPGATEWAY_NOT_SUPPORT_IMPL(__FILE__, __LINE__);
    void addRelay2_begin(const Common::ServerCallPtr &__call, const Common::String &confId, const Common::StrStrMap &params) override MPCALL_APPGATEWAY_NOT_SUPPORT_IMPL(__FILE__, __LINE__);
    void addNewRelay_begin(const Common::ServerCallPtr &__call, const Common::String &confId, const Common::StrStrMap &params) override MPCALL_APPGATEWAY_NOT_SUPPORT_IMPL(__FILE__, __LINE__);
    void forwardQuery_begin(const Common::ServerCallPtr &__call, Common::Long confNumber, const Common::StrVec &selectedJsme) override MPCALL_APPGATEWAY_NOT_SUPPORT_IMPL(__FILE__, __LINE__);
    void forwardQuery2_begin(const Common::ServerCallPtr &__call, const Common::String &privateRoomNumber, const Common::StrStrMap &params, const Common::StrVec &selectedJsme) override MPCALL_APPGATEWAY_NOT_SUPPORT_IMPL(__FILE__, __LINE__);
    void getRoomList_begin(const Common::ServerCallPtr &__call, const Common::String &domainId, const Common::String &appId, int page, int size, const Common::StrStrMap &params) override MPCALL_APPGATEWAY_NOT_SUPPORT_IMPL(__FILE__, __LINE__);
    void queryRoom_begin(const Common::ServerCallPtr &__call, const Common::String &domainId, const Common::String &appId, const Common::String &roomId, const Common::StrVec &excludeJsmeList) override MPCALL_APPGATEWAY_NOT_SUPPORT_IMPL(__FILE__, __LINE__);
    void kickoffUserFromRoom_begin(const Common::ServerCallPtr &__call, const Common::String &domainId, const Common::String &appId, const Common::String &roomId, const Common::StrVec &memberList, const Common::StrVec &excludeJsmeList) override MPCALL_APPGATEWAY_NOT_SUPPORT_IMPL(__FILE__, __LINE__);
    void destroyRoom_begin(const Common::ServerCallPtr &__call, const Common::String &domainId, const Common::String &appId, const Common::String &roomId, const Common::StrStrMap &params, const Common::StrVec &excludeJsmeList) override MPCALL_APPGATEWAY_NOT_SUPPORT_IMPL(__FILE__, __LINE__);
    void createAgent_begin(const Common::ServerCallPtr &__call, const Common::String &domainId, const Common::String &appId, const Common::String &roomId, const Common::String &type, const Common::StrStrMap &params) override;
    void add3_begin(const Common::ServerCallPtr &__call, const Common::String &privateRoomNumber, const Common::StrStrMap &params) override;
    void createTestAgent_begin(const Common::ServerCallPtr &__call, const Common::String &type, const Common::StrStrMap &params) override MPCALL_APPGATEWAY_NOT_SUPPORT_IMPL(__FILE__, __LINE__);
    void forwardadd3_begin(const Common::ServerCallPtr &__call, const Common::String &privateRoomNumber, const Common::StrStrMap &params, const Common::StrVec &selectedJsme) override MPCALL_APPGATEWAY_NOT_SUPPORT_IMPL(__FILE__, __LINE__);

private:
    void initCategoryType();
    void readRoomConfig();

private:
    Common::String _categoryType;
    Common::String _serviceId;
    SessionLocator::SessionLocatorPtr _locator;
    Common::StrStrMap _roomConfig;
};

} // namespace MpCall