//
// *****************************************************************************
// Copyright (c) 2023-2025 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//

#pragma once

#include <mutex>
#include <condition_variable>

#include "Common/Common.h"
#include "SessionLocator/SessionLocator.h"
#include "MpCall/MpCallSessionAgent.h"
#include "MpCall/MpCallLegacyAppServer.h"
#include "JSM/JSMSAgent.h"
#include "JSM/JSMSServer.h"
#include "SipGatewayEntry/SipGatewayEntryPub.h"

namespace MpCall
{
class AppSessionManager : virtual public Common::Shared
{
public:
    virtual Common::String addSession(const Common::String &id, const SessionLocator::SessionPtr &session) = 0;
    virtual Common::String addEntrySession(const Common::String &id, const SessionLocator::SessionPtr &session) = 0;
};

typedef Common::Handle<AppSessionManager> AppSessionManagerPtr;

class AppSession : public SessionLocator::Session, public MpCall::LegacyAppGatewayServer, public MpCall::LegacyAppEntryServer, public JSM::ConferenceListenerServer
{
public:
    enum State
    {
        StateIdle = 0,
        StateInviting,
        StateInvited,
        StateJoining,
        StateJoined,
        StateTerming,
        StateTermed,
    };

    static Common::String to_string(enum State state)
    {
        const Common::String __stateMain[] = {
            "Idle", "Inviting", "Invited", "Joining", "Joined", "Terming", "Termed"};
        return __stateMain[state];
    }

public:
    AppSession(const Common::ApplicationPtr &app, const Common::String &udid, const AppSessionManagerPtr &sessionManager)
        : _state(StateIdle)
        , _app(app)
        , _udid(udid)
        , _sessionManager(sessionManager)
        , _lastKeepAliveTicks(Common::getCurTicks())
    {
        if (!_app || !_sessionManager)
            throw Common::Exception("MissingDependency");
        _logContext = Common::LogContext::create();
    }

    virtual bool __ex(const Common::ServerCallPtr &__call, const Common::String &__cmd, const Common::IputStreamPtr &__iput) override
    {
        if (MpCall::LegacyAppGatewayServer::__ex(__call, __cmd, __iput))
            return true;
        if (MpCall::LegacyAppEntryServer::__ex(__call, __cmd, __iput))
            return true;
        if (JSM::ConferenceListenerServer::__ex(__call, __cmd, __iput))
            return true;
        return false;
    }

    // implement SessionLocator::Session
    virtual Common::String checkState(bool &closed) override;
    virtual void onError(const Common::CallError &error) override {}

    // implement MpCall::LegacyAppGatewayServer
    virtual bool invitation(const Common::ServerCallPtr& __call,const Common::String& sessId,const Common::String& caller,const Common::String& callee,const MpCall::RoomConfig& roomConfig,const MpCall::InviteConfig& inviteConfig) override;
    virtual bool canceled(const Common::ServerCallPtr& __call,const Common::String& userId,const Common::String& reason) override;
    virtual bool alerted(const Common::ServerCallPtr& __call,const Common::String& userId) override;
    virtual bool accepted(const Common::ServerCallPtr& __call,const Common::String& userId,bool isVideo,const MpCall::RoomConfig& roomConfig,const Common::String& uui) override;
    virtual bool rejected(const Common::ServerCallPtr &__call, const Common::String &userId, const Common::String &reason) override;
    virtual bool leaved(const Common::ServerCallPtr &__call, const Common::String &userId) override;
    virtual bool terminated(const Common::ServerCallPtr &__call, const Common::String &reason) override;

    // implement MpCall::LegacyAppEntryServer
    virtual bool join(const Common::ServerCallPtr &__call, const Common::String &accountId, const Common::String &privateRoomNumber, const Common::StrStrMap &params, Common::String &ep, Common::StrStrMap &outParams) override;
    virtual bool invite(const Common::ServerCallPtr &__call, const Common::String &caller, const Common::String &callee, const Common::String &uui, const Common::String &routeid, const Common::String &bindingUser) override;
    virtual bool cease(const Common::ServerCallPtr &__call) override;

    // implement JSM::ConferenceListenerServer
    virtual void onEvent_begin(const Common::ServerCallPtr &__call, const Common::String &jsmiId, const Common::String &content, const Common::StrStrMap &params) override;

private:
    Common::String getUserId(const Common::String &accountId);
    bool procEventJoin(const Common::String &content);
    bool procEventLeave(const Common::String &content);
    bool procEventPub(const Common::String &content);
    Common::String kickCommand(const Common::String &accountId);
    void __release(const Common::String &reason = "", const Common::String &detail = "");
    void onNotifyEventFailed(const Common::String &logInfo);

    void onSendInviteResult(bool success, const Common::String &failReason);

    void setState(enum State state);
    bool waitState(std::unique_lock<std::mutex> &lock, enum State state, int timeout);

private:
    Common::ApplicationPtr _app;
    AppSessionManagerPtr _sessionManager;
    std::mutex _mutex;
    enum State _state;
    std::condition_variable _stateChangeEvent;
    Common::String _accountId;
    Common::String _userId;
    Common::String _udid;
    Common::String _sessId;
    Common::String _objectId;
    Common::CallParamsPtr _clientParams;
    Common::LogContextPtr _logContext;
    Common::String _peerUserId;

    MpCall::RoomConfig _roomConfig;
    JSM::JSMIAgent _jsmiAgent;
    SipGatewayEntry::SipConfMessage _inviteMessage;

    MpCall::SessionEndpointAgent _mpcallAgent;
    unsigned int _lastKeepAliveTicks;

    friend class LegacyAppGateway;
};

typedef Common::Handle<AppSession> AppSessionPtr;

} // namespace MpCall