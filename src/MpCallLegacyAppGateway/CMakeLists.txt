aux_source_directory(. MPCALL_LEGACYAPPGATEWAY_SRC)
add_library(MpCallLegacyAppGatewayImpl
    ${MPCALL_LEGACYAPPGATEWAY_SRC}
)

target_include_directories(MpCallLegacyAppGatewayImpl PRIVATE
    ${DEFAULT_INCLUDE_DIRECTORIES}
    ${PROJECT_SOURCE_DIR}/3rd
)
target_compile_definitions(MpCallLegacyAppGatewayImpl PRIVATE
    ${DEFAULT_COMPILE_DEFINITIONS}
)

aux_source_directory(service MPCALL_LEGACYAPPGATEWAY_SERVICE_SRC)
add_executable(MpCallLegacyAppGateway
    ${WARP_GLIBC_SRC}
    ${MPCALL_LEGACYAPPGATEWAY_SERVICE_SRC}
    ${WARP_GLIBC_SRC}
)

target_include_directories(MpCallLegacyAppGateway PRIVATE
    ${DEFAULT_INCLUDE_DIRECTORIES}
    ${PROJECT_SOURCE_DIR}/3rd
)
target_compile_definitions(Mp<PERSON>allLegacyAppGateway PRIVATE
    ${DEFAULT_COMPILE_DEFINITIONS}
)

target_link_libraries(MpCallLegacyAppGateway MpCallLegacyAppGatewayImpl SessionLocator SipGatewayEntryRpc MpCallRpc JSMD AccountPub2 JsmInterface ServiceI NmsFoundation ServiceMain
    ${DEFAULT_SERVER_LIBRARIES}
    ${DEFAULT_SYS_LIBRARIES}
)
target_link_options(MpCallLegacyAppGateway PRIVATE
    ${DEFAULT_LINK_OPTIONS}
)

# UnitTest
aux_source_directory(test MPCALL_LEGACYAPPGATEWAY_TEST_SRC)
add_executable(MpCallLegacyAppGatewayUnitTest ${MPCALL_LEGACYAPPGATEWAY_TEST_SRC} ${MPCALL_LEGACYAPPGATEWAY_SERVICE_SRC})
target_include_directories(MpCallLegacyAppGatewayUnitTest PRIVATE
    ${DEFAULT_INCLUDE_DIRECTORIES}
    ${PROJECT_SOURCE_DIR}/3rd
)
target_compile_definitions(MpCallLegacyAppGatewayUnitTest PRIVATE
    ${DEFAULT_COMPILE_DEFINITIONS}
)

target_link_libraries(MpCallLegacyAppGatewayUnitTest MpCallLegacyAppGatewayImpl SessionLocator SipGatewayEntryRpc MpCallRpc JSMD AccountPub2 JsmInterface ServiceI NmsFoundation MpCallHigherConfig HigherConfig
    ${DEFAULT_SERVER_LIBRARIES}
    $ENV{JssSharedPath}/lib/${DEFAULT_LIB_PATH}/libprotobuf.a
    $ENV{JssSharedPath}/lib/${DEFAULT_LIB_PATH}/libyaml-cpp.a
    $ENV{JssSharedPath}/lib/${DEFAULT_LIB_PATH}/libgtest.a
    $ENV{JssSharedPath}/lib/${DEFAULT_LIB_PATH}/libgmock.a
    $ENV{JssSharedPath}/lib/${DEFAULT_LIB_PATH}/libgtest_main.a
    ${DEFAULT_SYS_LIBRARIES}
)
include(GoogleTest)
gtest_discover_tests(MpCallLegacyAppGatewayUnitTest)