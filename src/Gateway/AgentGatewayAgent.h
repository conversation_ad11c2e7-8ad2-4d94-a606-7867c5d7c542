﻿//
// *****************************************************************************
// Copyright(c) 2017-2025 Juphoon System Software Co., LTD. All rights reserved.
// *****************************************************************************
//
// Auto generated from: AgentGateway.def
// Warning: do not edit this file.
//

#ifndef __Gateway_AgentGatewayAgent_h
#define __Gateway_AgentGatewayAgent_h

#include "Gateway/AgentGatewayPub.h"

namespace Gateway
{

/**
 * 网关服务与终端交互接口
 */
class AgentGatewayAgent : public Common::Agent
{
public:
    AgentGatewayAgent(int zero = 0) : Common::Agent(zero) {}
    AgentGatewayAgent(const Common::Agent& agent) : Common::Agent(agent) {}
    AgentGatewayAgent(const Common::ObjectAgentPtr& agent) : Common::Agent(agent) {}

    /**
     * 入会成功后建立rpc直连
     *
     * @param[in]	instanceId		唯一实例id
     */
    bool connect(const Common::String& instanceId,Common::StrStrMap& params,const Common::CallParamsPtr& __params = 0) const throw();
    void connect_begin(const Common::AgentAsyncPtr& __async,const Common::String& instanceId,const Common::CallParamsPtr& __params = 0,const Common::ObjectPtr& __userdata = 0) const throw();
    static bool connect_end(int __rslt,const Common::IputStreamPtr& __iput,Common::StrStrMap& params) throw();

    /**
           * 入会成功后建立rpc直连
           *
           * @param[in]	instanceId		唯一实例id
           */
    bool connect2(const Common::String& instanceId,Common::StrStrMap& params,const Common::CallParamsPtr& __params = 0) const throw();
    void connect2_begin(const Common::AgentAsyncPtr& __async,const Common::String& instanceId,const Common::CallParamsPtr& __params = 0,const Common::ObjectPtr& __userdata = 0) const throw();
    static bool connect2_end(int __rslt,const Common::IputStreamPtr& __iput,Common::StrStrMap& params) throw();

    /**
     * 心跳
     *
     * @param[in]	instanceId		唯一实例id
     * @param[in]   params			备用
     */
    bool heartbeat(const Common::String& instanceId,const Common::StrStrMap& params,const Common::CallParamsPtr& __params = 0) const throw();
    void heartbeat_begin(const Common::AgentAsyncPtr& __async,const Common::String& instanceId,const Common::StrStrMap& params,const Common::CallParamsPtr& __params = 0,const Common::ObjectPtr& __userdata = 0) const throw();
    static bool heartbeat_end(int __rslt,const Common::IputStreamPtr& __iput) throw();

    /**
           * 结束客户端、释放资源
           *
           * @param[in]	instanceId		唯一实例id
           */
    bool release(const Common::String& reason,const Common::String& instanceId,const Common::StrStrMap& params,const Common::CallParamsPtr& __params = 0) const throw();
    void release_begin(const Common::AgentAsyncPtr& __async,const Common::String& reason,const Common::String& instanceId,const Common::StrStrMap& params,const Common::CallParamsPtr& __params = 0,const Common::ObjectPtr& __userdata = 0) const throw();
    static bool release_end(int __rslt,const Common::IputStreamPtr& __iput) throw();

    /**
     * 通过网关向目标终端推送消息
     *
     * @param[in]	instanceId		唯一实例id
     * @param[in]	data			消息
     */
    bool pushData(const Common::String& instanceId,const Common::StrVec& data,const Common::CallParamsPtr& __params = 0) const throw();
    void pushData_begin(const Common::AgentAsyncPtr& __async,const Common::String& instanceId,const Common::StrVec& data,const Common::CallParamsPtr& __params = 0,const Common::ObjectPtr& __userdata = 0) const throw();
    static bool pushData_end(int __rslt,const Common::IputStreamPtr& __iput) throw();

    /**
     * 从网关拉取指定终端的消息
           * 从网关获取前端数据，有数据情况会立即返回，没有数据RPC挂起，直到超时
     * 针对终端发起新的RPC调用，服务端收到返回老接口时，返回值为false，在outParams增加字段"reason"为"expired"，
     * 终端收到这个消息，不会发起新的RPC调用。
     *
           *
           * @param[in]	instanceId		唯一实例id
           * @param[in]   consumeId       上一次消费的消息id
           * @param[out]	vecData			消息列表
     * @param[out]	outParams	    其他可扩展出参
           */
    bool pullData(const Common::String& instanceId,Common::Long consumeId,Gateway::MsgVec& vecData,Common::StrStrMap& outParams,const Common::CallParamsPtr& __params = 0) const throw();
    void pullData_begin(const Common::AgentAsyncPtr& __async,const Common::String& instanceId,Common::Long consumeId,const Common::CallParamsPtr& __params = 0,const Common::ObjectPtr& __userdata = 0) const throw();
    static bool pullData_end(int __rslt,const Common::IputStreamPtr& __iput,Gateway::MsgVec& vecData,Common::StrStrMap& outParams) throw();

    /**
    * 请求推流,WebRoomGateway专用
    *
    * @param[in]	in @see CoordinateIn
    * @param[out]	out	@see CoordinateOut
    */
    bool publish(const Common::String& instanceId,const Gateway::CoordinateIn& corin,Gateway::CoordinateOut& corout,const Common::CallParamsPtr& __params = 0) const throw();
    void publish_begin(const Common::AgentAsyncPtr& __async,const Common::String& instanceId,const Gateway::CoordinateIn& corin,const Common::CallParamsPtr& __params = 0,const Common::ObjectPtr& __userdata = 0) const throw();
    static bool publish_end(int __rslt,const Common::IputStreamPtr& __iput,Gateway::CoordinateOut& corout) throw();

    /**
    * 请求拉流,WebRoomGateway专用
    *
    * @param[in] srsIn	@see CoordinateIn
    * @param[out]	srsOut	@see CoordinateOut
    */
    bool play(const Common::String& instanceId,const Gateway::CoordinateIn& corin,Gateway::CoordinateOut& corout,const Common::CallParamsPtr& __params = 0) const throw();
    void play_begin(const Common::AgentAsyncPtr& __async,const Common::String& instanceId,const Gateway::CoordinateIn& corin,const Common::CallParamsPtr& __params = 0,const Common::ObjectPtr& __userdata = 0) const throw();
    static bool play_end(int __rslt,const Common::IputStreamPtr& __iput,Gateway::CoordinateOut& corout) throw();

    /**
    * 请求推流,WebRoomGateway专用,多流场景
    * 在一个特定用户下使用streamId将不同的流隔离
    * 可以指定一个streamId(corin.streamId)推流，也可以传-1，此时由服务端默认使用一路新的流
    * 可以在服务端保存流的属性(corin.properties)
    *
    * @param[in]	in @see CoordinateIn2
    * @param[in]    userId 成员id,该参数不为空则认为是app的推流
    * @param[out]	out	@see CoordinateOut2
    */
    bool publish2(const Common::String& instanceId,const Common::String& userId,const Gateway::CoordinateIn2& corin,Gateway::CoordinateOut2& corout,const Common::CallParamsPtr& __params = 0) const throw();
    void publish2_begin(const Common::AgentAsyncPtr& __async,const Common::String& instanceId,const Common::String& userId,const Gateway::CoordinateIn2& corin,const Common::CallParamsPtr& __params = 0,const Common::ObjectPtr& __userdata = 0) const throw();
    static bool publish2_end(int __rslt,const Common::IputStreamPtr& __iput,Gateway::CoordinateOut2& corout) throw();

    /**
    * 请求拉流,WebRoomGateway专用,多流场景
    * 需要指定streamId，不指定则尝试使用streamId=0
    * 下发保存在服务端的流属性corout.properties
    *
    * @param[in] srsIn	@see CoordinateIn2
    * @param[out]	srsOut	@see CoordinateOut2
    */
    bool play2(const Common::String& instanceId,const Gateway::CoordinateIn2& corin,Gateway::CoordinateOut2& corout,const Common::CallParamsPtr& __params = 0) const throw();
    void play2_begin(const Common::AgentAsyncPtr& __async,const Common::String& instanceId,const Gateway::CoordinateIn2& corin,const Common::CallParamsPtr& __params = 0,const Common::ObjectPtr& __userdata = 0) const throw();
    static bool play2_end(int __rslt,const Common::IputStreamPtr& __iput,Gateway::CoordinateOut2& corout) throw();

    /**
     * 请求邀请一个房间的代理
     * @param[in] roomId 会议号
     * @param[in] userId 用户号
     * @param[in] appKey 业务号
     * @param[in] isPublish 是否推流
     * @param[in] roomParams 房间参数
     */
    bool inviteRoomDelegator(const Common::String& instanceId,const Common::String& roomId,const Common::String& appKey,const Common::String& userId,bool isPublish,const Common::String& roomParams,const Common::CallParamsPtr& __params = 0) const throw();
    void inviteRoomDelegator_begin(const Common::AgentAsyncPtr& __async,const Common::String& instanceId,const Common::String& roomId,const Common::String& appKey,const Common::String& userId,bool isPublish,const Common::String& roomParams,const Common::CallParamsPtr& __params = 0,const Common::ObjectPtr& __userdata = 0) const throw();
    static bool inviteRoomDelegator_end(int __rslt,const Common::IputStreamPtr& __iput) throw();

    /**
     * 加入房间,由推流agent发起
     * @param[in] roomDelegatorId
     * @param[in] userId 成员id
     * @param[out] instanceId 唯一实例id
     */
    bool join(const Common::String& roomDelegatorId,const Common::String& userId,Common::String& instanceId,const Common::CallParamsPtr& __params = 0) const throw();
    void join_begin(const Common::AgentAsyncPtr& __async,const Common::String& roomDelegatorId,const Common::String& userId,const Common::CallParamsPtr& __params = 0,const Common::ObjectPtr& __userdata = 0) const throw();
    static bool join_end(int __rslt,const Common::IputStreamPtr& __iput,Common::String& instanceId) throw();

    /**
     * 离开会议, 由推流agent发起
     * @param[in] roomDelegatorId
     * @param[in] userId
     * @param[in] instanceId 唯一实例id
     */
    bool leave(const Common::String& roomDelegatorId,const Common::String& userId,const Common::String& instanceId,const Common::CallParamsPtr& __params = 0) const throw();
    void leave_begin(const Common::AgentAsyncPtr& __async,const Common::String& roomDelegatorId,const Common::String& userId,const Common::String& instanceId,const Common::CallParamsPtr& __params = 0,const Common::ObjectPtr& __userdata = 0) const throw();
    static bool leave_end(int __rslt,const Common::IputStreamPtr& __iput) throw();

    /**
     * 测试 agent api地址注册
     * @param[in] type		        测试 agent 类型
     * @param[in] instanceId		唯一实例id
     * @param[in] apiUrl            测试 agent api地址
     * @param[out] objectId         objectId，用于heartbeat保活
     */
    bool registerTestAgent(const Common::String& type,const Common::String& instanceId,const Common::String& apiUrl,Common::String& objectId,const Common::CallParamsPtr& __params = 0) const throw();
    void registerTestAgent_begin(const Common::AgentAsyncPtr& __async,const Common::String& type,const Common::String& instanceId,const Common::String& apiUrl,const Common::CallParamsPtr& __params = 0,const Common::ObjectPtr& __userdata = 0) const throw();
    static bool registerTestAgent_end(int __rslt,const Common::IputStreamPtr& __iput,Common::String& objectId) throw();
};

};//namespace: Gateway

#endif //__Gateway_AgentGatewayAgent_h
