﻿//
// *****************************************************************************
// Copyright(c) 2017-2025 Juphoon System Software Co., LTD. All rights reserved.
// *****************************************************************************
//
// Auto generated from: AgentGateway.def
// Warning: do not edit this file.
//

#include "Gateway/AgentGatewayPub.h"

namespace Gateway
{

Msg::Msg() :
    sequence(0)
{
}

Msg::Msg(Common::Long x_sequence,const Common::String& x_data) :
    sequence(x_sequence),data(x_data)
{
}

bool Msg::operator<(const Msg&__obj) const
{
    if (this == &__obj) return false;
    if (sequence < __obj.sequence) return true;
    if (__obj.sequence < sequence) return false;
    if (data < __obj.data) return true;
    if (__obj.data < data) return false;
    return false;
}

bool Msg::operator==(const Msg&__obj) const
{
    if (this == &__obj) return true;
    if (sequence != __obj.sequence) return false;
    if (data != __obj.data) return false;
    return true;
}

void Msg::__write(const Common::OputStreamPtr& __oput) const
{
    __write_Msg(__oput,*this);
}

void Msg::__read(const Common::IputStreamPtr& __iput)
{
    __read_Msg(__iput,*this);
}

void Msg::__textWrite(const Common::OputStreamPtr& __oput,const Common::String& __name) const
{
    __textWrite_Msg(__oput,__name,*this);
}

bool Msg::__textRead(const Common::IputStreamPtr& __iput,const Common::String& __name,int __idx)
{
    return __textRead_Msg(__iput,__name,*this,__idx);
}

void __write_Msg(const Common::OputStreamPtr& __oput,const Gateway::Msg& __obj)
{
    __oput->write(__obj.sequence);
    __oput->write(__obj.data);
}

void __read_Msg(const Common::IputStreamPtr& __iput,Gateway::Msg& __obj)
{
    __iput->read(__obj.sequence);
    __iput->read(__obj.data);
}

void __textWrite_Msg(const Common::OputStreamPtr& __oput,const Common::String& __name,const Gateway::Msg& __obj)
{
    __oput->textStart(__name);
    __oput->textWrite("sequence",__obj.sequence);
    __oput->textWrite("data",__obj.data);
    __oput->textEnd();
}

bool __textRead_Msg(const Common::IputStreamPtr& __iput,const Common::String& __name,Gateway::Msg& __obj,int __idx)
{
    if (!__iput->textStart(__name,__idx)) return false;
    __iput->textRead("sequence",__obj.sequence,0);
    __iput->textRead("data",__obj.data,0);
    __iput->textEnd();
    return true;
}

CoordinateIn::CoordinateIn() :
    isScreenShare(false)
{
}

CoordinateIn::CoordinateIn(const Common::String& x_tid,const Common::String& x_sdp,bool x_isScreenShare,const Common::StrStrMap& x_inExt) :
    tid(x_tid),sdp(x_sdp),isScreenShare(x_isScreenShare),inExt(x_inExt)
{
}

bool CoordinateIn::operator<(const CoordinateIn&__obj) const
{
    if (this == &__obj) return false;
    if (tid < __obj.tid) return true;
    if (__obj.tid < tid) return false;
    if (sdp < __obj.sdp) return true;
    if (__obj.sdp < sdp) return false;
    if (isScreenShare < __obj.isScreenShare) return true;
    if (__obj.isScreenShare < isScreenShare) return false;
    if (inExt < __obj.inExt) return true;
    if (__obj.inExt < inExt) return false;
    return false;
}

bool CoordinateIn::operator==(const CoordinateIn&__obj) const
{
    if (this == &__obj) return true;
    if (tid != __obj.tid) return false;
    if (sdp != __obj.sdp) return false;
    if (isScreenShare != __obj.isScreenShare) return false;
    if (inExt != __obj.inExt) return false;
    return true;
}

void CoordinateIn::__write(const Common::OputStreamPtr& __oput) const
{
    __write_CoordinateIn(__oput,*this);
}

void CoordinateIn::__read(const Common::IputStreamPtr& __iput)
{
    __read_CoordinateIn(__iput,*this);
}

void CoordinateIn::__textWrite(const Common::OputStreamPtr& __oput,const Common::String& __name) const
{
    __textWrite_CoordinateIn(__oput,__name,*this);
}

bool CoordinateIn::__textRead(const Common::IputStreamPtr& __iput,const Common::String& __name,int __idx)
{
    return __textRead_CoordinateIn(__iput,__name,*this,__idx);
}

void __write_CoordinateIn(const Common::OputStreamPtr& __oput,const Gateway::CoordinateIn& __obj)
{
    __oput->write(__obj.tid);
    __oput->write(__obj.sdp);
    __oput->write(__obj.isScreenShare);
    Common::__write_StrStrMap(__oput,__obj.inExt);
}

void __read_CoordinateIn(const Common::IputStreamPtr& __iput,Gateway::CoordinateIn& __obj)
{
    __iput->read(__obj.tid);
    __iput->read(__obj.sdp);
    __iput->read(__obj.isScreenShare);
    Common::__read_StrStrMap(__iput,__obj.inExt);
}

void __textWrite_CoordinateIn(const Common::OputStreamPtr& __oput,const Common::String& __name,const Gateway::CoordinateIn& __obj)
{
    __oput->textStart(__name);
    __oput->textWrite("tid",__obj.tid);
    __oput->textWrite("sdp",__obj.sdp);
    __oput->textWrite("isScreenShare",__obj.isScreenShare);
    Common::__textWrite_StrStrMap(__oput,"inExt",__obj.inExt);
    __oput->textEnd();
}

bool __textRead_CoordinateIn(const Common::IputStreamPtr& __iput,const Common::String& __name,Gateway::CoordinateIn& __obj,int __idx)
{
    if (!__iput->textStart(__name,__idx)) return false;
    __iput->textRead("tid",__obj.tid,0);
    __iput->textRead("sdp",__obj.sdp,0);
    __iput->textRead("isScreenShare",__obj.isScreenShare,0);
    Common::__textRead_StrStrMap(__iput,"inExt",__obj.inExt,0);
    __iput->textEnd();
    return true;
}

CoordinateOut::CoordinateOut()
{
}

CoordinateOut::CoordinateOut(const Common::String& x_tid,const Common::String& x_sdp,const Common::StrStrMap& x_outExt) :
    tid(x_tid),sdp(x_sdp),outExt(x_outExt)
{
}

bool CoordinateOut::operator<(const CoordinateOut&__obj) const
{
    if (this == &__obj) return false;
    if (tid < __obj.tid) return true;
    if (__obj.tid < tid) return false;
    if (sdp < __obj.sdp) return true;
    if (__obj.sdp < sdp) return false;
    if (outExt < __obj.outExt) return true;
    if (__obj.outExt < outExt) return false;
    return false;
}

bool CoordinateOut::operator==(const CoordinateOut&__obj) const
{
    if (this == &__obj) return true;
    if (tid != __obj.tid) return false;
    if (sdp != __obj.sdp) return false;
    if (outExt != __obj.outExt) return false;
    return true;
}

void CoordinateOut::__write(const Common::OputStreamPtr& __oput) const
{
    __write_CoordinateOut(__oput,*this);
}

void CoordinateOut::__read(const Common::IputStreamPtr& __iput)
{
    __read_CoordinateOut(__iput,*this);
}

void CoordinateOut::__textWrite(const Common::OputStreamPtr& __oput,const Common::String& __name) const
{
    __textWrite_CoordinateOut(__oput,__name,*this);
}

bool CoordinateOut::__textRead(const Common::IputStreamPtr& __iput,const Common::String& __name,int __idx)
{
    return __textRead_CoordinateOut(__iput,__name,*this,__idx);
}

void __write_CoordinateOut(const Common::OputStreamPtr& __oput,const Gateway::CoordinateOut& __obj)
{
    __oput->write(__obj.tid);
    __oput->write(__obj.sdp);
    Common::__write_StrStrMap(__oput,__obj.outExt);
}

void __read_CoordinateOut(const Common::IputStreamPtr& __iput,Gateway::CoordinateOut& __obj)
{
    __iput->read(__obj.tid);
    __iput->read(__obj.sdp);
    Common::__read_StrStrMap(__iput,__obj.outExt);
}

void __textWrite_CoordinateOut(const Common::OputStreamPtr& __oput,const Common::String& __name,const Gateway::CoordinateOut& __obj)
{
    __oput->textStart(__name);
    __oput->textWrite("tid",__obj.tid);
    __oput->textWrite("sdp",__obj.sdp);
    Common::__textWrite_StrStrMap(__oput,"outExt",__obj.outExt);
    __oput->textEnd();
}

bool __textRead_CoordinateOut(const Common::IputStreamPtr& __iput,const Common::String& __name,Gateway::CoordinateOut& __obj,int __idx)
{
    if (!__iput->textStart(__name,__idx)) return false;
    __iput->textRead("tid",__obj.tid,0);
    __iput->textRead("sdp",__obj.sdp,0);
    Common::__textRead_StrStrMap(__iput,"outExt",__obj.outExt,0);
    __iput->textEnd();
    return true;
}

CoordinateIn2::CoordinateIn2() :
    isScreenShare(false),
    streamId(0)
{
}

CoordinateIn2::CoordinateIn2(const Common::String& x_tid,const Common::String& x_sdp,bool x_isScreenShare,int x_streamId,const Common::StrStrMap& x_properties) :
    tid(x_tid),sdp(x_sdp),isScreenShare(x_isScreenShare),streamId(x_streamId),properties(x_properties)
{
}

bool CoordinateIn2::operator<(const CoordinateIn2&__obj) const
{
    if (this == &__obj) return false;
    if (tid < __obj.tid) return true;
    if (__obj.tid < tid) return false;
    if (sdp < __obj.sdp) return true;
    if (__obj.sdp < sdp) return false;
    if (isScreenShare < __obj.isScreenShare) return true;
    if (__obj.isScreenShare < isScreenShare) return false;
    if (streamId < __obj.streamId) return true;
    if (__obj.streamId < streamId) return false;
    if (properties < __obj.properties) return true;
    if (__obj.properties < properties) return false;
    return false;
}

bool CoordinateIn2::operator==(const CoordinateIn2&__obj) const
{
    if (this == &__obj) return true;
    if (tid != __obj.tid) return false;
    if (sdp != __obj.sdp) return false;
    if (isScreenShare != __obj.isScreenShare) return false;
    if (streamId != __obj.streamId) return false;
    if (properties != __obj.properties) return false;
    return true;
}

void CoordinateIn2::__write(const Common::OputStreamPtr& __oput) const
{
    __write_CoordinateIn2(__oput,*this);
}

void CoordinateIn2::__read(const Common::IputStreamPtr& __iput)
{
    __read_CoordinateIn2(__iput,*this);
}

void CoordinateIn2::__textWrite(const Common::OputStreamPtr& __oput,const Common::String& __name) const
{
    __textWrite_CoordinateIn2(__oput,__name,*this);
}

bool CoordinateIn2::__textRead(const Common::IputStreamPtr& __iput,const Common::String& __name,int __idx)
{
    return __textRead_CoordinateIn2(__iput,__name,*this,__idx);
}

void __write_CoordinateIn2(const Common::OputStreamPtr& __oput,const Gateway::CoordinateIn2& __obj)
{
    __oput->write(__obj.tid);
    __oput->write(__obj.sdp);
    __oput->write(__obj.isScreenShare);
    __oput->write(__obj.streamId);
    Common::__write_StrStrMap(__oput,__obj.properties);
}

void __read_CoordinateIn2(const Common::IputStreamPtr& __iput,Gateway::CoordinateIn2& __obj)
{
    __iput->read(__obj.tid);
    __iput->read(__obj.sdp);
    __iput->read(__obj.isScreenShare);
    __iput->read(__obj.streamId);
    Common::__read_StrStrMap(__iput,__obj.properties);
}

void __textWrite_CoordinateIn2(const Common::OputStreamPtr& __oput,const Common::String& __name,const Gateway::CoordinateIn2& __obj)
{
    __oput->textStart(__name);
    __oput->textWrite("tid",__obj.tid);
    __oput->textWrite("sdp",__obj.sdp);
    __oput->textWrite("isScreenShare",__obj.isScreenShare);
    __oput->textWrite("streamId",__obj.streamId);
    Common::__textWrite_StrStrMap(__oput,"properties",__obj.properties);
    __oput->textEnd();
}

bool __textRead_CoordinateIn2(const Common::IputStreamPtr& __iput,const Common::String& __name,Gateway::CoordinateIn2& __obj,int __idx)
{
    if (!__iput->textStart(__name,__idx)) return false;
    __iput->textRead("tid",__obj.tid,0);
    __iput->textRead("sdp",__obj.sdp,0);
    __iput->textRead("isScreenShare",__obj.isScreenShare,0);
    __iput->textRead("streamId",__obj.streamId,0);
    Common::__textRead_StrStrMap(__iput,"properties",__obj.properties,0);
    __iput->textEnd();
    return true;
}

CoordinateOut2::CoordinateOut2() :
    streamId(0)
{
}

CoordinateOut2::CoordinateOut2(const Common::String& x_tid,const Common::String& x_sdp,const Common::String& x_streamUrl,int x_streamId,const Common::StrStrMap& x_properties) :
    tid(x_tid),sdp(x_sdp),streamUrl(x_streamUrl),streamId(x_streamId),properties(x_properties)
{
}

bool CoordinateOut2::operator<(const CoordinateOut2&__obj) const
{
    if (this == &__obj) return false;
    if (tid < __obj.tid) return true;
    if (__obj.tid < tid) return false;
    if (sdp < __obj.sdp) return true;
    if (__obj.sdp < sdp) return false;
    if (streamUrl < __obj.streamUrl) return true;
    if (__obj.streamUrl < streamUrl) return false;
    if (streamId < __obj.streamId) return true;
    if (__obj.streamId < streamId) return false;
    if (properties < __obj.properties) return true;
    if (__obj.properties < properties) return false;
    return false;
}

bool CoordinateOut2::operator==(const CoordinateOut2&__obj) const
{
    if (this == &__obj) return true;
    if (tid != __obj.tid) return false;
    if (sdp != __obj.sdp) return false;
    if (streamUrl != __obj.streamUrl) return false;
    if (streamId != __obj.streamId) return false;
    if (properties != __obj.properties) return false;
    return true;
}

void CoordinateOut2::__write(const Common::OputStreamPtr& __oput) const
{
    __write_CoordinateOut2(__oput,*this);
}

void CoordinateOut2::__read(const Common::IputStreamPtr& __iput)
{
    __read_CoordinateOut2(__iput,*this);
}

void CoordinateOut2::__textWrite(const Common::OputStreamPtr& __oput,const Common::String& __name) const
{
    __textWrite_CoordinateOut2(__oput,__name,*this);
}

bool CoordinateOut2::__textRead(const Common::IputStreamPtr& __iput,const Common::String& __name,int __idx)
{
    return __textRead_CoordinateOut2(__iput,__name,*this,__idx);
}

void __write_CoordinateOut2(const Common::OputStreamPtr& __oput,const Gateway::CoordinateOut2& __obj)
{
    __oput->write(__obj.tid);
    __oput->write(__obj.sdp);
    __oput->write(__obj.streamUrl);
    __oput->write(__obj.streamId);
    Common::__write_StrStrMap(__oput,__obj.properties);
}

void __read_CoordinateOut2(const Common::IputStreamPtr& __iput,Gateway::CoordinateOut2& __obj)
{
    __iput->read(__obj.tid);
    __iput->read(__obj.sdp);
    __iput->read(__obj.streamUrl);
    __iput->read(__obj.streamId);
    Common::__read_StrStrMap(__iput,__obj.properties);
}

void __textWrite_CoordinateOut2(const Common::OputStreamPtr& __oput,const Common::String& __name,const Gateway::CoordinateOut2& __obj)
{
    __oput->textStart(__name);
    __oput->textWrite("tid",__obj.tid);
    __oput->textWrite("sdp",__obj.sdp);
    __oput->textWrite("streamUrl",__obj.streamUrl);
    __oput->textWrite("streamId",__obj.streamId);
    Common::__textWrite_StrStrMap(__oput,"properties",__obj.properties);
    __oput->textEnd();
}

bool __textRead_CoordinateOut2(const Common::IputStreamPtr& __iput,const Common::String& __name,Gateway::CoordinateOut2& __obj,int __idx)
{
    if (!__iput->textStart(__name,__idx)) return false;
    __iput->textRead("tid",__obj.tid,0);
    __iput->textRead("sdp",__obj.sdp,0);
    __iput->textRead("streamUrl",__obj.streamUrl,0);
    __iput->textRead("streamId",__obj.streamId,0);
    Common::__textRead_StrStrMap(__iput,"properties",__obj.properties,0);
    __iput->textEnd();
    return true;
}
void __write_MsgVec(const Common::OputStreamPtr& __oput,const Gateway::MsgVec& __obj)
{
    __oput->write((int)__obj.size());
    vector<Gateway::Msg>::const_iterator it1;
    for (it1 = __obj.begin();it1 != __obj.end();it1++)
        Gateway::__write_Msg(__oput,*it1);
}

void __read_MsgVec(const Common::IputStreamPtr& __iput,Gateway::MsgVec& __obj)
{
    __obj.clear();
    int size;__iput->read(size);
    for (int i=0;i< size;i++)
    {
        Gateway::Msg m;
        Gateway::__read_Msg(__iput,m);
        __obj.push_back(m);
    }
}

void __textWrite_MsgVec(const Common::OputStreamPtr& __oput,const Common::String& __name,const Gateway::MsgVec& __obj)
{
    __oput->textArray(__name);
    vector<Gateway::Msg>::const_iterator it1;
    for (it1 = __obj.begin();it1 != __obj.end();it1++)
        Gateway::__textWrite_Msg(__oput,__name,*it1);
}

bool __textRead_MsgVec(const Common::IputStreamPtr& __iput,const Common::String& __name,Gateway::MsgVec& __obj)
{
    __obj.clear();
    int size = __iput->textCount(__name);
    for (int i=0;i<size;i++)
    {
        Gateway::Msg m;
        if (Gateway::__textRead_Msg(__iput,__name,m,i))
            __obj.push_back(m);
    }
    return true;
}

const char* TestAgentType_toString(Gateway::TestAgentType val)
{
    switch (val)
    {
    case TestAgentSipDialer: return (const char *)"TestAgentSipDialer";
    default: return (const char *)"unknown";
    }
}

};//namespace: Gateway
