﻿//
// *****************************************************************************
// Copyright(c) 2017-2025 Juphoon System Software Co., LTD. All rights reserved.
// *****************************************************************************
//
// Auto generated from: AgentGateway.def
// Warning: do not edit this file.
//

#include "Gateway/AgentGatewayAgent.h"

namespace Gateway
{

bool AgentGatewayAgent::connect(const Common::String& instanceId,Common::StrStrMap& params,const Common::CallParamsPtr& __params) const throw()
{
    try
    {
        int __loop = 0;
        while (1)
        {
            Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
            Common::VerListPtr __vers = __agent->verList("connect.AgentGateway.Gateway");
            short __ver = 0;
            if (__vers)
            {
                __ver = __vers->ver(true);
                if (__ver > 0) __ver = 0;
            }
            switch (__ver)
            {
            case 0:
                __oput->write((short)1);
                __oput->write((short)__ver);
                __oput->write(instanceId);
                break;
            default:
                throw Common::CallException(Common::ObjectAgent::versionError("connect.AgentGateway.Gateway"));
            }
            Common::IputStreamPtr __iput;
            Common::CallError __error;
            int __rslt = __agent->ex_sync("connect.AgentGateway.Gateway",__oput,__iput,__params,__error);
            if (__rslt == -1)
                throw Common::CallException(__error);
            if (__rslt>>16)
            {
                Assert((__rslt>>16) == VersionException);
                __loop ++;
                if (__loop >= 3)
                    throw Common::CallException(Common::ObjectAgent::versionError("connect.AgentGateway.Gateway"));
                continue;
            }
            bool __ret;
            switch (__rslt)
            {
            case 0:
                __iput->read(__ret);
                Common::__read_StrStrMap(__iput,params);
                break;
            default:
                throw Common::CallException(Common::ObjectAgent::versionError("connect.AgentGateway.Gateway"));
            }
            Common::ObjectAgent::processFinal(__iput);
            return __ret;
        }
    }
    catch (const Common::CallException& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
    catch (const Common::Exception& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
}

void AgentGatewayAgent::connect_begin(const Common::AgentAsyncPtr& __async,const Common::String& instanceId,const Common::CallParamsPtr& __params,const Common::ObjectPtr& __userdata) const throw()
{
    class __AgentAsync : public Common::AgentAsync
    {
    public:
        __AgentAsync(const Common::ObjectAgentPtr& __agent,const Common::AgentAsyncPtr& __async,const Common::String& instanceId,const Common::CallParamsPtr& __params,const Common::ObjectPtr& __userdata)
            : x__agent(__agent),x__async(__async),x_instanceId(instanceId),x__params(__params),x__userdata(__userdata),x__loop(0) {}

        void start()
        {
            Common::AgentAsyncPtr pthis = this;
            try
            {
                Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
                Common::VerListPtr __vers = x__agent->verList("connect.AgentGateway.Gateway");
                short __ver = 0;
                if (__vers)
                {
                    __ver = __vers->ver(true);
                    if (__ver > 0) __ver = 0;
                }
                switch (__ver)
                {
                case 0:
                    __oput->write((short)1);
                    __oput->write((short)__ver);
                    __oput->write(x_instanceId);
                    break;
                default:
                    throw Common::CallException(Common::ObjectAgent::versionError("connect.AgentGateway.Gateway"));
                }
                x__agent->ex_async(this,"connect.AgentGateway.Gateway",__oput,x__params,0);
            }
            catch (const Common::CallException& ex)
            {
                Common::ObjectAgent::throwException(x__async,ex,x__userdata);
            }
            catch (const Common::Exception& ex)
            {
                Common::ObjectAgent::throwException(x__async,ex,x__userdata);
            }
        }
        void cmdResult(int __rslt,const Common::IputStreamPtr& __iput,const Common::ObjectPtr& __userdata)
        {
            if ((__rslt>>16)!=VersionException)
            {
                if (x__async) x__async->cmdResult(__rslt,__iput,x__userdata);
                return;
            }
            x__loop ++;
            if (x__loop >= 3)
            {
                if (x__async)
                    Common::ObjectAgent::throwException(x__async, Common::CallException(Common::ObjectAgent::versionError("connect.AgentGateway.Gateway")), x__userdata);
                return;
            }
            start();
        }
    private:
        Common::ObjectAgentPtr x__agent;
        Common::AgentAsyncPtr x__async;
        Common::String x_instanceId;
        Common::CallParamsPtr x__params;
        Common::ObjectPtr x__userdata;
        int x__loop;
    };
    (new __AgentAsync(__agent,__async,instanceId,__params,__userdata))->start();
}

bool AgentGatewayAgent::connect_end(int __rslt,const Common::IputStreamPtr& __iput,Common::StrStrMap& params) throw()
{
    try
    {
        Common::CallError __error;
        if (Common::ObjectAgent::processException(__rslt,__iput,__error))
            throw Common::CallException(__error);
        Assert((__rslt>>16) == 0);
        bool __ret;
        switch (__rslt)
        {
        case 0:
            __iput->read(__ret);
            Common::__read_StrStrMap(__iput,params);
            break;
        default:
            throw Common::CallException(Common::ObjectAgent::versionError("connect.AgentGateway.Gateway"));
        }
        Common::ObjectAgent::processFinal(__iput);
        return __ret;
    }
    catch (const Common::CallException& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
    catch (const Common::Exception& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
}

bool AgentGatewayAgent::connect2(const Common::String& instanceId,Common::StrStrMap& params,const Common::CallParamsPtr& __params) const throw()
{
    try
    {
        int __loop = 0;
        while (1)
        {
            Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
            Common::VerListPtr __vers = __agent->verList("connect2.AgentGateway.Gateway");
            short __ver = 0;
            if (__vers)
            {
                __ver = __vers->ver(true);
                if (__ver > 0) __ver = 0;
            }
            switch (__ver)
            {
            case 0:
                __oput->write((short)1);
                __oput->write((short)__ver);
                __oput->write(instanceId);
                break;
            default:
                throw Common::CallException(Common::ObjectAgent::versionError("connect2.AgentGateway.Gateway"));
            }
            Common::IputStreamPtr __iput;
            Common::CallError __error;
            int __rslt = __agent->ex_sync("connect2.AgentGateway.Gateway",__oput,__iput,__params,__error);
            if (__rslt == -1)
                throw Common::CallException(__error);
            if (__rslt>>16)
            {
                Assert((__rslt>>16) == VersionException);
                __loop ++;
                if (__loop >= 3)
                    throw Common::CallException(Common::ObjectAgent::versionError("connect2.AgentGateway.Gateway"));
                continue;
            }
            bool __ret;
            switch (__rslt)
            {
            case 0:
                __iput->read(__ret);
                Common::__read_StrStrMap(__iput,params);
                break;
            default:
                throw Common::CallException(Common::ObjectAgent::versionError("connect2.AgentGateway.Gateway"));
            }
            Common::ObjectAgent::processFinal(__iput);
            return __ret;
        }
    }
    catch (const Common::CallException& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
    catch (const Common::Exception& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
}

void AgentGatewayAgent::connect2_begin(const Common::AgentAsyncPtr& __async,const Common::String& instanceId,const Common::CallParamsPtr& __params,const Common::ObjectPtr& __userdata) const throw()
{
    class __AgentAsync : public Common::AgentAsync
    {
    public:
        __AgentAsync(const Common::ObjectAgentPtr& __agent,const Common::AgentAsyncPtr& __async,const Common::String& instanceId,const Common::CallParamsPtr& __params,const Common::ObjectPtr& __userdata)
            : x__agent(__agent),x__async(__async),x_instanceId(instanceId),x__params(__params),x__userdata(__userdata),x__loop(0) {}

        void start()
        {
            Common::AgentAsyncPtr pthis = this;
            try
            {
                Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
                Common::VerListPtr __vers = x__agent->verList("connect2.AgentGateway.Gateway");
                short __ver = 0;
                if (__vers)
                {
                    __ver = __vers->ver(true);
                    if (__ver > 0) __ver = 0;
                }
                switch (__ver)
                {
                case 0:
                    __oput->write((short)1);
                    __oput->write((short)__ver);
                    __oput->write(x_instanceId);
                    break;
                default:
                    throw Common::CallException(Common::ObjectAgent::versionError("connect2.AgentGateway.Gateway"));
                }
                x__agent->ex_async(this,"connect2.AgentGateway.Gateway",__oput,x__params,0);
            }
            catch (const Common::CallException& ex)
            {
                Common::ObjectAgent::throwException(x__async,ex,x__userdata);
            }
            catch (const Common::Exception& ex)
            {
                Common::ObjectAgent::throwException(x__async,ex,x__userdata);
            }
        }
        void cmdResult(int __rslt,const Common::IputStreamPtr& __iput,const Common::ObjectPtr& __userdata)
        {
            if ((__rslt>>16)!=VersionException)
            {
                if (x__async) x__async->cmdResult(__rslt,__iput,x__userdata);
                return;
            }
            x__loop ++;
            if (x__loop >= 3)
            {
                if (x__async)
                    Common::ObjectAgent::throwException(x__async, Common::CallException(Common::ObjectAgent::versionError("connect2.AgentGateway.Gateway")), x__userdata);
                return;
            }
            start();
        }
    private:
        Common::ObjectAgentPtr x__agent;
        Common::AgentAsyncPtr x__async;
        Common::String x_instanceId;
        Common::CallParamsPtr x__params;
        Common::ObjectPtr x__userdata;
        int x__loop;
    };
    (new __AgentAsync(__agent,__async,instanceId,__params,__userdata))->start();
}

bool AgentGatewayAgent::connect2_end(int __rslt,const Common::IputStreamPtr& __iput,Common::StrStrMap& params) throw()
{
    try
    {
        Common::CallError __error;
        if (Common::ObjectAgent::processException(__rslt,__iput,__error))
            throw Common::CallException(__error);
        Assert((__rslt>>16) == 0);
        bool __ret;
        switch (__rslt)
        {
        case 0:
            __iput->read(__ret);
            Common::__read_StrStrMap(__iput,params);
            break;
        default:
            throw Common::CallException(Common::ObjectAgent::versionError("connect2.AgentGateway.Gateway"));
        }
        Common::ObjectAgent::processFinal(__iput);
        return __ret;
    }
    catch (const Common::CallException& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
    catch (const Common::Exception& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
}

bool AgentGatewayAgent::heartbeat(const Common::String& instanceId,const Common::StrStrMap& params,const Common::CallParamsPtr& __params) const throw()
{
    try
    {
        int __loop = 0;
        while (1)
        {
            Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
            Common::VerListPtr __vers = __agent->verList("heartbeat.AgentGateway.Gateway");
            short __ver = 0;
            if (__vers)
            {
                __ver = __vers->ver(true);
                if (__ver > 0) __ver = 0;
            }
            switch (__ver)
            {
            case 0:
                __oput->write((short)1);
                __oput->write((short)__ver);
                __oput->write(instanceId);
                Common::__write_StrStrMap(__oput,params);
                break;
            default:
                throw Common::CallException(Common::ObjectAgent::versionError("heartbeat.AgentGateway.Gateway"));
            }
            Common::IputStreamPtr __iput;
            Common::CallError __error;
            int __rslt = __agent->ex_sync("heartbeat.AgentGateway.Gateway",__oput,__iput,__params,__error);
            if (__rslt == -1)
                throw Common::CallException(__error);
            if (__rslt>>16)
            {
                Assert((__rslt>>16) == VersionException);
                __loop ++;
                if (__loop >= 3)
                    throw Common::CallException(Common::ObjectAgent::versionError("heartbeat.AgentGateway.Gateway"));
                continue;
            }
            bool __ret;
            switch (__rslt)
            {
            case 0:
                __iput->read(__ret);
                break;
            default:
                throw Common::CallException(Common::ObjectAgent::versionError("heartbeat.AgentGateway.Gateway"));
            }
            Common::ObjectAgent::processFinal(__iput);
            return __ret;
        }
    }
    catch (const Common::CallException& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
    catch (const Common::Exception& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
}

void AgentGatewayAgent::heartbeat_begin(const Common::AgentAsyncPtr& __async,const Common::String& instanceId,const Common::StrStrMap& params,const Common::CallParamsPtr& __params,const Common::ObjectPtr& __userdata) const throw()
{
    class __AgentAsync : public Common::AgentAsync
    {
    public:
        __AgentAsync(const Common::ObjectAgentPtr& __agent,const Common::AgentAsyncPtr& __async,const Common::String& instanceId,const Common::StrStrMap& params,const Common::CallParamsPtr& __params,const Common::ObjectPtr& __userdata)
            : x__agent(__agent),x__async(__async),x_instanceId(instanceId),x_params(params),x__params(__params),x__userdata(__userdata),x__loop(0) {}

        void start()
        {
            Common::AgentAsyncPtr pthis = this;
            try
            {
                Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
                Common::VerListPtr __vers = x__agent->verList("heartbeat.AgentGateway.Gateway");
                short __ver = 0;
                if (__vers)
                {
                    __ver = __vers->ver(true);
                    if (__ver > 0) __ver = 0;
                }
                switch (__ver)
                {
                case 0:
                    __oput->write((short)1);
                    __oput->write((short)__ver);
                    __oput->write(x_instanceId);
                    Common::__write_StrStrMap(__oput,x_params);
                    break;
                default:
                    throw Common::CallException(Common::ObjectAgent::versionError("heartbeat.AgentGateway.Gateway"));
                }
                x__agent->ex_async(this,"heartbeat.AgentGateway.Gateway",__oput,x__params,0);
            }
            catch (const Common::CallException& ex)
            {
                Common::ObjectAgent::throwException(x__async,ex,x__userdata);
            }
            catch (const Common::Exception& ex)
            {
                Common::ObjectAgent::throwException(x__async,ex,x__userdata);
            }
        }
        void cmdResult(int __rslt,const Common::IputStreamPtr& __iput,const Common::ObjectPtr& __userdata)
        {
            if ((__rslt>>16)!=VersionException)
            {
                if (x__async) x__async->cmdResult(__rslt,__iput,x__userdata);
                return;
            }
            x__loop ++;
            if (x__loop >= 3)
            {
                if (x__async)
                    Common::ObjectAgent::throwException(x__async, Common::CallException(Common::ObjectAgent::versionError("heartbeat.AgentGateway.Gateway")), x__userdata);
                return;
            }
            start();
        }
    private:
        Common::ObjectAgentPtr x__agent;
        Common::AgentAsyncPtr x__async;
        Common::String x_instanceId;
        Common::StrStrMap x_params;
        Common::CallParamsPtr x__params;
        Common::ObjectPtr x__userdata;
        int x__loop;
    };
    (new __AgentAsync(__agent,__async,instanceId,params,__params,__userdata))->start();
}

bool AgentGatewayAgent::heartbeat_end(int __rslt,const Common::IputStreamPtr& __iput) throw()
{
    try
    {
        Common::CallError __error;
        if (Common::ObjectAgent::processException(__rslt,__iput,__error))
            throw Common::CallException(__error);
        Assert((__rslt>>16) == 0);
        bool __ret;
        switch (__rslt)
        {
        case 0:
            __iput->read(__ret);
            break;
        default:
            throw Common::CallException(Common::ObjectAgent::versionError("heartbeat.AgentGateway.Gateway"));
        }
        Common::ObjectAgent::processFinal(__iput);
        return __ret;
    }
    catch (const Common::CallException& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
    catch (const Common::Exception& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
}

bool AgentGatewayAgent::release(const Common::String& reason,const Common::String& instanceId,const Common::StrStrMap& params,const Common::CallParamsPtr& __params) const throw()
{
    try
    {
        int __loop = 0;
        while (1)
        {
            Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
            Common::VerListPtr __vers = __agent->verList("release.AgentGateway.Gateway");
            short __ver = 0;
            if (__vers)
            {
                __ver = __vers->ver(true);
                if (__ver > 0) __ver = 0;
            }
            switch (__ver)
            {
            case 0:
                __oput->write((short)1);
                __oput->write((short)__ver);
                __oput->write(reason);
                __oput->write(instanceId);
                Common::__write_StrStrMap(__oput,params);
                break;
            default:
                throw Common::CallException(Common::ObjectAgent::versionError("release.AgentGateway.Gateway"));
            }
            Common::IputStreamPtr __iput;
            Common::CallError __error;
            int __rslt = __agent->ex_sync("release.AgentGateway.Gateway",__oput,__iput,__params,__error);
            if (__rslt == -1)
                throw Common::CallException(__error);
            if (__rslt>>16)
            {
                Assert((__rslt>>16) == VersionException);
                __loop ++;
                if (__loop >= 3)
                    throw Common::CallException(Common::ObjectAgent::versionError("release.AgentGateway.Gateway"));
                continue;
            }
            bool __ret;
            switch (__rslt)
            {
            case 0:
                __iput->read(__ret);
                break;
            default:
                throw Common::CallException(Common::ObjectAgent::versionError("release.AgentGateway.Gateway"));
            }
            Common::ObjectAgent::processFinal(__iput);
            return __ret;
        }
    }
    catch (const Common::CallException& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
    catch (const Common::Exception& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
}

void AgentGatewayAgent::release_begin(const Common::AgentAsyncPtr& __async,const Common::String& reason,const Common::String& instanceId,const Common::StrStrMap& params,const Common::CallParamsPtr& __params,const Common::ObjectPtr& __userdata) const throw()
{
    class __AgentAsync : public Common::AgentAsync
    {
    public:
        __AgentAsync(const Common::ObjectAgentPtr& __agent,const Common::AgentAsyncPtr& __async,const Common::String& reason,const Common::String& instanceId,const Common::StrStrMap& params,const Common::CallParamsPtr& __params,const Common::ObjectPtr& __userdata)
            : x__agent(__agent),x__async(__async),x_reason(reason),x_instanceId(instanceId),x_params(params),x__params(__params),x__userdata(__userdata),x__loop(0) {}

        void start()
        {
            Common::AgentAsyncPtr pthis = this;
            try
            {
                Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
                Common::VerListPtr __vers = x__agent->verList("release.AgentGateway.Gateway");
                short __ver = 0;
                if (__vers)
                {
                    __ver = __vers->ver(true);
                    if (__ver > 0) __ver = 0;
                }
                switch (__ver)
                {
                case 0:
                    __oput->write((short)1);
                    __oput->write((short)__ver);
                    __oput->write(x_reason);
                    __oput->write(x_instanceId);
                    Common::__write_StrStrMap(__oput,x_params);
                    break;
                default:
                    throw Common::CallException(Common::ObjectAgent::versionError("release.AgentGateway.Gateway"));
                }
                x__agent->ex_async(this,"release.AgentGateway.Gateway",__oput,x__params,0);
            }
            catch (const Common::CallException& ex)
            {
                Common::ObjectAgent::throwException(x__async,ex,x__userdata);
            }
            catch (const Common::Exception& ex)
            {
                Common::ObjectAgent::throwException(x__async,ex,x__userdata);
            }
        }
        void cmdResult(int __rslt,const Common::IputStreamPtr& __iput,const Common::ObjectPtr& __userdata)
        {
            if ((__rslt>>16)!=VersionException)
            {
                if (x__async) x__async->cmdResult(__rslt,__iput,x__userdata);
                return;
            }
            x__loop ++;
            if (x__loop >= 3)
            {
                if (x__async)
                    Common::ObjectAgent::throwException(x__async, Common::CallException(Common::ObjectAgent::versionError("release.AgentGateway.Gateway")), x__userdata);
                return;
            }
            start();
        }
    private:
        Common::ObjectAgentPtr x__agent;
        Common::AgentAsyncPtr x__async;
        Common::String x_reason;
        Common::String x_instanceId;
        Common::StrStrMap x_params;
        Common::CallParamsPtr x__params;
        Common::ObjectPtr x__userdata;
        int x__loop;
    };
    (new __AgentAsync(__agent,__async,reason,instanceId,params,__params,__userdata))->start();
}

bool AgentGatewayAgent::release_end(int __rslt,const Common::IputStreamPtr& __iput) throw()
{
    try
    {
        Common::CallError __error;
        if (Common::ObjectAgent::processException(__rslt,__iput,__error))
            throw Common::CallException(__error);
        Assert((__rslt>>16) == 0);
        bool __ret;
        switch (__rslt)
        {
        case 0:
            __iput->read(__ret);
            break;
        default:
            throw Common::CallException(Common::ObjectAgent::versionError("release.AgentGateway.Gateway"));
        }
        Common::ObjectAgent::processFinal(__iput);
        return __ret;
    }
    catch (const Common::CallException& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
    catch (const Common::Exception& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
}

bool AgentGatewayAgent::pushData(const Common::String& instanceId,const Common::StrVec& data,const Common::CallParamsPtr& __params) const throw()
{
    try
    {
        int __loop = 0;
        while (1)
        {
            Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
            Common::VerListPtr __vers = __agent->verList("pushData.AgentGateway.Gateway");
            short __ver = 0;
            if (__vers)
            {
                __ver = __vers->ver(true);
                if (__ver > 0) __ver = 0;
            }
            switch (__ver)
            {
            case 0:
                __oput->write((short)1);
                __oput->write((short)__ver);
                __oput->write(instanceId);
                Common::__write_StrVec(__oput,data);
                break;
            default:
                throw Common::CallException(Common::ObjectAgent::versionError("pushData.AgentGateway.Gateway"));
            }
            Common::IputStreamPtr __iput;
            Common::CallError __error;
            int __rslt = __agent->ex_sync("pushData.AgentGateway.Gateway",__oput,__iput,__params,__error);
            if (__rslt == -1)
                throw Common::CallException(__error);
            if (__rslt>>16)
            {
                Assert((__rslt>>16) == VersionException);
                __loop ++;
                if (__loop >= 3)
                    throw Common::CallException(Common::ObjectAgent::versionError("pushData.AgentGateway.Gateway"));
                continue;
            }
            bool __ret;
            switch (__rslt)
            {
            case 0:
                __iput->read(__ret);
                break;
            default:
                throw Common::CallException(Common::ObjectAgent::versionError("pushData.AgentGateway.Gateway"));
            }
            Common::ObjectAgent::processFinal(__iput);
            return __ret;
        }
    }
    catch (const Common::CallException& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
    catch (const Common::Exception& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
}

void AgentGatewayAgent::pushData_begin(const Common::AgentAsyncPtr& __async,const Common::String& instanceId,const Common::StrVec& data,const Common::CallParamsPtr& __params,const Common::ObjectPtr& __userdata) const throw()
{
    class __AgentAsync : public Common::AgentAsync
    {
    public:
        __AgentAsync(const Common::ObjectAgentPtr& __agent,const Common::AgentAsyncPtr& __async,const Common::String& instanceId,const Common::StrVec& data,const Common::CallParamsPtr& __params,const Common::ObjectPtr& __userdata)
            : x__agent(__agent),x__async(__async),x_instanceId(instanceId),x_data(data),x__params(__params),x__userdata(__userdata),x__loop(0) {}

        void start()
        {
            Common::AgentAsyncPtr pthis = this;
            try
            {
                Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
                Common::VerListPtr __vers = x__agent->verList("pushData.AgentGateway.Gateway");
                short __ver = 0;
                if (__vers)
                {
                    __ver = __vers->ver(true);
                    if (__ver > 0) __ver = 0;
                }
                switch (__ver)
                {
                case 0:
                    __oput->write((short)1);
                    __oput->write((short)__ver);
                    __oput->write(x_instanceId);
                    Common::__write_StrVec(__oput,x_data);
                    break;
                default:
                    throw Common::CallException(Common::ObjectAgent::versionError("pushData.AgentGateway.Gateway"));
                }
                x__agent->ex_async(this,"pushData.AgentGateway.Gateway",__oput,x__params,0);
            }
            catch (const Common::CallException& ex)
            {
                Common::ObjectAgent::throwException(x__async,ex,x__userdata);
            }
            catch (const Common::Exception& ex)
            {
                Common::ObjectAgent::throwException(x__async,ex,x__userdata);
            }
        }
        void cmdResult(int __rslt,const Common::IputStreamPtr& __iput,const Common::ObjectPtr& __userdata)
        {
            if ((__rslt>>16)!=VersionException)
            {
                if (x__async) x__async->cmdResult(__rslt,__iput,x__userdata);
                return;
            }
            x__loop ++;
            if (x__loop >= 3)
            {
                if (x__async)
                    Common::ObjectAgent::throwException(x__async, Common::CallException(Common::ObjectAgent::versionError("pushData.AgentGateway.Gateway")), x__userdata);
                return;
            }
            start();
        }
    private:
        Common::ObjectAgentPtr x__agent;
        Common::AgentAsyncPtr x__async;
        Common::String x_instanceId;
        Common::StrVec x_data;
        Common::CallParamsPtr x__params;
        Common::ObjectPtr x__userdata;
        int x__loop;
    };
    (new __AgentAsync(__agent,__async,instanceId,data,__params,__userdata))->start();
}

bool AgentGatewayAgent::pushData_end(int __rslt,const Common::IputStreamPtr& __iput) throw()
{
    try
    {
        Common::CallError __error;
        if (Common::ObjectAgent::processException(__rslt,__iput,__error))
            throw Common::CallException(__error);
        Assert((__rslt>>16) == 0);
        bool __ret;
        switch (__rslt)
        {
        case 0:
            __iput->read(__ret);
            break;
        default:
            throw Common::CallException(Common::ObjectAgent::versionError("pushData.AgentGateway.Gateway"));
        }
        Common::ObjectAgent::processFinal(__iput);
        return __ret;
    }
    catch (const Common::CallException& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
    catch (const Common::Exception& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
}

bool AgentGatewayAgent::pullData(const Common::String& instanceId,Common::Long consumeId,Gateway::MsgVec& vecData,Common::StrStrMap& outParams,const Common::CallParamsPtr& __params) const throw()
{
    try
    {
        int __loop = 0;
        while (1)
        {
            Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
            Common::VerListPtr __vers = __agent->verList("pullData.AgentGateway.Gateway");
            short __ver = 0;
            if (__vers)
            {
                __ver = __vers->ver(true);
                if (__ver > 0) __ver = 0;
            }
            switch (__ver)
            {
            case 0:
                __oput->write((short)1);
                __oput->write((short)__ver);
                __oput->write(instanceId);
                __oput->write(consumeId);
                break;
            default:
                throw Common::CallException(Common::ObjectAgent::versionError("pullData.AgentGateway.Gateway"));
            }
            Common::IputStreamPtr __iput;
            Common::CallError __error;
            int __rslt = __agent->ex_sync("pullData.AgentGateway.Gateway",__oput,__iput,__params,__error);
            if (__rslt == -1)
                throw Common::CallException(__error);
            if (__rslt>>16)
            {
                Assert((__rslt>>16) == VersionException);
                __loop ++;
                if (__loop >= 3)
                    throw Common::CallException(Common::ObjectAgent::versionError("pullData.AgentGateway.Gateway"));
                continue;
            }
            bool __ret;
            switch (__rslt)
            {
            case 0:
                __iput->read(__ret);
                Gateway::__read_MsgVec(__iput,vecData);
                Common::__read_StrStrMap(__iput,outParams);
                break;
            default:
                throw Common::CallException(Common::ObjectAgent::versionError("pullData.AgentGateway.Gateway"));
            }
            Common::ObjectAgent::processFinal(__iput);
            return __ret;
        }
    }
    catch (const Common::CallException& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
    catch (const Common::Exception& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
}

void AgentGatewayAgent::pullData_begin(const Common::AgentAsyncPtr& __async,const Common::String& instanceId,Common::Long consumeId,const Common::CallParamsPtr& __params,const Common::ObjectPtr& __userdata) const throw()
{
    class __AgentAsync : public Common::AgentAsync
    {
    public:
        __AgentAsync(const Common::ObjectAgentPtr& __agent,const Common::AgentAsyncPtr& __async,const Common::String& instanceId,Common::Long consumeId,const Common::CallParamsPtr& __params,const Common::ObjectPtr& __userdata)
            : x__agent(__agent),x__async(__async),x_instanceId(instanceId),x_consumeId(consumeId),x__params(__params),x__userdata(__userdata),x__loop(0) {}

        void start()
        {
            Common::AgentAsyncPtr pthis = this;
            try
            {
                Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
                Common::VerListPtr __vers = x__agent->verList("pullData.AgentGateway.Gateway");
                short __ver = 0;
                if (__vers)
                {
                    __ver = __vers->ver(true);
                    if (__ver > 0) __ver = 0;
                }
                switch (__ver)
                {
                case 0:
                    __oput->write((short)1);
                    __oput->write((short)__ver);
                    __oput->write(x_instanceId);
                    __oput->write(x_consumeId);
                    break;
                default:
                    throw Common::CallException(Common::ObjectAgent::versionError("pullData.AgentGateway.Gateway"));
                }
                x__agent->ex_async(this,"pullData.AgentGateway.Gateway",__oput,x__params,0);
            }
            catch (const Common::CallException& ex)
            {
                Common::ObjectAgent::throwException(x__async,ex,x__userdata);
            }
            catch (const Common::Exception& ex)
            {
                Common::ObjectAgent::throwException(x__async,ex,x__userdata);
            }
        }
        void cmdResult(int __rslt,const Common::IputStreamPtr& __iput,const Common::ObjectPtr& __userdata)
        {
            if ((__rslt>>16)!=VersionException)
            {
                if (x__async) x__async->cmdResult(__rslt,__iput,x__userdata);
                return;
            }
            x__loop ++;
            if (x__loop >= 3)
            {
                if (x__async)
                    Common::ObjectAgent::throwException(x__async, Common::CallException(Common::ObjectAgent::versionError("pullData.AgentGateway.Gateway")), x__userdata);
                return;
            }
            start();
        }
    private:
        Common::ObjectAgentPtr x__agent;
        Common::AgentAsyncPtr x__async;
        Common::String x_instanceId;
        Common::Long x_consumeId;
        Common::CallParamsPtr x__params;
        Common::ObjectPtr x__userdata;
        int x__loop;
    };
    (new __AgentAsync(__agent,__async,instanceId,consumeId,__params,__userdata))->start();
}

bool AgentGatewayAgent::pullData_end(int __rslt,const Common::IputStreamPtr& __iput,Gateway::MsgVec& vecData,Common::StrStrMap& outParams) throw()
{
    try
    {
        Common::CallError __error;
        if (Common::ObjectAgent::processException(__rslt,__iput,__error))
            throw Common::CallException(__error);
        Assert((__rslt>>16) == 0);
        bool __ret;
        switch (__rslt)
        {
        case 0:
            __iput->read(__ret);
            Gateway::__read_MsgVec(__iput,vecData);
            Common::__read_StrStrMap(__iput,outParams);
            break;
        default:
            throw Common::CallException(Common::ObjectAgent::versionError("pullData.AgentGateway.Gateway"));
        }
        Common::ObjectAgent::processFinal(__iput);
        return __ret;
    }
    catch (const Common::CallException& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
    catch (const Common::Exception& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
}

bool AgentGatewayAgent::publish(const Common::String& instanceId,const Gateway::CoordinateIn& corin,Gateway::CoordinateOut& corout,const Common::CallParamsPtr& __params) const throw()
{
    try
    {
        int __loop = 0;
        while (1)
        {
            Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
            Common::VerListPtr __vers = __agent->verList("publish.AgentGateway.Gateway");
            short __ver = 0;
            if (__vers)
            {
                __ver = __vers->ver(true);
                if (__ver > 0) __ver = 0;
            }
            switch (__ver)
            {
            case 0:
                __oput->write((short)1);
                __oput->write((short)__ver);
                __oput->write(instanceId);
                Gateway::__write_CoordinateIn(__oput,corin);
                break;
            default:
                throw Common::CallException(Common::ObjectAgent::versionError("publish.AgentGateway.Gateway"));
            }
            Common::IputStreamPtr __iput;
            Common::CallError __error;
            int __rslt = __agent->ex_sync("publish.AgentGateway.Gateway",__oput,__iput,__params,__error);
            if (__rslt == -1)
                throw Common::CallException(__error);
            if (__rslt>>16)
            {
                Assert((__rslt>>16) == VersionException);
                __loop ++;
                if (__loop >= 3)
                    throw Common::CallException(Common::ObjectAgent::versionError("publish.AgentGateway.Gateway"));
                continue;
            }
            bool __ret;
            switch (__rslt)
            {
            case 0:
                __iput->read(__ret);
                Gateway::__read_CoordinateOut(__iput,corout);
                break;
            default:
                throw Common::CallException(Common::ObjectAgent::versionError("publish.AgentGateway.Gateway"));
            }
            Common::ObjectAgent::processFinal(__iput);
            return __ret;
        }
    }
    catch (const Common::CallException& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
    catch (const Common::Exception& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
}

void AgentGatewayAgent::publish_begin(const Common::AgentAsyncPtr& __async,const Common::String& instanceId,const Gateway::CoordinateIn& corin,const Common::CallParamsPtr& __params,const Common::ObjectPtr& __userdata) const throw()
{
    class __AgentAsync : public Common::AgentAsync
    {
    public:
        __AgentAsync(const Common::ObjectAgentPtr& __agent,const Common::AgentAsyncPtr& __async,const Common::String& instanceId,const Gateway::CoordinateIn& corin,const Common::CallParamsPtr& __params,const Common::ObjectPtr& __userdata)
            : x__agent(__agent),x__async(__async),x_instanceId(instanceId),x_corin(corin),x__params(__params),x__userdata(__userdata),x__loop(0) {}

        void start()
        {
            Common::AgentAsyncPtr pthis = this;
            try
            {
                Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
                Common::VerListPtr __vers = x__agent->verList("publish.AgentGateway.Gateway");
                short __ver = 0;
                if (__vers)
                {
                    __ver = __vers->ver(true);
                    if (__ver > 0) __ver = 0;
                }
                switch (__ver)
                {
                case 0:
                    __oput->write((short)1);
                    __oput->write((short)__ver);
                    __oput->write(x_instanceId);
                    Gateway::__write_CoordinateIn(__oput,x_corin);
                    break;
                default:
                    throw Common::CallException(Common::ObjectAgent::versionError("publish.AgentGateway.Gateway"));
                }
                x__agent->ex_async(this,"publish.AgentGateway.Gateway",__oput,x__params,0);
            }
            catch (const Common::CallException& ex)
            {
                Common::ObjectAgent::throwException(x__async,ex,x__userdata);
            }
            catch (const Common::Exception& ex)
            {
                Common::ObjectAgent::throwException(x__async,ex,x__userdata);
            }
        }
        void cmdResult(int __rslt,const Common::IputStreamPtr& __iput,const Common::ObjectPtr& __userdata)
        {
            if ((__rslt>>16)!=VersionException)
            {
                if (x__async) x__async->cmdResult(__rslt,__iput,x__userdata);
                return;
            }
            x__loop ++;
            if (x__loop >= 3)
            {
                if (x__async)
                    Common::ObjectAgent::throwException(x__async, Common::CallException(Common::ObjectAgent::versionError("publish.AgentGateway.Gateway")), x__userdata);
                return;
            }
            start();
        }
    private:
        Common::ObjectAgentPtr x__agent;
        Common::AgentAsyncPtr x__async;
        Common::String x_instanceId;
        Gateway::CoordinateIn x_corin;
        Common::CallParamsPtr x__params;
        Common::ObjectPtr x__userdata;
        int x__loop;
    };
    (new __AgentAsync(__agent,__async,instanceId,corin,__params,__userdata))->start();
}

bool AgentGatewayAgent::publish_end(int __rslt,const Common::IputStreamPtr& __iput,Gateway::CoordinateOut& corout) throw()
{
    try
    {
        Common::CallError __error;
        if (Common::ObjectAgent::processException(__rslt,__iput,__error))
            throw Common::CallException(__error);
        Assert((__rslt>>16) == 0);
        bool __ret;
        switch (__rslt)
        {
        case 0:
            __iput->read(__ret);
            Gateway::__read_CoordinateOut(__iput,corout);
            break;
        default:
            throw Common::CallException(Common::ObjectAgent::versionError("publish.AgentGateway.Gateway"));
        }
        Common::ObjectAgent::processFinal(__iput);
        return __ret;
    }
    catch (const Common::CallException& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
    catch (const Common::Exception& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
}

bool AgentGatewayAgent::play(const Common::String& instanceId,const Gateway::CoordinateIn& corin,Gateway::CoordinateOut& corout,const Common::CallParamsPtr& __params) const throw()
{
    try
    {
        int __loop = 0;
        while (1)
        {
            Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
            Common::VerListPtr __vers = __agent->verList("play.AgentGateway.Gateway");
            short __ver = 0;
            if (__vers)
            {
                __ver = __vers->ver(true);
                if (__ver > 0) __ver = 0;
            }
            switch (__ver)
            {
            case 0:
                __oput->write((short)1);
                __oput->write((short)__ver);
                __oput->write(instanceId);
                Gateway::__write_CoordinateIn(__oput,corin);
                break;
            default:
                throw Common::CallException(Common::ObjectAgent::versionError("play.AgentGateway.Gateway"));
            }
            Common::IputStreamPtr __iput;
            Common::CallError __error;
            int __rslt = __agent->ex_sync("play.AgentGateway.Gateway",__oput,__iput,__params,__error);
            if (__rslt == -1)
                throw Common::CallException(__error);
            if (__rslt>>16)
            {
                Assert((__rslt>>16) == VersionException);
                __loop ++;
                if (__loop >= 3)
                    throw Common::CallException(Common::ObjectAgent::versionError("play.AgentGateway.Gateway"));
                continue;
            }
            bool __ret;
            switch (__rslt)
            {
            case 0:
                __iput->read(__ret);
                Gateway::__read_CoordinateOut(__iput,corout);
                break;
            default:
                throw Common::CallException(Common::ObjectAgent::versionError("play.AgentGateway.Gateway"));
            }
            Common::ObjectAgent::processFinal(__iput);
            return __ret;
        }
    }
    catch (const Common::CallException& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
    catch (const Common::Exception& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
}

void AgentGatewayAgent::play_begin(const Common::AgentAsyncPtr& __async,const Common::String& instanceId,const Gateway::CoordinateIn& corin,const Common::CallParamsPtr& __params,const Common::ObjectPtr& __userdata) const throw()
{
    class __AgentAsync : public Common::AgentAsync
    {
    public:
        __AgentAsync(const Common::ObjectAgentPtr& __agent,const Common::AgentAsyncPtr& __async,const Common::String& instanceId,const Gateway::CoordinateIn& corin,const Common::CallParamsPtr& __params,const Common::ObjectPtr& __userdata)
            : x__agent(__agent),x__async(__async),x_instanceId(instanceId),x_corin(corin),x__params(__params),x__userdata(__userdata),x__loop(0) {}

        void start()
        {
            Common::AgentAsyncPtr pthis = this;
            try
            {
                Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
                Common::VerListPtr __vers = x__agent->verList("play.AgentGateway.Gateway");
                short __ver = 0;
                if (__vers)
                {
                    __ver = __vers->ver(true);
                    if (__ver > 0) __ver = 0;
                }
                switch (__ver)
                {
                case 0:
                    __oput->write((short)1);
                    __oput->write((short)__ver);
                    __oput->write(x_instanceId);
                    Gateway::__write_CoordinateIn(__oput,x_corin);
                    break;
                default:
                    throw Common::CallException(Common::ObjectAgent::versionError("play.AgentGateway.Gateway"));
                }
                x__agent->ex_async(this,"play.AgentGateway.Gateway",__oput,x__params,0);
            }
            catch (const Common::CallException& ex)
            {
                Common::ObjectAgent::throwException(x__async,ex,x__userdata);
            }
            catch (const Common::Exception& ex)
            {
                Common::ObjectAgent::throwException(x__async,ex,x__userdata);
            }
        }
        void cmdResult(int __rslt,const Common::IputStreamPtr& __iput,const Common::ObjectPtr& __userdata)
        {
            if ((__rslt>>16)!=VersionException)
            {
                if (x__async) x__async->cmdResult(__rslt,__iput,x__userdata);
                return;
            }
            x__loop ++;
            if (x__loop >= 3)
            {
                if (x__async)
                    Common::ObjectAgent::throwException(x__async, Common::CallException(Common::ObjectAgent::versionError("play.AgentGateway.Gateway")), x__userdata);
                return;
            }
            start();
        }
    private:
        Common::ObjectAgentPtr x__agent;
        Common::AgentAsyncPtr x__async;
        Common::String x_instanceId;
        Gateway::CoordinateIn x_corin;
        Common::CallParamsPtr x__params;
        Common::ObjectPtr x__userdata;
        int x__loop;
    };
    (new __AgentAsync(__agent,__async,instanceId,corin,__params,__userdata))->start();
}

bool AgentGatewayAgent::play_end(int __rslt,const Common::IputStreamPtr& __iput,Gateway::CoordinateOut& corout) throw()
{
    try
    {
        Common::CallError __error;
        if (Common::ObjectAgent::processException(__rslt,__iput,__error))
            throw Common::CallException(__error);
        Assert((__rslt>>16) == 0);
        bool __ret;
        switch (__rslt)
        {
        case 0:
            __iput->read(__ret);
            Gateway::__read_CoordinateOut(__iput,corout);
            break;
        default:
            throw Common::CallException(Common::ObjectAgent::versionError("play.AgentGateway.Gateway"));
        }
        Common::ObjectAgent::processFinal(__iput);
        return __ret;
    }
    catch (const Common::CallException& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
    catch (const Common::Exception& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
}

bool AgentGatewayAgent::publish2(const Common::String& instanceId,const Common::String& userId,const Gateway::CoordinateIn2& corin,Gateway::CoordinateOut2& corout,const Common::CallParamsPtr& __params) const throw()
{
    try
    {
        int __loop = 0;
        while (1)
        {
            Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
            Common::VerListPtr __vers = __agent->verList("publish2.AgentGateway.Gateway");
            short __ver = 0;
            if (__vers)
            {
                __ver = __vers->ver(true);
                if (__ver > 0) __ver = 0;
            }
            switch (__ver)
            {
            case 0:
                __oput->write((short)1);
                __oput->write((short)__ver);
                __oput->write(instanceId);
                __oput->write(userId);
                Gateway::__write_CoordinateIn2(__oput,corin);
                break;
            default:
                throw Common::CallException(Common::ObjectAgent::versionError("publish2.AgentGateway.Gateway"));
            }
            Common::IputStreamPtr __iput;
            Common::CallError __error;
            int __rslt = __agent->ex_sync("publish2.AgentGateway.Gateway",__oput,__iput,__params,__error);
            if (__rslt == -1)
                throw Common::CallException(__error);
            if (__rslt>>16)
            {
                Assert((__rslt>>16) == VersionException);
                __loop ++;
                if (__loop >= 3)
                    throw Common::CallException(Common::ObjectAgent::versionError("publish2.AgentGateway.Gateway"));
                continue;
            }
            bool __ret;
            switch (__rslt)
            {
            case 0:
                __iput->read(__ret);
                Gateway::__read_CoordinateOut2(__iput,corout);
                break;
            default:
                throw Common::CallException(Common::ObjectAgent::versionError("publish2.AgentGateway.Gateway"));
            }
            Common::ObjectAgent::processFinal(__iput);
            return __ret;
        }
    }
    catch (const Common::CallException& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
    catch (const Common::Exception& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
}

void AgentGatewayAgent::publish2_begin(const Common::AgentAsyncPtr& __async,const Common::String& instanceId,const Common::String& userId,const Gateway::CoordinateIn2& corin,const Common::CallParamsPtr& __params,const Common::ObjectPtr& __userdata) const throw()
{
    class __AgentAsync : public Common::AgentAsync
    {
    public:
        __AgentAsync(const Common::ObjectAgentPtr& __agent,const Common::AgentAsyncPtr& __async,const Common::String& instanceId,const Common::String& userId,const Gateway::CoordinateIn2& corin,const Common::CallParamsPtr& __params,const Common::ObjectPtr& __userdata)
            : x__agent(__agent),x__async(__async),x_instanceId(instanceId),x_userId(userId),x_corin(corin),x__params(__params),x__userdata(__userdata),x__loop(0) {}

        void start()
        {
            Common::AgentAsyncPtr pthis = this;
            try
            {
                Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
                Common::VerListPtr __vers = x__agent->verList("publish2.AgentGateway.Gateway");
                short __ver = 0;
                if (__vers)
                {
                    __ver = __vers->ver(true);
                    if (__ver > 0) __ver = 0;
                }
                switch (__ver)
                {
                case 0:
                    __oput->write((short)1);
                    __oput->write((short)__ver);
                    __oput->write(x_instanceId);
                    __oput->write(x_userId);
                    Gateway::__write_CoordinateIn2(__oput,x_corin);
                    break;
                default:
                    throw Common::CallException(Common::ObjectAgent::versionError("publish2.AgentGateway.Gateway"));
                }
                x__agent->ex_async(this,"publish2.AgentGateway.Gateway",__oput,x__params,0);
            }
            catch (const Common::CallException& ex)
            {
                Common::ObjectAgent::throwException(x__async,ex,x__userdata);
            }
            catch (const Common::Exception& ex)
            {
                Common::ObjectAgent::throwException(x__async,ex,x__userdata);
            }
        }
        void cmdResult(int __rslt,const Common::IputStreamPtr& __iput,const Common::ObjectPtr& __userdata)
        {
            if ((__rslt>>16)!=VersionException)
            {
                if (x__async) x__async->cmdResult(__rslt,__iput,x__userdata);
                return;
            }
            x__loop ++;
            if (x__loop >= 3)
            {
                if (x__async)
                    Common::ObjectAgent::throwException(x__async, Common::CallException(Common::ObjectAgent::versionError("publish2.AgentGateway.Gateway")), x__userdata);
                return;
            }
            start();
        }
    private:
        Common::ObjectAgentPtr x__agent;
        Common::AgentAsyncPtr x__async;
        Common::String x_instanceId;
        Common::String x_userId;
        Gateway::CoordinateIn2 x_corin;
        Common::CallParamsPtr x__params;
        Common::ObjectPtr x__userdata;
        int x__loop;
    };
    (new __AgentAsync(__agent,__async,instanceId,userId,corin,__params,__userdata))->start();
}

bool AgentGatewayAgent::publish2_end(int __rslt,const Common::IputStreamPtr& __iput,Gateway::CoordinateOut2& corout) throw()
{
    try
    {
        Common::CallError __error;
        if (Common::ObjectAgent::processException(__rslt,__iput,__error))
            throw Common::CallException(__error);
        Assert((__rslt>>16) == 0);
        bool __ret;
        switch (__rslt)
        {
        case 0:
            __iput->read(__ret);
            Gateway::__read_CoordinateOut2(__iput,corout);
            break;
        default:
            throw Common::CallException(Common::ObjectAgent::versionError("publish2.AgentGateway.Gateway"));
        }
        Common::ObjectAgent::processFinal(__iput);
        return __ret;
    }
    catch (const Common::CallException& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
    catch (const Common::Exception& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
}

bool AgentGatewayAgent::play2(const Common::String& instanceId,const Gateway::CoordinateIn2& corin,Gateway::CoordinateOut2& corout,const Common::CallParamsPtr& __params) const throw()
{
    try
    {
        int __loop = 0;
        while (1)
        {
            Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
            Common::VerListPtr __vers = __agent->verList("play2.AgentGateway.Gateway");
            short __ver = 0;
            if (__vers)
            {
                __ver = __vers->ver(true);
                if (__ver > 0) __ver = 0;
            }
            switch (__ver)
            {
            case 0:
                __oput->write((short)1);
                __oput->write((short)__ver);
                __oput->write(instanceId);
                Gateway::__write_CoordinateIn2(__oput,corin);
                break;
            default:
                throw Common::CallException(Common::ObjectAgent::versionError("play2.AgentGateway.Gateway"));
            }
            Common::IputStreamPtr __iput;
            Common::CallError __error;
            int __rslt = __agent->ex_sync("play2.AgentGateway.Gateway",__oput,__iput,__params,__error);
            if (__rslt == -1)
                throw Common::CallException(__error);
            if (__rslt>>16)
            {
                Assert((__rslt>>16) == VersionException);
                __loop ++;
                if (__loop >= 3)
                    throw Common::CallException(Common::ObjectAgent::versionError("play2.AgentGateway.Gateway"));
                continue;
            }
            bool __ret;
            switch (__rslt)
            {
            case 0:
                __iput->read(__ret);
                Gateway::__read_CoordinateOut2(__iput,corout);
                break;
            default:
                throw Common::CallException(Common::ObjectAgent::versionError("play2.AgentGateway.Gateway"));
            }
            Common::ObjectAgent::processFinal(__iput);
            return __ret;
        }
    }
    catch (const Common::CallException& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
    catch (const Common::Exception& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
}

void AgentGatewayAgent::play2_begin(const Common::AgentAsyncPtr& __async,const Common::String& instanceId,const Gateway::CoordinateIn2& corin,const Common::CallParamsPtr& __params,const Common::ObjectPtr& __userdata) const throw()
{
    class __AgentAsync : public Common::AgentAsync
    {
    public:
        __AgentAsync(const Common::ObjectAgentPtr& __agent,const Common::AgentAsyncPtr& __async,const Common::String& instanceId,const Gateway::CoordinateIn2& corin,const Common::CallParamsPtr& __params,const Common::ObjectPtr& __userdata)
            : x__agent(__agent),x__async(__async),x_instanceId(instanceId),x_corin(corin),x__params(__params),x__userdata(__userdata),x__loop(0) {}

        void start()
        {
            Common::AgentAsyncPtr pthis = this;
            try
            {
                Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
                Common::VerListPtr __vers = x__agent->verList("play2.AgentGateway.Gateway");
                short __ver = 0;
                if (__vers)
                {
                    __ver = __vers->ver(true);
                    if (__ver > 0) __ver = 0;
                }
                switch (__ver)
                {
                case 0:
                    __oput->write((short)1);
                    __oput->write((short)__ver);
                    __oput->write(x_instanceId);
                    Gateway::__write_CoordinateIn2(__oput,x_corin);
                    break;
                default:
                    throw Common::CallException(Common::ObjectAgent::versionError("play2.AgentGateway.Gateway"));
                }
                x__agent->ex_async(this,"play2.AgentGateway.Gateway",__oput,x__params,0);
            }
            catch (const Common::CallException& ex)
            {
                Common::ObjectAgent::throwException(x__async,ex,x__userdata);
            }
            catch (const Common::Exception& ex)
            {
                Common::ObjectAgent::throwException(x__async,ex,x__userdata);
            }
        }
        void cmdResult(int __rslt,const Common::IputStreamPtr& __iput,const Common::ObjectPtr& __userdata)
        {
            if ((__rslt>>16)!=VersionException)
            {
                if (x__async) x__async->cmdResult(__rslt,__iput,x__userdata);
                return;
            }
            x__loop ++;
            if (x__loop >= 3)
            {
                if (x__async)
                    Common::ObjectAgent::throwException(x__async, Common::CallException(Common::ObjectAgent::versionError("play2.AgentGateway.Gateway")), x__userdata);
                return;
            }
            start();
        }
    private:
        Common::ObjectAgentPtr x__agent;
        Common::AgentAsyncPtr x__async;
        Common::String x_instanceId;
        Gateway::CoordinateIn2 x_corin;
        Common::CallParamsPtr x__params;
        Common::ObjectPtr x__userdata;
        int x__loop;
    };
    (new __AgentAsync(__agent,__async,instanceId,corin,__params,__userdata))->start();
}

bool AgentGatewayAgent::play2_end(int __rslt,const Common::IputStreamPtr& __iput,Gateway::CoordinateOut2& corout) throw()
{
    try
    {
        Common::CallError __error;
        if (Common::ObjectAgent::processException(__rslt,__iput,__error))
            throw Common::CallException(__error);
        Assert((__rslt>>16) == 0);
        bool __ret;
        switch (__rslt)
        {
        case 0:
            __iput->read(__ret);
            Gateway::__read_CoordinateOut2(__iput,corout);
            break;
        default:
            throw Common::CallException(Common::ObjectAgent::versionError("play2.AgentGateway.Gateway"));
        }
        Common::ObjectAgent::processFinal(__iput);
        return __ret;
    }
    catch (const Common::CallException& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
    catch (const Common::Exception& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
}

bool AgentGatewayAgent::inviteRoomDelegator(const Common::String& instanceId,const Common::String& roomId,const Common::String& appKey,const Common::String& userId,bool isPublish,const Common::String& roomParams,const Common::CallParamsPtr& __params) const throw()
{
    try
    {
        int __loop = 0;
        while (1)
        {
            Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
            Common::VerListPtr __vers = __agent->verList("inviteRoomDelegator.AgentGateway.Gateway");
            short __ver = 0;
            if (__vers)
            {
                __ver = __vers->ver(true);
                if (__ver > 0) __ver = 0;
            }
            switch (__ver)
            {
            case 0:
                __oput->write((short)1);
                __oput->write((short)__ver);
                __oput->write(instanceId);
                __oput->write(roomId);
                __oput->write(appKey);
                __oput->write(userId);
                __oput->write(isPublish);
                __oput->write(roomParams);
                break;
            default:
                throw Common::CallException(Common::ObjectAgent::versionError("inviteRoomDelegator.AgentGateway.Gateway"));
            }
            Common::IputStreamPtr __iput;
            Common::CallError __error;
            int __rslt = __agent->ex_sync("inviteRoomDelegator.AgentGateway.Gateway",__oput,__iput,__params,__error);
            if (__rslt == -1)
                throw Common::CallException(__error);
            if (__rslt>>16)
            {
                Assert((__rslt>>16) == VersionException);
                __loop ++;
                if (__loop >= 3)
                    throw Common::CallException(Common::ObjectAgent::versionError("inviteRoomDelegator.AgentGateway.Gateway"));
                continue;
            }
            bool __ret;
            switch (__rslt)
            {
            case 0:
                __iput->read(__ret);
                break;
            default:
                throw Common::CallException(Common::ObjectAgent::versionError("inviteRoomDelegator.AgentGateway.Gateway"));
            }
            Common::ObjectAgent::processFinal(__iput);
            return __ret;
        }
    }
    catch (const Common::CallException& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
    catch (const Common::Exception& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
}

void AgentGatewayAgent::inviteRoomDelegator_begin(const Common::AgentAsyncPtr& __async,const Common::String& instanceId,const Common::String& roomId,const Common::String& appKey,const Common::String& userId,bool isPublish,const Common::String& roomParams,const Common::CallParamsPtr& __params,const Common::ObjectPtr& __userdata) const throw()
{
    class __AgentAsync : public Common::AgentAsync
    {
    public:
        __AgentAsync(const Common::ObjectAgentPtr& __agent,const Common::AgentAsyncPtr& __async,const Common::String& instanceId,const Common::String& roomId,const Common::String& appKey,const Common::String& userId,bool isPublish,const Common::String& roomParams,const Common::CallParamsPtr& __params,const Common::ObjectPtr& __userdata)
            : x__agent(__agent),x__async(__async),x_instanceId(instanceId),x_roomId(roomId),x_appKey(appKey),x_userId(userId),x_isPublish(isPublish),x_roomParams(roomParams),x__params(__params),x__userdata(__userdata),x__loop(0) {}

        void start()
        {
            Common::AgentAsyncPtr pthis = this;
            try
            {
                Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
                Common::VerListPtr __vers = x__agent->verList("inviteRoomDelegator.AgentGateway.Gateway");
                short __ver = 0;
                if (__vers)
                {
                    __ver = __vers->ver(true);
                    if (__ver > 0) __ver = 0;
                }
                switch (__ver)
                {
                case 0:
                    __oput->write((short)1);
                    __oput->write((short)__ver);
                    __oput->write(x_instanceId);
                    __oput->write(x_roomId);
                    __oput->write(x_appKey);
                    __oput->write(x_userId);
                    __oput->write(x_isPublish);
                    __oput->write(x_roomParams);
                    break;
                default:
                    throw Common::CallException(Common::ObjectAgent::versionError("inviteRoomDelegator.AgentGateway.Gateway"));
                }
                x__agent->ex_async(this,"inviteRoomDelegator.AgentGateway.Gateway",__oput,x__params,0);
            }
            catch (const Common::CallException& ex)
            {
                Common::ObjectAgent::throwException(x__async,ex,x__userdata);
            }
            catch (const Common::Exception& ex)
            {
                Common::ObjectAgent::throwException(x__async,ex,x__userdata);
            }
        }
        void cmdResult(int __rslt,const Common::IputStreamPtr& __iput,const Common::ObjectPtr& __userdata)
        {
            if ((__rslt>>16)!=VersionException)
            {
                if (x__async) x__async->cmdResult(__rslt,__iput,x__userdata);
                return;
            }
            x__loop ++;
            if (x__loop >= 3)
            {
                if (x__async)
                    Common::ObjectAgent::throwException(x__async, Common::CallException(Common::ObjectAgent::versionError("inviteRoomDelegator.AgentGateway.Gateway")), x__userdata);
                return;
            }
            start();
        }
    private:
        Common::ObjectAgentPtr x__agent;
        Common::AgentAsyncPtr x__async;
        Common::String x_instanceId;
        Common::String x_roomId;
        Common::String x_appKey;
        Common::String x_userId;
        bool x_isPublish;
        Common::String x_roomParams;
        Common::CallParamsPtr x__params;
        Common::ObjectPtr x__userdata;
        int x__loop;
    };
    (new __AgentAsync(__agent,__async,instanceId,roomId,appKey,userId,isPublish,roomParams,__params,__userdata))->start();
}

bool AgentGatewayAgent::inviteRoomDelegator_end(int __rslt,const Common::IputStreamPtr& __iput) throw()
{
    try
    {
        Common::CallError __error;
        if (Common::ObjectAgent::processException(__rslt,__iput,__error))
            throw Common::CallException(__error);
        Assert((__rslt>>16) == 0);
        bool __ret;
        switch (__rslt)
        {
        case 0:
            __iput->read(__ret);
            break;
        default:
            throw Common::CallException(Common::ObjectAgent::versionError("inviteRoomDelegator.AgentGateway.Gateway"));
        }
        Common::ObjectAgent::processFinal(__iput);
        return __ret;
    }
    catch (const Common::CallException& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
    catch (const Common::Exception& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
}

bool AgentGatewayAgent::join(const Common::String& roomDelegatorId,const Common::String& userId,Common::String& instanceId,const Common::CallParamsPtr& __params) const throw()
{
    try
    {
        int __loop = 0;
        while (1)
        {
            Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
            Common::VerListPtr __vers = __agent->verList("join.AgentGateway.Gateway");
            short __ver = 0;
            if (__vers)
            {
                __ver = __vers->ver(true);
                if (__ver > 0) __ver = 0;
            }
            switch (__ver)
            {
            case 0:
                __oput->write((short)1);
                __oput->write((short)__ver);
                __oput->write(roomDelegatorId);
                __oput->write(userId);
                break;
            default:
                throw Common::CallException(Common::ObjectAgent::versionError("join.AgentGateway.Gateway"));
            }
            Common::IputStreamPtr __iput;
            Common::CallError __error;
            int __rslt = __agent->ex_sync("join.AgentGateway.Gateway",__oput,__iput,__params,__error);
            if (__rslt == -1)
                throw Common::CallException(__error);
            if (__rslt>>16)
            {
                Assert((__rslt>>16) == VersionException);
                __loop ++;
                if (__loop >= 3)
                    throw Common::CallException(Common::ObjectAgent::versionError("join.AgentGateway.Gateway"));
                continue;
            }
            bool __ret;
            switch (__rslt)
            {
            case 0:
                __iput->read(__ret);
                __iput->read(instanceId);
                break;
            default:
                throw Common::CallException(Common::ObjectAgent::versionError("join.AgentGateway.Gateway"));
            }
            Common::ObjectAgent::processFinal(__iput);
            return __ret;
        }
    }
    catch (const Common::CallException& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
    catch (const Common::Exception& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
}

void AgentGatewayAgent::join_begin(const Common::AgentAsyncPtr& __async,const Common::String& roomDelegatorId,const Common::String& userId,const Common::CallParamsPtr& __params,const Common::ObjectPtr& __userdata) const throw()
{
    class __AgentAsync : public Common::AgentAsync
    {
    public:
        __AgentAsync(const Common::ObjectAgentPtr& __agent,const Common::AgentAsyncPtr& __async,const Common::String& roomDelegatorId,const Common::String& userId,const Common::CallParamsPtr& __params,const Common::ObjectPtr& __userdata)
            : x__agent(__agent),x__async(__async),x_roomDelegatorId(roomDelegatorId),x_userId(userId),x__params(__params),x__userdata(__userdata),x__loop(0) {}

        void start()
        {
            Common::AgentAsyncPtr pthis = this;
            try
            {
                Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
                Common::VerListPtr __vers = x__agent->verList("join.AgentGateway.Gateway");
                short __ver = 0;
                if (__vers)
                {
                    __ver = __vers->ver(true);
                    if (__ver > 0) __ver = 0;
                }
                switch (__ver)
                {
                case 0:
                    __oput->write((short)1);
                    __oput->write((short)__ver);
                    __oput->write(x_roomDelegatorId);
                    __oput->write(x_userId);
                    break;
                default:
                    throw Common::CallException(Common::ObjectAgent::versionError("join.AgentGateway.Gateway"));
                }
                x__agent->ex_async(this,"join.AgentGateway.Gateway",__oput,x__params,0);
            }
            catch (const Common::CallException& ex)
            {
                Common::ObjectAgent::throwException(x__async,ex,x__userdata);
            }
            catch (const Common::Exception& ex)
            {
                Common::ObjectAgent::throwException(x__async,ex,x__userdata);
            }
        }
        void cmdResult(int __rslt,const Common::IputStreamPtr& __iput,const Common::ObjectPtr& __userdata)
        {
            if ((__rslt>>16)!=VersionException)
            {
                if (x__async) x__async->cmdResult(__rslt,__iput,x__userdata);
                return;
            }
            x__loop ++;
            if (x__loop >= 3)
            {
                if (x__async)
                    Common::ObjectAgent::throwException(x__async, Common::CallException(Common::ObjectAgent::versionError("join.AgentGateway.Gateway")), x__userdata);
                return;
            }
            start();
        }
    private:
        Common::ObjectAgentPtr x__agent;
        Common::AgentAsyncPtr x__async;
        Common::String x_roomDelegatorId;
        Common::String x_userId;
        Common::CallParamsPtr x__params;
        Common::ObjectPtr x__userdata;
        int x__loop;
    };
    (new __AgentAsync(__agent,__async,roomDelegatorId,userId,__params,__userdata))->start();
}

bool AgentGatewayAgent::join_end(int __rslt,const Common::IputStreamPtr& __iput,Common::String& instanceId) throw()
{
    try
    {
        Common::CallError __error;
        if (Common::ObjectAgent::processException(__rslt,__iput,__error))
            throw Common::CallException(__error);
        Assert((__rslt>>16) == 0);
        bool __ret;
        switch (__rslt)
        {
        case 0:
            __iput->read(__ret);
            __iput->read(instanceId);
            break;
        default:
            throw Common::CallException(Common::ObjectAgent::versionError("join.AgentGateway.Gateway"));
        }
        Common::ObjectAgent::processFinal(__iput);
        return __ret;
    }
    catch (const Common::CallException& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
    catch (const Common::Exception& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
}

bool AgentGatewayAgent::leave(const Common::String& roomDelegatorId,const Common::String& userId,const Common::String& instanceId,const Common::CallParamsPtr& __params) const throw()
{
    try
    {
        int __loop = 0;
        while (1)
        {
            Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
            Common::VerListPtr __vers = __agent->verList("leave.AgentGateway.Gateway");
            short __ver = 0;
            if (__vers)
            {
                __ver = __vers->ver(true);
                if (__ver > 0) __ver = 0;
            }
            switch (__ver)
            {
            case 0:
                __oput->write((short)1);
                __oput->write((short)__ver);
                __oput->write(roomDelegatorId);
                __oput->write(userId);
                __oput->write(instanceId);
                break;
            default:
                throw Common::CallException(Common::ObjectAgent::versionError("leave.AgentGateway.Gateway"));
            }
            Common::IputStreamPtr __iput;
            Common::CallError __error;
            int __rslt = __agent->ex_sync("leave.AgentGateway.Gateway",__oput,__iput,__params,__error);
            if (__rslt == -1)
                throw Common::CallException(__error);
            if (__rslt>>16)
            {
                Assert((__rslt>>16) == VersionException);
                __loop ++;
                if (__loop >= 3)
                    throw Common::CallException(Common::ObjectAgent::versionError("leave.AgentGateway.Gateway"));
                continue;
            }
            bool __ret;
            switch (__rslt)
            {
            case 0:
                __iput->read(__ret);
                break;
            default:
                throw Common::CallException(Common::ObjectAgent::versionError("leave.AgentGateway.Gateway"));
            }
            Common::ObjectAgent::processFinal(__iput);
            return __ret;
        }
    }
    catch (const Common::CallException& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
    catch (const Common::Exception& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
}

void AgentGatewayAgent::leave_begin(const Common::AgentAsyncPtr& __async,const Common::String& roomDelegatorId,const Common::String& userId,const Common::String& instanceId,const Common::CallParamsPtr& __params,const Common::ObjectPtr& __userdata) const throw()
{
    class __AgentAsync : public Common::AgentAsync
    {
    public:
        __AgentAsync(const Common::ObjectAgentPtr& __agent,const Common::AgentAsyncPtr& __async,const Common::String& roomDelegatorId,const Common::String& userId,const Common::String& instanceId,const Common::CallParamsPtr& __params,const Common::ObjectPtr& __userdata)
            : x__agent(__agent),x__async(__async),x_roomDelegatorId(roomDelegatorId),x_userId(userId),x_instanceId(instanceId),x__params(__params),x__userdata(__userdata),x__loop(0) {}

        void start()
        {
            Common::AgentAsyncPtr pthis = this;
            try
            {
                Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
                Common::VerListPtr __vers = x__agent->verList("leave.AgentGateway.Gateway");
                short __ver = 0;
                if (__vers)
                {
                    __ver = __vers->ver(true);
                    if (__ver > 0) __ver = 0;
                }
                switch (__ver)
                {
                case 0:
                    __oput->write((short)1);
                    __oput->write((short)__ver);
                    __oput->write(x_roomDelegatorId);
                    __oput->write(x_userId);
                    __oput->write(x_instanceId);
                    break;
                default:
                    throw Common::CallException(Common::ObjectAgent::versionError("leave.AgentGateway.Gateway"));
                }
                x__agent->ex_async(this,"leave.AgentGateway.Gateway",__oput,x__params,0);
            }
            catch (const Common::CallException& ex)
            {
                Common::ObjectAgent::throwException(x__async,ex,x__userdata);
            }
            catch (const Common::Exception& ex)
            {
                Common::ObjectAgent::throwException(x__async,ex,x__userdata);
            }
        }
        void cmdResult(int __rslt,const Common::IputStreamPtr& __iput,const Common::ObjectPtr& __userdata)
        {
            if ((__rslt>>16)!=VersionException)
            {
                if (x__async) x__async->cmdResult(__rslt,__iput,x__userdata);
                return;
            }
            x__loop ++;
            if (x__loop >= 3)
            {
                if (x__async)
                    Common::ObjectAgent::throwException(x__async, Common::CallException(Common::ObjectAgent::versionError("leave.AgentGateway.Gateway")), x__userdata);
                return;
            }
            start();
        }
    private:
        Common::ObjectAgentPtr x__agent;
        Common::AgentAsyncPtr x__async;
        Common::String x_roomDelegatorId;
        Common::String x_userId;
        Common::String x_instanceId;
        Common::CallParamsPtr x__params;
        Common::ObjectPtr x__userdata;
        int x__loop;
    };
    (new __AgentAsync(__agent,__async,roomDelegatorId,userId,instanceId,__params,__userdata))->start();
}

bool AgentGatewayAgent::leave_end(int __rslt,const Common::IputStreamPtr& __iput) throw()
{
    try
    {
        Common::CallError __error;
        if (Common::ObjectAgent::processException(__rslt,__iput,__error))
            throw Common::CallException(__error);
        Assert((__rslt>>16) == 0);
        bool __ret;
        switch (__rslt)
        {
        case 0:
            __iput->read(__ret);
            break;
        default:
            throw Common::CallException(Common::ObjectAgent::versionError("leave.AgentGateway.Gateway"));
        }
        Common::ObjectAgent::processFinal(__iput);
        return __ret;
    }
    catch (const Common::CallException& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
    catch (const Common::Exception& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
}

bool AgentGatewayAgent::registerTestAgent(const Common::String& type,const Common::String& instanceId,const Common::String& apiUrl,Common::String& objectId,const Common::CallParamsPtr& __params) const throw()
{
    try
    {
        int __loop = 0;
        while (1)
        {
            Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
            Common::VerListPtr __vers = __agent->verList("registerTestAgent.AgentGateway.Gateway");
            short __ver = 0;
            if (__vers)
            {
                __ver = __vers->ver(true);
                if (__ver > 0) __ver = 0;
            }
            switch (__ver)
            {
            case 0:
                __oput->write((short)1);
                __oput->write((short)__ver);
                __oput->write(type);
                __oput->write(instanceId);
                __oput->write(apiUrl);
                break;
            default:
                throw Common::CallException(Common::ObjectAgent::versionError("registerTestAgent.AgentGateway.Gateway"));
            }
            Common::IputStreamPtr __iput;
            Common::CallError __error;
            int __rslt = __agent->ex_sync("registerTestAgent.AgentGateway.Gateway",__oput,__iput,__params,__error);
            if (__rslt == -1)
                throw Common::CallException(__error);
            if (__rslt>>16)
            {
                Assert((__rslt>>16) == VersionException);
                __loop ++;
                if (__loop >= 3)
                    throw Common::CallException(Common::ObjectAgent::versionError("registerTestAgent.AgentGateway.Gateway"));
                continue;
            }
            bool __ret;
            switch (__rslt)
            {
            case 0:
                __iput->read(__ret);
                __iput->read(objectId);
                break;
            default:
                throw Common::CallException(Common::ObjectAgent::versionError("registerTestAgent.AgentGateway.Gateway"));
            }
            Common::ObjectAgent::processFinal(__iput);
            return __ret;
        }
    }
    catch (const Common::CallException& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
    catch (const Common::Exception& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
}

void AgentGatewayAgent::registerTestAgent_begin(const Common::AgentAsyncPtr& __async,const Common::String& type,const Common::String& instanceId,const Common::String& apiUrl,const Common::CallParamsPtr& __params,const Common::ObjectPtr& __userdata) const throw()
{
    class __AgentAsync : public Common::AgentAsync
    {
    public:
        __AgentAsync(const Common::ObjectAgentPtr& __agent,const Common::AgentAsyncPtr& __async,const Common::String& type,const Common::String& instanceId,const Common::String& apiUrl,const Common::CallParamsPtr& __params,const Common::ObjectPtr& __userdata)
            : x__agent(__agent),x__async(__async),x_type(type),x_instanceId(instanceId),x_apiUrl(apiUrl),x__params(__params),x__userdata(__userdata),x__loop(0) {}

        void start()
        {
            Common::AgentAsyncPtr pthis = this;
            try
            {
                Common::OputStreamPtr __oput = Common::OputStream::create(Common::StreamData);
                Common::VerListPtr __vers = x__agent->verList("registerTestAgent.AgentGateway.Gateway");
                short __ver = 0;
                if (__vers)
                {
                    __ver = __vers->ver(true);
                    if (__ver > 0) __ver = 0;
                }
                switch (__ver)
                {
                case 0:
                    __oput->write((short)1);
                    __oput->write((short)__ver);
                    __oput->write(x_type);
                    __oput->write(x_instanceId);
                    __oput->write(x_apiUrl);
                    break;
                default:
                    throw Common::CallException(Common::ObjectAgent::versionError("registerTestAgent.AgentGateway.Gateway"));
                }
                x__agent->ex_async(this,"registerTestAgent.AgentGateway.Gateway",__oput,x__params,0);
            }
            catch (const Common::CallException& ex)
            {
                Common::ObjectAgent::throwException(x__async,ex,x__userdata);
            }
            catch (const Common::Exception& ex)
            {
                Common::ObjectAgent::throwException(x__async,ex,x__userdata);
            }
        }
        void cmdResult(int __rslt,const Common::IputStreamPtr& __iput,const Common::ObjectPtr& __userdata)
        {
            if ((__rslt>>16)!=VersionException)
            {
                if (x__async) x__async->cmdResult(__rslt,__iput,x__userdata);
                return;
            }
            x__loop ++;
            if (x__loop >= 3)
            {
                if (x__async)
                    Common::ObjectAgent::throwException(x__async, Common::CallException(Common::ObjectAgent::versionError("registerTestAgent.AgentGateway.Gateway")), x__userdata);
                return;
            }
            start();
        }
    private:
        Common::ObjectAgentPtr x__agent;
        Common::AgentAsyncPtr x__async;
        Common::String x_type;
        Common::String x_instanceId;
        Common::String x_apiUrl;
        Common::CallParamsPtr x__params;
        Common::ObjectPtr x__userdata;
        int x__loop;
    };
    (new __AgentAsync(__agent,__async,type,instanceId,apiUrl,__params,__userdata))->start();
}

bool AgentGatewayAgent::registerTestAgent_end(int __rslt,const Common::IputStreamPtr& __iput,Common::String& objectId) throw()
{
    try
    {
        Common::CallError __error;
        if (Common::ObjectAgent::processException(__rslt,__iput,__error))
            throw Common::CallException(__error);
        Assert((__rslt>>16) == 0);
        bool __ret;
        switch (__rslt)
        {
        case 0:
            __iput->read(__ret);
            __iput->read(objectId);
            break;
        default:
            throw Common::CallException(Common::ObjectAgent::versionError("registerTestAgent.AgentGateway.Gateway"));
        }
        Common::ObjectAgent::processFinal(__iput);
        return __ret;
    }
    catch (const Common::CallException& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
    catch (const Common::Exception& ex)
    {
        Common::ObjectAgent::processFinal(ex);
        return false;
    }
}

};//namespace: Gateway
