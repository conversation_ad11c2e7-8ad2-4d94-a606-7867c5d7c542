﻿//
// *****************************************************************************
// Copyright(c) 2017-2025 Juphoon System Software Co., LTD. All rights reserved.
// *****************************************************************************
//
// Auto generated from: AgentGateway.def
// Warning: do not edit this file.
//

#ifndef __Gateway_AgentGatewayPub_h
#define __Gateway_AgentGatewayPub_h

#include "Common/Common.h"
#include "Common/TypesPub.h"

namespace Gateway
{

class Msg
{
public:
    Msg();
    Msg(Common::Long,const Common::String&);

    bool operator<(const Msg&) const;
    bool operator==(const Msg&) const;
    bool operator!=(const Msg&__obj) const { return !operator==(__obj);}
    void __write(const Common::OputStreamPtr&) const;
    void __read(const Common::IputStreamPtr&);
    void __textWrite(const Common::OputStreamPtr&,const Common::String&) const;
    bool __textRead(const Common::IputStreamPtr&,const Common::String&,int = 0);
public:
    Common::Long sequence;
    Common::String data;
};
void __write_Msg(const Common::OputStreamPtr&,const Gateway::Msg&);
void __read_Msg(const Common::IputStreamPtr&,Gateway::Msg&);
void __textWrite_Msg(const Common::OputStreamPtr&,const Common::String&,const Gateway::Msg&);
bool __textRead_Msg(const Common::IputStreamPtr&,const Common::String&,Gateway::Msg&,int = 0);

class CoordinateIn
{
public:
    CoordinateIn();
    CoordinateIn(const Common::String&,const Common::String&,bool,const Common::StrStrMap&);

    bool operator<(const CoordinateIn&) const;
    bool operator==(const CoordinateIn&) const;
    bool operator!=(const CoordinateIn&__obj) const { return !operator==(__obj);}
    void __write(const Common::OputStreamPtr&) const;
    void __read(const Common::IputStreamPtr&);
    void __textWrite(const Common::OputStreamPtr&,const Common::String&) const;
    bool __textRead(const Common::IputStreamPtr&,const Common::String&,int = 0);
public:
    Common::String tid;
    //任务id
    Common::String sdp;
    //offerSdp
    bool isScreenShare;
    //是否屏幕共享
    Common::StrStrMap inExt;
};
void __write_CoordinateIn(const Common::OputStreamPtr&,const Gateway::CoordinateIn&);
void __read_CoordinateIn(const Common::IputStreamPtr&,Gateway::CoordinateIn&);
void __textWrite_CoordinateIn(const Common::OputStreamPtr&,const Common::String&,const Gateway::CoordinateIn&);
bool __textRead_CoordinateIn(const Common::IputStreamPtr&,const Common::String&,Gateway::CoordinateIn&,int = 0);

class CoordinateOut
{
public:
    CoordinateOut();
    CoordinateOut(const Common::String&,const Common::String&,const Common::StrStrMap&);

    bool operator<(const CoordinateOut&) const;
    bool operator==(const CoordinateOut&) const;
    bool operator!=(const CoordinateOut&__obj) const { return !operator==(__obj);}
    void __write(const Common::OputStreamPtr&) const;
    void __read(const Common::IputStreamPtr&);
    void __textWrite(const Common::OputStreamPtr&,const Common::String&) const;
    bool __textRead(const Common::IputStreamPtr&,const Common::String&,int = 0);
public:
    Common::String tid;
    //任务id
    Common::String sdp;
    //answer sdp
    Common::StrStrMap outExt;
};
void __write_CoordinateOut(const Common::OputStreamPtr&,const Gateway::CoordinateOut&);
void __read_CoordinateOut(const Common::IputStreamPtr&,Gateway::CoordinateOut&);
void __textWrite_CoordinateOut(const Common::OputStreamPtr&,const Common::String&,const Gateway::CoordinateOut&);
bool __textRead_CoordinateOut(const Common::IputStreamPtr&,const Common::String&,Gateway::CoordinateOut&,int = 0);

class CoordinateIn2
{
public:
    CoordinateIn2();
    CoordinateIn2(const Common::String&,const Common::String&,bool,int,const Common::StrStrMap&);

    bool operator<(const CoordinateIn2&) const;
    bool operator==(const CoordinateIn2&) const;
    bool operator!=(const CoordinateIn2&__obj) const { return !operator==(__obj);}
    void __write(const Common::OputStreamPtr&) const;
    void __read(const Common::IputStreamPtr&);
    void __textWrite(const Common::OputStreamPtr&,const Common::String&) const;
    bool __textRead(const Common::IputStreamPtr&,const Common::String&,int = 0);
public:
    Common::String tid;
    //任务id
    Common::String sdp;
    //offerSdp
    bool isScreenShare;
    //是否屏幕共享
    int streamId;
    //流id 推流时传-1则使用一路新的流
    Common::StrStrMap properties;
};
void __write_CoordinateIn2(const Common::OputStreamPtr&,const Gateway::CoordinateIn2&);
void __read_CoordinateIn2(const Common::IputStreamPtr&,Gateway::CoordinateIn2&);
void __textWrite_CoordinateIn2(const Common::OputStreamPtr&,const Common::String&,const Gateway::CoordinateIn2&);
bool __textRead_CoordinateIn2(const Common::IputStreamPtr&,const Common::String&,Gateway::CoordinateIn2&,int = 0);

class CoordinateOut2
{
public:
    CoordinateOut2();
    CoordinateOut2(const Common::String&,const Common::String&,const Common::String&,int,const Common::StrStrMap&);

    bool operator<(const CoordinateOut2&) const;
    bool operator==(const CoordinateOut2&) const;
    bool operator!=(const CoordinateOut2&__obj) const { return !operator==(__obj);}
    void __write(const Common::OputStreamPtr&) const;
    void __read(const Common::IputStreamPtr&);
    void __textWrite(const Common::OputStreamPtr&,const Common::String&) const;
    bool __textRead(const Common::IputStreamPtr&,const Common::String&,int = 0);
public:
    Common::String tid;
    //任务id
    Common::String sdp;
    //answer sdp
    Common::String streamUrl;
    //streamUrl
    int streamId;
    //回传streamId
    Common::StrStrMap properties;
};
void __write_CoordinateOut2(const Common::OputStreamPtr&,const Gateway::CoordinateOut2&);
void __read_CoordinateOut2(const Common::IputStreamPtr&,Gateway::CoordinateOut2&);
void __textWrite_CoordinateOut2(const Common::OputStreamPtr&,const Common::String&,const Gateway::CoordinateOut2&);
bool __textRead_CoordinateOut2(const Common::IputStreamPtr&,const Common::String&,Gateway::CoordinateOut2&,int = 0);

typedef vector<Gateway::Msg> MsgVec;
void __write_MsgVec(const Common::OputStreamPtr&,const Gateway::MsgVec&);
void __read_MsgVec(const Common::IputStreamPtr&,Gateway::MsgVec&);
void __textWrite_MsgVec(const Common::OputStreamPtr&,const Common::String&,const Gateway::MsgVec&);
bool __textRead_MsgVec(const Common::IputStreamPtr&,const Common::String&,Gateway::MsgVec&);

enum TestAgentType
{
    TestAgentSipDialer,
};
const char* TestAgentType_toString(Gateway::TestAgentType val);

};//namespace: Gateway

#endif //__Gateway_AgentGatewayPub_h
