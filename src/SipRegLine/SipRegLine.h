//
// *****************************************************************************
// Copyright (c) 2025 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//

#pragma once

#include "SipLine/SipLineBase.h"

namespace SipMpCall
{

using SipRegLineSessionListener = SipLineSessionBaseListener;
typedef Common::Handle<SipRegLineSessionListener> SipRegLineSessionListenerPtr;

using SipRegLineSession = SipLineSessionBase;
typedef Common::Handle<SipRegLineSession> SipRegLineSessionPtr;

class SipRegLineListner : virtual public Common::Shared
{
public:
    virtual SipRegLineSessionListenerPtr onCreateSession(const SipRegLineSessionPtr &session) = 0;
};

typedef Common::Handle<SipRegLineListner> SipRegLineListnerPtr;

class SipRegLine;
typedef Common::Handle<SipRegLine> SipRegLinePtr;

class SipRegLine : virtual public Common::Shared
{
public:
    static SipRegLinePtr create(const SipRegLineListnerPtr &listener, const Common::ApplicationPtr &app);

    virtual bool isReady(Common::String &reason) = 0;
    virtual void close() = 0;
    virtual void schd() = 0;
    virtual void updateConfigs() = 0;

    virtual bool isRegistered(const std::string &username) = 0;
    virtual SipRegLineSessionPtr SipCall(const SipRegLineSessionListenerPtr &listener, const std::string &sessId, const std::string &caller, const std::string &callee, const std::string &pcOfferSdp, const std::map<std::string, std::string> &extHdrs) = 0;
};

} // namespace SipMpCall