//
// *****************************************************************************
// Copyright (c) 2025 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//

#include "SipRegLineI.h"

namespace SipMpCall
{

SipRegLineConn::SipRegLineConn(SipRegLineConnManager *connManager, const Common::NetSenderPtr &sender, const ListenerPtr &listener)
    : _connManager(connManager)
    , _sender(sender)
    , _listener(listener)
{
    insert(_conn<PERSON>anager, SipRegLineConnManager::ObjectId(SipRegLineConnId, connectionInfo()));
}

void SipRegLineConn::setCallId(const std::string &callId)
{
    _connManager->setConnCallId(callId, this);
}

void SipRegLineConn::close()
{
    Common::NetSenderPtr sender = _sender;
    if (!sender)
        return;

    UTIL_LOGFMT_IFO("SipRegLine", "close connection %s", connectionInfo().c_str());

    sender->close();
    _sender = nullptr;
    _listener = nullptr;
}

bool SipRegLineConn::schd(SipRegLineConnServer *server)
{
    Common::NetSenderPtr sender = _sender;
    return sender && !sender->isClosed();
}

std::string SipRegLineConn::connectionInfo()
{
    if (_connInfo.empty())
        _connInfo = localHostPort().to_string() + "->" + remoteHostPort().to_string();
    return _connInfo;
}

std::string SipRegLineConn::callerUri(const std::string &number, enum SimpleSipSession::SipUriFormat format, const std::string &prefix)
{
    return SimpleSipSession::SipUri::formatUri(format, prefix, number, localAddress());
}

std::string SipRegLineConn::calleeUri(const std::string &number, enum SimpleSipSession::SipUriFormat format, const std::string &prefix)
{
    return SimpleSipSession::SipUri::formatUri(format, prefix, number, remoteAddress());
}

void SipRegLineConn::recv(const unsigned char *data, int dataLen)
{
    ListenerPtr listener = _listener;
    if (!listener)
        return;

    try
    {
        std::string messageStr(reinterpret_cast<const char*>(data), dataLen);
        SimpleSipSession::SipMessagePtr message = SimpleSipSession::SipMessage::create(messageStr);
        if (!message)
        {
            UTIL_LOGFMT_WRN("SipRegLine", "connection %s failed to parse SIP message", connectionInfo().c_str());
            return;
        }

        UTIL_LOGFMT_IFO("SipRegLine", "connection %s received SIP message: %s", connectionInfo().c_str(), message->data().c_str());
        listener->onRecv(message, this);
    }
    catch (const std::exception& e)
    {
        UTIL_LOGFMT_ERR("SipRegLine", "connection %s exception caught while processing SIP message %s", connectionInfo().c_str(), e.what());
    }
}

void SipRegLineConn::onSendMessage(const SimpleSipSession::SipMessagePtr &message)
{
    Common::NetSenderPtr sender = _sender;
    if (!sender)
    {
        UTIL_LOG_WRN("SipRegLine", "Sender is null, cannot send message");
        return;
    }

    std::string data = message->data();
    UTIL_LOGFMT_IFO("SipRegLine", "connection %s sending SIP message: %s", connectionInfo().c_str(), data.c_str());
    sender->send(reinterpret_cast<const unsigned char *>(data.c_str()), data.length());
}

std::string SipRegLineConn::protocol()
{
    return "udp";
}

SimpleSipSession::SipHostPort SipRegLineConn::localHostPort()
{
    if (!_localHostPort.port)
    {
        Common::NetSenderPtr sender = _sender;
        if (sender)
        {
            Common::String host;
            int port;
            if (sender->getLocal(host, port))
            {
                _localHostPort.host = host.c_str();
                _localHostPort.port = port;
            }
        }
    }

    return _localHostPort;
}

SimpleSipSession::SipHostPort SipRegLineConn::remoteHostPort()
{
    if (!_remoteHostPort.port)
    {
        Common::NetSenderPtr sender = _sender;
        if (sender)
        {
            Common::String host;
            int port;
            if (sender->getRemote(host, port))
            {
                _remoteHostPort.host = host.c_str();
                _remoteHostPort.port = port;
            }
        }
    }

    return _remoteHostPort;
}

SipRegLineConnServer::SipRegLineConnServer(const ListenerPtr &listener)
    : _listener(listener)
{
}

bool SipRegLineConnServer::open(const Common::ApplicationPtr &app)
{
    Common::String host = "0.0.0.0";
    int port = 0;
    app->getAppConfig("SipRegServer.ListenHost", host);
    port = app->getAppConfigAsInt("SipRegServer.ListenPort");

    _sender = app->getDriver()->listen("udp", host, port, this);
    if (!_sender)
    {
        UTIL_LOGFMT_WRN("SipRegLine", "content:failed to open connection server on %s:%d", host.c_str(), port);
        return false;
    }

    _sender->getLocal(host, port);
    app->setStatistics("SipRegServer.ListenAddress", host + ":" + Common::String(port));

    UTIL_LOGFMT_IFO("SipRegLine", "content:open server on %s:%d", host.c_str(), port);
    return true;
}

void SipRegLineConnServer::close()
{
    UTIL_LOG_IFO("SipRegLine", "close connection server");

    _connManager.reset();

    if (_sender)
    {
        _sender->close();
        _sender = 0;
    }
    _listener = 0;
}

void SipRegLineConnServer::schd()
{
    _connManager.schd(this);
}

SipRegLineConnPtr SipRegLineConnServer::getConnByCallId(const std::string &callId)
{
    return _connManager.getConnByCallId(callId);
}

Common::NetReceiverPtr SipRegLineConnServer::recvConnection(const Common::NetSenderPtr &sender)
{
    if (!_listener)
        return nullptr;

    SipRegLineConnPtr conn = new SipRegLineConn(&_connManager, sender, this);
    return conn;
}

void SipRegLineConnServer::onRecv(const SimpleSipSession::SipMessagePtr &message, const SipRegLineConnPtr &conn)
{
    ListenerPtr listener = _listener;
    if (listener)
        listener->onRecv(message, conn);
}

SipRegLineSessionI::SipRegLineSessionI(SipRegLineSessionManager *sessionManager)
    : _termed(false)
    , _sessionManager(sessionManager)
{
}

SipRegLineSessionI::SipRegLineSessionI(SipRegLineSessionManager *sessionManager, const SipClient::SipCallSessionPtr &session)
    : _termed(false)
    , _sessionManager(sessionManager)
    , _session(session)
{
}

bool SipRegLineSessionI::open(const Common::ApplicationPtr &app, const SipRegLineConnPtr &conn, const SipRegLineSessionListenerPtr &listener, const SipClient::SipCallSessionPtr &session)
{
    std::lock_guard<std::mutex> lock(_mutex);

    _conn = conn;
    if (session)
        _session = session;
    _listener = listener;

    return true;
}

void SipRegLineSessionI::close()
{
    std::string sessId;
    std::string callId;

    do
    {
        std::lock_guard<std::mutex> lock(_mutex);
        _session = 0;
        _listener = 0;
        sessId = _sessId;
        callId = _callId;
    } while (0);

    UTIL_LOG_IFO_BEGIN
        UTIL_LOG("SipRegLine", "content:close sessId:" + Common::String(sessId.c_str()) + " callId:" + callId.c_str());
    UTIL_LOG_END
}

std::string SipRegLineSessionI::getSessId() const
{
    std::lock_guard<std::mutex> lock(_mutex);
    return _sessId;
}

void SipRegLineSessionI::setCallId(const std::string &callId)
{
    std::lock_guard<std::mutex> lock(_mutex);
    _callId = callId;
    _sessionManager->insert(SipRegLineSessionManager::ObjectId(SipRegLineSessionIdType::SipRegLineSessionCallId, callId), this);
}

void SipRegLineSessionI::setSessId(const std::string &sessId)
{
    std::lock_guard<std::mutex> lock(_mutex);
    _sessId = sessId;
    _sessionManager->insert(SipRegLineSessionManager::ObjectId(SipRegLineSessionIdType::SipRegLineSessionId, sessId), this);
}

std::string SipRegLineSessionI::getCallId() const
{
    std::lock_guard<std::mutex> lock(_mutex);
    return _callId;
}

bool SipRegLineSessionI::sendMessage(const SimpleSipSession::SipMessagePtr &message)
{
    std::lock_guard<std::mutex> lock(_mutex);
    if (!_conn)
    {
        UTIL_LOG_WRN("SipRegLine", "content:connection is null, cannot send message");
        return false;
    }

    _conn->onSendMessage(message);
    return true;
}

#define CHECK_INVOKE_VOID(_obj, _func, _log)         \
    auto __obj = _obj;                               \
    if (!__obj)                                      \
    {                                                \
        UTIL_LOG_DBG("SipRegLine", "content:" _log); \
        return;                                      \
    }                                                \
    __obj->_func

#define CHECK_CURRENT_SESSION(__session, _log)                          \
    if (_session != __session)                                          \
    {                                                                   \
        __session->SipTerm();                                           \
        UTIL_LOG_DBG("SipRegLine", "content:skip session event " _log); \
        return;                                                         \
    }

bool SipRegLineSessionI::SipSetContactUri(const std::string &pcContactUri)
{
    CHECK_INVOKE(_session, SipSetContactUri(pcContactUri), false, "session is null");
}

bool SipRegLineSessionI::SipAlert(const std::string &pcAnswerSdp)
{
    CHECK_INVOKE(_session, SipAlert(pcAnswerSdp), false, "session is null");
}

bool SipRegLineSessionI::SipAnswer(const std::string &pcAnswerSdp, const SipClient::SipCallExtHdrs &pcExtHdrs)
{
    CHECK_INVOKE(_session, SipAnswer(pcAnswerSdp, pcExtHdrs), false, "session is null");
}

bool SipRegLineSessionI::SipTerm()
{
    _listener = 0;
    auto session = _session;
    if (!session || !session->SipTerm())
    {
        if (!session)
            UTIL_LOG_WRN("SipRegLine", "content:sip term session is null");
        _termed = true;
        return false;
    }

    return true;
}

void SipRegLineSessionI::onCallIncoming(const SipClient::SipCallSessionPtr &session, const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg)
{
    _session = session;
    SimpleSipSession::SipMessagePtr msg = SimpleSipSession::SipMessage::create(pcSipMsg);
    setCallId(msg->getHeader(SimpleSipSession::HeaderCName::CALL_ID));
    CHECK_INVOKE_VOID(_listener, onCallIncoming(pcSdp, iStatCode, bSuptVideo, pcSipMsg), "listener is null");
}

void SipRegLineSessionI::onCallOutgoing(const SipClient::SipCallSessionPtr &session, const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg)
{
    CHECK_CURRENT_SESSION(session, "outgoing");
    CHECK_INVOKE_VOID(_listener, onCallOutgoing(pcSdp, iStatCode, bSuptVideo, pcSipMsg), "listener is null");
}

void SipRegLineSessionI::onCallAlerted(const SipClient::SipCallSessionPtr &session, const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg)
{
    CHECK_CURRENT_SESSION(session, "alerted");
    CHECK_INVOKE_VOID(_listener, onCallAlerted(pcSdp, iStatCode, bSuptVideo, pcSipMsg), "listener is null");
}

void SipRegLineSessionI::onCallAnswered(const SipClient::SipCallSessionPtr &session, const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg)
{
    CHECK_CURRENT_SESSION(session, "answered");
    CHECK_INVOKE_VOID(_listener, onCallAnswered(pcSdp, iStatCode, bSuptVideo, pcSipMsg), "listener is null");
}

void SipRegLineSessionI::onCallUpdate(const SipClient::SipCallSessionPtr &session, const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg)
{
    CHECK_CURRENT_SESSION(session, "update");
    CHECK_INVOKE_VOID(_listener, onCallUpdate(pcSdp, iStatCode, bSuptVideo, pcSipMsg), "listener is null");
}

void SipRegLineSessionI::onCallRequestUpdate(const SipClient::SipCallSessionPtr &session, const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg)
{
    CHECK_CURRENT_SESSION(session, "request update");
    CHECK_INVOKE_VOID(_listener, onCallRequestUpdate(pcSdp, iStatCode, bSuptVideo, pcSipMsg), "listener is null");
}

void SipRegLineSessionI::onCallResponseUdate(const SipClient::SipCallSessionPtr &session, const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg)
{
    CHECK_CURRENT_SESSION(session, "response update");
    CHECK_INVOKE_VOID(_listener, onCallResponseUdate(pcSdp, iStatCode, bSuptVideo, pcSipMsg), "listener is null");
}

void SipRegLineSessionI::onCallConnected(const SipClient::SipCallSessionPtr &session, const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg)
{
    CHECK_CURRENT_SESSION(session, "connected");
    CHECK_INVOKE_VOID(_listener, onCallConnected(pcSdp, iStatCode, bSuptVideo, pcSipMsg), "listener is null");
}

void SipRegLineSessionI::onCallRequestModify(const SipClient::SipCallSessionPtr &session, const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg)
{
    CHECK_CURRENT_SESSION(session, "request modify");
    CHECK_INVOKE_VOID(_listener, onCallRequestModify(pcSdp, iStatCode, bSuptVideo, pcSipMsg), "listener is null");
}

void SipRegLineSessionI::onCallResponseModify(const SipClient::SipCallSessionPtr &session, const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg)
{
    CHECK_CURRENT_SESSION(session, "response modify");
    CHECK_INVOKE_VOID(_listener, onCallResponseModify(pcSdp, iStatCode, bSuptVideo, pcSipMsg), "listener is null");
}

void SipRegLineSessionI::onCallTerminated(const SipClient::SipCallSessionPtr &session, const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg)
{
    do
    {
        std::lock_guard<std::mutex> lock(_mutex);
        _termed = true;
    } while (0);

    CHECK_INVOKE_VOID(_listener, onCallTerminated(pcSdp, iStatCode, bSuptVideo, pcSipMsg), "listener is null");
    _listener = 0;
}

SipRegLineI::SipRegLineI(const SipRegLineListnerPtr &listener, const Common::ApplicationPtr &app)
    : _listener(listener)
    , _app(app)
{
}

bool SipRegLineI::init()
{
    _connServer = new SipRegLineConnServer(this);
    if (!_connServer || !_connServer->open(_app))
    {
        UTIL_LOG_ERR("SipRegLine", "content:failed to create connection server");
        return false;
    }

    _sipDriver = SimpleSipSession::SipDriver::create();
    if (!_sipDriver)
    {
        UTIL_LOG_ERR("SipRegLine", "content:failed to create SIP driver");
        _connServer->close();
        _connServer = 0;
        return false;
    }

    _registration = SimpleSipSession::ServerRegistration::create(this, _sipDriver);
    if (!_registration)
    {
        UTIL_LOG_ERR("SipRegLine", "content:failed to create server registration");
        _sipDriver->close();
        _sipDriver = 0;
        _connServer->close();
        _connServer = 0;
        return false;
    }

    SipClient::SipCallInterfacePtr sipCall = SipClient::SipCallInterface::getInstance();
    if (!sipCall || !sipCall->addListener(this))
    {
        UTIL_LOG_ERR("SipRegLine", "content:failed to get SIP call interface");
        _registration->close();
        _registration = 0;
        _sipDriver->close();
        _sipDriver = 0;
        _connServer->close();
        _connServer = 0;
        return false;
    }

    UTIL_LOG_IFO("SipRegLine", "content:initialized successfully");
    return true;
}

bool SipRegLineI::isReady(Common::String &reason)
{
    if (!_listener)
    {
        reason = "No listener configured";
        return false;
    }

    if (!_app)
    {
        reason = "No application configured";
        return false;
    }

    return true;
}

void SipRegLineI::close()
{
    _sessionManager.reset();

    if (_registration)
    {
        _registration->close();
        _registration = 0;
    }

    if (_sipDriver)
    {
        _sipDriver->close();
        _sipDriver = 0;
    }

    if (_connServer)
    {
        _connServer->close();
        _connServer = 0;
    }
}

void SipRegLineI::schd()
{
    _sessionManager.schd(this);
}

bool SipRegLineI::isRegistered(const std::string &username)
{
    if (!_registration)
        return false;

    auto connection = _registration->getConnection(username);
    return connection != nullptr;
}

SipRegLineSessionPtr SipRegLineI::SipCall(const SipRegLineSessionListenerPtr &listener, const std::string &sessId, const std::string &caller, const std::string &callee, const std::string &pcOfferSdp, const std::map<std::string, std::string> &extHdrs)
{
    auto sipCall = SipClient::SipCallInterface::getInstance();
    if (!_listener || !listener || !sipCall)
        return nullptr;

    auto conn = SipRegLineConnPtr::dynamicCast(_registration->getConnection(callee));
    if (!conn)
    {
        UTIL_LOGFMT_DBG("SipRegLine", "content:connection not found for callee:%s", callee.c_str());
        return nullptr;
    }

    std::string callId = SimpleSipSession::SipMessage::genCallId(sessId, caller, callee, conn->localAddress());
    std::string callerUri = conn->callerUri(caller);
    std::string calleeUri = conn->calleeUri(callee);
    std::string contactUri = SimpleSipSession::SipUri::formatUri(SimpleSipSession::SipUri, "", caller, conn->localAddress());

    SipRegLineSessionIPtr sipLineSession;
    try
    {
        sipLineSession = new SipRegLineSessionI(&_sessionManager);
    }
    catch (const std::exception &e)
    {
        UTIL_LOG_ERR("SipRegLine", "content:create sip line session failed, reason:" + Common::String(e.what()));
        return nullptr;
    }

    SipClient::SipCallSessionPtr session = sipCall->SipCall(sipLineSession, callerUri, calleeUri, callId, contactUri, pcOfferSdp, "", {});
    if (!sipLineSession->open(_app, conn, listener, session))
    {
        if (session)
            session->SipTerm();
        UTIL_LOG_WRN("SipLine", "content:sip call failed, callerUri:" + Common::String(callerUri.c_str()) + " calleeUri:" + calleeUri.c_str());
        return nullptr;
    }

    sipLineSession->setSessId(sessId);
    sipLineSession->setCallId(callId);
    conn->setCallId(callId);
    UTIL_LOG_IFO("SipRegLine", "content:sip call callId:" + Common::String(callId.c_str()) + " sessId:" + sessId.c_str() + " callerUri:" + Common::String(callerUri.c_str()) + " calleeUri:" + calleeUri.c_str());
    return sipLineSession;
}

void SipRegLineI::onRecv(const SimpleSipSession::SipMessagePtr &message, const SipRegLineConnPtr &conn)
{
    SimpleSipSession::ServerRegistrationPtr registration = _registration;
    SipClient::SipCallInterfacePtr sipCall = SipClient::SipCallInterface::getInstance();

    if (!message || !registration || !sipCall)
        return;

    std::string method;
    if (!message->getMethod(method))
        return;

    if (method == "REGISTER")
    {
        if (!registration->recv(message, conn))
            UTIL_LOGFMT_WRN("SipRegLine", "content:failed to process REGISTER message %s", conn->connectionInfo().c_str());
        else
            UTIL_LOGFMT_DBG("SipRegLine", "content:processed REGISTER message %s", conn->connectionInfo().c_str());

        return;
    }

    if (!sipCall->RecvSipMsg(message->data()))
    {
        UTIL_LOGFMT_WRN("SipRegLine", "content:failed to process received message %s %s", method.c_str(), conn->connectionInfo().c_str());
    }
    else
    {
        std::string callId = message->getHeader(SimpleSipSession::HeaderCName::CALL_ID);
        UTIL_LOGFMT_DBG("SipRegLine", "content:processed received message %s %s callId:%s", method.c_str(), conn->connectionInfo().c_str(), callId.c_str());
        conn->setCallId(callId);
    }
}

bool SipRegLineI::onSend(const std::string &message)
{
    auto sipMsg = SimpleSipSession::SipMessage::create(message);
    if (!sipMsg)
    {
        UTIL_LOG_WRN("SipRegLine", "content:failed to create SIP message from string");
        return false;
    }

    std::string callId = sipMsg->getHeader(SimpleSipSession::HeaderCName::CALL_ID);
    auto session = _sessionManager.getSessByCallId(callId);
    if (!session)
    {
        UTIL_LOGFMT_DBG("SipRegLine", "content:session not found for call ID %s", callId.c_str());
        auto conn = _connServer->getConnByCallId(callId);
        if (!conn)
            return false;
        conn->onSendMessage(sipMsg);
        return true;
    }

    session->sendMessage(sipMsg);
    return true;
}

SipClient::SipCallSessionListenerPtr SipRegLineI::onCreateSession(const SipClient::SipCallSessionPtr &session)
{
    auto listener = _listener;
    auto registration = _registration;
    if (!listener || !registration)
    {
        UTIL_LOG_WRN("SipRegLine", "content:listener or registration is null, cannot create session");
        return nullptr;
    }

    std::string dsipname, uri;
    session->GetPeerUri(dsipname, uri);
    auto conn = SipMpCall::SipRegLineConnPtr::dynamicCast(registration->getConnection(uri));
    if (!conn)
    {
        UTIL_LOGFMT_DBG("SipRegLine", "content:connection not found for URI %s", uri.c_str());
        return nullptr;
    }

    try
    {
        auto lineSession = new SipRegLineSessionI(&_sessionManager, session);
        auto sessionListener = listener->onCreateSession(lineSession);
        if (!sessionListener)
        {
            UTIL_LOG_WRN("SipRegLine", "content:onCreateSession no session listener");
            return nullptr;
        }

        if (!lineSession->open(_app, conn, sessionListener))
        {
            UTIL_LOG_WRN("SipRegLine", "content:onCreateSession failed to open session");
            return nullptr;
        }

        return lineSession;
    }
    catch (const std::exception &e)
    {
        UTIL_LOG_ERR("SipRegLine", "content:failed to create line session, reason:" + Common::String(e.what()));
    }

    return nullptr;
}

SipRegLinePtr SipRegLine::create(const SipRegLineListnerPtr &listener, const Common::ApplicationPtr &app)
{
    if (!listener || !app)
    {
        UTIL_LOG_WRN("SipRegLine", "content:create failed, listener or app is null");
        return nullptr;
    }

    try
    {
        auto sipline = new SipRegLineI(listener, app);
        if (!sipline->init())
            return nullptr;

        UTIL_LOG_IFO("SipRegLine", "content:create sipline success");
        return sipline;
    }
    catch (const std::exception &e)
    {
        UTIL_LOG_ERR("SipRegLine", "content:create failed, reason:" + Common::String(e.what()));
        return nullptr;
    }
}

} // namespace SipMpCall