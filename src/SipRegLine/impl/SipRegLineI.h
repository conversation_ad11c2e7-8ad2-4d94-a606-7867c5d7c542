//
// *****************************************************************************
// Copyright (c) 2025 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//

#pragma once

#include "Common/Common.h"
#include "SipAdapter/SipCallInterface.h"
#include "SipRegLine/SipRegLine.h"
#include "SimpleSipSession/SipServerRegistration.h"
#include "SimpleSipSession/SipMessage.h"
#include "SimpleSipSession/SipUri.h"
#include "Util/SessionManager.h"
#include "Util/TimeExpirationCache.h"

#define CHECK_INVOKE(_obj, _func, _defret, _log)     \
    auto __obj = _obj;                               \
    if (!__obj)                                      \
    {                                                \
        UTIL_LOG_WRN("SipRegLine", "content:" _log); \
        return _defret;                              \
    }                                                \
    return __obj->_func
namespace SipMpCall
{

enum SipRegLineConnIdType
{
    SipRegLineConnId = 0,
    SipRegLineConnIdTypeSize
};

class SipRegLineConnServer;
class SipRegLineConn;
typedef Common::Handle<SipRegLineConn> SipRegLineConnPtr;

class SipRegLineConnManager : public SessionIndexManager<SipRegLineConnIdTypeSize, SipRegLineConnServer>
{
public:
    SipRegLineConnManager() : _callIdCache(3600) {}

    SipRegLineConnPtr getConnByCallId(const std::string &callId)
    {
        std::lock_guard<std::mutex> lock(_mutex);
        SipRegLineConnPtr conn;
        if (!_callIdCache.get(callId, conn))
            return nullptr;
        return conn;
    }

    void setConnCallId(const std::string &callId, const SipRegLineConnPtr &conn)
    {
        std::lock_guard<std::mutex> lock(_mutex);
        _callIdCache.put(callId, conn);
    }

private:
    TimeExpirationCache<std::string, SipRegLineConnPtr> _callIdCache;
};

class SipRegLineConn : public Common::NetReceiver, public SimpleSipSession::ServerRegistration::Connection, public SessionIndexManager<SipRegLineConnIdTypeSize, SipRegLineConnServer>::Session
{
public:
    class Listener : virtual public Common::Shared
    {
    public:
        virtual void onRecv(const SimpleSipSession::SipMessagePtr &message, const SipRegLineConnPtr &conn) = 0;
    };
    typedef Common::Handle<Listener> ListenerPtr;

public:
    SipRegLineConn(SipRegLineConnManager *connManager, const Common::NetSenderPtr &sender, const ListenerPtr &listener);

    void setCallId(const std::string &callId);
    void close() override;
    bool schd(SipRegLineConnServer *server) override;
    std::string connectionInfo();
    std::string localAddress() const { return _localHostPort.to_string(); }
    std::string remoteAddress() const { return _remoteHostPort.to_string(); }

    std::string callerUri(const std::string &number, enum SimpleSipSession::SipUriFormat format = SimpleSipSession::SipUri, const std::string &prefix = "");
    std::string calleeUri(const std::string &number, enum SimpleSipSession::SipUriFormat format = SimpleSipSession::SipUri, const std::string &prefix = "");

    // override Common::NetReceiver
    virtual void recv(const unsigned char *data, int dataLen) override;

    // override SimpleSipSession::ServerRegistration::Connection
    virtual void onSendMessage(const SimpleSipSession::SipMessagePtr &message) override;
    virtual std::string protocol() override;
    virtual SimpleSipSession::SipHostPort localHostPort() override;
    virtual SimpleSipSession::SipHostPort remoteHostPort() override;

private:
    SipRegLineConnManager *_connManager;
    Common::NetSenderPtr _sender;
    ListenerPtr _listener;
    SimpleSipSession::SipHostPort _localHostPort;
    SimpleSipSession::SipHostPort _remoteHostPort;
    std::string _connInfo;
};

class SipRegLineConnServer : public Common::NetReceiver, public SipRegLineConn::Listener
{
public:
    class Listener : virtual public Common::Shared
    {
    public:
        virtual void onRecv(const SimpleSipSession::SipMessagePtr &message, const SipRegLineConnPtr &conn) = 0;
    };
    typedef Common::Handle<Listener> ListenerPtr;

public:
    explicit SipRegLineConnServer(const ListenerPtr &listener);

    bool open(const Common::ApplicationPtr &app);
    void close();
    void schd();

    void setConnCallId(const std::string &callId, const SipRegLineConnPtr &conn);
    SipRegLineConnPtr getConnByCallId(const std::string &callId);

    // override Common::NetReceiver
    virtual Common::NetReceiverPtr recvConnection(const Common::NetSenderPtr &sender) override;

    // override SipRegLineConn::Listener
    virtual void onRecv(const SimpleSipSession::SipMessagePtr &message, const SipRegLineConnPtr &conn) override;

private:
    ListenerPtr _listener;
    Common::NetSenderPtr _sender;
    SipRegLineConnManager _connManager;
};

typedef Common::Handle<SipRegLineConnServer> SipRegLineConnServerPtr;

class SipRegLineI;
class SipRegLineSessionI;
typedef Common::Handle<SipRegLineSessionI> SipRegLineSessionIPtr;

enum SipRegLineSessionIdType
{
    SipRegLineSessionId = 0,
    SipRegLineSessionCallId,
    SipRegLineSessionIdTypeSize
};

class SipRegLineSessionManager : public SessionIndexManager<SipRegLineSessionIdTypeSize, SipRegLineI>
{
public:
    SipRegLineSessionIPtr getSess(const std::string &sessId) { return SipRegLineSessionIPtr::dynamicCast(getObject(ObjectId(SipRegLineSessionId, sessId))); }
    SipRegLineSessionIPtr getSessByCallId(const std::string &callId) { return SipRegLineSessionIPtr::dynamicCast(getObject(ObjectId(SipRegLineSessionCallId, callId))); }
};

class SipRegLineSessionI : virtual public SipRegLineSession, public SipClient::SipCallSessionListener, public SipRegLineSessionManager::Session
{
public:
    explicit SipRegLineSessionI(SipRegLineSessionManager *sessionManager);
    SipRegLineSessionI(SipRegLineSessionManager *sessionManager, const SipClient::SipCallSessionPtr &session);

    bool open(const Common::ApplicationPtr &app, const SipRegLineConnPtr &conn, const SipRegLineSessionListenerPtr &listener, const SipClient::SipCallSessionPtr &session = 0);
    void close() override;
    bool schd(SipRegLineI *line) override { return _termed; }

    std::string getSessId() const;
    void setCallId(const std::string &callId);
    bool sendMessage(const SimpleSipSession::SipMessagePtr &message);

    // override SipRegLineSession
    virtual void setSessId(const std::string &sessId) override;
    virtual std::string getCallId() const override;
    virtual bool SipSetContactUri(const std::string &pcContactUri) override;
    virtual bool SipAlert(const std::string &pcAnswerSdp) override;
    virtual bool SipAnswer(const std::string &pcAnswerSdp, const SipClient::SipCallExtHdrs &pcExtHdrs) override;
    virtual bool SipTerm() override;
    virtual bool SipUpdate(const std::string &pcOfferSdp) override { CHECK_INVOKE(_session, SipUpdate(pcOfferSdp), false, "session is null"); }
    virtual bool SipUpdateRsp(const std::string &pcAnswerSdp) override { CHECK_INVOKE(_session, SipUpdateRsp(pcAnswerSdp), false, "session is null"); }
    virtual bool SipAck(const std::string &pcAnswerSdp) override { CHECK_INVOKE(_session, SipAck(pcAnswerSdp), false, "session is null"); }
    virtual bool GetCalledUri(std::string &ppcDispName, std::string &ppcUri) override { CHECK_INVOKE(_session, GetCalledUri(ppcDispName, ppcUri), false, "session is null"); }
    virtual bool GetPeerUri(std::string &ppcDispName, std::string &ppcUri) override { CHECK_INVOKE(_session, GetPeerUri(ppcDispName, ppcUri), false, "session is null"); }
    virtual bool GetCallTermedReason(std::string &ppcSipPhase, std::string &ppcReason) override { CHECK_INVOKE(_session, GetCallTermedReason(ppcSipPhase, ppcReason), false, "session is null"); }

    // override SipClient::SipCallSessionListener
    void onCallIncoming(const SipClient::SipCallSessionPtr &session, const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) override;
    void onCallOutgoing(const SipClient::SipCallSessionPtr &session, const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) override;
    void onCallAlerted(const SipClient::SipCallSessionPtr &session, const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) override;
    void onCallAnswered(const SipClient::SipCallSessionPtr &session, const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) override;
    void onCallUpdate(const SipClient::SipCallSessionPtr &session, const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) override;
    void onCallRequestUpdate(const SipClient::SipCallSessionPtr &session, const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) override;
    void onCallResponseUdate(const SipClient::SipCallSessionPtr &session, const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) override;
    void onCallConnected(const SipClient::SipCallSessionPtr &session, const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) override;
    void onCallRequestModify(const SipClient::SipCallSessionPtr &session, const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) override;
    void onCallResponseModify(const SipClient::SipCallSessionPtr &session, const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) override;
    void onCallTerminated(const SipClient::SipCallSessionPtr &session, const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) override;

private:
    mutable std::mutex _mutex;
    std::string _sessId;
    std::string _callId;

    bool _termed;
    SipRegLineSessionManager *_sessionManager;
    SipRegLineSessionListenerPtr _listener;
    SipClient::SipCallSessionPtr _session;
    SipRegLineConnPtr _conn;
};

class SipRegLineI : public SipRegLine, public SipRegLineConnServer::Listener, public SimpleSipSession::ServerRegistration::Listener, public SipClient::SipCallListener
{
public:
    SipRegLineI(const SipRegLineListnerPtr &listener, const Common::ApplicationPtr &app);

    bool init();

    // override SipRegLine
    virtual bool isReady(Common::String &reason) override;
    virtual void close() override;
    virtual void schd() override;
    virtual void updateConfigs() override {}
    virtual bool isRegistered(const std::string &username) override;
    virtual SipRegLineSessionPtr SipCall(const SipRegLineSessionListenerPtr &listener, const std::string &sessId, const std::string &caller, const std::string &callee, const std::string &pcOfferSdp, const std::map<std::string, std::string> &extHdrs) override;

    // override SipRegLineConnServer::Listener
    virtual void onRecv(const SimpleSipSession::SipMessagePtr &message, const SipRegLineConnPtr &conn) override;

    // override SimpleSipSession::ServerRegistration::Listener
    virtual void onError(const Common::Error &error) override {}

    // override SipClient::SipCallListener
    virtual bool onSend(const std::string &message) override;
    virtual SipClient::SipCallSessionListenerPtr onCreateSession(const SipClient::SipCallSessionPtr &session) override;

private:
    bool sendByCallId(const SimpleSipSession::SipMessagePtr &message);

private:
    SipRegLineListnerPtr _listener;
    Common::ApplicationPtr _app;
    SimpleSipSession::SipDriverPtr _sipDriver;
    SimpleSipSession::ServerRegistrationPtr _registration;
    SipRegLineConnServerPtr _connServer;
    SipRegLineSessionManager _sessionManager;
};

} // namespace SipMpCall
