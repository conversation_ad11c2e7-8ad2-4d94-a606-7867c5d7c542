# library
aux_source_directory(impl SIP_REG_LINE_SRC)
add_library(SipRegLine
    ${SIP_REG_LINE_SRC}
)
target_include_directories(SipRegLine PRIVATE
    ${DEFAULT_INCLUDE_DIRECTORIES}
    ${PROJECT_SOURCE_DIR}/3rd/jssi/inc
)
target_compile_definitions(SipRegLine PRIVATE
    ${DEFAULT_COMPILE_DEFINITIONS}
)

# UnitTest
aux_source_directory(test SIP_LINE_TEST_SRC)
add_executable(SipRegLineUnitTest ${SIP_LINE_TEST_SRC})
target_include_directories(SipRegLineUnitTest PRIVATE
    ${DEFAULT_INCLUDE_DIRECTORIES}
    ${PROJECT_SOURCE_DIR}/3rd
)
target_compile_definitions(SipRegLineUnitTest PRIVATE
    ${DEFAULT_COMPILE_DEFINITIONS}
)

target_link_libraries(SipRegLineUnitTest SipRegLine SimpleSipSession SipAdapter Util JsmLog
    ${DEFAULT_SERVER_LIBRARIES}
    $ENV{JssSharedPath}/lib/${DEFAULT_LIB_PATH}/libgtest.a
    $ENV{JssSharedPath}/lib/${DEFAULT_LIB_PATH}/libgmock.a
    $ENV{JssSharedPath}/lib/${DEFAULT_LIB_PATH}/libgtest_main.a
    ${DEFAULT_SYS_LIBRARIES}
)
include(GoogleTest)
gtest_discover_tests(SipRegLineUnitTest)
