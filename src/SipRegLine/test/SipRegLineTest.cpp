//
// *****************************************************************************
// Copyright (c) 2025 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//

#include "SipAdapter/SipCallInterfaceMock.h"
#include "SipTestClient.h"

#include "gtest/gtest.h"
#include "Common/CommonEx.h"
#include "SipRegLine/SipRegLine.h"
#include "SipLine/SipLine.h"

#include <thread>
#include <chrono>
#include <map>
#include <string>

using ::testing::_;
using ::testing::Return;

namespace SipMpCall
{

// Simple test listener implementation
class TestSipRegLineListener : public SipRegLineListner
{
public:
    SipRegLineSessionListenerPtr onCreateSession(const SipRegLineSessionPtr &session) override
    {
        _lastCreatedSession = session;
        _sessionCreatedCount++;

        // Return a simple test session listener
        _testSessionListener = new TestSipRegLineSessionListener();
        return _testSessionListener;
    }

    SipRegLineSessionPtr _lastCreatedSession;
    int _sessionCreatedCount = 0;

public:
    class TestSipRegLineSessionListener : public SipLineSessionListener
    {
    public:
        void onCallIncoming(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) override { _incomingCallCount++; }
        void onCallOutgoing(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) override { _outgoingCallCount++; }
        void onCallAlerted(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) override {}
        void onCallAnswered(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) override {}
        void onCallUpdate(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) override {}
        void onCallRequestUpdate(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) override {}
        void onCallResponseUdate(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) override {}
        void onCallConnected(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) override {}
        void onCallRequestModify(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) override {}
        void onCallResponseModify(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) override {}
        void onCallTerminated(const std::string &pcSdp, unsigned int iStatCode, bool bSuptVideo, const std::string &pcSipMsg) override { _terminatedCallCount++; }

        int _incomingCallCount = 0;
        int _outgoingCallCount = 0;
        int _terminatedCallCount = 0;
    };

    Common::Handle<TestSipRegLineSessionListener> _testSessionListener;
};

class SipRegLineTest : public ::testing::Test
{
protected:
    void SetUp() override
    {
        // Initialize SipCallInterface mock injection
        _mockSipCall = new SipClient::SipCallInterfaceMock();
        SipClient::SipCallInterface::injectInstance(_mockSipCall);

        // Set up default mock expectations
        EXPECT_CALL(*_mockSipCall, addListener(::testing::_))
            .WillOnce([&](const SipClient::SipCallListenerPtr &listener) {
                // Store the listener for later use
                _sipCallListener = listener;
                return true;
            });

        // Initialize common configuration using simple map
        std::map<Common::String, Common::String> configs;
        configs["global.Log.Level"] = "3";
        configs["global.Log.Print"] = "1";
        configs["global.SipRegServer.ListenHost"] = "127.0.0.1";
        configs["global.SipRegServer.ListenPort"] = "15060";

        // Create application instance
        _app = Common::ApplicationEx::create("SipRegLineTest", "", 0, configs);
        ASSERT_TRUE(_app);
        ASSERT_TRUE(_app->activate());

        // Create test listener
        _testListener = new TestSipRegLineListener();

        // Create SipRegLine instance
        _sipRegLine = SipRegLine::create(_testListener, _app);
        ASSERT_TRUE(_sipRegLine);

        // Verify ready status
        Common::String reason;
        ASSERT_TRUE(_sipRegLine->isReady(reason));

        // Give some time for initialization
        std::this_thread::sleep_for(std::chrono::milliseconds(100));

        _testClient = new SipTestRegistrationClient("testcallee", "testcallee", "", "test.com");
        ASSERT_TRUE(_testClient->open(_app->getDriver(), "127.0.0.1", 15060));
        std::this_thread::sleep_for(std::chrono::milliseconds(1000));
        ASSERT_TRUE(_testClient->isReady());
    }

    void TearDown() override
    {
        if (_testClient)
        {
            _testClient->close();
            _testClient = nullptr;
        }

        if (_sipRegLine)
        {
            _sipRegLine->close();
            _sipRegLine = nullptr;
        }

        if (_app)
        {
            _app->indShutdown();
            while (!_app->isShutdown())
            {
                std::this_thread::sleep_for(std::chrono::milliseconds(10));
            }
            _app = nullptr;
        }

        _testListener = nullptr;
        SipClient::SipCallInterface::destroyInstance();
    }

protected:
    Common::ApplicationExPtr _app;
    SipRegLinePtr _sipRegLine;
    Common::Handle<TestSipRegLineListener> _testListener;
    SipClient::SipCallInterfaceMockPtr _mockSipCall;
    SipClient::SipCallListenerPtr _sipCallListener;
    SipTestRegistrationClientPtr _testClient;
};

// Test outgoing call flow (主叫流程测试)
TEST_F(SipRegLineTest, OutgoingCallFlow)
{
    auto sessionListener = new TestSipRegLineListener::TestSipRegLineSessionListener();
    std::string inviteMessage, byeMessage;

    // Create mock session for SipCall response
    SipClient::SipCallSessionMockPtr mockSession = new SipClient::SipCallSessionMock();

    // Set up mock expectations for outgoing call
    EXPECT_CALL(*_mockSipCall, SipCall(_, _, _, _, _, _, _, _))
        .WillOnce([&](const SipClient::SipCallSessionListenerPtr &listener,
                      const std::string &pcCallerUri,
                      const std::string &pcCalleeUri,
                      const std::string &pcCallId,
                      const std::string &pcContactUri,
                      const std::string &pcOfferSdp,
                      const std::string &pcUriParms,
                      const SipClient::SipCallExtHdrs &pcExtHdrs) -> SipClient::SipCallSessionPtr {
            inviteMessage = "INVITE sip:" + pcCalleeUri + " SIP/2.0\r\n"
                "CSeq: 1 INVITE\r\n"
                "Call-ID: " + pcCallId + "\r\n"
                "From: " + pcCallerUri + "\r\n"
                "To: " + pcCalleeUri + "\r\n"
                "Contact: " + pcContactUri + "\r\n"
                "Content-Type: application/sdp\r\n"
                "Content-Length: " + std::to_string(pcOfferSdp.length()) + "\r\n\r\n" +
                pcOfferSdp;
            byeMessage = "BYE sip:" + pcCalleeUri + " SIP/2.0\r\n"
                "CSeq: 2 BYE\r\n"
                "Call-ID: " + pcCallId + "\r\n"
                "From: " + pcCallerUri + "\r\n"
                "To: " + pcCalleeUri + "\r\n"
                "Content-Length: 0\r\n\r\n";
            return mockSession;
        });

    EXPECT_CALL(*_mockSipCall, RecvSipMsg(_)) .Times(2).WillRepeatedly([&](const std::string &message) {
            SimpleSipSession::SipMessagePtr sipMsg = SimpleSipSession::SipMessage::create(message);
            EXPECT_FALSE(sipMsg->isRequest());
            std::string method;
            sipMsg->getMethod(method);
            EXPECT_TRUE(method == "INVITE" || method == "BYE");
            return true;
        });

    EXPECT_CALL(_testClient->processor, process(_, _)).Times(2).WillRepeatedly([](SimpleSipSession::SipMessagePtr msg, const Common::NetSenderPtr &sender) {
        SimpleSipSession::SipMessagePtr rsp;
        std::string method;
        msg->getMethod(method);
        if (method == "INVITE")
            rsp = SimpleSipSession::SipMessage::genResponse(msg, 100, "Trying");
        else if (method == "BYE")
            rsp = SimpleSipSession::SipMessage::genResponse(msg, 200, "OK");
        return rsp;
    });
            // Simulate sending the SIP message
    // Set up mock session expectations
    EXPECT_CALL(*mockSession, sessId()).WillRepeatedly(Return(12345));
    EXPECT_CALL(*mockSession, SipTerm()).WillRepeatedly(Return(true));
    EXPECT_CALL(*mockSession, GetCalledUri(_, _)).WillRepeatedly([](std::string &dispName, std::string &uri) {
        dispName = "Test Callee";
        uri = "sip:testcallee@127.0.0.1";
        return true;
    });
    EXPECT_CALL(*mockSession, GetPeerUri(_, _)).WillRepeatedly([](std::string &dispName, std::string &uri) {
        dispName = "Test Caller";
        uri = "sip:testcaller@127.0.0.1";
        return true;
    });

    // Test parameters for outgoing call
    std::string sessId = "test-session-001";
    std::string caller = "testcaller";
    std::string callee = "testcallee";
    std::string offerSdp = "v=0\r\no=alice 2890844526 2890844527 IN IP4 host.atlanta.com\r\n";

    // Execute SipCall (主叫发起)
    // 关键动作1: 查找目标用户连接 (通过ServerRegistration)
    // 关键动作2: 指定Call-ID发起呼叫 (调用SipCallInterface)
    // 关键动作3: 建立CallId与Conn映射关系
    SipRegLineSessionPtr session = _sipRegLine->SipCall(
        sessionListener,
        sessId,
        caller,
        callee,
        offerSdp,
        {}
    );

    // Verify session creation
    ASSERT_TRUE(session);

    // Verify session ID setting
    session->setSessId(sessId);

    // Verify call ID is generated
    std::string callId = session->getCallId();
    EXPECT_FALSE(callId.empty());

    // Test retrieving call information
    std::string calledDispName, calledUri;
    session->GetCalledUri(calledDispName, calledUri);
    EXPECT_STREQ(calledUri.c_str(), "sip:testcallee@127.0.0.1");

    std::string peerDispName, peerUri;
    session->GetPeerUri(peerDispName, peerUri);
    EXPECT_STREQ(peerUri.c_str(), "sip:testcaller@127.0.0.1");
    _sipCallListener->onSend(inviteMessage);
    std::this_thread::sleep_for(std::chrono::milliseconds(100));

    // Terminate the call
    EXPECT_TRUE(session->SipTerm());
    _sipCallListener->onSend(byeMessage);
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
}

// Test incoming call flow (被叫流程测试)
TEST_F(SipRegLineTest, IncomingCallFlow)
{
    SipClient::SipCallSessionListenerPtr sessionListener;
    SipClient::SipCallSessionMockPtr mockSession = new SipClient::SipCallSessionMock();
    SimpleSipSession::SipMessagePtr rsp100;

    EXPECT_CALL(*mockSession, GetPeerUri(_, _)).WillOnce([&](std::string &dispName, std::string &uri) {
        dispName = "Test Caller";
        uri = "sip:<EMAIL>";
        return true;
    });

    EXPECT_CALL(*_mockSipCall, RecvSipMsg(_)).Times(2).WillRepeatedly([&](const std::string &message) {
        SimpleSipSession::SipMessagePtr sipMsg = SimpleSipSession::SipMessage::create(message);
        EXPECT_TRUE(sipMsg->isRequest());
        std::string method;
        sipMsg->getMethod(method);
        if (method == "INVITE")
        {
            sessionListener = _sipCallListener->onCreateSession(mockSession);
            rsp100 = SimpleSipSession::SipMessage::genResponse(sipMsg, 100, "Trying");
            rsp100->encode();
        }
        else if (method == "BYE")
        {
            sessionListener->onCallTerminated(mockSession, "", 200, false, message);
            SimpleSipSession::SipMessagePtr response = SimpleSipSession::SipMessage::genResponse(sipMsg, 200, "OK");
            response->encode();
            _sipCallListener->onSend(response->data());
        }
        else
        {
            EXPECT_TRUE(false) << "Unexpected SIP method: " << method;
        }

        return true;
    });

    EXPECT_CALL(_testClient->processor, process(_, _)).Times(2).WillRepeatedly([](SimpleSipSession::SipMessagePtr msg, const Common::NetSenderPtr &sender) {
        std::string method;
        msg->getMethod(method);
        EXPECT_FALSE(msg->isRequest());
        EXPECT_TRUE(method == "INVITE" || method == "BYE");
        return SimpleSipSession::SipMessagePtr();
    });

    std::string inviteMessage, byeMessage, tryingResponse, okResponse;

    // Prepare incoming INVITE message from test client
    std::string callId = "incoming-call-12345";
    std::string fromUri = "sip:<EMAIL>";
    std::string toUri = "sip:<EMAIL>";
    std::string contactUri = "sip:<EMAIL>:5061";
    std::string offerSdp = "v=0\r\no=testcaller 2890844526 2890844527 IN IP4 test.com\r\n"
                           "s=Incoming Call Session\r\n"
                           "c=IN IP4 test.com\r\n"
                           "t=0 0\r\n"
                           "m=audio 5004 RTP/AVP 0\r\n"
                           "a=rtpmap:0 PCMU/8000\r\n";

    inviteMessage = "INVITE " + toUri + " SIP/2.0\r\n"
                    "Via: SIP/2.0/UDP test.com:5061;branch=z9hG4bK123456\r\n"
                    "Max-Forwards: 70\r\n"
                    "From: <" + fromUri + ">;tag=caller123\r\n"
                    "To: <" + toUri + ">\r\n"
                    "Call-ID: " + callId + "\r\n"
                    "CSeq: 1 INVITE\r\n"
                    "Contact: <" + contactUri + ">\r\n"
                    "Content-Type: application/sdp\r\n"
                    "Content-Length: " + std::to_string(offerSdp.length()) + "\r\n\r\n" +
                    offerSdp;

    byeMessage = "BYE " + toUri + " SIP/2.0\r\n"
                 "Via: SIP/2.0/UDP test.com:5061;branch=z9hG4bK789012\r\n"
                 "Max-Forwards: 70\r\n"
                 "From: <" + fromUri + ">;tag=caller123\r\n"
                 "To: <" + toUri + ">;tag=callee456\r\n"
                 "Call-ID: " + callId + "\r\n"
                 "CSeq: 2 BYE\r\n"
                 "Content-Length: 0\r\n\r\n";
    // Step 1: Test client sends INVITE message
    // 被叫流程关键动作：SipRegLineI 收到 onRecv 时将 CallId 和 Conn 进行绑定
    SimpleSipSession::SipMessagePtr inviteMsg = SimpleSipSession::SipMessage::create(inviteMessage);
    ASSERT_TRUE(inviteMsg);

    // Simulate _testClient sending INVITE using string data
    _testClient->send(inviteMessage);
    std::this_thread::sleep_for(std::chrono::milliseconds(200));
    sessionListener->onCallIncoming(mockSession, "", 0, false, inviteMessage);
    _sipCallListener->onSend(rsp100->data());

    // Verify that a session was created for the incoming call
    EXPECT_EQ(_testListener->_sessionCreatedCount, 1);
    ASSERT_TRUE(_testListener->_lastCreatedSession);

    // Verify onCallIncoming notification was received
    EXPECT_EQ(_testListener->_testSessionListener->_incomingCallCount, 1);

    // Verify session details
    SipRegLineSessionPtr incomingSession = _testListener->_lastCreatedSession;
    EXPECT_STREQ(incomingSession->getCallId().c_str(), callId.c_str());

    // Step 2: Test client sends BYE message
    SimpleSipSession::SipMessagePtr byeMsg = SimpleSipSession::SipMessage::create(byeMessage);
    ASSERT_TRUE(byeMsg);

    // Simulate _testClient sending BYE using string data
    _testClient->send(byeMessage);
    std::this_thread::sleep_for(std::chrono::milliseconds(200));

    // Verify onCallTerminated notification was received
    EXPECT_EQ(_testListener->_testSessionListener->_terminatedCallCount, 1);

    // Clean up
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
}

} // namespace SipMpCall
