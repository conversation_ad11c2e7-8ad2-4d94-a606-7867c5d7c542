//
// *****************************************************************************
// Copyright (c) 2025 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//

#pragma once

#include "gmock/gmock.h"
#include "SimpleSipSession/SipFlowRegistration.h"
#include "SimpleSipSession/SipMessage.h"
#include "Common/Net.h"
#include "SimpleSipSession/SipTransport.h"

class SipTestClientProcessor
{
public:
    virtual SimpleSipSession::SipMessagePtr process(SimpleSipSession::SipMessagePtr msg, const Common::NetSenderPtr &sender) = 0;
};

class SipTestClientProcessorMock : public SipTestClientProcessor
{
public:
    MOCK_METHOD(SimpleSipSession::SipMessagePtr, process, (SimpleSipSession::SipMessagePtr msg, const Common::NetSenderPtr &sender), (override));
};

class SipTestClient : public Common::NetReceiver
{
public:
    virtual bool open(const Common::NetDriverPtr &driver, const std::string &ip, int port)
    {
        _sender = driver->connect("udp", "", 0, ip.c_str(), port, this);
        return _sender != nullptr;
    }

    virtual void close()
    {
        _sender->close();
        _sender = 0;
    }

    virtual void recv(const unsigned char *data, int dataLen) override
    {
        SimpleSipSession::SipMessagePtr msg = SimpleSipSession::SipMessage::create(std::string((const char *)data, dataLen));
        if (!msg)
            return;

        if (onRecv(msg))
            return;

        msg = processor.process(msg, _sender);
        if (msg)
        {
            msg->encode();
            _sender->send((const unsigned char *)msg->data().c_str(), msg->data().size());
        }
    }

    virtual bool onRecv(const SimpleSipSession::SipMessagePtr &message) { return false; }

    virtual bool send(const std::string &data)
    {
        _sender->send((const unsigned char *)data.c_str(), data.size());
        return true;
    }

public:
    SipTestClientProcessorMock processor;

protected:
    Common::NetSenderPtr _sender;
};

typedef Common::Handle<SipTestClient> SipTestClientPtr;

class SipTestRegistrationClient : public SipTestClient, public SimpleSipSession::Registration::Listener, public SimpleSipSession::SipTransport
{
public:
    SipTestRegistrationClient(const std::string &_username, const std::string &_authname, const std::string &_password, const std::string &_realm, int _expireSeconds = 3600)
        : _config(_username, _authname, _password, _realm, _expireSeconds)
        , _ready(false)
    {
    }

    virtual bool open(const Common::NetDriverPtr &driver, const std::string &ip, int port) override
    {
        if (!SipTestClient::open(driver, ip, port))
            return false;

        _sipDriver = SimpleSipSession::SipDriver::create();
        _regFlow = SimpleSipSession::Registration::create(_sipDriver, this, this, _config);
        return true;
    }

    void close () override
    {
        if (_regFlow)
        {
            _regFlow->close();
            _regFlow = nullptr;
        }
        if (_sipDriver)
        {
            _sipDriver->close();
            _sipDriver = nullptr;
        }
        SipTestClient::close();
    }

    virtual bool onRecv(const SimpleSipSession::SipMessagePtr &message) override
    {
        if (_regFlow)
            return _regFlow->recvMessage(message);
        return false;
    }

    bool isReady() const { return _ready; }

    // Registration Listener interface
    virtual void onError() override {}
    virtual void onSendMessage(const SimpleSipSession::SipMessagePtr &message) override { send(message->data()); }
    virtual void onRegistered() override { _ready = true;}
    virtual void onRegisterFailed() override { _ready = false;}
    virtual void onUnregistered() override { _ready = false;}

    // SipTransport interface
    virtual std::string protocol() override { return "udp"; }
    virtual SimpleSipSession::SipHostPort localHostPort() override
    {
        Common::String host;
        int port;
        _sender->getLocal(host, port);
        return SimpleSipSession::SipHostPort(host.c_str(), port);
    }
    virtual SimpleSipSession::SipHostPort remoteHostPort() override
    {
        Common::String host;
        int port;
        _sender->getRemote(host, port);
        return SimpleSipSession::SipHostPort(host.c_str(), port);
    }

private:
    bool _ready;
    SimpleSipSession::SipDriverPtr _sipDriver;
    SimpleSipSession::SipFlowPtr _regFlow;
    SimpleSipSession::Registration::Config _config;
};

typedef Common::Handle<SipTestRegistrationClient> SipTestRegistrationClientPtr;
