//
// *****************************************************************************
// Copyright (c) 2023 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//
// Created on 2023/12/9 by <PERSON>
//

#pragma once

#include "SdpMessage.h"

namespace SimpleSipSession
{

class OfferAnswerModel
{
public:
    enum Option
    {
        OptionIpv4,
        OptionAudioPort,
        OptionAudioState,

        OptionVideoPort,
        OptionVideoState
    };

public:
    bool init(const std::string &sdp);

    bool setOption(enum Option option, const std::string &value);

    std::string genOffer();
    bool setAnswer(const std::string &answer);
    std::string genAnswer(const std::string &offer);

    bool getCodec(bool audio, std::string &codec, int &payloadType) const;
    bool getRemoteHostPort(bool audio, std::string &host, int &port) const;

private:
    SdpSess _localSdp;
    SdpSess _remoteSdp;
    SdpSess _negoedSdp;
};

} // namespace SimpleSipSession
