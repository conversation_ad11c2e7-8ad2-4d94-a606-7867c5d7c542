//
// *****************************************************************************
// Copyright (c) 2023 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//
// Created on 2023/12/23 by <PERSON>
//

#pragma once

#include "Common/Util.h"
#include "SimpleSipSession/SipDriver.h"
#include "SimpleSipSession/SipMessage.h"
#include "SimpleSipSession/trans/SipTransaction.h"

namespace SimpleSipSession
{

class SipFlowListener : virtual public Common::Shared
{
public:
    virtual void onError() = 0;
    virtual void onSendMessage(const SipMessagePtr &message) = 0;
};

typedef Common::Handle<SipFlowListener> SipFlowListenerPtr;

class SipFlow : virtual public Common::Shared
{
public:
    virtual void close() = 0;
    virtual bool recvMessage(const SipMessagePtr &message) = 0;
};

typedef Common::Handle<SipFlow> SipFlowPtr;

} // namespace SimpleSipSession
