INVITE ${REQUEST_URI} SIP/2.0
From: ${FROM_ADDR};tag=${FROM_PARAMS}
To: ${TO_ADDR}
Contact: <${LOCAL_CONTACT_URI}>
Allow: INVITE,ACK,BYE,CANCEL,UPDATE
Call-ID: ${CALL_ID}
CSeq: ${SEQ_NUMBER} INVITE
Max-Forwards: 70
User-Agent: MSP/1.0
Via: SIP/2.0/UDP ${LOCAL_PUBLIC_ADDR};branch=${VIA_BRANCH};rport
Content-Type: application/sdp
Content-Length: ${CONTENT_LENGTH}

${LOCAL_OFFER_SDP}
v=0
o=- ${LOCAL_SDP_SESSION} ${LOCAL_SDP_VERSION} IN IP4 ${LOCAL_RTP_IP}
s=-
c=IN IP4 ${LOCAL_RTP_IP}
t=0 0
m=audio ${LOCAL_RTP_AUDIO_PORT} RTP/AVP 0 8 101
a=rtpmap:0 PCMU/8000
a=rtpmap:8 PCMA/8000
a=ptime:20
a=rtpmap:101 telephone-event/8000
a=fmtp:101 0-15
a=sendrecv


SIP/2.0 200 OK
Via: SIP/2.0/UDP ${LOCAL_PUBLIC_ADDR};branch=${${VIA_BRANCH}};rport=${LOCAL_PORT}
From: "057156508425" <sip:057156508425@*************:7610>;tag=${FROM_PARAMS}
To: "13276828056" <sip:13276828056@*************:7610>;tag=${TO_PARAMS}
Call-ID: J1C_qG1B20000IcOlRc21eJF_a@${LOCAL_PUBLIC_IP}
CSeq: 1 INVITE
Contact: <sip:13276828056@*************:7610;transport=udp>
User-Agent: ClpMS-UA/****
Allow: INVITE, ACK, BYE, CANCEL, OPTIONS, MESSAGE, INFO, UPDATE, REGISTER, REFER, NOTIFY
Require: timer
Supported: timer, path, replaces
Allow-Events: talk, hold, conference, refer
Session-Expires: 1800;refresher=uac
Content-Type: application/sdp
Content-Disposition: session
Content-Length: 357
P-Charging-Vector: icid-value="fca16a56a3436102e108389865cd0117.3893209900.316110549.138";term-ioi=zj.ims.chinaunicom.cn
P-Asserted-Identity: "Outbound Call" <sip:fs999613276828056@*************>

v=0
o=ClpMS 997908387 997908388 IN IP4 *************
s=ClpMS
c=IN IP4 *************
b=AS:49
b=RS:600
b=RR:2000
t=0 0
m=audio 10174 RTP/AVP 104 109
b=AS:41
b=RS:600
b=RR:2000
a=rtpmap:104 AMR-WB/16000/1
a=fmtp:104 octet-align=1;mode-change-capability=2;max-red=0
a=rtpmap:109 telephone-event/16000
a=fmtp:109 0-15
a=maxptime:240
a=ptime:20


ACK ${REQUEST_URI} SIP/2.0
From: ${FROM_ADDR};tag=${FROM_PARAMS}
To: ${TO_ADDR};tag=${TO_PARAMS}
Contact: <${LOCAL_CONTACT_URI}>
Call-ID: ${CALL_ID}
CSeq: ${SEQ_NUMBER} ACK
Max-Forwards: 70
User-Agent: MSP/1.0
Via: SIP/2.0/UDP ${LOCAL_PUBLIC_ADDR};branch=${VIA_BRANCH};rport
Content-Length: 0