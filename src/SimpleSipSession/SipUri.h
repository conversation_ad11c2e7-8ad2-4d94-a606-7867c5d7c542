//
// *****************************************************************************
// Copyright (c) 2024 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//
// Created on 2024/5/20 by <PERSON>
//

#pragma once

#include <string>

namespace SimpleSipSession
{

enum SipUriFormat
{
    UnknownUri = -1,
    SipUri = 0,
    SipPhoneUri,
    TelUri
};

class SipUri
{
public:
    static std::string uri(const std::string &field);
    static std::string user(const std::string &uri);
    static std::string hostport(const std::string &uri);

    static std::string formatUri(enum SipUriFormat format, const std::string &prefix, const std::string &number, const std::string &address);
    static std::string formatNameAddr(enum SipUriFormat format, const std::string &prefix, const std::string &number, const std::string &address, const std::string &displayName);
};

} // namespace SimpleSipSession
