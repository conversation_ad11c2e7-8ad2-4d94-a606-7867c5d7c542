//
// *****************************************************************************
// Copyright (c) 2023 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//
// Created on 2023/12/23 by <PERSON>
//

#pragma once

#include "Common/Util.h"

namespace SimpleSipSession
{

class SipScheduler : virtual public Common::Shared
{
public:
    SipScheduler() : _closed(false) {}

    virtual std::string name() = 0;
    virtual void close() { _closed = true; }
    virtual bool schd() { return !_closed; }

protected:
    bool _closed;
};

typedef Common::Handle<SipScheduler> SipSchedulerPtr;

class SipDriver;
typedef Common::Handle<SipDriver> SipDriverPtr;

class SipDriver : virtual public Common::Shared
{
public:
    static SipDriverPtr create();

    virtual void close() = 0;
    virtual bool addSchd(const SipSchedulerPtr &scheduler) = 0;
};

} // namespace SimpleSipSession
