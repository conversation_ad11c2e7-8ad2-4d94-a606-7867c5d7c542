//
// *****************************************************************************
// Copyright (c) 2025 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//

#pragma once

#include "SipFlow.h"

namespace SimpleSipSession
{

class KeepAlive
{
public:
    class Flow : public SipFlow
    {
    public:
        virtual void trigger() = 0;
    };

    typedef Common::Handle<Flow> FlowPtr;

    static FlowPtr create(const SipDriverPtr &driver, const SipTransportPtr &transport, const SipFlowListenerPtr &listener, int intervalSeconds);
};

} // namespace SimpleSipSession
