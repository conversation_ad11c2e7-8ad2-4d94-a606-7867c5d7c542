//
// *****************************************************************************
// Copyright (c) 2023 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//
// Created on 2023/6/27 by <PERSON>
//

#pragma once

#include "Common/Util.h"

namespace SimpleSipSession
{
class SipMessageContext
{
public:
    virtual bool getValue(const std::string &key, std::string &value) = 0;
};

class SipMessage;
typedef Common::Handle<SipMessage> SipMessagePtr;

class SipMessage : virtual public Common::Shared
{
public:
    static SipMessagePtr create(const std::string &content);
    static SipMessagePtr clone(const SipMessagePtr &message);

    static SipMessagePtr genRequest(const SipMessagePtr &request, const std::string &method, const std::string &uri);
    static SipMessagePtr nextRequest(const SipMessagePtr &request, const std::string &method, const std::string &uri);
    static SipMessagePtr genResponse(const SipMessagePtr &request, int statusCode, const std::string &reasonPhrase);

    static std::string genCallId(const std::string &sessId, const std::string &caller, const std::string &callee, const std::string &address);

    virtual std::string logInfo() = 0;
    virtual std::string data() = 0;
    virtual std::string encode(SipMessageContext *context = nullptr) = 0;

    virtual bool isRequest() = 0;
    virtual bool getRequestLine(std::string &method, std::string &uri) = 0;
    virtual bool getStatusLine(int &statusCode, std::string &reasonPhrase) = 0;

    virtual std::string getHeader(const std::string &name) = 0;
    virtual std::string getBody() = 0;

    virtual bool getMethod(std::string &method) = 0;

    virtual bool addHeader(const std::string &name, const std::string &value) = 0;
    virtual bool setHeader(const std::string &name, const std::string &value) = 0;
};

} // namespace SimpleSipSession
