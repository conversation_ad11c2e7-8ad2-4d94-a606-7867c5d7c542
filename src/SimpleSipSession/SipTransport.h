//
// *****************************************************************************
// Copyright (c) 2023 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//
// Created on 2023/12/28 by <PERSON>
//

#pragma once

#include "Common/Util.h"
#include <string>

namespace SimpleSipSession
{

struct SipHostPort
{
    SipHostPort() = default;
    SipHostPort(const std::string &_host, int _port)
        : host(_host)
        , port(_port)
    {
    }

    std::string to_string() const
    {
        return host + ":" + std::to_string(port);
    }

    bool decode(const std::string &str);

    std::string host;
    int port = 0;
};

class SipTransport : virtual public Common::Shared
{
public:
    virtual std::string protocol() = 0;
    virtual SipHostPort localHostPort() = 0;
    virtual SipHostPort remoteHostPort() = 0;
};

typedef Common::Handle<SipTransport> SipTransportPtr;

} // namespace SimpleSipSession
