//
// *****************************************************************************
// Copyright (c) 2023 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//
// Created on 2023/12/8 by <PERSON>
//

#pragma once

#include "JsmLog/JsmLog.h"
#include <exception>
#include <random>
#include <sstream>
#include <string>
#include <map>
#include <vector>

namespace SimpleSipSession
{

typedef std::vector<std::string> SdpLines;
typedef std::vector<std::string> SdpTokens;

static bool splitLines(const std::string &content, SdpLines &lines)
{
    std::stringstream ss(content);
    std::string line;
    while (std::getline(ss, line))
    {
        if (line.empty())
            continue;
        if (line.at(line.size() - 1) == '\r')
            line = line.substr(0, line.size() - 1);
        if (line.empty())
            continue;
        lines.push_back(line);
    }

    return true;
}

static bool splitTokens(const std::string &content, SdpTokens &tokens, char dlm = ' ')
{
    std::stringstream ss(content);
    std::string token;
    while (std::getline(ss, token, dlm))
        tokens.push_back(token);

    return true;
}

class SdpCodec
{
public:
    explicit SdpCodec(int _payload)
        : payload(_payload)
        , clockRate(0)
    {
    }

    virtual bool from_string(const SdpLines &lines)
    {
        std::string rtpmapPrefix = "a=rtpmap:" + std::to_string(payload);
        std::string fmtpPrefix = "a=fmtp:" + std::to_string(payload);

        for (const auto &line : lines)
        {
            if (line.find(rtpmapPrefix) == 0)
            {
                SdpTokens tokens;
                splitTokens(line.substr(rtpmapPrefix.size() + 1), tokens, '/');
                encodingName = tokens[0];
                clockRate = std::stoi(tokens[1]);
            }
            else if (line.find(fmtpPrefix) == 0)
            {
                fmtpParams = line.substr(fmtpPrefix.size() + 1);
            }
        }

        return true;
    }

    virtual bool to_string(SdpLines &lines)
    {
        if (clockRate <= 0)
            return true;

        lines.push_back("a=rtpmap:" + std::to_string(payload) + " " + encodingName + "/" + std::to_string(clockRate));
        if (!fmtpParams.empty())
            lines.push_back("a=fmtp:" + std::to_string(payload) + " " + fmtpParams);

        return true;
    }

public:
    int payload;
    std::string encodingName;
    int clockRate;
    std::string fmtpParams;
};

class SdpMstream
{
public:
    enum State
    {
        StateNone = -1,
        StateInactive = 0,
        StateSendOnly = 1,
        StateRecvOnly = 2,
        StateSendRecv = 3
    };

public:
    SdpMstream()
        : port(0)
        , state(StateNone)
    {
    }

    virtual bool from_string(const SdpLines &lines)
    {
        SdpTokens tokens;
        splitTokens(lines[0].substr(2), tokens);
        if (tokens.size() < 4)
        {
            LOG_WARNING("SdpMessage", "sdp mstream invalid " + lines[0]);
            return false;
        }

        mtype = tokens[0];
        port = std::stoi(tokens[1]);
        transport = tokens[2];
        for (int i = 3; i < tokens.size(); i++)
        {
            SdpCodec codec = SdpCodec(std::stoi(tokens[i]));
            if (!codec.from_string(lines))
            {
                LOG_WARNING("SdpMessage", "sdp mstream invalid payload:" + std::to_string(codec.payload));
                return false;
            }
            codecs.push_back(codec);
        }

        enum State __state = StateNone;
        for (int i = 1; i < lines.size(); i++)
        {
            if (lines[i] == "a=inactive")
                __state = StateInactive;
            else if (lines[i] == "a=sendonly")
                __state = StateSendOnly;
            else if (lines[i] == "a=recvonly")
                __state = StateRecvOnly;
            else if (lines[i] == "a=sendrecv")
                __state = StateSendRecv;
            else if (lines[i].find("i=") == 0)
                info = lines[i].substr(2);
            else if (lines[i].find("a=") == 0 && lines[i].find("a=rtpmap:") != 0 && lines[i].find("a=fmtp:") != 0)
                attrs.push_back(lines[i].substr(2));
        }
        state = __state;

        return true;
    }

    virtual bool to_string(SdpLines &lines)
    {
        std::string payloads;
        for (const auto &codec : codecs)
            payloads += std::to_string(codec.payload) + " ";
        if (payloads.empty())
        {
            LOG_WARNING("SdpMessage", "sdp mstream no codec");
            return false;
        }
        payloads = payloads.substr(0, payloads.size() - 1);

        lines.push_back("m=" + mtype + " " + std::to_string(port) + " " + transport + " " + payloads);
        if (!info.empty())
            lines.push_back("i=" + info);

        for (auto &codec : codecs)
        {
            if (!codec.to_string(lines))
            {
                LOG_WARNING("SdpMessage", "sdp mstream to_string failed, codec:" + codec.encodingName);
                return false;
            }
        }

        for (const auto &attr : attrs)
            lines.push_back("a=" + attr);

        switch (state)
        {
        case StateInactive: lines.push_back("a=inactive"); break;
        case StateSendOnly: lines.push_back("a=sendonly"); break;
        case StateRecvOnly: lines.push_back("a=recvonly"); break;
        case StateSendRecv: lines.push_back("a=sendrecv"); break;
        default: break;
        }

        return true;
    }

    void setState(int stateVal)
    {
        switch (stateVal)
        {
        case 0: state = StateInactive; break;
        case 1: state = StateSendOnly; break;
        case 2: state = StateRecvOnly; break;
        case 3: state = StateSendRecv; break;
        default: state = StateNone; break;
        }
    }

public:
    std::string mtype;
    int port;
    std::string transport;
    enum State state;
    std::string info;
    std::vector<std::string> attrs;
    std::vector<SdpCodec> codecs;
};

class SdpSess
{
public:
    SdpSess()
        : version(0)
    {
    }

    virtual bool from_string(const SdpLines &lines)
    {
        for (auto it = lines.begin(); it != lines.end(); )
        {
            SdpTokens tokens;
            if (it->find("o=") == 0)
            {
                splitTokens(it->substr(2), tokens);
                if (tokens.size() < 6)
                {
                    LOG_WARNING("SdpMessage", "sdp session invalid " + (*it));
                    return false;
                }
                sessId = tokens[1];
                version = std::stoul(tokens[2]);
                ipv4 = tokens[5];
            }
            else if (it->find("c=") == 0)
            {
                splitTokens(it->substr(2), tokens);
                if (tokens.size() < 3)
                {
                    LOG_WARNING("SdpMessage", "sdp session invalid " + (*it));
                    return false;
                }
                ipv4 = tokens[2];
            }
            else if (it->find("m=") == 0)
            {
                SdpLines mlines;
                do
                {
                    mlines.push_back(*(it++));
                } while (it != lines.end() && it->find("m=") != 0);

                SdpMstream mstream;
                if (!mstream.from_string(mlines))
                {
                    LOG_WARNING("SdpMessage", "sdp session invalid " + mlines[0]);
                    return false;
                }

                mstreams[mstream.mtype] = mstream;
                continue;
            }

            it++;
        }

        return true;
    }

    virtual bool to_string(SdpLines &lines)
    {
        lines.push_back("v=0");
        lines.push_back("o=- " + sessId + " " + std::to_string(version) + " IN IP4 " + ipv4);
        lines.push_back("s=-");
        lines.push_back("c=IN IP4 " + ipv4);
        lines.push_back("t=0 0");

        for (auto kv : mstreams)
        {
            if (!kv.second.to_string(lines))
            {
                LOG_WARNING("SdpMessage", "sdp session to_string failed.");
                return false;
            }
        }

        return true;
    }

    virtual bool from_string(const std::string &content)
    {
        std::stringstream ss(content);

        SdpLines lines;
        std::string line;
        while (std::getline(ss, line))
        {
            if (line.empty())
                continue;
            if (line.at(line.size() - 1) == '\r')
                line = line.substr(0, line.size() - 1);
            if (line.empty())
                continue;
            lines.push_back(line);
        }

        if (lines.empty())
        {
            LOG_WARNING("SdpMessage", "sdp message from_string empty content");
            return false;
        }

        try
        {
            return from_string(lines);
        }
        catch (const std::exception &e)
        {
            LOG_WARNING("SdpMessage", "sdp message decode exception:" + std::string(e.what()) + " sdp:" + content);
            return false;
        }
    }

    virtual bool to_string(std::string &content)
    {
        SdpLines lines;
        if (!to_string(lines))
        {
            LOG_WARNING("SdpMesssage", "sdp message to_string failed.");
            return false;
        }

        for (const auto &line : lines)
            content += line + "\r\n";
        content += "\r\n";

        return true;
    }

    void genSessId()
    {
        std::random_device rd;
        std::mt19937 gen(rd());
        std::uniform_int_distribution<> dist(0x1fffffff, 0x7fffffff);

        sessId = std::to_string(dist(gen));
    }

public:
    std::string sessId;
    unsigned long version;
    std::string ipv4;
    std::map<std::string, SdpMstream> mstreams;
};

} // namespace SimpleSipSession