//
// *****************************************************************************
// Copyright (c) 2025 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//

#pragma once

#include "SimpleSipSession/SipServerRegistration.h"
#include "SimpleSipSession/trans/SipTransaction.h"
#include "SimpleSipSession/base/SipTimer.h"
#include <string>

namespace SimpleSipSession
{

class RegTrans : public SipTransactionListener
{
public:
    void close();
    bool checkClosed() const { return _trans->getState() == SipTransaction::StateTermed; }

    // implement SipMessageContext
    virtual bool getValue(const std::string &key, std::string &value) override { return false; };

    // implement SipTransactionListener
    virtual void onError(const Common::Error &error) override {};
    virtual void sendMessage(const SipMessagePtr &message) override;
    virtual void onSucceed(const SipMessagePtr &message) override {}
    virtual void onFailed(const SipMessagePtr &message) override {}
    virtual void onRequest(const SipMessagePtr &message) override {}
    virtual void onResponse(const SipMessagePtr &message) override {}

public:
    SipTransactionPtr _trans;
    ServerRegistration::ConnectionPtr _connection;
};

typedef Common::Handle<RegTrans> RegTransPtr;

class RegistrationBinding : public Common::ListNode<RegistrationBinding>
{
public:
    RegistrationBinding(const std::string &username, const std::string &address, const ServerRegistration::ListenerPtr &listener);

    std::string username() const { return _username; }
    std::string address() const { return _address; }
    int expires() const;

    void close();
    bool isClosed() const { return _listener == nullptr; }
    bool checkClosed();
    bool recvMessage(const SipMessagePtr &message, const SipDriverPtr &driver, const ServerRegistration::ConnectionPtr &connection, SipTimerManager &timerManager);
    ServerRegistration::ConnectionPtr getConnection() const { return _connection; }

private:
    ServerRegistration::ListenerPtr _listener;
    std::string _username;
    std::string _address;
    ServerRegistration::ConnectionPtr _connection;
    int _expires;
    unsigned int _expiredTicks;

    std::map<std::string, RegTransPtr> _trans;
};

typedef Common::Handle<RegistrationBinding> RegistrationBindingPtr;

class RegistrationBindingIndex
{
public:
    void clear();
    bool insert(const RegistrationBindingPtr &binding);
    void remove(RegistrationBindingPtr binding);
    void remove(const std::string &usernameOfAddress);
    RegistrationBindingPtr find(const std::string &usernameOfAddress);

    unsigned int size() const { return _bindingList.size(); }
    Common::ListNode<RegistrationBinding>* begin() const { return _bindingList.head(); }
    const Common::ListNode<RegistrationBinding>* end() const { return _bindingList.end(); }
    Common::ListNode<RegistrationBinding>* polling();

private:
    std::map<std::string, RegistrationBindingPtr> _bindingIndex;
    Common::List<RegistrationBinding> _bindingList;
};

class ServerRegistrationI : public ServerRegistration, public SipTimerManager, public SipScheduler
{
public:
    ServerRegistrationI(const ListenerPtr &listener, const SipDriverPtr &driver);

    // implement ServerRegistration
    virtual void close() override;
    virtual std::vector<BindingInfo> stats() override;
    virtual bool recv(const SipMessagePtr &message, const ConnectionPtr &connection) override;
    virtual ConnectionPtr getConnection(const std::string &usernameOfUri) override;

    // implement SipTimerManager
    virtual void onTimeout(const std::string &name) override;

    // implement SipScheduler
    virtual std::string name() override { return "ServerRegistration"; }
    virtual bool schd() override;

private:
    SipDriverPtr _driver;
    ListenerPtr _listener;
    RegistrationBindingIndex _bindingIndex;
    unsigned int _lastSchdTicks;
};

} // namespace SimpleSipSession