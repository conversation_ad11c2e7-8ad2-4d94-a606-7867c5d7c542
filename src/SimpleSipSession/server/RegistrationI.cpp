//
// *****************************************************************************
// Copyright (c) 2025 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//

#include "RegistrationI.h"
#include "SimpleSipSession/SipTypes.h"
#include "SimpleSipSession/SipDriver.h"
#include "SimpleSipSession/trans/SipNist.h"
#include "SimpleSipSession/SipUri.h"

namespace SimpleSipSession
{

static std::string getAddressOfRecord(const SipMessagePtr &message)
{
    return SipUri::uri(message->getHeader(HeaderCName::TO));
}

static int getExpires(const SipMessagePtr &message)
{
    auto expires = message->getHeader(HeaderCName::EXPIRES);
    auto contact = message->getHeader(HeaderCName::CONTACT);
    if (!contact.empty())
    {
        auto pos = contact.find("expires=");
        if (pos != std::string::npos)
        {
            auto pos2 = contact.find(";", pos);
            if (pos2 == std::string::npos)
                expires = contact.substr(pos + 8);
            else
                expires = contact.substr(pos + 8, pos2 - pos - 8);
        }
    }

    try
    {
        return std::stoi(expires);
    }
    catch (const std::exception &e)
    {
        LOG_ERROR("ServerRegistration", "content:get expires failed, expires:" + expires + " error:" + e.what());
        return -1;
    }
}

void RegTrans::close()
{
    if (!_trans)
        return;

    _trans->terminate("close");
    _trans = 0;
    _connection = 0;
}

void RegTrans::sendMessage(const SipMessagePtr &message)
{
    _connection->onSendMessage(message);
}

RegistrationBinding::RegistrationBinding(const std::string &username, const std::string &address, const ServerRegistration::ListenerPtr &listener)
    : _username(username)
    , _address(address)
    , _listener(listener)
    , _expires(0)
{
}

int RegistrationBinding::expires() const
{
    if (_expires <= 0)
        return 0;

    int expires = (_expiredTicks - Common::getCurTicks()) / 1000;
    if (expires < 0 || expires > _expires)
        return 0;

    return expires;
}

void RegistrationBinding::close()
{
    if (isClosed())
        return;

    LOG_INFO("RegistrationBinding", "content:address:" + _address + " close");

    for (auto &trans : _trans)
        trans.second->close();
    _trans.clear();

    _connection = 0;
    _expires = 0;
    _listener = 0;
}

bool RegistrationBinding::checkClosed()
{
    for (auto it = _trans.begin(); it != _trans.end();)
    {
        if (it->second->checkClosed())
        {
            it->second->close();
            it = _trans.erase(it);
        }
        else
            ++it;
    }

    return _trans.empty() && _expires == 0;
}

bool RegistrationBinding::recvMessage(const SipMessagePtr &message, const SipDriverPtr &driver, const ServerRegistration::ConnectionPtr &connection, SipTimerManager &timerManager)
{
    std::string via = message->getHeader(HeaderCName::VIA);
    auto it = _trans.find(via);
    if (it == _trans.end())
    {
        auto ret = _trans.emplace(via, new RegTrans());
        it = ret.first;

        auto trans = SipNist::create(SipTransactionConfig(driver, connection, it->second));
        if (!trans)
        {
            LOG_ERROR("RegistrationBinding", "content:address:" + _address + " create trans failed");
            return false;
        }

        it->second->_trans = trans;
        it->second->_connection = connection;
    }

    if (!it->second->_trans->recvMessage(message))
    {
        LOG_WARNING("RegistrationBinding", "content:address:" + _address + " recv message failed");
        return false;
    }

    if (it->second->_trans->getState() == SipTransaction::StateProcessing)
    {
        int statusCode;
        _expires = getExpires(message);
        if (_expires < 0)
            statusCode = StatusCode::BAD_REQUEST;
        else
            statusCode = _listener->onRegisterRequest(_username, _address);

        if (_expires > 0 && statusCode == StatusCode::OK)
            timerManager.startTimer(_address, _expires * 1000);
        else
            timerManager.stopTimer(_address);

        if (!it->second->_trans->response(statusCode))
        {
            LOG_ERROR("RegistrationBinding", "content:address:" + _address + " response failed");
            return false;
        }

        if (statusCode == StatusCode::OK)
        {
            LOG_INFO("RegistrationBinding", "content:address:" + _address + " update connection:" + connection->localHostPort().to_string() + "->" + connection->remoteHostPort().to_string() + " expires:" + std::to_string(_expires));
            _connection = connection;
            _expiredTicks = Common::getCurTicks() + _expires * 1000;
        }
        else if (_connection)
        {
            LOG_INFO("RegistrationBinding", "content:address:" + _address + " clear connection:" + _connection->localHostPort().to_string() + "->" + _connection->remoteHostPort().to_string());
            _expires = 0;
            _connection = 0;
        }
    }

    LOG_DEBUG("RegistrationBinding", "content:address:" + _address + " process request success");
    return true;
}

void RegistrationBindingIndex::clear()
{
    _bindingList.clear();
    _bindingIndex.clear();
}

bool RegistrationBindingIndex::insert(const RegistrationBindingPtr &binding)
{
    auto it = _bindingIndex.find(binding->username());
    if (it != _bindingIndex.end())
    {
        LOG_WARNING("RegistrationBindingIndex", "content:username:" + binding->username() + " already exists");
        return false;
    }

    it = _bindingIndex.find(binding->address());
    if (it != _bindingIndex.end())
    {
        LOG_WARNING("RegistrationBindingIndex", "content:address:" + binding->address() + " already exists");
        return false;
    }

    LOG_DEBUG("RegistrationBindingIndex", "content:insert username:" + binding->username() + " address:" + binding->address());
    _bindingIndex.insert(std::make_pair(binding->username(), binding));
    _bindingIndex.insert(std::make_pair(binding->address(), binding));
    _bindingList.append(binding.get());
    return true;
}

void RegistrationBindingIndex::remove(RegistrationBindingPtr binding)
{
    _bindingIndex.erase(binding->username());
    _bindingIndex.erase(binding->address());
    binding->remove();
}

void RegistrationBindingIndex::remove(const std::string &usernameOfAddress)
{
    auto it = _bindingIndex.find(usernameOfAddress);
    if (it != _bindingIndex.end())
    {
        LOG_DEBUG("RegistrationBindingIndex", "content:remove username:" + it->second->username() + " address:" + it->second->address());
        remove(it->second);
    }
}

RegistrationBindingPtr RegistrationBindingIndex::find(const std::string &usernameOfAddress)
{
    auto it = _bindingIndex.find(usernameOfAddress);
    if (it == _bindingIndex.end())
        return 0;

    return it->second;
}

Common::ListNode<RegistrationBinding> *RegistrationBindingIndex::polling()
{
    auto it = _bindingList.head();
    if (it == _bindingList.end())
        return nullptr;

    it->remove();
    _bindingList.append(it);

    return it;
}

ServerRegistrationI::ServerRegistrationI(const ListenerPtr &listener, const SipDriverPtr &driver)
    : _driver(driver)
    , _listener(listener)
{
}

void ServerRegistrationI::close()
{
    Common::RecLock lock(this);

    if (!_driver)
        return;

    LOG_INFO("ServerRegistration", "content:close");

    SipScheduler::close();

    for (auto it = _bindingIndex.begin(); it != _bindingIndex.end(); it = it->next())
        it->data()->close();
    _bindingIndex.clear();
    _driver = 0;
    _listener = 0;
}

std::vector<ServerRegistration::BindingInfo> ServerRegistrationI::stats()
{
    std::vector<ServerRegistration::BindingInfo> bindingInfos;
    for (auto it = _bindingIndex.begin(); it != _bindingIndex.end(); it = it->next())
    {
        auto binding = it->data();
        if (binding->isClosed())
            continue;

        bindingInfos.push_back(ServerRegistration::BindingInfo(binding->username(), binding->address(), binding->expires()));
    }

    return bindingInfos;
}

bool ServerRegistrationI::recv(const SipMessagePtr &message, const ServerRegistration::ConnectionPtr &connection)
{
    Common::RecLock lock(this);

    if (!message->isRequest())
    {
        LOG_DEBUG("ServerRegistration", "content:recv response message");
        return false;
    }

    std::string method;
    if (!message->getMethod(method) || method != Method::REGISTER)
    {
        LOG_DEBUG("ServerRegistration", "content:recv " + method + " not register message");
        return false;
    }

    auto address = getAddressOfRecord(message);
    if (address.empty())
    {
        LOG_DEBUG("ServerRegistration", "content:get address failed");
        return false;
    }

    auto binding = _bindingIndex.find(address);
    if (binding)
    {
        if (binding->isClosed())
        {
            LOG_DEBUG("ServerRegistration", "content:binding closed, address:" + address);
            _bindingIndex.remove(binding);
            binding = 0;
        }
    }
    if (!binding)
    {
        LOG_DEBUG("ServerRegistration", "content:new register binding, address:" + address);
        binding = new RegistrationBinding(SipUri::user(address), address, _listener);
        if (!_bindingIndex.insert(binding))
        {
            LOG_ERROR("ServerRegistration", "content:insert binding failed, address:" + address);
            return false;
        }
    }

    return binding->recvMessage(message, _driver, connection, *this);
}

ServerRegistration::ConnectionPtr ServerRegistrationI::getConnection(const std::string &usernameOfUri)
{
    Common::RecLock lock(this);

    auto binding = _bindingIndex.find(usernameOfUri);
    if (!binding)
    {
        LOG_INFO("ServerRegistration", "content:connection not found, usernameOfUri:" + usernameOfUri);
        return 0;
    }

    auto ret = binding->getConnection();
    if (!ret)
    {
        LOG_INFO("ServerRegistration", "content:no valid connection, usernameOfUri:" + usernameOfUri);
        return 0;
    }

    LOG_DEBUG("ServerRegistration", "content:get connection success, usernameOfUri:" + usernameOfUri + " connection:" + ret->localHostPort().to_string() + "->" + ret->remoteHostPort().to_string());
    return ret;
}

void ServerRegistrationI::onTimeout(const std::string &name)
{
    Common::RecLock lock(this);

    auto binding = _bindingIndex.find(name);
    if (!binding)
    {
        LOG_WARNING("ServerRegistration", "content:address:" + name + " onTimeout not found");
        return;
    }

    LOG_INFO("ServerRegistration", "content:address:" + name + " onTimeout, close binding");
    binding->close();
    _bindingIndex.remove(binding);
}

bool ServerRegistrationI::schd()
{
    Common::RecLock lock(this);

    if (!SipScheduler::schd())
    {
        LOG_INFO("ServerRegistration", "content:schd failed");
        return false;
    }

    SipTimerManager::schd();

    unsigned int size = _bindingIndex.size();
    if (size > 10000)
        size = 10000;

    for (int i = 0; i < size; i++)
    {
        auto it = _bindingIndex.polling();
        if (!it)
            break;

        auto binding = it->data();
        if (binding->checkClosed())
        {
            binding->close();
            _bindingIndex.remove(binding);
        }
    }

    return true;
}

ServerRegistrationPtr ServerRegistration::create(const ListenerPtr &listener, const SipDriverPtr &driver)
{
    try
    {
        Common::Handle<ServerRegistrationI> serverRegistration = new ServerRegistrationI(listener, driver);
        if (!driver->addSchd(serverRegistration))
        {
            LOG_ERROR("ServerRegistration", "content:add schd failed");
            return 0;
        }

        return serverRegistration;
    }
    catch (const std::exception &e)
    {
        LOG_ERROR("ServerRegistration", "content:create failed, error:" + std::string(e.what()));
        return 0;
    }
}

} // namespace SimpleSipSession