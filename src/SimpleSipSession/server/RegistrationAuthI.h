#pragma once

#include "SimpleSipSession/SipServerRegistration.h"

namespace SimpleSipSession
{
class RegistrationAuth : public Common::Shared
{
public:
    RegistrationAuth(const std::string &username, const std::string &password)
        : _username(username)
        , _password(password)
    {
    }

    const std::string &username() const { return _username; }
    const std::string &password() const { return _password; }
    bool authenticate();
    void updatePassword(const std::string &password) { _password = password; }

private:
    std::string calcHA1();
    std::string calcHA2();
    std::string calcResponse();

private:
    std::string _username;
    std::string _password;
    std::string _nonce;
    std::string _realm;
    std::string _qop;
    std::string _stale;
    std::string _cnonce;
    std::string _rspauth;
    std::string _addressOfRecord;
    std::string _nextNonce;
    std::string _opaque;
    std::string _nonceCount;
    bool _authenticated;
};

typedef Common::Handle<RegistrationAuth> RegistrationAuthPtr;

class RegistrationAuthManager : public ServerRegistration::Listener
{
public:
    RegistrationAuthManager()
    {
    }

    // implement ServerRegistration::Listener
    virtual int onRegisterRequest(const std::string &username, const std::string &uri, const SipMessagePtr &message) override;
    virtual void onError(const Common::Error &error) override;

    void addUser(const std::string &username, const std::string &password);
    void removeUser(const std::string &username);
    void updateUser(const std::string &username, const std::string &password);

    void clear();

private:
    RegistrationAuthPtr findUser(const std::string &username);

private:
    std::map<std::string, RegistrationAuthPtr> _authIndex;
};

}