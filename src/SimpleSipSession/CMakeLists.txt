aux_source_directory(base SIMPLE_SIP_SESSION_BASE_SRC)
aux_source_directory(trans SIMPLE_SIP_SESSION_TRANS_SRC)
aux_source_directory(flow SIMPLE_SIP_SESSION_FLOW_SRC)
aux_source_directory(server SIMPLE_SIP_SESSION_SERVER_SRC)
add_library(SimpleSipSession
    ${SIMPLE_SIP_SESSION_BASE_SRC}
    ${SIMPLE_SIP_SESSION_TRANS_SRC}
    ${SIMPLE_SIP_SESSION_FLOW_SRC}
    ${SIMPLE_SIP_SESSION_SERVER_SRC}
)
target_include_directories(SimpleSipSession PRIVATE
    ${DEFAULT_INCLUDE_DIRECTORIES}
    ${PROJECT_SOURCE_DIR}/3rd/jssi/inc
)
target_compile_definitions(SimpleSipSession PRIVATE
    ${DEFAULT_COMPILE_DEFINITIONS}
)

# UnitTest
aux_source_directory(test SIMPLE_SIP_SESSION_TEST_SRC)
add_executable(SimpleSipSessionUnitTest ${SIMPLE_SIP_SESSION_TEST_SRC})
target_include_directories(SimpleSipSessionUnitTest PRIVATE
    ${DEFAULT_INCLUDE_DIRECTORIES}
    ${PROJECT_SOURCE_DIR}/3rd
)
target_compile_definitions(SimpleSipSessionUnitTest PRIVATE
    ${DEFAULT_COMPILE_DEFINITIONS}
)

target_link_libraries(SimpleSipSessionUnitTest SimpleSipSession JsmLog
    ${DEFAULT_SERVER_LIBRARIES}
    $ENV{JssSharedPath}/lib/${DEFAULT_LIB_PATH}/libgtest.a
    $ENV{JssSharedPath}/lib/${DEFAULT_LIB_PATH}/libgmock.a
    $ENV{JssSharedPath}/lib/${DEFAULT_LIB_PATH}/libgtest_main.a
    ${DEFAULT_SYS_LIBRARIES}
)
include(GoogleTest)
gtest_discover_tests(SimpleSipSessionUnitTest)

# cli
add_executable(SimpleSipSessionCli
    cli/SimpleSipSessionCli.cpp
    ${WARP_GLIBC_SRC}
)

target_include_directories(SimpleSipSessionCli PRIVATE
    ${DEFAULT_INCLUDE_DIRECTORIES}
    ${PROJECT_SOURCE_DIR}/3rd
)
target_compile_definitions(SimpleSipSessionCli PRIVATE
    ${DEFAULT_COMPILE_DEFINITIONS}
)

target_link_libraries(SimpleSipSessionCli SimpleSipSession JsmLog CliMain
    ${DEFAULT_SERVER_LIBRARIES}
    ${DEFAULT_SYS_LIBRARIES}
)
target_link_options(SimpleSipSessionCli PRIVATE
    ${DEFAULT_LINK_OPTIONS}
)