//
// *****************************************************************************
// Copyright (c) 2023 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//
// Created on 2023/12/23 by <PERSON>
//

#include "JsmLog/JsmLog.h"
#include "KeepAliveI.h"
#include "SimpleSipSession/SipDriver.h"
#include "SimpleSipSession/SipMessage.h"
#include "SimpleSipSession/trans/SipNict.h"
#include "SimpleSipSession/SipTypes.h"
#include "SimpleSipSession/trans/SipTransaction.h"

namespace SimpleSipSession
{

static constexpr const char *OPTIONS = "\
OPTIONS sip:${REMOTE_HOST_PORT} SIP/2.0\r\n\
From: <sip:${LOCAL_HOST_PORT}>${FROM_PARAMS}\r\n\
To: <sip:${REMOTE_HOST_PORT}>${TO_PARAMS}\r\n\
Call-ID: ${CALL_ID}@${LOCAL_HOST}\r\n\
CSeq: ${SEQ_NUM} OPTIONS\r\n\
Max-Forwards: 70\r\n\
User-Agent: SimpleSipSession\r\n\
Via: SIP/2.0/UDP ${LOCAL_HOST_PORT};branch=${VIA_BRANCH};rport\r\n\
Content-Length: 0\r\n\r\n\
";

// implement SipFlow
void KeepAliveI::close()
{
    LOG_INFO("KeepAlive", "content:close");
    SipScheduler::close();
    _driver = 0;
    _listener = 0;
    _intervalSeconds = 0;
    _trans = 0;
}

bool KeepAliveI::schd()
{
    if (!SipScheduler::schd())
        return false;

    if (_intervalSeconds <= 0 || Common::getCurTicks() - _lastRequestTicks < _intervalSeconds * 1000 || _trans)
        return true;

    LOG_INFO("KeepAlive", "content:create new transaction");
    _lastRequestTicks = Common::getCurTicks();
    _trans = SipNict::create(SipTransactionConfig(_driver, _transport, this), SipMessage::create(OPTIONS));
    if (!_trans)
        LOG_WARNING("KeepAlive", "content:create transaction failed");

    return true;
}

bool KeepAliveI::recvMessage(const SipMessagePtr &message)
{
    if (!_trans || message->isRequest())
        return false;

    std::string method;
    if (!message->getMethod(method) || method != Method::OPTIONS)
        return false;

    return _trans->recvMessage(message);
}

void KeepAliveI::trigger()
{
    LOG_INFO("KeepAlive", "content:trigger now");
    _lastRequestTicks = Common::getCurTicks() - _intervalSeconds * 1000;
}

void KeepAliveI::onError(const Common::Error &error)
{
    if (_listener)
        _listener->onError();
    _trans = 0;
}

void KeepAliveI::sendMessage(const SipMessagePtr &message)
{
    if (_listener)
        _listener->onSendMessage(SimpleSipSession::SipMessage::create(message->data()));
}

void KeepAliveI::onSucceed(const SipMessagePtr &message)
{
    LOG_INFO("KeepAlive", "content:succeed message:" + message->logInfo());
    _trans = 0;
}

void KeepAliveI::onFailed(const SipMessagePtr &message)
{
    LOG_WARNING("KeepAlive", "content:failed message:" + message->logInfo());
    _trans = 0;
}

bool KeepAliveI::getValue(const std::string &key, std::string &value)
{
    return false;
}

KeepAlive::FlowPtr KeepAlive::create(const SipDriverPtr &driver, const SipTransportPtr &transport, const SipFlowListenerPtr &listener, int intervalSeconds)
{
    try
    {
        Common::Handle<KeepAliveI> flow = new KeepAliveI(driver, transport, listener, intervalSeconds);
        if (!driver->addSchd(flow.get()))
        {
            LOG_WARNING("KeepAlive", "content:add scheduler failed");
            return nullptr;
        }

        LOG_INFO("KeepAlive", "content:create succeed");
        return flow.get();
    }
    catch (const std::exception &e)
    {
        LOG_WARNING("KeepAlive", "content:create failed, reason:" + std::string(e.what()));
        return nullptr;
    }
}

} // namespace SimpleSipSession
