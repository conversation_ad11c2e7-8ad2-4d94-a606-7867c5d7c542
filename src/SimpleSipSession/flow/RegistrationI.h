//
// *****************************************************************************
// Copyright (c) 2024 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//
// Created on 2024/1/6 by <PERSON>
//

#pragma once

#include "Common/Util.h"
#include "SimpleSipSession/SipFlowRegistration.h"
#include "SimpleSipSession/trans/SipNict.h"
#include "SimpleSipSession/SipTransport.h"

namespace SimpleSipSession
{

enum RegistrationState
{
    RegistrationStateIdle,
    RegistrationStateRegistering,
    RegistrationStateAuthing,
    RegistrationStateRegistered,
    RegistrationStateReregistering,
    RegistrationStateReauthing,
    RegistrationStateFialed,
    RegistrationStateUnregistering,
    RegistrationStateUnregistered,
};

class RegistrationI : public Registration::Flow, public SipTransactionListener, public SipScheduler
{
public:
    RegistrationI(const SipDriverPtr &driver, const SipTransportPtr &transport, const Registration::ListenerPtr &listener, const Registration::Config &config)
        : _driver(driver)
        , _transport(transport)
        , _listener(listener)
        , _config(config)
        , _lastRequestTicks(Common::getCurTicks() - 3600000)
        , _registering(true)
        , _state(RegistrationStateIdle)
        , _seqNo(0)
        , _realm(config.realm)
        , _nonceCount(0)
        , _expireSeconds(config.expireSeconds)
    {
    }

    // implement SipFlow
    virtual std::string name() override { return "Registration"; }
    virtual void close() override;
    virtual bool schd() override;
    virtual bool recvMessage(const SipMessagePtr &message) override;

    // implement Registration::Flow
    virtual void unregister() override;

    // implement SipTransactionListener
    virtual void onError(const Common::Error &error) override;
    virtual void sendMessage(const SipMessagePtr &message) override;
    virtual void onSucceed(const SipMessagePtr &message) override;
    virtual void onFailed(const SipMessagePtr &message) override;
    virtual void onRequest(const SipMessagePtr &message) override {}
    virtual void onResponse(const SipMessagePtr &message) override {}

    // implement MessageContext
    virtual bool getValue(const std::string &key, std::string &value) override;

private:
    void setState(enum RegistrationState state);
    std::string getCallId(const std::string &callId);
    int getSeqNum(const std::string &cseq);
    int getExpires(const SipMessagePtr &msg);
    std::string getAuthValue(const std::string &auth, const std::string &key);
    bool calcResponse();

private:
    SipDriverPtr _driver;
    SipTransportPtr _transport;
    Registration::ListenerPtr _listener;
    Registration::Config _config;
    unsigned int _lastRequestTicks;
    SipTransactionPtr _trans;
    enum RegistrationState _state;
    unsigned int _stateChangedTicks;
    bool _registering;

    std::string _callId;
    int _seqNo;
    std::string _realm;
    std::string _nonce;
    int _nonceCount;
    std::string _opaque;
    std::string _algorithm;
    std::string _qop;
    std::string _cnonce;
    std::string _response;
    int _expireSeconds;

    friend class Registration;
};

} // namespace SimpleSipSession
