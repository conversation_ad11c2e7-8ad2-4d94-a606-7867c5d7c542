//
// *****************************************************************************
// Copyright (c) 2024 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//
// Created on 2024/1/6 by <PERSON>
//

#include "Common/Util.h"
#include "JsmLog/JsmLog.h"
#include "RegistrationI.h"
#include "SimpleSipSession/SipDriver.h"
#include "SimpleSipSession/SipMessage.h"
#include "SimpleSipSession/SipFlow.h"
#include "SimpleSipSession/trans/SipNict.h"
#include "SimpleSipSession/SipTypes.h"
#include "SimpleSipSession/trans/SipTransaction.h"
#include <cstdio>
#include <cstdlib>
#include <string>

namespace SimpleSipSession
{

static constexpr const char *REGISTER_INIT = "\
REGISTER ${REQUEST_URI} SIP/2.0\r\n\
From: <${ADDRESS_OF_RECORD}>${FROM_PARAMS}\r\n\
To: <${ADDRESS_OF_RECORD}>${TO_PARAMS}\r\n\
Call-ID: ${CALL_ID}@${LOCAL_HOST}\r\n\
CSeq: ${SEQ_NUM} REGISTER\r\n\
Max-Forwards: 70\r\n\
User-Agent: SimpleSipSession\r\n\
Via: SIP/2.0/UDP ${LOCAL_HOST_PORT};branch=${VIA_BRANCH};rport\r\n\
Contact: <sip:${USERNAME}@${LOCAL_HOST_PORT}>\r\n\
Expires: ${EXPIRES}\r\n\
Authorization: Digest username=\"${AUTHNAME}\",realm=\"${REALM}\",uri=\"${REQUEST_URI}\",nonce=\"\",response=\"\"\r\n\
Content-Length: 0\r\n\r\n\
";

static constexpr const char *REGISTER_AUTH = "\
REGISTER ${REQUEST_URI} SIP/2.0\r\n\
From: <${ADDRESS_OF_RECORD}>${FROM_PARAMS}\r\n\
To: <${ADDRESS_OF_RECORD}>${TO_PARAMS}\r\n\
Call-ID: ${CALL_ID}@${LOCAL_HOST}\r\n\
CSeq: ${SEQ_NUM} REGISTER\r\n\
Max-Forwards: 70\r\n\
User-Agent: SimpleSipSession\r\n\
Via: SIP/2.0/UDP ${LOCAL_HOST_PORT};branch=${VIA_BRANCH};rport\r\n\
Contact: <sip:${USERNAME}@${LOCAL_HOST_PORT}>\r\n\
Expires: ${EXPIRES}\r\n\
Authorization: Digest username=\"${AUTHNAME}\",realm=\"${REALM}\",uri=\"${ADDRESS_OF_RECORD}\",nonce=\"${NONCE}\",response=\"${RESPONSE}\",algorithm=${ALGORITHM}${AUTH_QOP_PARAM}${AUTH_NC_PARAM}${AUTH_CNONCE_PARAM}${AUTH_OPAQUE_PARAM}\r\n\
Content-Length: 0\r\n\r\n\
";

static std::string to_string(enum RegistrationState state)
{
    switch (state)
    {
    case RegistrationStateIdle:
        return "Idle";
    case RegistrationStateRegistering:
        return "Registering";
    case RegistrationStateAuthing:
        return "Authing";
    case RegistrationStateRegistered:
        return "Registered";
    case RegistrationStateReregistering:
        return "Reregistering";
    case RegistrationStateReauthing:
        return "Reauthing";
    case RegistrationStateFialed:
        return "Fialed";
    case RegistrationStateUnregistering:
        return "Unregistering";
    case RegistrationStateUnregistered:
        return "Unregistered";
    default:
        return "Unknown";
    }
}

// implement SipFlow
void RegistrationI::close()
{
    LOG_INFO("Registration", "content:close");
    SipScheduler::close();
    _driver = 0;
    _listener = 0;
    _trans = 0;
}

bool RegistrationI::schd()
{
    if (!SipScheduler::schd())
        return false;

    if (_registering)
    {
        switch (_state)
        {
        case RegistrationStateIdle:
            _trans = SipNict::create(SipTransactionConfig(_driver, _transport, this), SipMessage::create(REGISTER_INIT));
            setState(RegistrationStateRegistering);
            break;
        case RegistrationStateRegistered:
            // re-register after 50% expire seconds
            if (Common::getCurTicks() - _stateChangedTicks > (_expireSeconds / 2) * 1000)
            {
                calcResponse();
                _seqNo++;
                _trans = SipNict::create(SipTransactionConfig(_driver, _transport, this), SipMessage::create(REGISTER_AUTH));
                setState(RegistrationStateReregistering);
            }
            break;
        case RegistrationStateFialed:
            // retry after 60 seconds
            if (Common::getCurTicks() - _stateChangedTicks > 60000)
            {
                _callId.clear();
                _seqNo = 0;
                setState(RegistrationStateIdle);
            }
            break;
        default:
            break;
        }
    }
    else
    {
        switch (_state)
        {
        case RegistrationStateIdle:
            setState(RegistrationStateUnregistered);
            break;
        case RegistrationStateRegistered:
            _config.expireSeconds = 0;
            calcResponse();
            _seqNo++;
            _trans = SipNict::create(SipTransactionConfig(_driver, _transport, this), SipMessage::create(REGISTER_AUTH));
            setState(RegistrationStateUnregistering);
            break;
        case RegistrationStateFialed:
            setState(RegistrationStateUnregistered);
            break;
        default:
            break;
        }
    }

    return true;
}

bool RegistrationI::recvMessage(const SipMessagePtr &message)
{
    if (!_trans || message->isRequest())
        return false;

    std::string method;
    if (!message->getMethod(method) || method != Method::REGISTER)
        return false;

    return _trans->recvMessage(message);
}

void RegistrationI::unregister()
{
    LOG_INFO("Registration", "content:unregister");
    _registering = false;
}

void RegistrationI::onError(const Common::Error &error)
{
    _trans = 0;
    setState(RegistrationStateFialed);
}

void RegistrationI::sendMessage(const SipMessagePtr &message)
{
    auto listener = _listener;
    if (listener)
        listener->onSendMessage(SimpleSipSession::SipMessage::create(message->data()));
}

void RegistrationI::onSucceed(const SipMessagePtr &message)
{
    _trans = 0;
    _callId = getCallId(message->getHeader(Header::CALL_ID));
    _seqNo = getSeqNum(message->getHeader(Header::CSEQ));
    _expireSeconds = getExpires(message);

    std::string info =message->getHeader(Header::AUTHENTICATION_INFO);
    if (!info.empty())
    {
        std::string nonce = getAuthValue(info, "nextnonce");
        if (!nonce.empty() && _nonce != nonce)
        {
            _nonce = nonce;
            _nonceCount = 0;
        }
    }

    LOG_INFO("Registration", "content:succeed message:" + message->logInfo() + " expire:" + std::to_string(_expireSeconds));

    if (!_registering)
    {
        if (_state == RegistrationStateUnregistering)
        {
            setState(RegistrationStateUnregistered);
        }
        else
        {
            setState(RegistrationStateRegistered);
            _config.expireSeconds = 0;
            calcResponse();
            _seqNo++;
            _trans = SipNict::create(SipTransactionConfig(_driver, _transport, this), SipMessage::create(REGISTER_AUTH));
            setState(RegistrationStateUnregistering);
        }
    }
    else
    {
        if (_expireSeconds > 0)
            setState(RegistrationStateRegistered);
        else
            setState(RegistrationStateUnregistered);
    }
}

void RegistrationI::onFailed(const SipMessagePtr &message)
{
    int statusCode;
    std::string reasonPhrase;
    message->getStatusLine(statusCode, reasonPhrase);
    _trans = 0;

    if (!_registering)
    {
        setState(RegistrationStateUnregistered);
        return;
    }

    switch (_state)
    {
    case RegistrationStateRegistering:
    case RegistrationStateReregistering: {
        std::string auth;
        if (statusCode == 401)
            auth = message->getHeader(Header::WWW_AUTHENTICATE);
        else if (statusCode == 407)
            auth = message->getHeader(Header::PROXY_AUTHENTICATE);
        else
        {
            LOG_WARNING("Registration", "content:register failed, status code:" + std::to_string(statusCode) + " reason:" + reasonPhrase);
            setState(RegistrationStateFialed);
            break;
        }

        if (auth.empty())
        {
            LOG_WARNING("Registration", "content:no authenticate header");
            setState(RegistrationStateFialed);
            break;
        }

        _callId = getCallId(message->getHeader(Header::CALL_ID));
        _seqNo = getSeqNum(message->getHeader(Header::CSEQ));

        std::string nonce = getAuthValue(auth, "nonce");
        if (_nonce != nonce)
        {
            _nonce = nonce;
            _nonceCount = 0;
        }
        _cnonce = "01a62a6501a62a45"; // Common::randString().c_str();
        _algorithm = getAuthValue(auth, "algorithm");
        _opaque = getAuthValue(auth, "opaque");
        _qop = getAuthValue(auth, "qop");
        _realm = getAuthValue(auth, "realm");

        if (!calcResponse())
        {
            LOG_WARNING("Registration", "content:calc response failed");
            setState(RegistrationStateFialed);
            break;
        }

        _seqNo++;
        _trans = SipNict::create(SipTransactionConfig(_driver, _transport, this), SipMessage::create(REGISTER_AUTH));
        if (RegistrationStateRegistering)
            setState(RegistrationStateAuthing);
        else
            setState(RegistrationStateReauthing);
        return;
    }

    case RegistrationStateAuthing:
    case RegistrationStateReauthing:
        LOG_WARNING("Registration", "content:auth failed, status code:" + std::to_string(statusCode) + " reason:" + reasonPhrase);
        setState(RegistrationStateFialed);
        break;

    default:
        LOG_WARNING("Registration", "content:unexpected failed, status code:" + std::to_string(statusCode) + " reason:" + reasonPhrase + " state:" + to_string(_state));
        break;
    }
}

bool RegistrationI::getValue(const std::string &key, std::string &value)
{
    if (key == PlaceHolder::REQUEST_URI)
    {
        value = "sip:" + _config.realm;
        return true;
    }
    else if (key == PlaceHolder::ADDRESS_OF_RECORD)
    {
        value = "sip:" + _config.username + "@" + _config.realm;
        return true;
    }
    else if (key == PlaceHolder::EXPIRES)
    {
        value = std::to_string(_config.expireSeconds);
        return true;
    }
    else if (key == PlaceHolder::USERNAME)
    {
        value = _config.username;
        return true;
    }
    else if (key == PlaceHolder::AUTHNAME)
    {
        value = _config.authname;
        return true;
    }
    else if (key == PlaceHolder::REALM)
    {
        value = _realm;
        return true;
    }
    else if (key == PlaceHolder::NONCE)
    {
        value = _nonce;
        return true;
    }
    else if (key == PlaceHolder::RESPONSE)
    {
        value = _response;
        return true;
    }
    else if (key == PlaceHolder::ALGORITHM)
    {
        value = _algorithm;
        return true;
    }
    else if (key == PlaceHolder::CALL_ID && !_callId.empty())
    {
        value = _callId;
        return true;
    }
    else if (key == PlaceHolder::SEQ_NUM && _seqNo > 0)
    {
        value = std::to_string(_seqNo);
        return true;
    }
    else if (key == PlaceHolder::AUTH_QOP_PARAM)
    {
        value = _qop.empty() ? "" : ",qop=" + _qop;
        return true;
    }
    else if (key == PlaceHolder::AUTH_NC_PARAM)
    {
        char buf[16];
        snprintf(buf, sizeof(buf), "%08x", _nonceCount);
        value = _qop.empty() ? "" : ",nc=" + std::string(buf);
        return true;
    }
    else if (key == PlaceHolder::AUTH_CNONCE_PARAM)
    {
        value = _qop.empty() ? "" : ",cnonce=\"" + _cnonce + "\"";
        return true;
    }
    else if (key == PlaceHolder::AUTH_OPAQUE_PARAM)
    {
        value = _qop.empty() ? "" : ",opaque=\"" + _opaque + "\"";
        return true;
    }

    return false;
}

void RegistrationI::setState(enum RegistrationState state)
{
    if (_state == state)
        return;

    LOG_INFO("Registration", "content:state changed from " + to_string(_state) + " to " + to_string(state));
    enum RegistrationState lastState = _state;
    _state = state;
    _stateChangedTicks = Common::getCurTicks();

    if ((lastState == RegistrationStateRegistering || lastState == RegistrationStateAuthing) && _state == RegistrationStateRegistered)
    {
        auto listener = _listener;
        if (listener)
            listener->onRegistered();
    }
    else if (_state == RegistrationStateFialed)
    {
        auto listener = _listener;
        if (listener)
            listener->onRegisterFailed();
    }
    else if (_state == RegistrationStateUnregistered)
    {
        auto listener = _listener;
        if (listener)
            listener->onUnregistered();
    }
}

std::string RegistrationI::getCallId(const std::string &callId)
{
    size_t pos = callId.find('@');
    return callId.substr(0, pos);
}

int RegistrationI::getSeqNum(const std::string &cseq)
{
    size_t pos = cseq.find(' ');
    return std::stoi(cseq.substr(0, pos));
}

int RegistrationI::getExpires(const SipMessagePtr &msg)
{
    std::string expires = msg->getHeader(Header::EXPIRES);
    if (!expires.empty())
        return std::stoi(expires);

    std::string contact = msg->getHeader(Header::CONTACT);
    size_t pos = contact.find("expires=");
    if (pos != std::string::npos)
        return std::stoi(contact.substr(pos + 8));

    return _expireSeconds;
}

std::string RegistrationI::getAuthValue(const std::string &auth, const std::string &key)
{
    size_t pos = auth.find(key + "");
    if (pos == std::string::npos)
        return "";

    pos = auth.find('=', pos + key.size());
    if (pos == std::string::npos)
        return "";

    ++pos;
    size_t end = auth.find(',', pos);
    if (end == std::string::npos)
        end = auth.size();

    if (auth[pos] == '"')
    {
        ++pos;
        if (end > pos && auth[end - 1] == '"')
            --end;
    }

    return auth.substr(pos, end - pos);
}

bool RegistrationI::calcResponse()
{
    Common::String HA1;
    if (_algorithm == "MD5")
        HA1 = Common::md5((_config.authname + ":" + _realm + ":" + _config.password).c_str());
    else if (_algorithm == "MD5-sess")
        HA1 = Common::md5(Common::md5((_config.authname + ":" + _realm + ":" + _config.password).c_str()) + ":" + _nonce.c_str() + ":" + _cnonce.c_str());
    else
    {
        LOG_WARNING("Registration", "content:unknown algorithm:" + _algorithm);
        return false;
    }

    // calc HA2, only support qop=auth
    if (!_qop.empty() && _qop != "auth")
    {
        LOG_WARNING("Registration", "content:unknown qop:" + _qop);
        return false;
    }
    Common::String HA2 = Common::md5(("REGISTER:sip:" + _config.username + "@" + _config.realm).c_str());

    if (_qop.empty())
        _response = Common::md5(HA1 + ":" + _nonce.c_str() + ":" + HA2).c_str();
    else
    {
        char nc[16];
        snprintf(nc, sizeof(nc), "%08x", ++_nonceCount);
        _response = Common::md5(HA1 + ":" + _nonce.c_str() + ":" + nc + ":" + _cnonce.c_str() + ":" + _qop.c_str() + ":" + HA2).c_str();
    }

    return true;
}

Registration::FlowPtr Registration::create(const SipDriverPtr &driver, const SipTransportPtr &transport, const Registration::ListenerPtr &listener, const Registration::Config &config)
{
    if (config.username.empty())
    {
        LOG_WARNING("Registration", "content:username is empty");
        return nullptr;
    }

    if (config.authname.empty())
    {
        LOG_WARNING("Registration", "content:authname is empty");
        return nullptr;
    }

    if (config.realm.empty())
    {
        LOG_WARNING("Registration", "content:realm is empty");
        return nullptr;
    }

    try
    {
        Common::Handle<RegistrationI> flow = new RegistrationI(driver, transport, listener, config);
        if (!driver->addSchd(flow.get()))
        {
            LOG_WARNING("Registration", "content:add scheduler failed");
            return nullptr;
        }

        LOG_INFO("Registration", "content:create succeed");
        return flow.get();
    }
    catch (const std::exception &e)
    {
        LOG_WARNING("Registration", "content:create failed, reason:" + std::string(e.what()));
        return nullptr;
    }
}

} // namespace SimpleSipSession
