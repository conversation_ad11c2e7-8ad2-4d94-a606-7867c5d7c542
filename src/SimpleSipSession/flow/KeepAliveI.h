//
// *****************************************************************************
// Copyright (c) 2023 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//
// Created on 2023/12/23 by <PERSON>
//

#pragma once

#include "Common/Util.h"
#include "SimpleSipSession/SipFlowKeepAlive.h"
#include "SimpleSipSession/trans/SipNict.h"
#include "SimpleSipSession/SipTransport.h"

namespace SimpleSipSession
{

class KeepAliveI : public KeepAlive::Flow, public SipTransactionListener, public SipScheduler
{
public:
    KeepAliveI(const SipDriverPtr &driver, const SipTransportPtr &transport, const SipFlowListenerPtr &listener, int intervalSeconds)
        : _driver(driver)
        , _transport(transport)
        , _listener(listener)
        , _intervalSeconds(intervalSeconds)
        , _lastRequestTicks(Common::getCurTicks() - 3600000)
    {
    }

    // implement SipFlow
    virtual std::string name() override { return "KeepAlive"; }
    virtual void close() override;
    virtual bool schd() override;
    virtual bool recvMessage(const SipMessagePtr &message) override;

    // implement KeepAlive::Flow
    virtual void trigger() override;

    // implement SipTransactionListener
    virtual void onError(const Common::Error &error) override;
    virtual void sendMessage(const SipMessagePtr &message) override;
    virtual void onSucceed(const SipMessagePtr &message) override;
    virtual void onFailed(const SipMessagePtr &message) override;
    virtual void onRequest(const SipMessagePtr &message) override {}
    virtual void onResponse(const SipMessagePtr &message) override {}

    // implement MessageContext
    virtual bool getValue(const std::string &key, std::string &value) override;

private:
    SipDriverPtr _driver;
    SipTransportPtr _transport;
    SipFlowListenerPtr _listener;
    int _intervalSeconds;
    unsigned int _lastRequestTicks;
    SipTransactionPtr _trans;

    friend class KeepAlive;
};

} // namespace SimpleSipSession
