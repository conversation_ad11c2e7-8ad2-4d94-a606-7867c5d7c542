//
// *****************************************************************************
// Copyright (c) 2023 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//
// Created on 2023/12/23 by <PERSON>
//

#pragma once

#include <string>

namespace SimpleSipSession
{

namespace Method
{
    const char *const INVITE = "INVITE";
    const char *const PRACK = "PRACK";
    const char *const ACK = "ACK";
    const char *const BYE = "BYE";
    const char *const CANCEL = "CANCEL";
    const char *const REGISTER = "REGISTER";
    const char *const OPTIONS = "OPTIONS";
    const char *const INFO = "INFO";
    const char *const MESSAGE = "MESSAGE";
    const char *const SUBSCRIBE = "SUBSCRIBE";
    const char *const NOTIFY = "NOTIFY";
    const char *const PUBLISH = "PUBLISH";
} // namespace Method

namespace StatusCode
{
    const int TRYING = 100;
    const int RINGING = 180;
    const int CALL_IS_BEING_FORWARDED = 181;
    const int QUEUED = 182;
    const int PROGRESS = 183;

    const int OK = 200;
    const int ACCEPTED = 202;
    const int NO_CONTENT = 204;

    const int MOVED_PERMANENTLY = 301;
    const int MOVED_TEMPORARILY = 302;

    const int BAD_REQUEST = 400;
    const int UNAUTHORIZED = 401;
    const int NOT_FOUND = 404;
    const int METHOD_NOT_ALLOWED = 405;
    const int NOT_ACCEPTABLE = 406;
    const int REQUEST_TIMEOUT = 408;
    const int GONE = 410;
    const int TOO_MANY_REQUESTS = 429;

    const int INTERNAL_SERVER_ERROR = 500;
    const int BAD_GATEWAY = 502;
    const int SERVICE_UNAVAILABLE = 503;

    std::string getReasonPharse(int statusCode);
} // namespace StatusCode

namespace ReasonPharse
{
    const char *const TRYING = "Trying";
    const char *const RINGING = "Ringing";
    const char *const CALL_IS_BEING_FORWARDED = "Call Is Being Forwarded";
    const char *const QUEUED = "Queued";
    const char *const PROGRESS = "Progress";

    const char *const OK = "OK";
    const char *const ACCEPTED = "Accepted";
    const char *const NO_CONTENT = "No Content";

    const char *const MOVED_PERMANENTLY = "Moved Permanently";
    const char *const MOVED_TEMPORARILY = "Moved Temporarily";

    const char *const BAD_REQUEST = "Bad Request";
    const char *const UNAUTHORIZED = "Unauthorized";
    const char *const NOT_FOUND = "Not Found";
    const char *const METHOD_NOT_ALLOWED = "Method Not Allowed";
    const char *const NOT_ACCEPTABLE = "Not Acceptable";
    const char *const REQUEST_TIMEOUT = "Request Timeout";
    const char *const GONE = "Gone";
    const char *const TOO_MANY_REQUESTS = "Too Many Requests";

    const char *const INTERNAL_SERVER_ERROR = "Internal Server Error";
    const char *const BAD_GATEWAY = "Bad Gateway";
    const char *const SERVICE_UNAVAILABLE = "Service Unavailable";
}

namespace Header
{
    const char *const FROM = "From";
    const char *const TO = "To";
    const char *const CALL_ID = "Call-ID";
    const char *const CSEQ = "CSeq";
    const char *const VIA = "Via";
    const char *const CONTACT = "Contact";
    const char *const CONTENT_TYPE = "Content-Type";
    const char *const CONTENT_LENGTH = "Content-Length";
    const char *const MAX_FORWARDS = "Max-Forwards";
    const char *const ROUTE = "Route";
    const char *const RECORD_ROUTE = "Record-Route";
    const char *const EXPIRES = "Expires";
    const char *const WWW_AUTHENTICATE = "WWW-Authenticate";
    const char *const AUTHORIZATION = "Authorization";
    const char *const AUTHENTICATION_INFO = "Authentication-Info";
    const char *const PROXY_AUTHENTICATE = "Proxy-Authenticate";
    const char *const PROXY_AUTHORIZATION = "Proxy-Authorization";
    const char *const USER_AGENT = "User-Agent";
    const char *const SERVER = "Server";

    const char *const ACCEPT = "Accept";
    const char *const ACCEPT_ENCODING = "Accept-Encoding";
    const char *const ALLOW = "Allow";
} // namespace Header

namespace HeaderCName
{
    const char *const FROM = "from";
    const char *const TO = "to";
    const char *const CALL_ID = "call-id";
    const char *const CSEQ = "cseq";
    const char *const VIA = "via";
    const char *const CONTACT = "contact";
    const char *const CONTENT_TYPE = "content-type";
    const char *const CONTENT_LENGTH = "content-length";
    const char *const MAX_FORWARDS = "max-forwards";
    const char *const ROUTE = "route";
    const char *const RECORD_ROUTE = "record-route";
    const char *const EXPIRES = "expires";
    const char *const AUTHORIZATION = "authorization";
    const char *const PROXY_AUTHORIZATION = "proxy-authorization";
    const char *const USER_AGENT = "user-agent";
    const char *const SERVER = "server";

    const char *const ACCEPT = "accept";
    const char *const ACCEPT_ENCODING = "accept-encoding";
    const char *const ALLOW = "allow";
} // namespace HeaderCName

namespace PlaceHolder
{
    const char *const REQUEST_URI = "REQUEST_URI";
    const char *const FROM_PARAMS = "FROM_PARAMS";
    const char *const TO_PARAMS = "TO_PARAMS";
    const char *const SEQ_NUM = "SEQ_NUM";
    const char *const CALL_ID = "CALL_ID";
    const char *const VIA_BRANCH = "VIA_BRANCH";
    const char *const LOCAL_HOST = "LOCAL_HOST";
    const char *const LOCAL_HOST_PORT = "LOCAL_HOST_PORT";
    const char *const REMOTE_HOST_PORT = "REMOTE_HOST_PORT";
    const char *const ADDRESS_OF_RECORD = "ADDRESS_OF_RECORD";
    const char *const EXPIRES = "EXPIRES";
    const char *const USERNAME = "USERNAME";
    const char *const AUTHNAME = "AUTHNAME";
    const char *const REALM = "REALM";
    const char *const NONCE = "NONCE";
    const char *const RESPONSE = "RESPONSE";
    const char *const ALGORITHM = "ALGORITHM";
    const char *const AUTH_QOP_PARAM = "AUTH_QOP_PARAM";
    const char *const AUTH_NC_PARAM = "AUTH_NC_PARAM";
    const char *const AUTH_CNONCE_PARAM = "AUTH_CNONCE_PARAM";
    const char *const AUTH_OPAQUE_PARAM = "AUTH_OPAQUE_PARAM";
} // namespace PlaceHolder

} // namespace SimpleSipSession
