//
// *****************************************************************************
// Copyright (c) 2025 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//

#include "SimpleSipSession/SipTypes.h"

namespace SimpleSipSession
{

namespace StatusCode
{
    std::string getReasonPharse(int statusCode)
    {
        switch (statusCode)
        {
        case StatusCode::TRYING:
            return ReasonPharse::TRYING;
        case StatusCode::RINGING:
            return ReasonPharse::RINGING;
        case StatusCode::CALL_IS_BEING_FORWARDED:
            return ReasonPharse::CALL_IS_BEING_FORWARDED;
        case StatusCode::QUEUED:
            return ReasonPharse::QUEUED;
        case StatusCode::PROGRESS:
            return ReasonPharse::PROGRESS;

        case StatusCode::OK:
            return ReasonPharse::OK;
        case StatusCode::ACCEPTED:
            return ReasonPharse::ACCEPTED;
        case StatusCode::NO_CONTENT:
            return ReasonPharse::NO_CONTENT;

        case StatusCode::MOVED_PERMANENTLY:
            return ReasonPharse::MOVED_PERMANENTLY;
        case StatusCode::MOVED_TEMPORARILY:
            return ReasonPharse::MOVED_TEMPORARILY;

        case StatusCode::BAD_REQUEST:
            return ReasonPharse::BAD_REQUEST;
        case StatusCode::UNAUTHORIZED:
            return ReasonPharse::UNAUTHORIZED;
        case StatusCode::NOT_FOUND:
            return ReasonPharse::NOT_FOUND;
        case StatusCode::METHOD_NOT_ALLOWED:
            return ReasonPharse::METHOD_NOT_ALLOWED;
        case StatusCode::NOT_ACCEPTABLE:
            return ReasonPharse::NOT_ACCEPTABLE;
        case StatusCode::REQUEST_TIMEOUT:
            return ReasonPharse::REQUEST_TIMEOUT;
        case StatusCode::GONE:
            return ReasonPharse::GONE;
        case StatusCode::TOO_MANY_REQUESTS:
            return ReasonPharse::TOO_MANY_REQUESTS;

        case StatusCode::INTERNAL_SERVER_ERROR:
            return ReasonPharse::INTERNAL_SERVER_ERROR;
        case StatusCode::BAD_GATEWAY:
            return ReasonPharse::BAD_GATEWAY;
        case StatusCode::SERVICE_UNAVAILABLE:
            return ReasonPharse::SERVICE_UNAVAILABLE;

        default:
            return "";
        }
    }
} // namespace StatusCode

} // namespace SimpleSipSession
