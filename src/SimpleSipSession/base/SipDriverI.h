//
// *****************************************************************************
// Copyright (c) 2023 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//
// Created on 2023/12/23 by <PERSON>
//

#pragma once

#include "Common/Util.h"
#include "SimpleSipSession/SipDriver.h"

namespace SimpleSipSession
{

class SipDriverI : public SipDriver, public Common::Thread
{
public:
    SipDriverI() : _closed(false) {}

    // implement SipFlowManager
    virtual void close() override;
    virtual bool addSchd(const SipSchedulerPtr &scheduler) override;

    // implement Common::Thread
    virtual void onRun() override;

private:
    Common::RecMutex _mutex;
    bool _closed;
    std::set<SipSchedulerPtr> _schds;
    std::set<SipSchedulerPtr> _schdsToAdd;
};

} // namespace SimpleSipSession
