//
// *****************************************************************************
// Copyright (c) 2023 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//
// Created on 2023/12/28 by <PERSON>
//

#include "JsmLog/JsmLog.h"
#include "SipTimer.h"

namespace SimpleSipSession
{

unsigned int SipTimer::_T1 = 500;
unsigned int SipTimer::_T2 = 4000;
unsigned int SipTimer::_T4 = 5000;

void SipTimer::reset(int t1, int t2, int t4)
{
    if (t1 > 0)
        _T1 = t1;
    if (t2 > 0)
        _T2 = t2;
    if (t4 > 0)
        _T4 = t4;
    LOG_INFO("SipTimer", "content:update T1:" + std::to_string(_T1) + " T2:" + std::to_string(_T2) + " T4:" + std::to_string(_T4));
}

SipTimer::SipTimer(const std::string &_name)
    : name(_name)
    , _lastTicks(0)
    , _expireMs(0)
{
}

SipTimer::~SipTimer()
{
    LOG_DEBUG("SipTrans", "content:destroy timer:" + name);
}

void SipTimer::start(unsigned int expireMs)
{
    LOG_DEBUG("SipTrans", "content:start timer:" + name + " expire:" + std::to_string(expireMs));
    _lastTicks = Common::getCurTicks();
    _expireMs = expireMs;
}

void SipTimer::stop()
{
    LOG_DEBUG("SipTrans", "content:stop timer:" + name);
    _expireMs = 0;
}

bool SipTimer::schd()
{
    if (_expireMs <= 0 || Common::getCurTicks() - _lastTicks < _expireMs)
        return false;

    LOG_INFO("SipTrans", "content:timer:" + name + " fired.");
    _lastTicks = Common::getCurTicks();
    return true;
}

void SipTimerManager::schd()
{
    Common::RecLock lock(this);

    for (auto &kv : _timers)
    {
        if (kv.second->schd())
            onTimeout(kv.first);
    }
}

void SipTimerManager::startTimer(const std::string &name, unsigned int expireMs)
{
    Common::RecLock lock(this);

    auto it = _timers.find(name);
    if (it != _timers.end())
    {
        it->second->start(expireMs);
        return;
    }

    SipTimerPtr timer = new SipTimer(name);
    _timers.insert(make_pair(name, timer));
    timer->start(expireMs);
}

void SipTimerManager::stopTimer(const std::string &name)
{
    Common::RecLock lock(this);

    auto it = _timers.find(name);
    if (it != _timers.end())
        it->second->stop();
}

} // namespace SimpleSipSession
