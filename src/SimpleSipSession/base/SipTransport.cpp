//
// *****************************************************************************
// Copyright (c) 2023 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//
// Created on 2023/12/28 by <PERSON>
//

#include "SimpleSipSession/SipTransport.h"

namespace SimpleSipSession
{

bool SipHostPort::decode(const std::string &str)
{
    if (str.empty())
        return false;

    std::string::size_type pos = str.rfind(':');
    if (pos == 0)
        return false;

    if (pos == std::string::npos)
    {
        host = str.c_str();
        port = 5060;
    }
    else
    {
        port = std::stoi(str.substr(pos + 1));
        if (port <= 0 || port > 65535)
            return false;

        host = str.substr(0, pos);
    }

    if (host.empty() || host.size() > 255)
        return false;

    return true;
}

} // namespace SimpleSipSession
