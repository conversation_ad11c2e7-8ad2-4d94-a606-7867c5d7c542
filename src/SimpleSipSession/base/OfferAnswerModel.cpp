//
// *****************************************************************************
// Copyright (c) 2023 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//
// Created on 2023/12/9 by <PERSON>
//

#include "SimpleSipSession/OfferAnswerModel.h"
#include "JsmLog/JsmLog.h"
#include <string>
#include <vector>

namespace SimpleSipSession
{

bool OfferAnswerModel::init(const std::string &sdp)
{
    if (_localSdp.sessId.empty())
    {
        if (!_localSdp.from_string(sdp))
        {
            LOG_WARNING("OfferAnswer", "init local sdp failed, sdp:" + sdp);
            return false;
        }

        _localSdp.genSessId();
    }
    else
    {
        unsigned long version = _localSdp.version;
        std::string sessId = _localSdp.sessId;

        if (!_localSdp.from_string(sdp))
        {
            LOG_WARNING("OfferAnswer", "update local sdp failed, sdp:" + sdp);
            return false;
        }

        _localSdp.version = version;
        _localSdp.sessId = sessId;
    }

    return true;
}

bool OfferAnswerModel::setOption(enum Option option, const std::string &value)
{
    switch (option)
    {
    case OptionIpv4:
        LOG_INFO("OfferAnswer", "set option ipv4:" + value);
        _localSdp.ipv4 = value;
        break;
    case OptionAudioPort:
        LOG_INFO("OfferAnswer", "set option audio port:" + value);
        _localSdp.mstreams["audio"].port = std::stoi(value);
        break;
    case OptionAudioState:
        LOG_INFO("OfferAnswer", "set option audio state:" + value);
        _localSdp.mstreams["audio"].setState(std::stoi(value));
        break;
    case OptionVideoPort:
        LOG_INFO("OfferAnswer", "set option video port:" + value);
        _localSdp.mstreams["video"].port = std::stoi(value);
        break;
    case OptionVideoState:
        LOG_INFO("OfferAnswer", "set option video state:" + value);
        _localSdp.mstreams["video"].setState(std::stoi(value));
        break;

    default:
        LOG_WARNING("OfferAnswer", "set option unknown:" + std::to_string(option));
        return false;
    }

    return true;
}

std::string OfferAnswerModel::genOffer()
{
    std::string sdp;
    _localSdp.to_string(sdp);
    _localSdp.version++;
    return sdp;
}

bool OfferAnswerModel::setAnswer(const std::string &answer)
{
    SdpSess peerSdp;
    if (!peerSdp.from_string(answer))
    {
        LOG_WARNING("OfferAnswer", "decode answer failed");
        return false;
    }

    _negoedSdp = peerSdp;
    _remoteSdp = peerSdp;

    return true;
}

std::string OfferAnswerModel::genAnswer(const std::string &offer)
{
    SdpSess peerSdp;
    if (!peerSdp.from_string(offer))
    {
        LOG_WARNING("OfferAnswer", "decode offer failed");
        return "";
    }

    _remoteSdp = peerSdp;
    _negoedSdp = _localSdp;
    for (auto it = _negoedSdp.mstreams.begin(); it != _negoedSdp.mstreams.end(); )
    {
        auto itPeerMstream = peerSdp.mstreams.find(it->first);
        if (itPeerMstream == peerSdp.mstreams.end())
        {
            _negoedSdp.mstreams.erase(it++);
            continue;
        }

        std::vector<SdpCodec> codecs;
        for (auto &peerCodec : itPeerMstream->second.codecs)
        {
            for (auto &localCodec : it->second.codecs)
            {
                if ((!peerCodec.encodingName.empty() && localCodec.encodingName == peerCodec.encodingName) || peerCodec.payload == localCodec.payload)
                {
                    localCodec.payload = peerCodec.payload;
                    localCodec.fmtpParams = peerCodec.fmtpParams;
                    codecs.push_back(localCodec);
                    break;
                }
            }
        }

        it->second.codecs = codecs;
        it->second.attrs = itPeerMstream->second.attrs;

        enum SdpMstream::State state;
        switch (itPeerMstream->second.state)
        {
        case SdpMstream::StateInactive:
            state = SdpMstream::StateInactive;
            break;
        case SdpMstream::StateSendOnly:
            state = SdpMstream::StateRecvOnly;
            break;
        case SdpMstream::StateRecvOnly:
            state = SdpMstream::StateSendOnly;
            break;
        case SdpMstream::StateSendRecv:
            state = SdpMstream::StateSendRecv;
            break;
        default:
            state = SdpMstream::StateNone;
            break;
        }

        if (it->second.state == SdpMstream::StateNone || it->second.state == SdpMstream::StateSendRecv)
            it->second.state = state;
        else if (it->second.state != state)
            it->second.state = SdpMstream::StateInactive;

        ++it;
    }

    std::string sdp;
    _negoedSdp.to_string(sdp);
    return sdp;
}

bool OfferAnswerModel::getCodec(bool audio, std::string &codec, int &payloadType) const
{
    auto it = _localSdp.mstreams.find(audio ? "audio" : "video");
    if (it == _localSdp.mstreams.end())
        return false;

    if (it->second.codecs.empty())
        return false;

    codec = it->second.codecs[0].encodingName;
    payloadType = it->second.codecs[0].payload;
    return true;
}

bool OfferAnswerModel::getRemoteHostPort(bool audio, std::string &host, int &port) const
{
    auto it = _remoteSdp.mstreams.find(audio ? "audio" : "video");
    if (it == _remoteSdp.mstreams.end())
        return false;

    auto itPeerMstream = _remoteSdp.mstreams.find(audio ? "audio" : "video");
    if (itPeerMstream == _remoteSdp.mstreams.end())
        return false;

    host = _remoteSdp.ipv4;
    port = itPeerMstream->second.port;

    return true;
}

} // namespace SimpleSipSession
