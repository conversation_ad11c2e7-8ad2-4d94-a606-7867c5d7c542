//
// *****************************************************************************
// Copyright (c) 2024 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//
// Created on 2024/5/20 by <PERSON>
//

#include "SimpleSipSession/SipUri.h"

namespace SimpleSipSession
{

std::string SipUri::uri(const std::string &field)
{
    size_t pos = field.find('<');
    if (pos == std::string::npos)
        return "";

    size_t pos1 = field.find('>', pos);
    if (pos1 == std::string::npos)
        return "";

    return field.substr(pos + 1, pos1 - pos - 1);
}

std::string SipUri::user(const std::string &uri)
{
    size_t pos = uri.find(':');
    if (pos == std::string::npos)
        return "";

    size_t pos1 = uri.find('@');
    if (pos1 == std::string::npos)
        return uri.substr(pos + 1);

    return uri.substr(pos + 1, pos1 - pos - 1);
}

std::string SipUri::hostport(const std::string &uri)
{
    size_t pos = uri.find('@');
    if (pos == std::string::npos)
        return "";

    size_t pos1 = uri.find(';', pos);
    if (pos1 == std::string::npos)
        return uri.substr(pos + 1);

    return uri.substr(pos + 1, pos1 - pos - 1);
}

std::string SipUri::formatUri(enum SipUriFormat format, const std::string &prefix, const std::string &number, const std::string &address)
{
    std::string uri;
    switch (format)
    {
    case SipPhoneUri:
        uri = "sip:" + prefix + number + "@" + address.c_str() + ";user=phone";
        break;

    case TelUri:
        uri = "tel:" + prefix + number;
        break;

    default:
        uri = "sip:" + prefix + number + "@" + address.c_str();
        break;
    }

    return uri;
}

std::string SipUri::formatNameAddr(enum SipUriFormat format, const std::string &prefix, const std::string &number, const std::string &address, const std::string &displayName)
{
    std::string addrSpec = formatUri(format, prefix, number, address);
    if (displayName.empty())
        return "<" + addrSpec + ">";

    return "\"" + displayName + "\" <" + addrSpec + ">";
}

} // namespace SimpleSipSession
