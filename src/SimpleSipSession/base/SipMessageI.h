//
// *****************************************************************************
// Copyright (c) 2023 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//
// Created on 2023/6/27 by <PERSON>
//

#pragma once

#include "Common/TypesPub.h"
#include "Common/Util.h"
#include "SimpleSipSession/SipTypes.h"
#include "SimpleSipSession/SipMessage.h"
#include <vector>

namespace SimpleSipSession
{

class TokenParser
{
public:
    explicit TokenParser(const std::string &content)
        : _content(content + " ")
    {
    }

    std::string operator[](int pos);
    bool get(int pos, std::string &token);

private:
    std::string _content;
};

class SipHeaderValue
{
public:
    SipHeaderValue() = default;
    SipHeaderValue(const std::string &content) // cpp-check-suppress explicitConstructor
        : _content(content)
    {
    }

    std::string encode(SipMessageContext *context = nullptr);
    operator std::string();

private:
    std::string _content;
    std::string _value;
};

class SipHeader
{
public:
    SipHeader(const std::string &name); // cpp-check-suppress explicitConstructor
    SipHeader(const std::string &name, const std::string &value);
    SipHeader(const SipHeader &header);

    void clear();
    void operator=(const std::string &value);
    void addValue(const std::string &value);
    std::string getValue(int index = 0);

    std::string encode(SipMessageContext *context = nullptr);

public:
    std::string name;
    std::vector<SipHeaderValue> values;
};

class SipMessageI : public SipMessage
{
public:
    explicit SipMessageI(const std::string &content);
    SipMessageI(const SipMessageI &message);

    virtual std::string logInfo() override;
    virtual std::string data() override { return _content; }
    virtual std::string encode(SipMessageContext *context = nullptr) override;
    virtual bool isRequest() override;
    virtual bool getRequestLine(std::string &method, std::string &uri) override;
    virtual bool getStatusLine(int &statusCode, std::string &reasonPhrase) override;
    virtual std::string getHeader(const std::string &name) override;
    virtual std::string getBody() override;
    virtual bool getMethod(std::string &method) override;
    virtual bool addHeader(const std::string &name, const std::string &value) override;
    virtual bool setHeader(const std::string &name, const std::string &value) override;

    bool parseContent();

private:
    std::string getToken(int &pos);
    std::string getRestOfLine(int &pos);
    bool jumpToNextLine(int &pos);
    std::string normalizeHeader(const std::string &name);
    bool getSdpMstream(const std::string &type, int &port, std::vector<std::string> &codecs);
    std::string getSdpMstream(const std::string &type);
    std::string getSdpRtpMap(const std::string &payload);

private:
    std::string _content;
    bool _request;
    std::string _method;
    SipHeaderValue _uri;
    int _statusCode;
    std::string _reasonPhrase;

    SipHeader _from;
    SipHeader _to;
    SipHeader _callId;
    SipHeader _cseq;
    SipHeader _via;
    SipHeader _maxForwards;
    SipHeader _contentLength;
    SipHeader _contentType;
    std::map<std::string, SipHeader> _headers;
    std::string _body;

    friend class SipMessage;
};

typedef Common::Handle<SipMessageI> SipMessageIPtr;

} // namespace SimpleSipSession
