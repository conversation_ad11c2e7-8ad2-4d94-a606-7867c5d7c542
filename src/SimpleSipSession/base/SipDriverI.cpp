//
// *****************************************************************************
// Copyright (c) 2023 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//
// Created on 2023/12/23 by <PERSON>
//

#include "JsmLog/JsmLog.h"
#include "SipDriverI.h"
#include <exception>

namespace SimpleSipSession
{

void SipDriverI::close()
{
    LOG_INFO("SipDriver", "content:close SipDriver.");

    stopRun(true);

    Common::RecLock lock(_mutex);
    _closed = true;

    for (auto it = _schds.begin(); it != _schds.end(); ++it)
        (*it)->close();

    for (auto it = _schdsToAdd.begin(); it != _schdsToAdd.end(); ++it)
        (*it)->close();

    _schds.clear();
    _schdsToAdd.clear();
}

bool SipDriverI::addSchd(const SipSchedulerPtr &scheduler)
{
    Common::RecLock lock(_mutex);
    if (_closed)
    {
        LOG_WARNING("SipDriver", "content:closed, add scheduler failed:" + scheduler->name());
        return false;
    }

    LOG_INFO("SipDriver", "content:add scheduler:" + scheduler->name());
    _schdsToAdd.insert(scheduler);
    return true;
}

void SipDriverI::onRun()
{
    while (isRunning())
    {
        Common::sleep(50);

        do
        {
            Common::RecLock lock(_mutex);
            _schds.insert(_schdsToAdd.begin(), _schdsToAdd.end());
            _schdsToAdd.clear();
        } while (0);

        for (auto it = _schds.begin(); it != _schds.end();)
        {
            if (!(*it)->schd())
            {
                LOG_INFO("SipDriver", "content:remove scheduler:" + (*it)->name());
                _schds.erase(it++);
            }
            else
            {
                ++it;
            }
        }
    }
}

SipDriverPtr SipDriver::create()
{
    try
    {
        Common::Handle<SipDriverI> mgr = new SipDriverI();
        if (!mgr->startRun(0, "SipDriver"))
        {
            LOG_WARNING("SipDriver", "content:start SipDriver failed.");
            return nullptr;
        }

        LOG_INFO("SipDriver", "content:create SipDriver succeed.");
        return mgr.get();
    }
    catch (const std::exception &e)
    {
        LOG_WARNING("SipDriver", "content:create SipDriver failed, reason:" + std::string(e.what()));
    }

    return nullptr;
}

} // namespace SimpleSipSession
