//
// *****************************************************************************
// Copyright (c) 2023 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//
// Created on 2023/6/27 by <PERSON>
//

#include "Common/TypesPub.h"
#include "Common/Util.h"
#include "JsmLog/JsmLog.h"
#include "SimpleSipSession/SipMessage.h"
#include "SimpleSipSession/SipTypes.h"
#include "SipMessageI.h"
#include <cctype>
#include <exception>
#include <regex>
#include <string>

namespace SimpleSipSession
{

static std::string to_lower(const std::string &str)
{
    std::string lower;
    for (const auto &c : str)
        lower += std::tolower(c);
    return lower;
}

std::string TokenParser::operator[](int index)
{
    std::string ret;
    if (!get(index, ret))
        return "";

    return ret;
}

bool TokenParser::get(int index, std::string &token)
{
    int start = 0, i = 0, end;
    do
    {
        end = _content.find_first_of(" \r\n", start);
        if (end == std::string::npos)
            return false;

        if (end > start && i++ >= index)
            break;

        start = end + 1;
    } while (true);

    token = _content.substr(start, end - start);
    return true;
}

std::string SipHeaderValue::encode(SipMessageContext *context)
{
    _value = _content;
    if (!context)
        return _value;

    std::regex reg("\\$\\{[a-zA-Z0-9_]+\\}");

    for (std::smatch match; std::regex_search(_value, match, reg);)
    {
        std::string key = match[0].str().substr(2, match[0].str().size() - 3);
        std::string value;
        if (context->getValue(key, value))
        {
            LOG_VERBOSE("SipMessage", "content:replace " + key + " with " + value);
            _value = std::regex_replace(_value, std::regex("\\$\\{" + key + "\\}"), value);
        }
        else
        {
            LOG_VERBOSE("SipMessage", "content:replace " + key + " with empty");
            _value = std::regex_replace(_value, std::regex("\\$\\{" + key + "\\}"), "");
        }
    }

    return _value;
}

SipHeaderValue::operator std::string()
{
    return _value.empty() ? _content : _value;
}

SipHeader::SipHeader(const std::string &_name)
    : name(_name)
{
}

SipHeader::SipHeader(const std::string &_name, const std::string &_value)
    : name(_name)
{
    values.push_back(_value);
}

SipHeader::SipHeader(const SipHeader &header)
    : name(header.name)
    , values(header.values)
{
}

void SipHeader::clear()
{
    values.clear();
}

void SipHeader::operator=(const std::string &value)
{
    values.push_back(value);
}

void SipHeader::addValue(const std::string &value)
{
    values.push_back(value);
}

std::string SipHeader::getValue(int index)
{
    if (index >= values.size())
        return "";
    return values[index];
}

std::string SipHeader::encode(SipMessageContext *context)
{
    std::string content;
    for (auto &value : values)
        content += name + ": " + value.encode(context) + "\r\n";

    return content;
}

SipMessageI::SipMessageI(const std::string &content)
    : _content(content)
    , _request(false)
    , _statusCode(0)
    , _from(Header::FROM)
    , _to(Header::TO)
    , _callId(Header::CALL_ID)
    , _cseq(Header::CSEQ)
    , _via(Header::VIA)
    , _maxForwards(Header::MAX_FORWARDS)
    , _contentLength(Header::CONTENT_LENGTH)
    , _contentType(Header::CONTENT_TYPE)
{
}

SipMessageI::SipMessageI(const SipMessageI &message)
    : _content(message._content)
    , _request(message._request)
    , _method(message._method)
    , _uri(message._uri)
    , _statusCode(message._statusCode)
    , _reasonPhrase(message._reasonPhrase)
    , _headers(message._headers)
    , _body(message._body)
    , _from(message._from)
    , _to(message._to)
    , _callId(message._callId)
    , _cseq(message._cseq)
    , _via(message._via)
    , _maxForwards(message._maxForwards)
    , _contentLength(message._contentLength)
    , _contentType(message._contentType)
{
}

std::string SipMessageI::logInfo()
{
    std::string text = "call-id:" + getHeader("call-id") + " ";
    if (_request)
        text += _method + " " + _uri.encode();
    else
    {
        text += std::to_string(_statusCode) + " " + _reasonPhrase;
        text += " (" + TokenParser(getHeader("cseq"))[1] + ")";
    }

    std::string ms = getSdpMstream("audio");
    if (!ms.empty())
        text +=  " " + ms;
    ms = getSdpMstream("video");
    if (!ms.empty())
        text +=  " " + ms;

    return text;
}

std::string SipMessageI::encode(SipMessageContext *context)
{
    _content.clear();

    if (_request)
        _content = _method + " " + _uri.encode(context) + " SIP/2.0\r\n";
    else
        _content = "SIP/2.0 " + std::to_string(_statusCode) + " " + _reasonPhrase + "\r\n";

    // encode common headers
    _content += _from.encode(context);
    _content += _to.encode(context);
    _content += _callId.encode(context);
    _content += _cseq.encode(context);
    _content += _via.encode(context);
    _content += _maxForwards.encode(context);

    // encode headers
    for (auto &header : _headers)
        _content += header.second.encode(context);

    _content += _contentType.encode(context);
    _content += _contentLength.encode(context);
    _content += "\r\n";

    // encode body
    if (!_body.empty())
        _content += _body;

    return _content;
}

bool SipMessageI::isRequest()
{
    return _request;
}

bool SipMessageI::getRequestLine(std::string &method, std::string &uri)
{
    if (!_request)
        return false;

    method = _method;
    uri = _uri;
    return true;
}

bool SipMessageI::getStatusLine(int &statusCode, std::string &reasonPhrase)
{
    if (_request)
        return false;

    statusCode = _statusCode;
    reasonPhrase = _reasonPhrase;
    return true;
}

std::string SipMessageI::getHeader(const std::string &name)
{
    std::string cname = normalizeHeader(name);
    if (cname == HeaderCName::FROM)
        return _from.getValue();
    else if (cname == HeaderCName::TO)
        return _to.getValue();
    else if (cname == HeaderCName::CALL_ID)
        return _callId.getValue();
    else if (cname == HeaderCName::CSEQ)
        return _cseq.getValue();
    else if (cname == HeaderCName::VIA)
        return _via.getValue();
    else if (cname == HeaderCName::MAX_FORWARDS)
        return _maxForwards.getValue();
    else if (cname == HeaderCName::CONTENT_LENGTH)
        return _contentLength.getValue();
    else if (cname == HeaderCName::CONTENT_TYPE)
        return _contentType.getValue();

    auto it = _headers.find(cname);
    if (it == _headers.end())
        return "";
    return it->second.getValue();
}

std::string SipMessageI::getBody()
{
    return _body;
}

bool SipMessageI::getMethod(std::string &method)
{
    if (_request)
    {
        method = _method;
        return true;
    }

    method = TokenParser(getHeader(HeaderCName::CSEQ))[1];
    return true;
}

bool SipMessageI::addHeader(const std::string &name, const std::string &value)
{
    std::string cname = normalizeHeader(name);
    SipHeader *header = nullptr;
    if (cname == HeaderCName::FROM)
        header = &_from;
    else if (cname == HeaderCName::TO)
        header = &_to;
    else if (cname == HeaderCName::CALL_ID)
        header = &_callId;
    else if (cname == HeaderCName::CSEQ)
        header = &_cseq;
    else if (cname == HeaderCName::VIA)
        header = &_via;
    else if (cname == HeaderCName::MAX_FORWARDS)
        header = &_maxForwards;
    else if (cname == HeaderCName::CONTENT_LENGTH)
        header = &_contentLength;
    else if (cname == HeaderCName::CONTENT_TYPE)
        header = &_contentType;
    else
    {
        auto ret = _headers.insert(std::make_pair(cname, SipHeader(name)));
        header = &(ret.first->second);
    }

    header->addValue(value);
    return true;
}

bool SipMessageI::setHeader(const std::string &name, const std::string &value)
{
    std::string cname = normalizeHeader(name);
    SipHeader *header = nullptr;
    if (cname == HeaderCName::FROM)
        header = &_from;
    else if (cname == HeaderCName::TO)
        header = &_to;
    else if (cname == HeaderCName::CALL_ID)
        header = &_callId;
    else if (cname == HeaderCName::CSEQ)
        header = &_cseq;
    else if (cname == HeaderCName::VIA)
        header = &_via;
    else if (cname == HeaderCName::MAX_FORWARDS)
        header = &_maxForwards;
    else if (cname == HeaderCName::CONTENT_LENGTH)
        header = &_contentLength;
    else if (cname == HeaderCName::CONTENT_TYPE)
        header = &_contentType;
    else
    {
        auto ret = _headers.insert(std::make_pair(cname, SipHeader(name)));
        header = &(ret.first->second);
    }

    header->clear();
    header->addValue(value);
    return true;
}

bool SipMessageI::parseContent()
{
    int pos = 0;
    std::string token = getToken(pos);
    if (token.empty())
    {
        LOG_WARNING("SipMessage", "content:invalid format sip message.");
        return false;
    }

    if (token == "SIP/2.0")
    {
        _request = false;
        _statusCode = std::stoi(getToken(pos));
        if (_statusCode == 0)
        {
            LOG_WARNING("SipMessage", "content:invalid format sip message, invalid status code");
            return false;
        }
        _reasonPhrase = getRestOfLine(pos);
    }
    else
    {
        _request = true;
        _method = token;
        if (_method != "INVITE" && _method != "ACK" && _method != "PRACK" && _method != "CANCEL" && _method != "BYE" && _method != "UPDATE" && _method != "REGISTER"
            && _method != "INFO" && _method != "MESSAGE" && _method != "OPTIONS" && _method != "SUBSCRIBE" && _method != "NOTIFY")
        {
            LOG_WARNING("SipMessage", "content:invalid format sip message, invalid method:" + _method);
            return false;
        }
        _uri = getToken(pos);
        if (((std::string)_uri).empty())
        {
            LOG_WARNING("SipMessage", "content:invalid format sip message, no request uri");
            return false;
        }
    }

    jumpToNextLine(pos);
    int headerEnd = _content.find("\r\n\r\n", pos);
    if (headerEnd <= pos)
    {
        LOG_WARNING("SipMessage", "content:invalid format sip message, no header end");
        return false;
    }

    Common::StrVec lines;
    Common::String(_content.substr(pos, headerEnd - pos).c_str()).split(lines, "\r\n");
    for (auto &line : lines)
    {
        int sepPos = line.find(':');
        if (sepPos <= 0)
        {
            LOG_WARNING("SipMessage", "content:invalid format sip message, invalid header");
            return false;
        }

        std::string name = line.substr(0, sepPos).trim().c_str();
        std::string cname = normalizeHeader(name);
        if (cname == HeaderCName::FROM)
            _from = line.substr(sepPos + 1).trim().c_str();
        else if (cname == HeaderCName::TO)
            _to = line.substr(sepPos + 1).trim().c_str();
        else if (cname == HeaderCName::CALL_ID)
            _callId = line.substr(sepPos + 1).trim().c_str();
        else if (cname == HeaderCName::CSEQ)
            _cseq = line.substr(sepPos + 1).trim().c_str();
        else if (cname == HeaderCName::VIA)
            _via = line.substr(sepPos + 1).trim().c_str();
        else if (cname == HeaderCName::MAX_FORWARDS)
            _maxForwards = line.substr(sepPos + 1).trim().c_str();
        else if (cname == HeaderCName::CONTENT_LENGTH)
            _contentLength = line.substr(sepPos + 1).trim().c_str();
        else if (cname == HeaderCName::CONTENT_TYPE)
            _contentType = line.substr(sepPos + 1).trim().c_str();
        else
        {
            auto ret = _headers.insert(std::make_pair(cname, SipHeader(name)));
            ret.first->second.addValue(line.substr(sepPos + 1).trim().c_str());
        }
    }

    _body = _content.substr(headerEnd + 4);
    return true;
}

std::string SipMessageI::getToken(int &pos)
{
    int sep = _content.find_first_of(" \r\n", pos, 3);
    if (sep <= pos)
        return "";

    std::string ret = _content.substr(pos, sep - pos);
    if (_content[sep] == ' ')
        pos = sep + 1;
    else
        pos = sep;
    return ret;
}

std::string SipMessageI::getRestOfLine(int &pos)
{
    int sep = _content.find("\r\n", pos);
    if (sep <= pos)
        return "";

    std::string ret = _content.substr(pos, sep - pos);
    pos = sep;
    return ret;
}

bool SipMessageI::jumpToNextLine(int &pos)
{
    int sep = _content.find("\r\n", pos);
    if (sep <= pos)
        return false;

    pos = sep + 2;
    return true;
}

std::string SipMessageI::normalizeHeader(const std::string &name)
{
    static std::map<std::string, std::string> AbbrMaps = {{"c", "content-type"},
                                                          {"e", "content-encoding"},
                                                          {"f", "from"},
                                                          {"i", "call-id"},
                                                          {"k", "supported"},
                                                          {"l", "content-length"},
                                                          {"m", "contact"},
                                                          {"s", "subject"},
                                                          {"t", "to"},
                                                          {"v", "via"}};
    std::string lname = to_lower(name);
    if (name.size() == 1)
    {
        auto it = AbbrMaps.find(lname);
        if (it != AbbrMaps.end())
            return it->second;
    }

    return lname;
}

bool SipMessageI::getSdpMstream(const std::string &type, int &port, std::vector<std::string> &codecs)
{
    std::string text = "m=" + type;
    int pos = _content.find(text);
    if (pos == std::string::npos)
        return false;
    pos += text.size() + 1;

    std::string portStr = getToken(pos);
    if (portStr.empty())
        return false;
    port = std::stoi(portStr);

    // skip RTP/AVP
    getToken(pos);

    do
    {
        std::string payload = getToken(pos);
        if (payload.empty())
            break;

        codecs.push_back(getSdpRtpMap(payload));
    } while (true);

    return true;
}

std::string SipMessageI::getSdpMstream(const std::string &type)
{
    int port;
    std::vector<std::string> codecs;
    if (!getSdpMstream(type, port, codecs))
        return "";

    std::string text = type + "/" + std::to_string(port) + "(";
    for (const auto &codec : codecs)
        text += codec + ",";
    if (text[text.size() - 1] == ',')
        text = text.substr(0, text.size() - 1);
    text += ")";
    return text;
}

std::string SipMessageI::getSdpRtpMap(const std::string &payload)
{
    std::string text = "a=rtpmap:" + payload;
    int pos = _body.find(text);
    if (pos == std::string::npos)
        return payload;

    int epos = _body.find_first_of("/\r\n", pos);
    pos += text.size() + 1;
    return payload + ":" + _body.substr(pos, epos - pos);
}

SipMessagePtr SipMessage::create(const std::string &content)
{
    try
    {
        SipMessageIPtr msg = new SipMessageI(content);
        if (!msg->parseContent())
            return nullptr;
        return msg;
    }
    catch (const std::exception &e)
    {
        LOG_WARNING("SipMessage", "content:create sip message from content failed, reason:" + std::string(e.what()));
        return nullptr;
    }
}

SipMessagePtr SipMessage::clone(const SipMessagePtr &message)
{
    try
    {
        return new SipMessageI(*(SipMessageIPtr::dynamicCast(message)));
    }
    catch (const std::exception &e)
    {
        LOG_WARNING("SipMessage", "content:clone sip message, reason:" + std::string(e.what()));
        return nullptr;
    }
}

SipMessagePtr SipMessage::genRequest(const SipMessagePtr &request, const std::string &method, const std::string &uri)
{
    SipMessageIPtr msg = SipMessageIPtr::dynamicCast(SipMessage::clone(request));
    msg->_request = true;
    msg->_method = method;
    msg->_uri = uri;

    std::string from = msg->getHeader(HeaderCName::FROM);
    std::string to = msg->getHeader(HeaderCName::TO);
    msg->setHeader(HeaderCName::FROM, to);
    msg->setHeader(HeaderCName::TO, from);

    msg->setHeader(HeaderCName::CSEQ, "1 " + method);

    std::string via = msg->getHeader(HeaderCName::VIA);
    size_t pos = via.find(";branch=");
    if (pos == std::string::npos)
        via += ";branch=" + std::string(Common::randString().c_str());
    else
        via = via.substr(0, pos) + ";branch=z9hG4bK" + std::string(Common::randString().c_str());
    msg->setHeader(HeaderCName::VIA, via);

    return msg;
}

SipMessagePtr SipMessage::nextRequest(const SipMessagePtr &request, const std::string &method, const std::string &uri)
{
    SipMessageIPtr msg = SipMessageIPtr::dynamicCast(SipMessage::clone(request));
    msg->_request = true;
    msg->_method = method;
    msg->_uri = uri;

    int nextSeq = std::stoi(TokenParser(msg->getHeader(HeaderCName::CSEQ))[0]) + 1;
    msg->setHeader(HeaderCName::CSEQ, std::to_string(nextSeq) + " " + method);

    std::string via = msg->getHeader(HeaderCName::VIA);
    size_t pos = via.find(";branch=");
    if (pos == std::string::npos)
        via += ";branch=" + std::string(Common::randString().c_str());
    else
        via = via.substr(0, pos) + ";branch=z9hG4bK" + std::string(Common::randString().c_str());
    msg->setHeader(HeaderCName::VIA, via);

    return msg;
}

SipMessagePtr SipMessage::genResponse(const SipMessagePtr &request, int statusCode, const std::string &reasonPhrase)
{
    SipMessageIPtr response = SipMessageIPtr::dynamicCast(SipMessage::clone(request));

    response->_request = false;
    response->_statusCode = statusCode;
    response->_reasonPhrase = reasonPhrase;

    std::string to = response->getHeader(Header::TO);
    if (to.find(";tag=") == std::string::npos)
        response->setHeader(Header::TO, to + ";tag=" + std::string(Common::randString().c_str()));

    return response;
}

std::string SipMessage::genCallId(const std::string &sessId, const std::string &caller, const std::string &callee, const std::string &address)
{
    static unsigned int salt = 0;
    std::string callId = sessId + caller + callee + std::to_string(salt++);
    callId = std::string(Common::md5(callId.c_str()).c_str()) + "@" + address;
    return callId;
}

} // namespace SimpleSipSession
