//
// *****************************************************************************
// Copyright (c) 2023 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//
// Created on 2023/12/28 by <PERSON>
//

#pragma once

#include "Common/Util.h"

namespace SimpleSipSession
{

class SipTimer : virtual public Common::Shared
{
public:
    static unsigned int T1() { return _T1; }
    static unsigned int T2() { return _T2; }
    static unsigned int T4() { return _T4; }
    static void reset(int t1, int t2, int t4);

    static unsigned int TimerC() { return 180 * 1000; }

    // ict
    static unsigned int TimerA() { return _T1; }
    static unsigned int TimerB() { return 64 * _T1; }
    static unsigned int TimerD() { return 64 * _T1; }

    // nict
    static unsigned int TimerE() { return _T1; }
    static unsigned int TimerF() { return 64 * _T1; }
    static unsigned int TimerK() { return T4(); }

    // ist
    static unsigned int TimerG() { return _T1; }
    static unsigned int TimerH() { return 64 * _T1; }
    static unsigned int TimerI() { return T4(); }

    // nist
    static unsigned int TimerJ() { return T4(); }

private:
    static unsigned int _T1;
    static unsigned int _T2;
    static unsigned int _T4;

public:
    explicit SipTimer(const std::string &_name);
    ~SipTimer();

    void start(unsigned int expireMs);
    void stop();
    bool schd();

    std::string name;
    unsigned int _lastTicks;
    unsigned int _expireMs;
};

typedef Common::Handle<SipTimer> SipTimerPtr;

class SipTimerManager : virtual public Common::RecMutex
{
public:
    void startTimer(const std::string &name, unsigned int expireMs);
    void stopTimer(const std::string &name);

protected:
    void schd();
    virtual void onTimeout(const std::string &name) = 0;

protected:
    std::map<std::string, SipTimerPtr> _timers;
};

} // namespace SimpleSipSession