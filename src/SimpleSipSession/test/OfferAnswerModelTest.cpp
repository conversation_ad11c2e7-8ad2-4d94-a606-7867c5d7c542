//
// *****************************************************************************
// Copyright (c) 2025 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//

#include "gtest/gtest.h"

#include "SimpleSipSession/OfferAnswerModel.h"

static const std::string AudioSdp = "\
v=0\r\n\
o=- 423353741 423353741 IN IP4 *************\r\n\
s=-\r\n\
c=IN IP4 *************\r\n\
t=0 0\r\n\
m=audio 20476 RTP/AVP 0 8 103 101\r\n\
a=rtpmap:0 PCMU/8000\r\n\
a=rtpmap:8 PCMA/8000\r\n\
a=rtpmap:103 AMR/8000\r\n\
a=fmtp:103 mode-set=0,1,2,3,4,5,6;octet-align=1\r\n\
a=ptime:20\r\n\
a=rtpmap:101 telephone-event/8000\r\n\
a=fmtp:101 0-15\r\n\
a=sendrecv\r\n\
";

static const std::string VideoSdp = "\
v=0\r\n\
o=- 423353741 423353741 IN IP4 *************\r\n\
s=-\r\n\
c=IN IP4 *************\r\n\
t=0 0\r\n\
m=audio 20476 RTP/AVP 0 8 103 101\r\n\
a=rtpmap:0 PCMU/8000\r\n\
a=rtpmap:8 PCMA/8000\r\n\
a=rtpmap:103 AMR/8000\r\n\
a=fmtp:103 mode-set=0,1,2,3,4,5,6;octet-align=1\r\n\
a=ptime:20\r\n\
a=rtpmap:101 telephone-event/8000\r\n\
a=fmtp:101 0-15\r\n\
a=sendrecv\r\n\
m=video 20506 RTP/AVP 121\r\n\
a=rtpmap:121 H264/90000\r\n\
a=fmtp:121 profile-level-id=42801F; packetization-mode=1\r\n\
a=sendrecv\r\n\
";

TEST(OfferAnswerModelTest, AudioOffer)
{
    SimpleSipSession::OfferAnswerModel model;
    model.init(AudioSdp);
    std::string offer = model.genOffer();
    EXPECT_FALSE(offer.empty());
    std::cout << offer << std::endl;

    const std::string answer = "\
v=0\r\n\
o=- 1234 0 IN IP4 *************\r\n\
s=-\r\n\
c=IN IP4 *************\r\n\
t=0 0\r\n\
m=audio 10000 RTP/AVP 0 101\r\n\
a=rtpmap:0 PCMU/8000\r\n\
a=rtpmap:101 telephone-event/8000\r\n\
a=fmtp:101 0-15\r\n\
a=sendrecv\r\n\
";
    model.setAnswer(answer);
    std::string host;
    int port;
    model.getRemoteHostPort(true, host, port);
    EXPECT_EQ(host, "*************");
    EXPECT_EQ(port, 10000);
    std::string codec;
    int payloadType;
    model.getCodec(true, codec, payloadType);
    EXPECT_EQ(codec, "PCMU");
    EXPECT_EQ(payloadType, 0);
}

TEST(OfferAnswerModelTest, AudioAnswer)
{
    SimpleSipSession::OfferAnswerModel model;
    model.init(AudioSdp);

    const std::string offer = "\
v=0\r\n\
o=- 1234 0 IN IP4 *************\r\n\
s=-\r\n\
c=IN IP4 *************\r\n\
t=0 0\r\n\
m=audio 10000 RTP/AVP 0 101\r\n\
a=rtpmap:0 PCMU/8000\r\n\
a=rtpmap:101 telephone-event/8000\r\n\
a=fmtp:101 0-15\r\n\
a=sendrecv\r\n\
";
    std::string answer = model.genAnswer(offer);
    EXPECT_FALSE(answer.empty());
    std::cout << answer << std::endl;

    std::string host;
    int port;
    model.getRemoteHostPort(true, host, port);
    EXPECT_EQ(host, "*************");
    EXPECT_EQ(port, 10000);
    std::string codec;
    int payloadType;
    model.getCodec(true, codec, payloadType);
    EXPECT_EQ(codec, "PCMU");
    EXPECT_EQ(payloadType, 0);
}

TEST(OfferAnswerModelTest, VideoOffer)
{
    SimpleSipSession::OfferAnswerModel model;
    model.init(VideoSdp);
    std::string offer = model.genOffer();
    EXPECT_FALSE(offer.empty());
    std::cout << offer << std::endl;

    const std::string answer = "\
v=0\r\n\
o=- 1234 0 IN IP4 *************\r\n\
s=-\r\n\
c=IN IP4 *************\r\n\
t=0 0\r\n\
m=audio 10000 RTP/AVP 0 101\r\n\
a=rtpmap:0 PCMU/8000\r\n\
a=rtpmap:101 telephone-event/8000\r\n\
a=fmtp:101 0-15\r\n\
a=sendrecv\r\n\
m=video 20000 RTP/AVP 121\r\n\
a=rtpmap:121 H264/90000\r\n\
a=fmtp:121 profile-level-id=42801F; packetization-mode=1\r\n\
a=sendrecv\r\n\
";
    model.setAnswer(answer);
    std::string host;
    int port;
    model.getRemoteHostPort(true, host, port);
    EXPECT_EQ(host, "*************");
    EXPECT_EQ(port, 10000);
    std::string codec;
    int payloadType;
    model.getCodec(true, codec, payloadType);
    EXPECT_EQ(codec, "PCMU");
    EXPECT_EQ(payloadType, 0);

    model.getRemoteHostPort(false, host, port);
    EXPECT_EQ(host, "*************");
    EXPECT_EQ(port, 20000);
    model.getCodec(false, codec, payloadType);
    EXPECT_EQ(codec, "H264");
    EXPECT_EQ(payloadType, 121);
}

TEST(OfferAnswerModelTest, VideoAnswer)
{
    SimpleSipSession::OfferAnswerModel model;
    model.init(VideoSdp);

    const std::string offer = "\
v=0\r\n\
o=- 1234 0 IN IP4 *************\r\n\
s=-\r\n\
c=IN IP4 *************\r\n\
t=0 0\r\n\
m=audio 10000 RTP/AVP 0 101\r\n\
a=rtpmap:0 PCMU/8000\r\n\
a=rtpmap:101 telephone-event/8000\r\n\
a=fmtp:101 0-15\r\n\
a=sendrecv\r\n\
m=video 20000 RTP/AVP 121\r\n\
a=rtpmap:121 H264/90000\r\n\
a=fmtp:121 profile-level-id=42801F; packetization-mode=1\r\n\
a=sendrecv\r\n\
";
    std::string answer = model.genAnswer(offer);
    EXPECT_FALSE(answer.empty());
    std::cout << answer << std::endl;

    std::string host;
    int port;
    model.getRemoteHostPort(true, host, port);
    EXPECT_EQ(host, "*************");
    EXPECT_EQ(port, 10000);
    std::string codec;
    int payloadType;
    model.getCodec(true, codec, payloadType);
    EXPECT_EQ(codec, "PCMU");
    EXPECT_EQ(payloadType, 0);

    model.getRemoteHostPort(false, host, port);
    EXPECT_EQ(host, "*************");
    EXPECT_EQ(port, 20000);
    model.getCodec(false, codec, payloadType);
    EXPECT_EQ(codec, "H264");
    EXPECT_EQ(payloadType, 121);
}

TEST(OfferAnswerModelTest, VideoAnswerAudio)
{
    SimpleSipSession::OfferAnswerModel model;
    model.init(AudioSdp);

    const std::string offer = "\
v=0\r\n\
o=- 1234 0 IN IP4 *************\r\n\
s=-\r\n\
c=IN IP4 *************\r\n\
t=0 0\r\n\
m=audio 10000 RTP/AVP 0 101\r\n\
a=rtpmap:0 PCMU/8000\r\n\
a=rtpmap:101 telephone-event/8000\r\n\
a=fmtp:101 0-15\r\n\
a=sendrecv\r\n\
m=video 20000 RTP/AVP 121\r\n\
a=rtpmap:121 H264/90000\r\n\
a=fmtp:121 profile-level-id=42801F; packetization-mode=1\r\n\
a=sendrecv\r\n\
";
    std::string answer = model.genAnswer(offer);
    EXPECT_FALSE(answer.empty());
    std::cout << answer << std::endl;

    std::string host;
    int port;
    model.getRemoteHostPort(true, host, port);
    EXPECT_EQ(host, "*************");
    EXPECT_EQ(port, 10000);
    std::string codec;
    int payloadType;
    model.getCodec(true, codec, payloadType);
    EXPECT_EQ(codec, "PCMU");
    EXPECT_EQ(payloadType, 0);
}

TEST(OfferAnswerModelTest, AddVideo)
{
    SimpleSipSession::OfferAnswerModel model;
    model.init(AudioSdp);
    std::string offer = model.genOffer();
    EXPECT_FALSE(offer.empty());
    std::cout << offer << std::endl;

    const std::string answerAudio = "\
v=0\r\n\
o=- 1234 0 IN IP4 *************\r\n\
s=-\r\n\
c=IN IP4 *************\r\n\
t=0 0\r\n\
m=audio 10000 RTP/AVP 0 101\r\n\
a=rtpmap:0 PCMU/8000\r\n\
a=rtpmap:101 telephone-event/8000\r\n\
a=fmtp:101 0-15\r\n\
a=sendrecv\r\n\
m=video 20000 RTP/AVP 121\r\n\
a=rtpmap:121 H264/90000\r\n\
a=fmtp:121 profile-level-id=42801F; packetization-mode=1\r\n\
a=sendrecv\r\n\
";
    model.setAnswer(answerAudio);

    std::string host;
    int port;
    model.getRemoteHostPort(true, host, port);
    EXPECT_EQ(host, "*************");
    EXPECT_EQ(port, 10000);
    std::string codec;
    int payloadType;
    model.getCodec(true, codec, payloadType);
    EXPECT_EQ(codec, "PCMU");
    EXPECT_EQ(payloadType, 0);

    model.init(VideoSdp);
    offer = model.genOffer();
    EXPECT_FALSE(offer.empty());
    std::cout << offer << std::endl;

    const std::string answerVideo = "\
v=0\r\n\
o=- 1234 1 IN IP4 *************\r\n\
s=-\r\n\
c=IN IP4 *************\r\n\
t=0 0\r\n\
m=audio 10000 RTP/AVP 0 101\r\n\
a=rtpmap:0 PCMU/8000\r\n\
a=rtpmap:101 telephone-event/8000\r\n\
a=fmtp:101 0-15\r\n\
a=sendrecv\r\n\
m=video 20000 RTP/AVP 121\r\n\
a=rtpmap:121 H264/90000\r\n\
a=fmtp:121 profile-level-id=42801F; packetization-mode=1\r\n\
a=sendrecv\r\n\
";
    model.setAnswer(answerVideo);

    model.getRemoteHostPort(true, host, port);
    EXPECT_EQ(host, "*************");
    EXPECT_EQ(port, 10000);
    model.getCodec(true, codec, payloadType);
    EXPECT_EQ(codec, "PCMU");
    EXPECT_EQ(payloadType, 0);

    model.getRemoteHostPort(false, host, port);
    EXPECT_EQ(host, "*************");
    EXPECT_EQ(port, 20000);
    model.getCodec(false, codec, payloadType);
    EXPECT_EQ(codec, "H264");
    EXPECT_EQ(payloadType, 121);
}
