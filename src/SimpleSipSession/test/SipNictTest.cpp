//
// *****************************************************************************
// Copyright (c) 2023 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//
// Created on 2023/7/7 by <PERSON>
//

#include "gmock/gmock.h"
#include "gtest/gtest.h"

#include "Common/Util.h"
#include "SimpleSipSession/mock/SipTransactionMock.h"
#include "SimpleSipSession/mock/SipTransportMock.h"

#include "SimpleSipSession/SipMessage.h"
#include "SimpleSipSession/base/SipTimer.h"
#include "SimpleSipSession/trans/SipNict.h"
#include "SimpleSipSession/trans/SipTransaction.h"

using ::testing::_;
using ::testing::An;
using ::testing::StrEq;
using ::testing::Return;
using ::testing::NotNull;

MATCHER_P(CStrEq, element, "")
{
    return arg == element;
}

class SipNictTest : public ::testing::Test
{
public:
    void SetUp()
    {
        JsmClient::setLogLevel(3);
        SimpleSipSession::SipTimer::reset(50, 400, 500);

        _driver = SimpleSipSession::SipDriver::create();
        transport = new SimpleSipSession::SipTransportMock();
        listener = new SimpleSipSession::SipTransactionListenerMock();
    }

    void TearDown()
    {
        _driver->close();
        listener = 0;
    }

    SimpleSipSession::SipDriverPtr _driver;
    SimpleSipSession::SipTransportMockPtr transport;
    SimpleSipSession::SipTransactionListenerMockPtr listener;

    static constexpr const char *OPTIONS = "\
OPTIONS sip:**************:5060 SIP/2.0\r\n\
To: <sip:123456@**************>\r\n\
From: <sip:123456@**************>;tag=Hc3OAGi-I8-V00NKl8y321z1n1\r\n\
Call-ID: d108OG1B30000Isl8x321u3Ida@************\r\n\
CSeq: 1 OPTIONS\r\n\
Max-Forwards: 70\r\n\
User-Agent: MSP/1.0\r\n\
Via: SIP/2.0/UDP ************:5068;branch=z9hG4bKdbs0OAGy-I8-V00NKl8z321f3I;rport\r\n\
Content-Length: 0\r\n\r\n)";

    static constexpr const char *OPTIONS_100 = "\
SIP/2.0 100 Trying\r\n\
Via: SIP/2.0/UDP ************:5068;branch=z9hG4bK-t4-H20OAGy-I8-V00NKl8C321;rport=5068\r\n\
Call-ID: d108OG1B30000Isl8x321u3Ida@************\r\n\
From: \"057156912366\"<sip:057156912366@**************:5060>;tag=rBibI3OAGi-I8-V00NKl8B321J\r\n\
To: \"15757857320\"<sip:15757857320@**************:5060>\r\n\
CSeq: 1 OPTIONS\r\n\
Content-Length: 0\r\n\r\n";

    static constexpr const char *OPTIONS_200 = "\
SIP/2.0 200 OK\r\n\
Via: SIP/2.0/UDP ************:5068;branch=z9hG4bKdbs0OAGy-I8-V00NKl8z321f3I;rport=5068\r\n\
Call-ID: d108OG1B30000Isl8x321u3Ida@************\r\n\
From: <sip:123456@**************>;tag=Hc3OAGi-I8-V00NKl8y321z1n1\r\n\
To: <sip:123456@**************>;tag=c1f8ec3ceb.rqoryyozrorrx-vqwy\r\n\
CSeq: 1 OPTIONS\r\n\
Accept: application/sdp,application/pkcs7-mime,application/bear,multipart/signed,multipart/mixed,multipart/alternative\r\n\
Accept-Language: en\r\n\
Allow: INVITE,ACK,CANCEL,OPTIONS,BYE,UPDATE,SUBSCRIBE,REFER,PRACK,REGISTER,INFO,PUBLISH,MESSAGE,NOTIFY\r\n\
Contact: <sip:**************:5060;transport=udp;Hpt=9068_16;CxtId=3;TRC=ffffffff-ffffffff>\r\n\
Supported: timer\r\n\
Content-Length: 0\r\n\r\n";

};

TEST_F(SipNictTest, Normal)
{
    EXPECT_CALL(*(listener.get()), onSucceed(_)).Times(1);
    EXPECT_CALL(*(listener.get()), sendMessage(An<const SimpleSipSession::SipMessagePtr &>())).Times(5);

    EXPECT_CALL(*(transport.get()), localHostPort()).WillRepeatedly(Return(SimpleSipSession::SipHostPort("*************", 5060)));
    EXPECT_CALL(*(transport.get()), remoteHostPort()).WillRepeatedly(Return(SimpleSipSession::SipHostPort("********", 5060)));

    // test start
    SimpleSipSession::SipMessagePtr msg100 = SimpleSipSession::SipMessage::create(OPTIONS_100);
    ASSERT_TRUE(msg100 != nullptr);
    SimpleSipSession::SipMessagePtr msg200 = SimpleSipSession::SipMessage::create(OPTIONS_200);
    ASSERT_TRUE(msg200 != nullptr);

    SimpleSipSession::SipTransactionPtr trans = SimpleSipSession::SipNict::create(SimpleSipSession::SipTransactionConfig(_driver, transport, listener), SimpleSipSession::SipMessage::create(OPTIONS));
    ASSERT_TRUE(trans != nullptr);

    Common::sleep(1000);
    trans->recvMessage(msg100);

    bool closed;
    for (int i = 0; i < 10; i++)
    {
        if (i < 5)
            trans->recvMessage(msg200);
        Common::sleep(50);
        closed = (trans->getState() == SimpleSipSession::SipTransaction::StateCompleted);
        if (closed)
            break;
    }

    EXPECT_TRUE(closed);

    for (int i = 0; i < 10; i++)
    {
        Common::sleep(50);
        closed = (trans->getState() == SimpleSipSession::SipTransaction::StateTermed);
        if (closed)
            break;
    }

    EXPECT_TRUE(closed);
}
