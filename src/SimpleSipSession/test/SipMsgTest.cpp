//
// *****************************************************************************
// Copyright (c) 2023 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//
// Created on 2023/7/7 by <PERSON>
//

#include "gtest/gtest.h"
#include "Common/Util.h"
#include "SimpleSipSession/SipMessage.h"
#include "JsmLog/JsmLog.h"
#include "SimpleSipSession/SipTypes.h"

TEST(SipMsg, Options)
{
    const char *OPTIONS = "\
OPTIONS sip:**************:5060 SIP/2.0\r\n\
To: <sip:123456@**************>\r\n\
From: <sip:123456@**************>;tag=Hc3OAGi-I8-V00NKl8y321z1n1\r\n\
Call-ID: d108OG1B30000Isl8x321u3Ida@************\r\n\
CSeq: 1 OPTIONS\r\n\
Max-Forwards: 70\r\n\
User-Agent: MSP/1.0\r\n\
Via: SIP/2.0/UDP ************:5068;branch=z9hG4bKdbs0OAGy-I8-V00NKl8z321f3I;rport\r\n\
Content-Length: 0\r\n\r\n)";

    JsmClient::setLogLevel(3);
    SimpleSipSession::SipMessagePtr msg = SimpleSipSession::SipMessage::create(OPTIONS);
    EXPECT_TRUE(msg != nullptr);
}

TEST(SipMsg, Invite)
{
    const char *INVITE = "\
INVITE sip:+<EMAIL> SIP/2.0\r\n\
Via: SIP/2.0/UDP **************:5060;branch=z9hG4bKqi1i0q2207biyyb02yd8ui1gi;Role=3;Hpt=9098_16;TRC=ffffffff-173e\r\n\
Record-Route: <sip:**************:5060;transport=udp;lr;Hpt=9098_16;CxtId=4;TRC=ffffffff-173e;X-HwB2bUaCookie=21082>\r\n\
Call-ID: isbcVS8Ncc1gB21Ivf8O6yW1_w..\r\n\
From: <tel:+8613396690013>;tag=938e9d7deb.rqoryyozrorrx-vqxy\r\n\
To: <tel:057456156027;phone-context=ims.mnc011.mcc460.3gppnetwork.org>\r\n\
CSeq: 1 INVITE\r\n\
Allow: INVITE,ACK,CANCEL,OPTIONS,BYE,UPDATE,SUBSCRIBE,REFER,PRACK,REGISTER,INFO,PUBLISH,MESSAGE,NOTIFY\r\n\
Contact: <sip:p65560t1700125284m497460c27054s3@**************:5060;transport=udp;Hpt=9098_16;CxtId=4;TRC=ffffffff-173e>;+g.3gpp.accesstype=\"cellular\";+g.3gpp.icsi-ref=\"urn%3Aurn-7%3A3gpp-service.ims.icsi.mmtel\"\r\n\
Max-Forwards: 69\r\n\
Supported: histinfo,100rel,timer\r\n\
User-Agent: Ericsson MTAS - CXP2010134/1 R29C04\r\n\
Allow-Events: talk,hold,conference,presence,as-feature-event,dialog,line-seize,call-info,sla,include-session-description,presence.winfo,message-summary,refer\r\n\
Session-Expires: 1800;refresher=uac\r\n\
Min-SE: 90\r\n\
P-Asserted-Identity: <tel:+8613396690013>\r\n\
Privacy: none\r\n\
P-Visited-Network-ID: psbc14ber.sh.hz.zj.ims.mnc011.mcc460.3gppnetwork.org\r\n\
P-Charging-Function-Addresses: ccf=ccftst1.chinamobile.com;ccf=ccftst2.chinamobile.com\r\n\
P-Charging-Vector: icid-value=sg2.psbc14ber.hz.zj.nod-1700-125284-247583-530272079;orig-ioi=ericsson.com;term-ioi=ge.chinamobile.com\r\n\
P-Early-Media: supported\r\n\
P-Preferred-Service: urn:urn-7:3gpp-service.ims.icsi.mmtel\r\n\
P-Ericsson.Invocation-History: as=SCCAS,foiwf,MMTelAS;sescase=orig;regstate=reg\r\n\
Session-ID: 25f9edff5ee56df3e4b93153e345f441\r\n\
X-Ecgi: 46011e6b84d9da06\r\n\
Accept-Contact: *;+g.3gpp.icsi-ref=\"urn%3Aurn-7%3A3gpp-service.ims.icsi.mmtel\"\r\n\
Content-Length: 421\r\n\
Content-Type: application/sdp\r\n\
Content-Disposition: session\r\n\
\r\n\
v=0\r\n\
o=- 510421884917503877 0 IN IP4 **************\r\n\
s=SBC call\r\n\
c=IN IP4 **************\r\n\
t=0 0\r\n\
m=audio 11488 RTP/AVP 8 101 102 111 103\r\n\
a=rtpmap:8 PCMA/8000\r\n\
a=rtpmap:101 telephone-event/8000\r\n\
a=fmtp:101 0-15\r\n\
a=ptime:20\r\n\
a=rtpmap:102 AMR-WB/16000\r\n\
a=fmtp:102 mode-change-capability=2;max-red=0\r\n\
a=rtpmap:111 AMR/8000\r\n\
a=fmtp:111 mode-change-capability=2;max-red=0\r\n\
a=rtpmap:103 telephone-event/16000\r\n\
a=fmtp:103 0-15\r\n\
\r\n\
";

    JsmClient::setLogLevel(3);
    SimpleSipSession::SipMessagePtr msg = SimpleSipSession::SipMessage::create(INVITE);
    EXPECT_TRUE(msg != nullptr);
    std::cout << msg->logInfo() << std::endl;
}

TEST(SipMsg, Invite100)
{
    const char *INVITE_100 = "\
SIP/2.0 100 Trying\r\n\
To: <tel:057456156027;phone-context=ims.mnc011.mcc460.3gppnetwork.org>\r\n\
From: <tel:+8613396690013>;tag=938e9d7deb.rqoryyozrorrx-vqxy\r\n\
CSeq: 1 INVITE\r\n\
Call-ID: isbcVS8Ncc1gB21Ivf8O6yW1_w..\r\n\
Via: SIP/2.0/UDP **************:5060;branch=z9hG4bKqi1i0q2207biyyb02yd8ui1gi;Role=3;Hpt=9098_16;TRC=ffffffff-173e;received=**************\r\n\
Content-Length: 0\r\n\
\r\n\
";

    JsmClient::setLogLevel(3);
    SimpleSipSession::SipMessagePtr msg = SimpleSipSession::SipMessage::create(INVITE_100);
    EXPECT_TRUE(msg != nullptr);
    std::cout << msg->logInfo() << std::endl;
}

TEST(SipMsg, Invite200)
{
    const char *INVITE_200 = "\
SIP/2.0 200 OK\r\n\
Via: SIP/2.0/UDP **************:10469;branch=z9hG4bKG321Mt4-Hs0aqOyk4u_V00hwHp;rport=10469\r\n\
Record-Route: <sip:**************:5060;transport=udp;lr;Hpt=8e98_116;CxtId=3;TRC=ffffffff-ffffffff;X-HwB2bUaCookie=5022>\r\n\
Call-ID: 21jt4-Ga1C0Kak610000IfGpE3@************\r\n\
From: \"057456156027\"<sip:057456156027@**************:10469>;tag=321MtNtbc3aqOik4u_V00hwHpF\r\n\
To: \"15058086097\"<sip:15058086097@**************:5060>;tag=5ef84630eb.rqoryyozrorry-vqxq\r\n\
CSeq: 1 INVITE\r\n\
Allow: INVITE,ACK,CANCEL,OPTIONS,BYE,UPDATE,SUBSCRIBE,REFER,PRACK,REGISTER,INFO,PUBLISH,MESSAGE,NOTIFY\r\n\
Contact: <sip:b672752e-57d3-4c57-ac31-268abb25330d@**************:5060;transport=udp;zte-did=4-5-20481-3146-12-1184-65535;Hpt=8e98_16;CxtId=3;TRC=ffffffff-ffffffff>;video;+g.3gpp.mid-call;+g.3gpp.srvcc-alerting;audio;+g.3gpp.icsi-ref=\"urn%%3Aurn-7%%3A3gpp-service.ims.icsi.mmtel\"\r\n\
Require: timer\r\n\
Supported: 100rel,timer\r\n\
User-Agent: Xiaomi_MI 8_Android10_125\r\n\
Session-Expires: 1800;refresher=uas\r\n\
P-Asserted-Identity: <sip:+<EMAIL>>,<tel:+8615058086097>\r\n\
P-Access-Network-Info: 3GPP-E-UTRAN;utran-cell-id-3gpp=460005847FCAED87;network-provided;sbc-domain=sbc.0574.010.zj.chinamobile.com;ue-ip=[2409:812a:2448:668e:2d44:7ebb:3498:a3d9];ue-port=41393\r\n\
P-Charging-Vector: icid-value=\"fca16a56a3436102e108389865cd0117.3908933592.40614241.108\";term-ioi=chinamobile;pdngw=*************;eps-info=\"eps-item=1;eps-sig=no;ecid=178F1E10;flow-id=({1,1},{1,2}),eps-item=2;eps-sig=no;ecid=178F1E11;flow-id=({2,1},{2,2})\"\r\n\
Content-Length: 691\r\n\
Content-Type: application/sdp\r\n\
Content-Disposition: session\r\n\
\r\n\
v=0\r\n\
o=- 3132276630625616419 0 IN IP4 **************\r\n\
s=SBC call\r\n\
c=IN IP4 **************\r\n\
b=AS:848\r\n\
b=RS:8600\r\n\
b=RR:8000\r\n\
t=0 0\r\n\
m=audio 52860 RTP/AVP 104 109\r\n\
b=AS:41\r\n\
b=RS:600\r\n\
b=RR:2000\r\n\
a=rtpmap:104 AMR-WB/16000/1\r\n\
a=fmtp:104 octet-align=1;mode-change-capability=2;max-red=0\r\n\
a=rtpmap:109 telephone-event/16000\r\n\
a=fmtp:109 0-15\r\n\
a=sendrecv\r\n\
a=maxptime:240\r\n\
a=ptime:20\r\n\
m=video 32208 RTP/AVPF 121\r\n\
b=AS:807\r\n\
b=RS:8000\r\n\
b=RR:6000\r\n\
a=rtpmap:121 H264/90000\r\n\
a=fmtp:121 profile-level-id=42C00D;sprop-parameter-sets=Z0LADdoEgtoBtChNQA==,aM4G4g==;packetization-mode=1;sar-understood=16;sar-supported=1\r\n\
a=rtcp-fb:* nack\r\n\
a=rtcp-fb:* ccm fir\r\n\
a=recvonly\r\n\
a=extmap:1 urn:3gpp:video-orientation\r\n\
\r\n\
";

    JsmClient::setLogLevel(3);
    SimpleSipSession::SipMessagePtr msg = SimpleSipSession::SipMessage::create(INVITE_200);
    EXPECT_TRUE(msg != nullptr);
    int statusCode;
    std::string reasonPhrase;
    EXPECT_TRUE(msg->getStatusLine(statusCode, reasonPhrase));
    EXPECT_EQ(statusCode, 200);
    EXPECT_EQ(reasonPhrase, "OK");
    std::cout << msg->logInfo() << std::endl;
}

TEST(SipMsg, Invite481)
{
    const char *INVITE_481 = "\
SIP/2.0 481 Call Leg/Transaction Does Not Exist\r\n\
To: \"+8657427836857\"<sip:+<EMAIL>>;tag=H3uAKik_udV007f8xx321Aa28b\r\n\
From: \"15058086097\"<tel:15058086097>;tag=ztesipQ6uuWZtR*2-2-20481*fdjf.2\r\n\
CSeq: 1001 INVITE\r\n\
Call-ID: 17425898a1045a5beb3353c0a86527f9719876000@**************:39825\r\n\
Via: SIP/2.0/UDP ************:5070;branch=z9hG4bK-*3*-1-d26e11848de392d73b12taN1;received=::\r\n\
Server: MSP/1.0\r\n\
Content-Length: 0\r\n\
\r\n\
";

    JsmClient::setLogLevel(3);
    SimpleSipSession::SipMessagePtr msg = SimpleSipSession::SipMessage::create(INVITE_481);
    EXPECT_TRUE(msg != nullptr);
    int statusCode;
    std::string reasonPhrase;
    EXPECT_TRUE(msg->getStatusLine(statusCode, reasonPhrase));
    EXPECT_EQ(statusCode, 481);
    EXPECT_EQ(reasonPhrase, "Call Leg/Transaction Does Not Exist");
    EXPECT_EQ(msg->getHeader("To"), "\"+8657427836857\"<sip:+<EMAIL>>;tag=H3uAKik_udV007f8xx321Aa28b");
    EXPECT_EQ(msg->getHeader("From"), "\"15058086097\"<tel:15058086097>;tag=ztesipQ6uuWZtR*2-2-20481*fdjf.2");
    EXPECT_EQ(msg->getHeader("CSeq"), "1001 INVITE");
    EXPECT_EQ(msg->getHeader("Call-ID"), "17425898a1045a5beb3353c0a86527f9719876000@**************:39825");
    EXPECT_EQ(msg->getHeader("Via"), "SIP/2.0/UDP ************:5070;branch=z9hG4bK-*3*-1-d26e11848de392d73b12taN1;received=::");
    EXPECT_EQ(msg->getHeader("Server"), "MSP/1.0");
    EXPECT_EQ(msg->getHeader("Content-Length"), "0");
    std::cout << msg->logInfo() << std::endl;
}