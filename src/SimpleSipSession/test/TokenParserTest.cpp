//
// *****************************************************************************
// Copyright (c) 2023 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//
// Created on 2023/11/16 by <PERSON>
//

#include "gtest/gtest.h"
#include "SimpleSipSession/base/SipMessageI.h"

TEST(<PERSON><PERSON><PERSON><PERSON><PERSON>, Cseq)
{
    SimpleSipSession::TokenParser tp("1 OPTIONS");
    EXPECT_TRUE(tp[0] == "1");
    EXPECT_TRUE(tp[1] == "OPTIONS");
    EXPECT_TRUE(tp[2] == "");
}