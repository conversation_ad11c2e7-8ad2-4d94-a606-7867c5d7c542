//
// *****************************************************************************
// Copyright (c) 2023 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//
// Created on 2023/7/7 by <PERSON>
//

#include "gmock/gmock.h"
#include "gtest/gtest.h"

#include "Common/Util.h"
#include "SimpleSipSession/SipDriver.h"
#include "SimpleSipSession/mock/SipTransactionMock.h"

#include "SimpleSipSession/SipMessage.h"
#include "SimpleSipSession/base/SipTimer.h"
#include "SimpleSipSession/trans/SipTransaction.h"
#include "SimpleSipSession/trans/SipTransactionI.h"

using ::testing::_;
using ::testing::An;
using ::testing::NotNull;
using ::testing::StrEq;

MATCHER_P(CStrEq, element, "")
{
    return arg == element;
}

class SipTransTest : public ::testing::Test
{
public:
    void SetUp()
    {
        JsmClient::setLogLevel(3);
        SimpleSipSession::SipTimer::reset(50, 400, 500);

        listener = new SimpleSipSession::SipTransactionListenerMock();
    }

    void TearDown()
    {
        listener = 0;
    }

    SimpleSipSession::SipTransactionListenerMockPtr listener;

    static constexpr const char *OPTIONS = "OPTIONS sip:**************:5060 SIP/2.0\r\n\
To: <sip:123456@**************>\r\n\
From: <sip:123456@**************>;tag=Hc3OAGi-I8-V00NKl8y321z1n1\r\n\
Call-ID: d108OG1B30000Isl8x321u3Ida@************\r\n\
CSeq: 1 OPTIONS\r\n\
Max-Forwards: 70\r\n\
User-Agent: MSP/1.0\r\n\
Via: SIP/2.0/UDP ************:5068;branch=z9hG4bKdbs0OAGy-I8-V00NKl8z321f3I;rport\r\n\
Content-Length: 0\r\n\r\n)";
};

TEST_F(SipTransTest, Normal)
{
    SimpleSipSession::SipTransactionIMockPtr trans = new SimpleSipSession::SipTransactionIMock(SimpleSipSession::SipTransactionConfig(0, 0, listener));
    EXPECT_TRUE(trans != nullptr);

    EXPECT_CALL(*(trans.get()), onTimeout(CStrEq("Timer50"))).Times(3);
    EXPECT_CALL(*(trans.get()), onTimeout(CStrEq("Timer70"))).Times(2);

    trans->startTimer("Timer50", 50);
    trans->startTimer("Timer70", 70);

    for (int i = 0; i < 16; i++)
    {
        Common::sleep(10);
        trans->schd();
    }

    trans->stopTimer("Timer50");
    trans->stopTimer("Timer70");
    for (int i = 0; i < 10; i++)
    {
        Common::sleep(10);
        trans->schd();
    }
}