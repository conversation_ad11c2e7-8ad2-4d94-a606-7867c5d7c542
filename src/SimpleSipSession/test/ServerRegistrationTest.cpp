//
// *****************************************************************************
// Copyright (c) 2025 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//

#include "gmock/gmock.h"
#include "gtest/gtest.h"

#include "SimpleSipSession/mock/SipServerRegistrationMock.h"
#include "SimpleSipSession/SipServerRegistration.h"
#include "SimpleSipSession/SipDriver.h"
#include "SimpleSipSession/SipMessage.h"
#include "SimpleSipSession/SipFlow.h"
#include "JsmLog/JsmLog.h"

using ::testing::_;
using ::testing::An;
using ::testing::Invoke;
using ::testing::Return;

class ServerRegistrationTest : public ::testing::Test
{
public:
    void SetUp() override
    {
        JsmClient::setLogLevel(5);
        _driver = SimpleSipSession::SipDriver::create();
        _listener = new SimpleSipSession::ServerRegistrationListenerMock();
        _connection = new SimpleSipSession::ServerRegistrationConnectionMock();
        _serverRegistration = SimpleSipSession::ServerRegistration::create(_listener, _driver);
        ASSERT_TRUE(_serverRegistration != nullptr);
    }

    void TearDown() override
    {
        _serverRegistration->close();
        _driver->close();
    }

    SimpleSipSession::SipDriverPtr _driver;
    SimpleSipSession::ServerRegistrationPtr _serverRegistration;
    SimpleSipSession::ServerRegistrationConnectionMockPtr _connection;
    SimpleSipSession::ServerRegistrationListenerMockPtr _listener;
};

TEST_F(ServerRegistrationTest, Normal)
{
    EXPECT_CALL(*_listener, onRegisterRequest("bob", "sip:bob@*************:5060")).Times(2).WillRepeatedly(Return(SimpleSipSession::StatusCode::OK));
    EXPECT_CALL(*_connection, localHostPort()).WillRepeatedly(Return(SimpleSipSession::SipHostPort("*************", 5060)));
    EXPECT_CALL(*_connection, remoteHostPort()).WillRepeatedly(Return(SimpleSipSession::SipHostPort("*************", 5060)));
    EXPECT_CALL(*_connection, onSendMessage(_)).Times(3).WillRepeatedly(Invoke([](const SimpleSipSession::SipMessagePtr &message) {
        std::cout << "onSendMessage:" << message->encode() << std::endl;
        EXPECT_FALSE(message->isRequest());
        std::string method;
        message->getMethod(method);
        EXPECT_EQ(method, "REGISTER");
        int statusCode;
        std::string reasonPhrase;
        EXPECT_TRUE(message->getStatusLine(statusCode, reasonPhrase));
        EXPECT_EQ(statusCode, 200);
        EXPECT_EQ(reasonPhrase, "OK");
    }));

    SimpleSipSession::SipMessagePtr message = SimpleSipSession::SipMessage::create("\
REGISTER sip:*************:5060 SIP/2.0\r\n\
Via: SIP/2.0/UDP *************:5060;branch=z9hG4bKdbs0OAGy-I8-V00NKl8z321f3I;rport\r\n\
Max-Forwards: 70\r\n\
From: <sip:bob@*************:5060>;tag=Hc3OAGi-I8-V00NKl8y321z1n1\r\n\
To: <sip:bob@*************:5060>\r\n\
Call-ID: d108OG1B30000Isl8x321u3Ida@************\r\n\
CSeq: 1 REGISTER\r\n\
User-Agent: MSP/1.0\r\n\
Authorization: Digest username=\"bob\", realm=\"*************\", uri=\"sip:*************:5060\"\r\n\
Expires: 10\r\n\
Content-Length: 0\r\n\r\n");

    EXPECT_TRUE(_serverRegistration->recv(message, _connection));
    EXPECT_TRUE(_serverRegistration->getConnection("bob") == _connection);
    EXPECT_TRUE(_serverRegistration->getConnection("alice") == nullptr);

    Common::sleep(500);
    EXPECT_TRUE(_serverRegistration->recv(message, _connection));

    Common::sleep(5000);

    SimpleSipSession::SipMessagePtr message2 = SimpleSipSession::SipMessage::create("\
REGISTER sip:*************:5060 SIP/2.0\r\n\
Via: SIP/2.0/UDP *************:5060;branch=z9hG4bKdbs0OAGy-I8-V00NKl8z321f3I2;rport\r\n\
Max-Forwards: 70\r\n\
From: <sip:bob@*************:5060>;tag=Hc3OAGi-I8-V00NKl8y321z1n2\r\n\
To: <sip:bob@*************:5060>\r\n\
Call-ID: d108OG1B30000Isl8x321u3Ida@************\r\n\
CSeq: 2 REGISTER\r\n\
User-Agent: MSP/1.0\r\n\
Authorization: Digest username=\"bob\", realm=\"*************\", uri=\"sip:*************:5060\"\r\n\
Expires: 10\r\n\
Content-Length: 0\r\n\r\n");

    EXPECT_TRUE(_serverRegistration->recv(message2, _connection));
    EXPECT_TRUE(_serverRegistration->getConnection("bob") == _connection);
    EXPECT_TRUE(_serverRegistration->getConnection("alice") == nullptr);

    Common::sleep(9000);
    EXPECT_TRUE(_serverRegistration->getConnection("bob") == _connection);

    Common::sleep(1100);
    EXPECT_TRUE(_serverRegistration->getConnection("bob") == nullptr);
}

TEST_F(ServerRegistrationTest, Timeout)
{
    EXPECT_CALL(*_listener, onRegisterRequest("bob", "sip:bob@*************:5060")).WillOnce(Return(SimpleSipSession::StatusCode::OK));
    EXPECT_CALL(*_connection, localHostPort()).WillRepeatedly(Return(SimpleSipSession::SipHostPort("*************", 5060)));
    EXPECT_CALL(*_connection, remoteHostPort()).WillRepeatedly(Return(SimpleSipSession::SipHostPort("*************", 5060)));
    EXPECT_CALL(*_connection, onSendMessage(_)).Times(1).WillRepeatedly(Invoke([](const SimpleSipSession::SipMessagePtr &message) {
        std::cout << "onSendMessage:" << message->encode() << std::endl;
        EXPECT_FALSE(message->isRequest());
        std::string method;
        message->getMethod(method);
        EXPECT_EQ(method, "REGISTER");
        int statusCode;
        std::string reasonPhrase;
        EXPECT_TRUE(message->getStatusLine(statusCode, reasonPhrase));
        EXPECT_EQ(statusCode, 200);
        EXPECT_EQ(reasonPhrase, "OK");
    }));

    SimpleSipSession::SipMessagePtr message = SimpleSipSession::SipMessage::create("\
REGISTER sip:*************:5060 SIP/2.0\r\n\
Via: SIP/2.0/UDP *************:5060;branch=z9hG4bKdbs0OAGy-I8-V00NKl8z321f3I;rport\r\n\
Max-Forwards: 70\r\n\
From: <sip:bob@*************:5060>;tag=Hc3OAGi-I8-V00NKl8y321z1n1\r\n\
To: <sip:bob@*************:5060>\r\n\
Call-ID: d108OG1B30000Isl8x321u3Ida@************\r\n\
CSeq: 1 REGISTER\r\n\
User-Agent: MSP/1.0\r\n\
Authorization: Digest username=\"bob\", realm=\"*************\", uri=\"sip:*************:5060\"\r\n\
Expires: 10\r\n\
Content-Length: 0\r\n\r\n");

    EXPECT_TRUE(_serverRegistration->recv(message, _connection));
    EXPECT_TRUE(_serverRegistration->getConnection("bob") == _connection);

    std::vector<SimpleSipSession::ServerRegistration::BindingInfo> bindingInfos = _serverRegistration->stats();
    EXPECT_EQ(bindingInfos.size(), 1);
    EXPECT_EQ(bindingInfos[0].username, "bob");
    EXPECT_EQ(bindingInfos[0].address, "sip:bob@*************:5060");
    EXPECT_NEAR(bindingInfos[0].expires, 10, 1);
    for (auto &bindingInfo : bindingInfos)
        std::cout << "bindingInfo:" << bindingInfo.username << " " << bindingInfo.address << " " << bindingInfo.expires << std::endl;

    Common::sleep(8800);
    bindingInfos = _serverRegistration->stats();
    EXPECT_EQ(bindingInfos.size(), 1);
    EXPECT_EQ(bindingInfos[0].username, "bob");
    EXPECT_EQ(bindingInfos[0].address, "sip:bob@*************:5060");
    EXPECT_NEAR(bindingInfos[0].expires, 1, 1);

    for (auto &bindingInfo : bindingInfos)
        std::cout << "bindingInfo:" << bindingInfo.username << " " << bindingInfo.address << " " << bindingInfo.expires << std::endl;

    Common::sleep(1300);
    bindingInfos = _serverRegistration->stats();
    EXPECT_TRUE(_serverRegistration->getConnection("bob") == nullptr);
    bindingInfos = _serverRegistration->stats();
    EXPECT_EQ(bindingInfos.size(), 0);
    for (auto &bindingInfo : bindingInfos)
        std::cout << "bindingInfo:" << bindingInfo.username << " " << bindingInfo.address << " " << bindingInfo.expires << std::endl;
}

TEST_F(ServerRegistrationTest, Timeout3)
{
    EXPECT_CALL(*_listener, onRegisterRequest(_, _)).Times(3).WillRepeatedly(Return(SimpleSipSession::StatusCode::OK));
    EXPECT_CALL(*_connection, localHostPort()).WillRepeatedly(Return(SimpleSipSession::SipHostPort("*************", 5060)));
    EXPECT_CALL(*_connection, remoteHostPort()).WillRepeatedly(Return(SimpleSipSession::SipHostPort("*************", 5060)));
    EXPECT_CALL(*_connection, onSendMessage(_)).Times(3).WillRepeatedly(Invoke([](const SimpleSipSession::SipMessagePtr &message) {
        std::cout << "onSendMessage:" << message->encode() << std::endl;
        EXPECT_FALSE(message->isRequest());
        std::string method;
        message->getMethod(method);
        EXPECT_EQ(method, "REGISTER");
        int statusCode;
        std::string reasonPhrase;
        EXPECT_TRUE(message->getStatusLine(statusCode, reasonPhrase));
        EXPECT_EQ(statusCode, 200);
        EXPECT_EQ(reasonPhrase, "OK");
    }));

    SimpleSipSession::SipMessagePtr alice_register = SimpleSipSession::SipMessage::create("\
REGISTER sip:*************:5060 SIP/2.0\r\n\
Via: SIP/2.0/UDP *************:5060;branch=z9hG4bKdbs0OAGy-I8-V00NKl8z321f3I0;rport\r\n\
Max-Forwards: 70\r\n\
From: <sip:alice@*************:5060>;tag=Hc3OAGi-I8-V00NKl8y321z1n10\r\n\
To: <sip:alice@*************:5060>\r\n\
Call-ID: d108OG1B30000Isl8x321u3Ida@************\r\n\
CSeq: 1 REGISTER\r\n\
User-Agent: MSP/1.0\r\n\
Authorization: Digest username=\"alice\", realm=\"*************\", uri=\"sip:*************:5060\"\r\n\
Expires: 10\r\n\
Content-Length: 0\r\n\r\n");

    SimpleSipSession::SipMessagePtr bob_register = SimpleSipSession::SipMessage::create("\
REGISTER sip:*************:5060 SIP/2.0\r\n\
Via: SIP/2.0/UDP *************:5060;branch=z9hG4bKdbs0OAGy-I8-V00NKl8z321f3I;rport\r\n\
Max-Forwards: 70\r\n\
From: <sip:bob@*************:5060>;tag=Hc3OAGi-I8-V00NKl8y321z1n1\r\n\
To: <sip:bob@*************:5060>\r\n\
Call-ID: d108OG1B30000Isl8x321u3Ida@************\r\n\
CSeq: 1 REGISTER\r\n\
User-Agent: MSP/1.0\r\n\
Authorization: Digest username=\"bob\", realm=\"*************\", uri=\"sip:*************:5060\"\r\n\
Expires: 15\r\n\
Content-Length: 0\r\n\r\n");

    SimpleSipSession::SipMessagePtr carol_register = SimpleSipSession::SipMessage::create("\
REGISTER sip:*************:5060 SIP/2.0\r\n\
Via: SIP/2.0/UDP *************:5060;branch=z9hG4bKdbs0OAGy-I8-V00NKl8z321f3I2;rport\r\n\
Max-Forwards: 70\r\n\
From: <sip:carol@*************:5060>;tag=Hc3OAGi-I8-V00NKl8y321z1n12\r\n\
To: <sip:carol@*************:5060>\r\n\
Call-ID: d108OG1B30000Isl8x321u3Ida@************\r\n\
CSeq: 1 REGISTER\r\n\
User-Agent: MSP/1.0\r\n\
Authorization: Digest username=\"carol\", realm=\"*************\", uri=\"sip:*************:5060\"\r\n\
Expires: 20\r\n\
Content-Length: 0\r\n\r\n");

    EXPECT_TRUE(_serverRegistration->recv(alice_register, _connection));
    EXPECT_TRUE(_serverRegistration->getConnection("alice") == _connection);
    EXPECT_TRUE(_serverRegistration->recv(bob_register, _connection));
    EXPECT_TRUE(_serverRegistration->getConnection("bob") == _connection);
    EXPECT_TRUE(_serverRegistration->recv(carol_register, _connection));
    EXPECT_TRUE(_serverRegistration->getConnection("carol") == _connection);

    std::vector<SimpleSipSession::ServerRegistration::BindingInfo> bindingInfos = _serverRegistration->stats();
    EXPECT_EQ(bindingInfos.size(), 3);
    for (auto &bindingInfo : bindingInfos)
        std::cout << "bindingInfo:" << bindingInfo.username << " " << bindingInfo.address << " " << bindingInfo.expires << std::endl;

    Common::sleep(10100);
    EXPECT_TRUE(_serverRegistration->getConnection("alice") == nullptr);
    bindingInfos = _serverRegistration->stats();
    EXPECT_EQ(bindingInfos.size(), 2);
    for (auto &bindingInfo : bindingInfos)
        std::cout << "bindingInfo:" << bindingInfo.username << " " << bindingInfo.address << " " << bindingInfo.expires << std::endl;

    Common::sleep(5000);
    EXPECT_TRUE(_serverRegistration->getConnection("bob") == nullptr);
    bindingInfos = _serverRegistration->stats();
    EXPECT_EQ(bindingInfos.size(), 1);
    for (auto &bindingInfo : bindingInfos)
        std::cout << "bindingInfo:" << bindingInfo.username << " " << bindingInfo.address << " " << bindingInfo.expires << std::endl;

    Common::sleep(5000);
    EXPECT_TRUE(_serverRegistration->getConnection("carol") == nullptr);
    bindingInfos = _serverRegistration->stats();
    EXPECT_EQ(bindingInfos.size(), 0);
    for (auto &bindingInfo : bindingInfos)
        std::cout << "bindingInfo:" << bindingInfo.username << " " << bindingInfo.address << " " << bindingInfo.expires << std::endl;
}
