//
// *****************************************************************************
// Copyright (c) 2023 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//
// Created on 2023/7/7 by <PERSON>
//

#include "gmock/gmock.h"
#include "gtest/gtest.h"

#include "Common/Util.h"
#include "SimpleSipSession/SipTransport.h"
#include "SimpleSipSession/mock/SipTransportMock.h"
#include "SimpleSipSession/mock/SipTransactionMock.h"

#include "SimpleSipSession/SipTypes.h"
#include "SimpleSipSession/SipMessage.h"
#include "SimpleSipSession/base/SipTimer.h"
#include "SimpleSipSession/trans/SipNist.h"
#include "SimpleSipSession/trans/SipTransaction.h"

using ::testing::_;
using ::testing::An;
using ::testing::StrEq;
using ::testing::Return;
using ::testing::NotNull;

MATCHER_P(CStrEq, element, "")
{
    return arg == element;
}

class SipNistTest : public ::testing::Test
{
public:
    void SetUp()
    {
        JsmClient::setLogLevel(3);
        SimpleSipSession::SipTimer::reset(50, 400, 500);

        _driver = SimpleSipSession::SipDriver::create();
        transport = new SimpleSipSession::SipTransportMock();
        listener = new SimpleSipSession::SipTransactionListenerMock();
    }

    void TearDown()
    {
        _driver->close();
        listener = 0;
    }

    SimpleSipSession::SipDriverPtr _driver;
    SimpleSipSession::SipTransportMockPtr transport;
    SimpleSipSession::SipTransactionListenerMockPtr listener;

    static constexpr const char *OPTIONS = "OPTIONS sip:**************:5060 SIP/2.0\r\n\
To: <sip:123456@**************>\r\n\
From: <sip:123456@**************>;tag=Hc3OAGi-I8-V00NKl8y321z1n1\r\n\
Call-ID: d108OG1B30000Isl8x321u3Ida@************\r\n\
CSeq: 1 OPTIONS\r\n\
Max-Forwards: 70\r\n\
User-Agent: MSP/1.0\r\n\
Via: SIP/2.0/UDP ************:5068;branch=z9hG4bKdbs0OAGy-I8-V00NKl8z321f3I;rport\r\n\
Content-Length: 0\r\n\r\n)";

};

TEST_F(SipNistTest, Normal)
{
    EXPECT_CALL(*(listener.get()), onRequest(_)).Times(1);
    EXPECT_CALL(*(listener.get()), sendMessage(An<const SimpleSipSession::SipMessagePtr &>())).Times(5);

    EXPECT_CALL(*(transport.get()), localHostPort()).WillRepeatedly(Return(SimpleSipSession::SipHostPort("*************", 5060)));
    EXPECT_CALL(*(transport.get()), remoteHostPort()).WillRepeatedly(Return(SimpleSipSession::SipHostPort("********", 5060)));

    // test start
    SimpleSipSession::SipMessagePtr msg = SimpleSipSession::SipMessage::create(OPTIONS);
    ASSERT_TRUE(msg != nullptr);

    SimpleSipSession::SipTransactionPtr trans = SimpleSipSession::SipNist::create(SimpleSipSession::SipTransactionConfig(_driver, transport, listener));
    ASSERT_TRUE(trans != nullptr);
    trans->recvMessage(msg);

    for (int i = 0; i < 3; i++)
    {
        Common::sleep(500 << i);
        trans->recvMessage(msg);
    }

    EXPECT_TRUE(trans->response(200, SimpleSipSession::ReasonPharse::OK));
    trans->recvMessage(msg);

    bool closed;
    for (int i = 0; i < 10; i++)
    {
        Common::sleep(50);
        closed = (trans->getState() == SimpleSipSession::SipTransaction::StateCompleted);
        if (closed)
            break;
    }

    EXPECT_TRUE(closed);

    for (int i = 0; i < 10; i++)
    {
        Common::sleep(50);
        closed = (trans->getState() == SimpleSipSession::SipTransaction::StateTermed);
        if (closed)
            break;
    }

    EXPECT_TRUE(closed);
}
