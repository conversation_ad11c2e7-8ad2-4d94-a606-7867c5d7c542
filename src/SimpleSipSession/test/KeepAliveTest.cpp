//
// *****************************************************************************
// Copyright (c) 2023 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//
// Created on 2023/12/23 by <PERSON>
//

#include "gmock/gmock.h"
#include "gtest/gtest.h"

#include "JsmLog/JsmLog.h"
#include "SimpleSipSession/mock/SipFlowMock.h"
#include "SimpleSipSession/mock/SipTransportMock.h"
#include "SimpleSipSession/SipDriver.h"
#include "SimpleSipSession/SipMessage.h"
#include "SimpleSipSession/SipFlowKeepAlive.h"

using ::testing::An;
using ::testing::Invoke;
using ::testing::Return;

class KeepAliveTest : public ::testing::Test
{
public:
    virtual void SetUp()
    {
        JsmClient::setLogLevel(3);
        _driver = SimpleSipSession::SipDriver::create();
        _transport = new SimpleSipSession::SipTransportMock();
        _listener = new SimpleSipSession::SipFlowListenerMock();
    }

    virtual void TearDown()
    {
        _driver->close();
    }

    SimpleSipSession::SipDriverPtr _driver;
    SimpleSipSession::SipTransportMockPtr _transport;
    SimpleSipSession::SipFlowListenerMockPtr _listener;
};

TEST_F(KeepAliveTest, testKeepAlive)
{
    EXPECT_CALL(*(_transport.get()), localHostPort()).WillRepeatedly(Return(SimpleSipSession::SipHostPort("*************", 5060)));
    EXPECT_CALL(*(_transport.get()), remoteHostPort()).WillRepeatedly(Return(SimpleSipSession::SipHostPort("********", 5060)));

    SimpleSipSession::KeepAlive::FlowPtr flow = SimpleSipSession::KeepAlive::create(_driver, _transport, _listener, 10);

    EXPECT_CALL(*(_listener.get()), onSendMessage(An<const SimpleSipSession::SipMessagePtr &>())).Times(5).WillRepeatedly(Invoke([&](const SimpleSipSession::SipMessagePtr &messge) {
        std::cout << messge->data() << std::endl;
        SimpleSipSession::SipMessagePtr response = SimpleSipSession::SipMessage::genResponse(SimpleSipSession::SipMessage::create(messge->data()), 200, "OK");
        std::cout << response->encode() << std::endl;
        flow->recvMessage(response);
    }));

    Common::sleep(32000);
    flow->trigger();
    Common::sleep(1000);
}
