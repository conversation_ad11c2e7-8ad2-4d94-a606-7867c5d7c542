//
// *****************************************************************************
// Copyright (c) 2023 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//
// Created on 2023/12/10 by <PERSON>
//

#include "gtest/gtest.h"

#include "SimpleSipSession/SdpMessage.h"

TEST(SdpMsg, T1)
{
    const char *msg = "\
v=0\r\n\
o=- 3455926086 0 IN IP4 ***********\r\n\
s=-\r\n\
c=IN IP4 ***********\r\n\
t=0 0\r\n\
m=audio 39478 RTP/AVP 0 8 101\r\n\
a=rtpmap:101 telephone-event/8000\r\n\
a=ptime:20\r\n\
a=maxptime:400\r\n\
m=video 39712 RTP/AVP 125 121\r\n\
i=3gppCvo\r\n\
a=rtpmap:125 H264-SVC/90000\r\n\
a=fmtp:125 profile-level-id=42800C; packetization-mode=1\r\n\
a=rtpmap:121 H264/90000\r\n\
a=fmtp:121 profile-level-id=42800C; packetization-mode=1\r\n\
a=extmap:1 urn:3gpp:video-orientation\r\n\
\r\n\
";

    SimpleSipSession::SdpSess sdp;
    ASSERT_TRUE(sdp.from_string(msg));
    std::string out;
    ASSERT_TRUE(sdp.to_string(out));
    ASSERT_FALSE(out.empty());
    std::cout << out << "-------------------" << std::endl;
    ASSERT_TRUE(out == msg);
}

TEST(SdpMsg, T2)
{
    const char *msg = "\
v=0\r\n\
o=- 1437078850 1437078850 IN IP4 **************\r\n\
s=-\r\n\
c=IN IP4 **************\r\n\
t=0 0\r\n\
m=audio 10460 RTP/AVP 104 0 8 103 101 109\r\n\
a=rtpmap:104 AMR-WB/16000\r\n\
a=fmtp:104 octet-align=1\r\n\
a=rtpmap:0 PCMU/8000\r\n\
a=rtpmap:8 PCMA/8000\r\n\
a=rtpmap:103 AMR/8000\r\n\
a=fmtp:103 mode-set=0,1,2,3,4,5,6;octet-align=1\r\n\
a=rtpmap:101 telephone-event/8000\r\n\
a=fmtp:101 0-15\r\n\
a=rtpmap:109 telephone-event/16000\r\n\
a=fmtp:109 0-15\r\n\
a=ptime:20\r\n\
a=sendrecv\r\n\
m=video 10464 RTP/AVPF 121\r\n\
i=3gppCvo\r\n\
a=rtpmap:121 H264/90000\r\n\
a=fmtp:121 profile-level-id=42800D; packetization-mode=1\r\n\
a=rtcp-fb:* ccm fir\r\n\
a=rtcp-fb:* nack\r\n\
a=rtcp-fb:* ack rpsi\r\n\
a=extmap:1 urn:3gpp:video-orientation\r\n\
a=sendonly\r\n\
\r\n\
";

    SimpleSipSession::SdpSess sdp;
    ASSERT_TRUE(sdp.from_string(msg));
    std::string out;
    ASSERT_TRUE(sdp.to_string(out));
    ASSERT_FALSE(out.empty());
    std::cout << out << "-------------------" << std::endl;
    ASSERT_TRUE(out == msg);

}