//
// *****************************************************************************
// Copyright (c) 2023 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//
// Created on 2023/12/23 by <PERSON>
//

#include "gmock/gmock.h"
#include "gtest/gtest.h"

#include "JsmLog/JsmLog.h"
#include "SimpleSipSession/SipTypes.h"
#include "SimpleSipSession/mock/SipFlowMock.h"
#include "SimpleSipSession/mock/SipTransportMock.h"
#include "SimpleSipSession/SipDriver.h"
#include "SimpleSipSession/SipMessage.h"
#include "SimpleSipSession/SipFlow.h"

using ::testing::An;
using ::testing::Invoke;
using ::testing::Return;

class RegistrationTest : public ::testing::Test
{
public:
    virtual void SetUp()
    {
        JsmClient::setLogLevel(3);
        _driver = SimpleSipSession::SipDriver::create();
        _transport = new SimpleSipSession::SipTransportMock();
        _listener = new SimpleSipSession::RegistrationListenerMock();
    }

    virtual void TearDown()
    {
        _driver->close();
    }

    SimpleSipSession::SipDriverPtr _driver;
    SimpleSipSession::SipTransportMockPtr _transport;
    SimpleSipSession::RegistrationListenerMockPtr _listener;
};

TEST_F(RegistrationTest, Normal)
{
    EXPECT_CALL(*(_transport.get()), localHostPort()).WillRepeatedly(Return(SimpleSipSession::SipHostPort("*************", 5060)));
    EXPECT_CALL(*(_transport.get()), remoteHostPort()).WillRepeatedly(Return(SimpleSipSession::SipHostPort("********", 5060)));

    SimpleSipSession::Registration::FlowPtr flow = SimpleSipSession::Registration::create(_driver, _transport, _listener, SimpleSipSession::Registration::Config("500", "500", "500", "pbx.juphoon.com", 3600));

    EXPECT_CALL(*(_listener.get()), onSendMessage(An<const SimpleSipSession::SipMessagePtr &>())).Times(3).WillRepeatedly(Invoke([&](const SimpleSipSession::SipMessagePtr &message) {
        std::cout << message->data() << std::endl;

        SimpleSipSession::SipMessagePtr response;
        if (message->getHeader(SimpleSipSession::Header::CSEQ) == "1 REGISTER")
        {
            response = SimpleSipSession::SipMessage::genResponse(SimpleSipSession::SipMessage::create(message->data()), 401, SimpleSipSession::ReasonPharse::UNAUTHORIZED);
            response->addHeader(SimpleSipSession::Header::WWW_AUTHENTICATE, "Digest realm=\"newrocktech\",nonce=\"66C5AC241DD499F958FE4ACB4E4514C13168F31F\",algorithm=MD5");
        }
        else if (message->getHeader(SimpleSipSession::Header::CSEQ) == "2 REGISTER")
        {
            EXPECT_TRUE(message->getHeader(SimpleSipSession::Header::AUTHORIZATION).find("response=\"6a352f0d02d19a0b0edbf653f655fe00\"") != std::string::npos);
            response = SimpleSipSession::SipMessage::genResponse(SimpleSipSession::SipMessage::create(message->data()), 200, SimpleSipSession::ReasonPharse::OK);
            response->setHeader(SimpleSipSession::Header::EXPIRES, "60");
        }
        else
        {
            EXPECT_TRUE(message->getHeader(SimpleSipSession::Header::AUTHORIZATION).find("response=\"6a352f0d02d19a0b0edbf653f655fe00\"") != std::string::npos);
            response = SimpleSipSession::SipMessage::genResponse(SimpleSipSession::SipMessage::create(message->data()), 200, SimpleSipSession::ReasonPharse::OK);
            response->setHeader(SimpleSipSession::Header::EXPIRES, "0");
        }

        std::cout << response->encode() << std::endl;
        flow->recvMessage(response);
    }));

    EXPECT_CALL(*(_listener.get()), onRegistered()).Times(1);
    EXPECT_CALL(*(_listener.get()), onUnregistered()).Times(1);

    Common::sleep(10000);

    flow->unregister();
    Common::sleep(10000);
}

TEST_F(RegistrationTest, QopAuth)
{
    EXPECT_CALL(*(_transport.get()), localHostPort()).WillRepeatedly(Return(SimpleSipSession::SipHostPort("*************", 5060)));
    EXPECT_CALL(*(_transport.get()), remoteHostPort()).WillRepeatedly(Return(SimpleSipSession::SipHostPort("********", 5060)));

    SimpleSipSession::Registration::FlowPtr flow = SimpleSipSession::Registration::create(_driver, _transport, _listener, SimpleSipSession::Registration::Config("+8657427836857", "+<EMAIL>", "89aHZrreLd1R", "ngv.ims.chinaunicom.cn", 3600));

    EXPECT_CALL(*(_listener.get()), onSendMessage(An<const SimpleSipSession::SipMessagePtr &>())).Times(3).WillRepeatedly(Invoke([&](const SimpleSipSession::SipMessagePtr &message) {
        std::cout << message->data() << std::endl;

        SimpleSipSession::SipMessagePtr response;
        if (message->getHeader(SimpleSipSession::Header::CSEQ) == "1 REGISTER")
        {
            response = SimpleSipSession::SipMessage::genResponse(SimpleSipSession::SipMessage::create(message->data()), 401, SimpleSipSession::ReasonPharse::UNAUTHORIZED);
            response->addHeader(SimpleSipSession::Header::WWW_AUTHENTICATE, R"(Digest realm="ims.mnc001.mcc460.3gppnetwork.org",domain="sip:scscf02.cd.ngv.ims.chinaunicom.cn",nonce="e9dc3e58282f0ceb179281ff3e3fc587",opaque="enRlX29wYXF1ZV8wMDA0MDAwMw==",stale=TRUE,algorithm=MD5,qop="auth")");
        }
        else if (message->getHeader(SimpleSipSession::Header::CSEQ) == "2 REGISTER")
        {
            EXPECT_TRUE(message->getHeader(SimpleSipSession::Header::AUTHORIZATION).find("response=\"31b07c7ccf0c6c0d2391596a38ba4c45\"") != std::string::npos);
            response = SimpleSipSession::SipMessage::genResponse(SimpleSipSession::SipMessage::create(message->data()), 200, SimpleSipSession::ReasonPharse::OK);
            response->setHeader(SimpleSipSession::Header::EXPIRES, "10");
            response->setHeader(SimpleSipSession::Header::AUTHENTICATION_INFO, R"(nextnonce="4fe4b56fdd96f0c678b78b3ce1f286fb",qop=auth,rspauth="02b9ae0ac5e3ed716178070c688f229f",cnonce="01a62a6501a62a45",nc=00000001)");
        }
        else if (message->getHeader(SimpleSipSession::Header::CSEQ) == "3 REGISTER")
        {
            EXPECT_TRUE(message->getHeader(SimpleSipSession::Header::AUTHORIZATION).find("response=\"71adef9e34c3ded508ee2e43ee41e66e\"") != std::string::npos);
            response = SimpleSipSession::SipMessage::genResponse(SimpleSipSession::SipMessage::create(message->data()), 200, SimpleSipSession::ReasonPharse::OK);
            response->setHeader(SimpleSipSession::Header::EXPIRES, "0");
        }
        else
        {
            response = SimpleSipSession::SipMessage::genResponse(SimpleSipSession::SipMessage::create(message->data()), 200, SimpleSipSession::ReasonPharse::OK);
            response->setHeader(SimpleSipSession::Header::EXPIRES, "0");
        }

        std::cout << response->encode() << std::endl;
        flow->recvMessage(response);
    }));

    EXPECT_CALL(*(_listener.get()), onRegistered()).Times(1);
    EXPECT_CALL(*(_listener.get()), onUnregistered()).Times(1);
    Common::sleep(10000);
}