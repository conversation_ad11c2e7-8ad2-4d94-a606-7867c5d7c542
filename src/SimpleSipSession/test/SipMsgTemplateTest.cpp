//
// *****************************************************************************
// Copyright (c) 2023 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//
// Created on 2023/12/28 by <PERSON>
//

#include "gmock/gmock.h"
#include "gtest/gtest.h"

#include "JsmLog/JsmLog.h"
#include "SimpleSipSession/SipMessage.h"
#include "SimpleSipSession/mock/SipMessageMock.h"

using ::testing::_;
using ::testing::StrEq;
using ::testing::Return;

TEST(SipMsgTemplate, Options)
{
    const char *OPTIONS = "\
OPTIONS sip:${REMOTE_HOST_PORT} SIP/2.0\r\n\
From: <sip:${LOCAL_HOST_PORT}>;tag=Hc3OAGi-I8-V00NKl8y321z1n1\r\n\
To: <sip:${REMOTE_HOST_PORT}>${TO_PARAMS}\r\n\
Call-ID: d108OG1B30000Isl8x321u3Ida@************\r\n\
CSeq: 1 OPTIONS\r\n\
Via: SIP/2.0/UDP ************:5068;branch=z9hG4bKdbs0OAGy-I8-V00NKl8z321f3I;rport\r\n\
Max-Forwards: 70\r\n\
User-Agent: MSP/1.0\r\n\
Content-Length: 0\r\n\r\n";

    JsmClient::setLogLevel(4);
    SimpleSipSession::SipMessagePtr msg = SimpleSipSession::SipMessage::create(OPTIONS);
    EXPECT_TRUE(msg != nullptr);

    SimpleSipSession::SipMessageContextMock context;
    EXPECT_CALL(context, getValue(StrEq("REMOTE_HOST_PORT"), _)).Times(2).WillRepeatedly([](const std::string &, std::string &value) {
        value = "********:5060";
        return true;
    });
    EXPECT_CALL(context, getValue(StrEq("LOCAL_HOST_PORT"), _)).WillOnce([](const std::string &, std::string &value) {
        value = "*************:5060";
        return true;
    });
    EXPECT_CALL(context, getValue(StrEq("TO_PARAMS"), _)).WillOnce([](const std::string &, std::string &value) {
        value = "";
        return true;
    });

    std::string result = msg->encode(&context);
    std::cout << result << std::endl;
    EXPECT_STREQ(result.c_str(), "\
OPTIONS sip:********:5060 SIP/2.0\r\n\
From: <sip:*************:5060>;tag=Hc3OAGi-I8-V00NKl8y321z1n1\r\n\
To: <sip:********:5060>\r\n\
Call-ID: d108OG1B30000Isl8x321u3Ida@************\r\n\
CSeq: 1 OPTIONS\r\n\
Via: SIP/2.0/UDP ************:5068;branch=z9hG4bKdbs0OAGy-I8-V00NKl8z321f3I;rport\r\n\
Max-Forwards: 70\r\n\
User-Agent: MSP/1.0\r\n\
Content-Length: 0\r\n\r\n");
}