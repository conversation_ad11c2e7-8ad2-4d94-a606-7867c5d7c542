//
// *****************************************************************************
// Copyright (c) 2025 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//

#pragma once

#include "SipFlow.h"

namespace SimpleSipSession
{

class Registration
{
public:
    struct Config
    {
        Config(const std::string &_username, const std::string &_authname, const std::string &_password, const std::string &_realm, int _expireSeconds = 3600)
            : username(_username)
            , authname(_authname)
            , password(_password)
            , realm(_realm)
            , expireSeconds(_expireSeconds)
        {
        }

        std::string username;
        std::string authname;
        std::string password;
        std::string realm;
        int expireSeconds;
    };

    class Listener : public SipFlowListener
    {
    public:
        virtual void onRegistered() = 0;
        virtual void onRegisterFailed() = 0;
        virtual void onUnregistered() = 0;
    };

    typedef Common::Handle<Listener> ListenerPtr;

    class Flow : public SipFlow
    {
    public:
        virtual void unregister() = 0;
    };

    typedef Common::Handle<Flow> FlowPtr;

    static FlowPtr create(const SipDriverPtr &driver, const SipTransportPtr &transport, const ListenerPtr &listener, const Config &config);
};

} // namespace SimpleSipSession
