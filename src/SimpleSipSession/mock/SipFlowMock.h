//
// *****************************************************************************
// Copyright (c) 2023 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//
// Created on 2023/12/23 by <PERSON>
//

#pragma once

#include "gmock/gmock.h"

#include "SimpleSipSession/SipFlowRegistration.h"

namespace SimpleSipSession
{

class SipFlowListenerMock : public SipFlowListener
{
public:
    MOCK_METHOD(void, onError, (), (override));
    MOCK_METHOD(void, onSendMessage, (const SipMessagePtr &message), (override));
};

typedef Common::Handle<SipFlowListenerMock> SipFlowListenerMockPtr;

class RegistrationListenerMock : public Registration::Listener
{
public:
    MOCK_METHOD(void, onError, (), (override));
    MOCK_METHOD(void, onSendMessage, (const SipMessagePtr &message), (override));
    MOCK_METHOD(void, onRegistered, (), (override));
    MOCK_METHOD(void, onRegisterFailed, (), (override));
    MOCK_METHOD(void, onUnregistered, (), (override));
};

typedef Common::Handle<RegistrationListenerMock> RegistrationListenerMockPtr;

} // namespace SimpleSipSession