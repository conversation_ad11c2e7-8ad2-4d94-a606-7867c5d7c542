//
// *****************************************************************************
// Copyright (c) 2025 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//

#pragma once

#include "gmock/gmock.h"

#include "SimpleSipSession/SipServerRegistration.h"

namespace SimpleSipSession
{

class ServerRegistrationListenerMock : public ServerRegistration::Listener
{
public:
    MOCK_METHOD(void, onError, (const Common::Error &error), (override));
    MOCK_METHOD(int, onRegisterRequest, (const std::string &username, const std::string &uri), (override));
};

typedef Common::Handle<ServerRegistrationListenerMock> ServerRegistrationListenerMockPtr;

class ServerRegistrationConnectionMock : public ServerRegistration::Connection
{
public:
    MOCK_METHOD(std::string, protocol, (), (override));
    MOCK_METHOD(SipHostPort, localHostPort, (), (override));
    MOCK_METHOD(SipHostPort, remoteHostPort, (), (override));
    MOCK_METHOD(void, onSendMessage, (const SipMessagePtr &message), (override));
};

typedef Common::Handle<ServerRegistrationConnectionMock> ServerRegistrationConnectionMockPtr;

class ServerRegistrationMock : public ServerRegistration
{
public:
    MOCK_METHOD(void, close, (), (override));
    MOCK_METHOD(std::vector<ServerRegistration::BindingInfo>, stats, (), (override));
    MOCK_METHOD(bool, recv, (const SipMessagePtr &message, const ServerRegistration::ConnectionPtr &connection), (override));
    MOCK_METHOD(ServerRegistration::ConnectionPtr, getConnection, (const std::string &username), (override));
};

typedef Common::Handle<ServerRegistrationMock> ServerRegistrationMockPtr;

} // namespace SimpleSipSession