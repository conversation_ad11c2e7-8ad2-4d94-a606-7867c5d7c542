//
// *****************************************************************************
// Copyright (c) 2023 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//
// Created on 2023/7/7 by <PERSON>
//

#pragma once

#include "gmock/gmock.h"

#include "SimpleSipSession/trans/SipTransaction.h"
#include "SimpleSipSession/trans/SipTransactionI.h"

namespace SimpleSipSession
{

class SipTransactionListenerMock : public SipTransactionListener
{
public:
    MOCK_METHOD(bool, getValue, (const std::string &key, std::string &value), (override));

    MOCK_METHOD(void, onError, (const Common::Error &error), (override));

    MOCK_METHOD(void, sendMessage, (const SipMessagePtr &message), (override));

    MOCK_METHOD(void, onSucceed, (const SipMessagePtr &message), (override));
    MOCK_METHOD(void, onFailed, (const SipMessagePtr &message), (override));

    MOCK_METHOD(void, onRequest, (const SipMessagePtr &message), (override));
    MOCK_METHOD(void, onResponse, (const SipMessagePtr &message), (override));
};

typedef Common::Handle<SipTransactionListenerMock> SipTransactionListenerMockPtr;

class SipTransactionIMock : public SipTransactionI
{
public:
    SipTransactionIMock(const SipTransactionConfig &config)
        : SipTransactionI(config)
    {
    }

    MOCK_METHOD(enum Type, type, (), (override));
    MOCK_METHOD(std::string, name, (), (override));
    MOCK_METHOD(bool, recvMessage, (const SimpleSipSession::SipMessagePtr &message), (override));

    MOCK_METHOD(void, onTimeout, (const std::string &name), (override));
};

typedef Common::Handle<SipTransactionIMock> SipTransactionIMockPtr;

} // namespace SimpleSipSession