//
// *****************************************************************************
// Copyright (c) 2023 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//
// Created on 2023/7/7 by <PERSON>
//

#pragma once

#include "SimpleSipSession/SipMessage.h"
#include "gmock/gmock.h"

namespace SimpleSipSession
{

class SipMessageContextMock : public SipMessageContext
{
public:
    MOCK_METHOD(bool, getValue, (const std::string &key, std::string &value), (override));
};

class SipMessageMock : public SipMessage
{
public:
    MOCK_METHOD(bool, isRequest, (), (override));
    MOCK_METHOD(bool, getRequestLine, (std::string & method, std::string &uri), (override));
    MOCK_METHOD(bool, getStatusLine, (int &statusCode, std::string &reasonPhrase), (override));

    MOCK_METHOD(std::string, getHeader, (const std::string &name), (override));
    MOCK_METHOD(std::string, getBody, (), (override));
};

typedef Common::Handle<SipMessageMock> SipMessageMockPtr;

} // namespace SimpleSipSession