//
// *****************************************************************************
// Copyright (c) 2023 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//
// Created on 2023/7/7 by <PERSON>
//

#pragma once

#include "gmock/gmock.h"
#include "SimpleSipSession/SipTransport.h"

namespace SimpleSipSession
{

class SipTransportMock : public SipTransport
{
public:
    MOCK_METHOD(std::string, protocol, (), (override));
    MOCK_METHOD(SipHostPort, localHostPort, (), (override));
    MOCK_METHOD(SipHostPort, remoteHostPort, (), (override));
};

typedef Common::Handle<SipTransportMock> SipTransportMockPtr;

} // namespace SimpleSipSession