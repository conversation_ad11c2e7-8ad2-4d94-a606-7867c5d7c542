//
// *****************************************************************************
// Copyright (c) 2025 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//

#pragma once

#include "Common/Util.h"
#include "Common/Common.h"
#include "SipDriver.h"
#include "SipMessage.h"
#include "SipTransport.h"
#include "SipTypes.h"

namespace SimpleSipSession
{

class ServerRegistration;
typedef Common::Handle<ServerRegistration> ServerRegistrationPtr;

class ServerRegistration : virtual public Common::Shared
{
public:
    class Listener : virtual public Common::Shared
    {
    public:
        virtual void onError(const Common::Error &error) = 0;
        virtual int onRegisterRequest(const std::string &username, const std::string &uri) { return StatusCode::OK; };
    };

    typedef Common::Handle<Listener> ListenerPtr;

    class Connection : public SipTransport
    {
    public:
        virtual void onSendMessage(const SipMessagePtr &message) = 0;
    };

    typedef Common::Handle<Connection> ConnectionPtr;

    struct BindingInfo
    {
        BindingInfo(const std::string &username, const std::string &address, int expires)
            : username(username)
            , address(address)
            , expires(expires)
        {
        }

        std::string username;
        std::string address;
        int expires;
    };

public:
    static ServerRegistrationPtr create(const ListenerPtr &listener, const SipDriverPtr &driver);

    virtual void close() = 0;
    virtual std::vector<BindingInfo> stats() = 0;

    virtual bool recv(const SipMessagePtr &message, const ConnectionPtr &connection) = 0;
    virtual ConnectionPtr getConnection(const std::string &usernameOfUri) = 0;
};

} // namespace SimpleSipSession
