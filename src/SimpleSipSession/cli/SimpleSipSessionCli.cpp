//
// *****************************************************************************
// Copyright (c) 2024 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//
// Created on 2024/1/7 by <PERSON>
//

#include "CliMain/CliMain.h"
#include "Common/Common.h"
#include "Common/Net.h"
#include "Common/Util.h"
#include "MpCall/MpCallPub.h"
#include "SimpleSipSession/SipDriver.h"
#include "SimpleSipSession/SipMessage.h"
#include "SimpleSipSession/SipFlowRegistration.h"
#include "SimpleSipSession/SipTransport.h"
#include "clipp/clipp.hpp"
#include <cstdlib>
#include <iterator>
#include <string>
#include <sys/stat.h>
#include <sys/types.h>
#include <unistd.h>

class SimpleSipSessionCli : public ::Cli::Cli, public CLIpp::Shell, public Common::NetReceiver, public SimpleSipSession::SipTransport, public SimpleSipSession::Registration::Listener
{
public:
    SimpleSipSessionCli()
    {
    }

    bool setup() override
    {
        AddFunction("init", SimpleSipSessionCli::init, "init <user> <server> [password=xxx] [realm=xxx] [expires=xxx]");
        AddFunction("call", SimpleSipSessionCli::call, "call <callee>");
        AddFunction("term", SimpleSipSessionCli::term, "term");

        std::cout << R"(
Welcome to SimpleSipSessionCli program

Usage:
        )" << std::endl;
        DisplayUsage();

        _sipDriver = SimpleSipSession::SipDriver::create();

        return true;
    }

    void teardown() override
    {
        _sipDriver->close();
        _sipDriver = 0;

        if (_sender)
        {
            _sender->close();
            _sender = 0;
        }
    }

    int onRun() override
    {
        Run();
        return 0;
    }

    virtual void recv(const unsigned char *data, int dataLen) override
    {
        std::cout << std::string((const char *)data, dataLen) << std::endl;
        SimpleSipSession::SipMessagePtr message = SimpleSipSession::SipMessage::create(std::string((const char *)data, dataLen));
        _registration->recvMessage(message);
    }

    virtual std::string protocol() override
    {
        return "udp";
    }

    virtual SimpleSipSession::SipHostPort localHostPort() override
    {
        if (!_sender)
            return SimpleSipSession::SipHostPort();

        Common::String host;
        int port;
        _sender->getLocal(host, port);
        return SimpleSipSession::SipHostPort(host.c_str(), port);
    }

    virtual SimpleSipSession::SipHostPort remoteHostPort() override
    {
        if (!_sender)
            return SimpleSipSession::SipHostPort();

        Common::String host;
        int port;
        _sender->getRemote(host, port);
        return SimpleSipSession::SipHostPort(host.c_str(), port);
    }

    virtual void onError() override
    {

    }

    virtual void onSendMessage(const SimpleSipSession::SipMessagePtr &message) override
    {
        if (_sender)
        {
            std::cout << message->data() << std::endl;
            _sender->send((const unsigned char *)message->data().c_str(), message->data().size());
        }
    }

    virtual void onRegistered() override
    {
        std::cout << "Registered" << std::endl;
    }

    virtual void onRegisterFailed() override
    {
        std::cout << "Register failed" << std::endl;
    }

    virtual void onUnregistered() override
    {
        std::cout << "Unregistered" << std::endl;
    }

#define SHELL_FUNCTION(__class, __name, __count, __usage)        \
    static void __name(CLIpp::Shell *s, int argc, string args[]) \
    {                                                            \
        if (argc < __count)                                      \
        {                                                        \
            std::cerr << __usage << std::endl;                   \
            return;                                              \
        }                                                        \
        dynamic_cast<__class *>(s)->__name(argc, args);          \
    }                                                            \
    void __name(int argc, string args[])

    SHELL_FUNCTION(SimpleSipSessionCli, init, 3, "init <user> <server> [password=xxx] [realm=xxx] [expires=xxx]")
    {
        _user = args[1];
        _server = args[2];

        int expires = 3600;
        for (int i = 3; i < argc; i++)
        {
            if (args[i].find("password=") == 0)
                _password = args[i].substr(9);
            else if (args[i].find("realm=") == 0)
                _realm = args[i].substr(6);
            else if (args[i].find("expires=") == 0)
                expires = std::stoi(args[i].substr(8));
        }

        if (_realm.empty())
            _realm = _server;

        std::string remoteHost = _server;
        int remotePort = 5060;
        size_t pos = remoteHost.find(':');
        if (pos != std::string::npos)
        {
            remotePort = std::stoi(remoteHost.substr(pos + 1));
            remoteHost = remoteHost.substr(0, pos);
        }
        _sender = application->getDriver()->connect("udp", "", 5060, remoteHost.c_str(), remotePort, this);

        if (!_password.empty())
            _registration = SimpleSipSession::Registration::create(_sipDriver, this, this, SimpleSipSession::Registration::Config(_user, _user, _password, _realm, expires));
    }

    SHELL_FUNCTION(SimpleSipSessionCli, call, 2, "call <callee>")
    {
    }

    SHELL_FUNCTION(SimpleSipSessionCli, term, 1, "term")
    {
    }

public:
    Common::NetSenderPtr _sender;
    std::string _user;
    std::string _server;
    std::string _password;
    std::string _realm;

    SimpleSipSession::SipDriverPtr _sipDriver;
    SimpleSipSession::SipFlowPtr _registration;
};

REGISTER_CLI("SimpleSipSessionCli", SimpleSipSessionCli);