//
// *****************************************************************************
// Copyright (c) 2023 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//
// Created on 2023/6/29 by <PERSON>
//

#pragma once

#include "Common/Util.h"
#include "SimpleSipSession/SipDriver.h"
#include "SimpleSipSession/SipMessage.h"
#include "SipTransactionI.h"

namespace SimpleSipSession
{

class SipNist : public SipTransactionI, public SipMessageContext
{
public:
    static SipTransactionPtr create(const SipTransactionConfig &config);

    SipNist(const SipTransactionConfig &config)
        : SipTransactionI(config)
        , _statusCode(0)
    {
    }

    virtual enum Type type() override { return NonInviteServerTransaction; }
    virtual std::string name() override { return "NIST:" + _method; }
    virtual void close() override;

    virtual bool recvMessage(const SipMessagePtr &message) override;
    virtual void onTimeout(const std::string &name) override;
    virtual bool terminate(const std::string &reason) override;

    virtual bool getValue(const std::string &key, std::string &value) override;

    virtual bool response(int statusCode, const std::string &reasonPhrase = "") override;

private:
    static constexpr const char *TimerJ = "TimerJ";
    SipMessagePtr _request;
    std::string _method;
    SipMessagePtr _response;
    SipMessagePtr _1xxResponse;
    int _statusCode;
};

typedef Common::Handle<SipNist> SipNistPtr;

} // namespace SimpleSipSession