//
// *****************************************************************************
// Copyright (c) 2023 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//
// Created on 2023/7/1 by <PERSON>
//

#include "SipNist.h"
#include "Common/Util.h"
#include <exception>
#include "JsmLog/JsmLog.h"
#include "SimpleSipSession/SipMessage.h"
#include "SimpleSipSession/SipTypes.h"

namespace SimpleSipSession
{

void SipNist::close()
{
    Common::RecLock lock(this);

    _request = 0;
    _1xxResponse = 0;
    _response = 0;

    SipTransactionI::close();
}

bool SipNist::recvMessage(const SipMessagePtr &message)
{
    Common::RecLock lock(this);

    std::string uri;
    if (!message->getRequestLine(_method, uri))
    {
        LOG_WARNING("SipNist", "content:received invalid message");
        return false;
    }

    if (!_request)
        _request = message;

    if (getState() == StateIdle)
    {
        LOG_INFO("SipNist", "content:idle received request:" + _method + " uri:" + uri + " reponse:100");

        setState(StateProcessing);
        Common::TmpUnlock unlock(this);
        _listener->onRequest(message);
        return true;
    }
    else if (getState() == StateProcessing)
    {
        LOG_INFO("SipNist", "content:processing received request:" + _method + " uri:" + uri + " reponse:100");

        if (!_1xxResponse)
        {
            _1xxResponse = SipMessage::genResponse(_request, 100, ReasonPharse::TRYING);
            if (!_1xxResponse)
            {
                terminate("build 100 response failed.");
                return false;
            }
            _1xxResponse->encode(this);
        }

        Common::TmpUnlock unlock(this);
        _listener->sendMessage(_1xxResponse);
        return true;
    }
    else if (getState() == StateCompleted)
    {
        LOG_INFO("SipNist", "content:completed received request:" + _method + " uri:" + uri + " reponse:" + std::to_string(_statusCode));

        Common::TmpUnlock unlock(this);
        _listener->sendMessage(_response);
        return true;
    }

    LOG_WARNING("SipNist", "content:received message invalid state:" + std::string(StateDesc(getState())));
    return false;
}

void SipNist::onTimeout(const std::string &name)
{
    if (name == TimerJ)
    {
        terminate("TimerJ timeout");
    }
}

bool SipNist::terminate(const std::string &reason)
{
    if (getState() == StateTermed)
        return true;

    stopTimer(TimerJ);

    LOG_INFO("SipNist", "content:terminate reason:" + reason);
    setState(StateTermed);
    return true;
}

bool SipNist::getValue(const std::string &key, std::string &value)
{
    // todo
    return false;
}

bool SipNist::response(int statusCode, const std::string &reasonPhrase)
{
    Common::RecLock lock(this);

    if (_response)
    {
        LOG_WARNING("SipNist", "content:duplicate response:" + std::to_string(statusCode));
        return false;
    }

    _statusCode = statusCode;
    LOG_INFO("SipNist", "content:response request:" + _method + " reponse:" + std::to_string(_statusCode));

    _response = SipMessage::genResponse(_request, _statusCode, reasonPhrase.empty() ? StatusCode::getReasonPharse(_statusCode) : reasonPhrase);
    if (!_response)
    {
        terminate("build 100 response failed.");
        return false;
    }
    _response->encode(this);

    startTimer(TimerJ, SipTimer::TimerJ());

    setState(StateCompleted);

    Common::TmpUnlock unlock(this);
    _listener->sendMessage(_response);
    return true;
}

SipTransactionPtr SipNist::create(const SipTransactionConfig &config)
{
    if (!config.driver || !config.transport || !config.listener)
    {
        LOG_WARNING("SipNist", "content:invalid parameter");
        return nullptr;
    }

    SipNistPtr trans = nullptr;
    try
    {
        trans = new SipNist(config);
        if (!config.driver->addSchd(trans.get()))
        {
            LOG_WARNING("SipNist", "content:add scheduler failed");
            return nullptr;
        }
    }
    catch (const std::exception &e)
    {
        LOG_WARNING("SipNist", "content:create non-invite client transaction failed, reason:" + std::string(e.what()));
        return nullptr;
    }

    return trans.get();
}

} // namespace SimpleSipSession