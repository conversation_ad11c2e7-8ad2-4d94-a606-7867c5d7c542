//
// *****************************************************************************
// Copyright (c) 2023 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//
// Created on 2023/6/29 by <PERSON>
//

#pragma once

#include "Common/Common.h"
#include "Common/Util.h"
#include "JsmLog/JsmLog.h"
#include "SimpleSipSession/SipDriver.h"
#include "SimpleSipSession/base/SipTimer.h"
#include "SimpleSipSession/SipTransport.h"
#include "SipTransaction.h"

namespace SimpleSipSession
{

class SipTransactionI : public SipTransaction, public SipScheduler, public SipTimerManager
{
public:
    SipTransactionI(const SipTransactionConfig &config);
    virtual ~SipTransactionI();

    // implement SipScheduler
    virtual void close() override;
    virtual bool schd() override;

    // implement SipTransaction
    virtual bool request(const std::string &method) override;
    virtual bool response(int statusCode, const std::string &reasonPhrase) override;
    virtual bool terminate(const std::string &reason) override;

    void setState(enum State state);
    enum State getState() const override;

protected:
    bool getValue(const std::string &key, std::string &value);

protected:
    SipTransportPtr _transport;
    SipTransactionListenerPtr _listener;
    enum State _state;
    std::string _callId;
};

} // namespace SimpleSipSession