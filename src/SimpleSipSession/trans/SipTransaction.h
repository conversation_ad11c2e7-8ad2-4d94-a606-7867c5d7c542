//
// *****************************************************************************
// Copyright (c) 2023 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//
// Created on 2023/6/29 by <PERSON>
//

#pragma once

#include "Common/Common.h"
#include "SimpleSipSession/SipMessage.h"
#include "SimpleSipSession/SipDriver.h"
#include "SimpleSipSession/SipTransport.h"

namespace SimpleSipSession
{

class SipTransactionListener : public SipMessageContext, virtual public Common::Shared
{
public:
    virtual void onError(const Common::Error &error) = 0;

    virtual void sendMessage(const SipMessagePtr &message) = 0;

    virtual void onSucceed(const SipMessagePtr &message) = 0;
    virtual void onFailed(const SipMessagePtr &message) = 0;

    virtual void onRequest(const SipMessagePtr &message) = 0;
    virtual void onResponse(const SipMessagePtr &message) = 0;
};

typedef Common::Handle<SipTransactionListener> SipTransactionListenerPtr;

class SipTransaction;
typedef Common::Handle<SipTransaction> SipTransactionPtr;

class SipTransaction : virtual public Common::Shared
{
public:
    enum Type
    {
        NonInviteClientTransaction,
        NonInviteServerTransaction,
        InviteClientTransaction,
        InviteServerTransaction
    };

    enum State
    {
        StateIdle,
        StateProcessing,
        StateCompleted,
        StateTermed
    };

    static const char * StateDesc(enum State state)
    {
        static const char * __desc[] = {"Idle", "Processing", "Completed", "Termed"};
        return __desc[state];
    }

public:

    virtual enum Type type() = 0;
    virtual enum State getState() const = 0;
    virtual bool recvMessage(const SipMessagePtr &message) = 0;
    virtual bool request(const std::string &method) = 0;
    virtual bool response(int statusCode, const std::string &reasonPhrase = "") = 0;
    virtual bool terminate(const std::string &reason) = 0;
};

struct SipTransactionConfig
{
    SipTransactionConfig(const SipDriverPtr &_driver, const SipTransportPtr &_transport, const SipTransactionListenerPtr &_listener)
        : driver(_driver)
        , transport(_transport)
        , listener(_listener)
    {
    }

    SipDriverPtr driver;
    SipTransportPtr transport;
    SipTransactionListenerPtr listener;
};

} // namespace SimpleSipSession