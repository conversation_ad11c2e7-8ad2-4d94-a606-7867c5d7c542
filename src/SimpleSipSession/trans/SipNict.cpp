//
// *****************************************************************************
// Copyright (c) 2023 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//
// Created on 2023/7/1 by <PERSON>
//

#include "SipNict.h"
#include "Common/Util.h"
#include <exception>
#include <string>
#include "SimpleSipSession/SipMessage.h"
#include "SimpleSipSession/trans/SipTransactionI.h"
#include "JsmLog/JsmLog.h"
#include "SimpleSipSession/Error.h"
#include "SimpleSipSession/SipTypes.h"

namespace SimpleSipSession
{

bool SipNict::schd()
{
    Common::RecLock lock(this);
    if (getState() == StateIdle)
        request();

    return SipTransactionI::schd();
}

bool SipNict::recvMessage(const SipMessagePtr &message)
{
    Common::RecLock lock(this);

    if (_callId != message->getHeader(HeaderCName::CALL_ID))
        return false;

    int statusCode;
    std::string reason;
    if (!message->getStatusLine(statusCode, reason))
    {
        LOG_WARNING("SipNict", "content:received invalid message");
        return false;
    }

    if (getState() == StateProcessing)
    {
        stopTimer(TimerE);

        if (statusCode <= 199)
        {
            LOG_INFO("SipNict", "content:received provision response:" + std::to_string(statusCode));
            return true;
        }

        LOG_INFO("SipNict", "content:received response:" + std::to_string(statusCode));
        stopTimer(TimerF);
        startTimer(TimerK, SipTimer::TimerK());
        setState(StateCompleted);

        Common::TmpUnlock unlock(this);
        if (statusCode <= 299)
            _listener->onSucceed(message);
        else
            _listener->onFailed(message);

        return true;
    }
    else if (getState() == StateCompleted)
    {
        LOG_INFO("SipNict", "content:received response:" + std::to_string(statusCode));
        return true;
    }

    return false;
}

void SipNict::onTimeout(const std::string &name)
{
    if (name == TimerE)
    {
        if (_lastTimerE < SipTimer::T2())
            _lastTimerE *= 2;
        else
            _lastTimerE = SipTimer::T2();
        startTimer(TimerE, _lastTimerE);

        Common::TmpUnlock unlock(this);
        _listener->sendMessage(_request);
    }
    else if (name == TimerF)
    {
        terminate("TimerF timeout");

        Common::TmpUnlock unlock(this);
        _listener->onError(TransError::NoResponse(__ERROR_LOC__));
    }
    else if (name == TimerK)
    {
        terminate("TimerK timeout");
    }
}

bool SipNict::terminate(const std::string &reason)
{
    if (getState() == StateTermed)
        return true;

    stopTimer(TimerE);
    stopTimer(TimerF);
    stopTimer(TimerK);

    LOG_INFO("SipNict", "content:terminate reason:" + reason);
    setState(StateTermed);
    return true;
}

bool SipNict::getValue(const std::string &key, std::string &value)
{
    do
    {
        if (!_listener->getValue(key, value) && !SipTransactionI::getValue(key, value))
        {
            if (key == PlaceHolder::FROM_PARAMS)
                value = ";tag=" + std::string(Common::randString().c_str());
            else if (key == PlaceHolder::TO_PARAMS)
                value = "";
            else if (key == PlaceHolder::CALL_ID)
                value = Common::randString().c_str();
            else if (key == PlaceHolder::VIA_BRANCH)
                value = "z9hG4bK" + std::string(Common::randString().c_str());
            else if (key == PlaceHolder::SEQ_NUM)
                value = "1";
            else
                break;
        }

        return true;
    } while (0);

    LOG_WARNING("SipNict", "content:unknown key:" + key);
    return false;
}

bool SipNict::request()
{
    Common::RecLock lock(this);

    _request->encode(this);
    _callId = _request->getHeader(HeaderCName::CALL_ID);

    startTimer(TimerE, _lastTimerE);
    startTimer(TimerF, SipTimer::TimerF());

    setState(StateProcessing);

    Common::TmpUnlock unlock(this);
    _listener->sendMessage(_request);
    return true;
}

SipTransactionPtr SipNict::create(const SipTransactionConfig &config, const SipMessagePtr &reuqest)
{
    if (!config.driver || !config.transport || !config.listener || !reuqest)
    {
        LOG_WARNING("SipNict", "content:invalid parameter");
        return nullptr;
    }

    SipNictPtr trans = nullptr;
    try
    {
        trans = new SipNict(config, reuqest);
        if (!config.driver->addSchd(trans.get()))
        {
            LOG_WARNING("SipNict", "content:add scheduler failed");
            return nullptr;
        }
    }
    catch (const std::exception &e)
    {
        LOG_WARNING("SipNict", "content:create non-invite client transaction failed, reason:" + std::string(e.what()));
        return nullptr;
    }

    return trans.get();
}

} // namespace SimpleSipSession