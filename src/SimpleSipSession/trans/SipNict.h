//
// *****************************************************************************
// Copyright (c) 2023 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//
// Created on 2023/6/29 by <PERSON>
//

#pragma once

#include "Common/Util.h"
#include "SimpleSipSession/SipMessage.h"
#include "SipTransactionI.h"
#include <string>

namespace SimpleSipSession
{

class SipNict : public SipTransactionI, public SipMessageContext
{
public:
    static SipTransactionPtr create(const SipTransactionConfig &config, const SipMessagePtr &request);

    SipNict(const SipTransactionConfig &config, const SipMessagePtr &request)
        : SipTransactionI(config)
        , _request(request)
        , _lastTimerE(SipTimer::TimerE())
    {
        _request->getMethod(method);
    }

    virtual enum Type type() override { return NonInviteClientTransaction; }
    virtual std::string name() override { return "NICT:" + method; }
    virtual bool schd() override;

    virtual bool recvMessage(const SipMessagePtr &message) override;
    virtual void onTimeout(const std::string &name) override;
    virtual bool terminate(const std::string &reason) override;

    virtual bool getValue(const std::string &key, std::string &value) override;

    bool request();

public:
    std::string method;

private:
    static constexpr const char *TimerE = "TimerE";
    static constexpr const char *TimerF = "TimerF";
    static constexpr const char *TimerK = "TimerK";

    unsigned int _lastTimerE;
    SipMessagePtr _request;
};

typedef Common::Handle<SipNict> SipNictPtr;

} // namespace SimpleSipSession