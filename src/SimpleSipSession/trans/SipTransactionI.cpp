//
// *****************************************************************************
// Copyright (c) 2023 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//
// Created on 2023/6/29 by <PERSON>
//

#include "Common/Common.h"
#include "Common/Util.h"
#include "JsmLog/JsmLog.h"
#include "SimpleSipSession/SipDriver.h"
#include "SimpleSipSession/base/SipTimer.h"
#include "SimpleSipSession/SipTypes.h"
#include "SipTransactionI.h"

namespace SimpleSipSession
{

SipTransactionI::SipTransactionI(const SipTransactionConfig &config)
    : _transport(config.transport)
    , _listener(config.listener)
    , _state(StateIdle)
{
}

SipTransactionI::~SipTransactionI()
{
    LOG_INFO("SipTrans", "content:destroy transaction:" + _callId);
}

void SipTransactionI::close()
{
    Common::RecLock lock(this);

    setState(StateTermed);
    _listener = 0;
    _transport = 0;
    SipScheduler::close();
}

bool SipTransactionI::schd()
{
    if (!SipScheduler::schd())
        return false;

    Common::RecLock lock(this);

    if (_state == StateTermed)
        return false;

    SipTimerManager::schd();
    return true;
}

bool SipTransactionI::request(const std::string &method)
{
    LOG_WARNING("SipTrans", "content:not implement request:" + method);
    return false;
}

bool SipTransactionI::response(int statusCode, const std::string &reasonPhrase)
{
    LOG_WARNING("SipTrans", "content:not implement reponse:" + std::to_string(statusCode));
    return false;
}

bool SipTransactionI::terminate(const std::string &reason)
{
    LOG_WARNING("SipTrans", "content:invalid terminate");
    return false;
}

void SipTransactionI::setState(enum SipTransaction::State state)
{
    Common::RecLock lock(this);

    if (_state == state)
        return;

    LOG_INFO("SipTrans", "content:update state:" + std::string(StateDesc(_state)) + "->" + StateDesc(state));
    _state = state;
}

enum SipTransaction::State SipTransactionI::getState() const
{
    return _state;
}

bool SipTransactionI::getValue(const std::string &key, std::string &value)
{
    do
    {
        if (key == PlaceHolder::LOCAL_HOST)
            value = _transport->localHostPort().host;
        else if (key == PlaceHolder::LOCAL_HOST_PORT)
            value = _transport->localHostPort().to_string();
        else if (key == PlaceHolder::REMOTE_HOST_PORT)
            value = _transport->remoteHostPort().to_string();
        else
            break;

        return true;
    } while (0);

    return false;
}

} // namespace SimpleSipSession
