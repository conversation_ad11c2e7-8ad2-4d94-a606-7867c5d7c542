//
// *****************************************************************************
// Copyright (c) 2025 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//

#include "MpCallHigherConfig/MpCallHigherConfig.h"
#include "gtest/gtest.h"
#include "Common/CommonEx.h"
#include "Common/TypesPub.h"

class LegacyAppGatewayHigherConfigTest : public ::testing::Test
{
protected:
    void SetUp() override
    {
        Common::StrStrMap config = {
            {"global.Log.Level", "2"},
            {"global.Log.Print", "1"},
        };
        _app = Common::ApplicationEx::create("jrtcMpCallLegacyAppGateway.DC0.Main01", "", 0, config);
        _app->activate();
    }

    void TearDown() override
    {
        _app->indShutdown();
        while (!_app->isShutdown())
            Common::sleep(10);
    }

    Common::ApplicationExPtr _app;
};

TEST_F(LegacyAppGatewayHigherConfigTest, ApplyHigherConfigWithB03FeatureTag)
{
    // Set the FeatureTag to B03
    _app->setConfig("global.LegacyAppGateway.FeatureTag", "B03");

    // Apply higher config
    SipMpCall::applyHigherConfig(_app);

    // Verify that B03 specific configurations are applied
    Common::String value;

    // Check LegacyAppGateway.SipCoreNetMap.B03Cisco
    EXPECT_TRUE(_app->getAppConfig("LegacyAppGateway.SipCoreNetMap.B03Cisco", value));
    EXPECT_STREQ(value.c_str(), "CiscoMobile;");

    // Check LegacyAppGateway.SipCoreNetMap.B03CCECisco
    EXPECT_TRUE(_app->getAppConfig("LegacyAppGateway.SipCoreNetMap.B03CCECisco", value));
    EXPECT_STREQ(value.c_str(), "CCECiscoMobile;");

    // Check LegacyAppGateway.SipCoreNetMap.B03TBPHuawei
    EXPECT_TRUE(_app->getAppConfig("LegacyAppGateway.SipCoreNetMap.B03TBPHuawei", value));
    EXPECT_STREQ(value.c_str(), "TBPHuaweiMobile;");

    // Check LegacyAppGateway.SipCoreNetMap.B03HuaweiOp1
    EXPECT_TRUE(_app->getAppConfig("LegacyAppGateway.SipCoreNetMap.B03HuaweiOp1", value));
    EXPECT_STREQ(value.c_str(), "SSDMobile;EMFMobile;");

    // Check LegacyAppGateway.SipCoreNetMap.B03HuaweiOp2
    EXPECT_TRUE(_app->getAppConfig("LegacyAppGateway.SipCoreNetMap.B03HuaweiOp2", value));
    EXPECT_STREQ(value.c_str(), "TBPAOKEMobile;YXWMobile;STMobile;CRMMobile;EZGMobile;YDXSMobile;QHFWMobile;YXHBMobile;");

    // Check LegacyAppGateway.SipCoreNetMap.B03Yugao
    EXPECT_TRUE(_app->getAppConfig("LegacyAppGateway.SipCoreNetMap.B03Yugao", value));
    EXPECT_STREQ(value.c_str(), "YGHBMobile;YGMobile;");

    // Check LegacyAppGateway.SipCoreNetMap.B03NGCC
    EXPECT_TRUE(_app->getAppConfig("LegacyAppGateway.SipCoreNetMap.B03NGCC", value));
    EXPECT_STREQ(value.c_str(), "NGMobile;");

    // Check LegacyAppGateway.SipCoreNetDomains.B03CCECisco
    EXPECT_TRUE(_app->getAppConfig("LegacyAppGateway.SipCoreNetDomains.B03CCECisco", value));
    EXPECT_STREQ(value.c_str(), "103300");

    // Check LegacyAppGateway.SipCoreNetDomains.B03TBPHuawei
    EXPECT_TRUE(_app->getAppConfig("LegacyAppGateway.SipCoreNetDomains.B03TBPHuawei", value));
    EXPECT_STREQ(value.c_str(), "103301");

    // Check LegacyAppGateway.SipCoreNetDomains.B03HuaweiOp1
    EXPECT_TRUE(_app->getAppConfig("LegacyAppGateway.SipCoreNetDomains.B03HuaweiOp1", value));
    EXPECT_STREQ(value.c_str(), "103303;200014");

    // Check LegacyAppGateway.SipCoreNetDomains.B03HuaweiOp2
    EXPECT_TRUE(_app->getAppConfig("LegacyAppGateway.SipCoreNetDomains.B03HuaweiOp2", value));
    EXPECT_STREQ(value.c_str(), "103302;200015;200017;200018;200019;200020;200021");

    // Check MpCallRoomConfig.ConfigKeyRecordLayout
    EXPECT_TRUE(_app->getAppConfig("MpCallRoomConfig.ConfigKeyRecordLayout", value));
    EXPECT_STREQ(value.c_str(), "b03_voip_mix_aac");

    // Check MpCallRoomConfig.ConfigKeyOrdinaryRecord
    EXPECT_TRUE(_app->getAppConfig("MpCallRoomConfig.ConfigKeyOrdinaryRecord", value));
    EXPECT_STREQ(value.c_str(), "0");
}

TEST_F(LegacyAppGatewayHigherConfigTest, ApplyHigherConfigWithUnknownFeatureTag)
{
    // Set the FeatureTag to an unknown value
    _app->setConfig("global.LegacyAppGateway.FeatureTag", "UnknownTag");

    // Apply higher config
    SipMpCall::applyHigherConfig(_app);

    // Verify that no B03 configurations are applied for unknown FeatureTag
    Common::String value;

    // These should not be set for unknown FeatureTag
    EXPECT_FALSE(_app->getAppConfig("LegacyAppGateway.SipCoreNetMap.B03Cisco", value));
    EXPECT_FALSE(_app->getAppConfig("LegacyAppGateway.SipCoreNetMap.B03CCECisco", value));
    EXPECT_FALSE(_app->getAppConfig("LegacyAppGateway.SipCoreNetMap.B03TBPHuawei", value));
    EXPECT_FALSE(_app->getAppConfig("LegacyAppGateway.SipCoreNetMap.B03HuaweiOp1", value));
    EXPECT_FALSE(_app->getAppConfig("LegacyAppGateway.SipCoreNetMap.B03HuaweiOp2", value));
    EXPECT_FALSE(_app->getAppConfig("LegacyAppGateway.SipCoreNetMap.B03Yugao", value));
    EXPECT_FALSE(_app->getAppConfig("LegacyAppGateway.SipCoreNetMap.B03NGCC", value));
    EXPECT_FALSE(_app->getAppConfig("LegacyAppGateway.SipCoreNetDomains.B03CCECisco", value));
    EXPECT_FALSE(_app->getAppConfig("LegacyAppGateway.SipCoreNetDomains.B03TBPHuawei", value));
    EXPECT_FALSE(_app->getAppConfig("LegacyAppGateway.SipCoreNetDomains.B03HuaweiOp1", value));
    EXPECT_FALSE(_app->getAppConfig("LegacyAppGateway.SipCoreNetDomains.B03HuaweiOp2", value));
    EXPECT_FALSE(_app->getAppConfig("MpCallRoomConfig.ConfigKeyRecordLayout", value));
    EXPECT_FALSE(_app->getAppConfig("MpCallRoomConfig.ConfigKeyOrdinaryRecord", value));
}

TEST_F(LegacyAppGatewayHigherConfigTest, ApplyHigherConfigWithEmptyFeatureTag)
{
    // Don't set FeatureTag (empty)

    // Apply higher config
    SipMpCall::applyHigherConfig(_app);

    // Verify that no B03 configurations are applied for empty FeatureTag
    Common::String value;

    // These should not be set for empty FeatureTag
    EXPECT_FALSE(_app->getAppConfig("LegacyAppGateway.SipCoreNetMap.B03Cisco", value));
    EXPECT_FALSE(_app->getAppConfig("LegacyAppGateway.SipCoreNetMap.B03CCECisco", value));
    EXPECT_FALSE(_app->getAppConfig("LegacyAppGateway.SipCoreNetMap.B03TBPHuawei", value));
    EXPECT_FALSE(_app->getAppConfig("LegacyAppGateway.SipCoreNetMap.B03HuaweiOp1", value));
    EXPECT_FALSE(_app->getAppConfig("LegacyAppGateway.SipCoreNetMap.B03HuaweiOp2", value));
    EXPECT_FALSE(_app->getAppConfig("LegacyAppGateway.SipCoreNetMap.B03Yugao", value));
    EXPECT_FALSE(_app->getAppConfig("LegacyAppGateway.SipCoreNetMap.B03NGCC", value));
    EXPECT_FALSE(_app->getAppConfig("LegacyAppGateway.SipCoreNetDomains.B03CCECisco", value));
    EXPECT_FALSE(_app->getAppConfig("LegacyAppGateway.SipCoreNetDomains.B03TBPHuawei", value));
    EXPECT_FALSE(_app->getAppConfig("LegacyAppGateway.SipCoreNetDomains.B03HuaweiOp1", value));
    EXPECT_FALSE(_app->getAppConfig("LegacyAppGateway.SipCoreNetDomains.B03HuaweiOp2", value));
    EXPECT_FALSE(_app->getAppConfig("MpCallRoomConfig.ConfigKeyRecordLayout", value));
    EXPECT_FALSE(_app->getAppConfig("MpCallRoomConfig.ConfigKeyOrdinaryRecord", value));
}

TEST_F(LegacyAppGatewayHigherConfigTest, ApplyHigherConfigMultipleTimes)
{
    // Set the FeatureTag to B03
    _app->setConfig("global.LegacyAppGateway.FeatureTag", "B03");

    // Apply higher config multiple times
    SipMpCall::applyHigherConfig(_app);
    SipMpCall::applyHigherConfig(_app);
    SipMpCall::applyHigherConfig(_app);

    // Verify that configurations are still correct after multiple applications
    Common::String value;

    // Check a few key configurations
    EXPECT_TRUE(_app->getAppConfig("LegacyAppGateway.SipCoreNetMap.B03Cisco", value));
    EXPECT_STREQ(value.c_str(), "CiscoMobile;");

    EXPECT_TRUE(_app->getAppConfig("LegacyAppGateway.SipCoreNetDomains.B03HuaweiOp1", value));
    EXPECT_STREQ(value.c_str(), "103303;200014");

    EXPECT_TRUE(_app->getAppConfig("MpCallRoomConfig.ConfigKeyRecordLayout", value));
    EXPECT_STREQ(value.c_str(), "b03_voip_mix_aac");
}

TEST_F(LegacyAppGatewayHigherConfigTest, ApplyHigherConfigWithPreExistingConfig)
{
    // Set some pre-existing configurations
    _app->setConfig("global.LegacyAppGateway.SipCoreNetMap.B03Cisco", "ExistingCiscoMobile;");
    _app->setConfig("global.MpCallRoomConfig.ConfigKeyRecordLayout", "existing_layout");

    // Set the FeatureTag to B03
    _app->setConfig("global.LegacyAppGateway.FeatureTag", "B03");

    // Apply higher config
    SipMpCall::applyHigherConfig(_app);

    // Verify that pre-existing configurations are not overridden
    Common::String value;

    // These should retain their original values
    EXPECT_TRUE(_app->getAppConfig("LegacyAppGateway.SipCoreNetMap.B03Cisco", value));
    EXPECT_STREQ(value.c_str(), "ExistingCiscoMobile;");

    EXPECT_TRUE(_app->getAppConfig("MpCallRoomConfig.ConfigKeyRecordLayout", value));
    EXPECT_STREQ(value.c_str(), "existing_layout");

    // But new configurations should be applied
    EXPECT_TRUE(_app->getAppConfig("LegacyAppGateway.SipCoreNetMap.B03CCECisco", value));
    EXPECT_STREQ(value.c_str(), "CCECiscoMobile;");

    EXPECT_TRUE(_app->getAppConfig("LegacyAppGateway.SipCoreNetDomains.B03CCECisco", value));
    EXPECT_STREQ(value.c_str(), "103300");

    EXPECT_TRUE(_app->getAppConfig("MpCallRoomConfig.ConfigKeyOrdinaryRecord", value));
    EXPECT_STREQ(value.c_str(), "0");
}
