//
// *****************************************************************************
// Copyright (c) 2025 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//

#include "MpCallHigherConfig/MpCallHigherConfig.h"
#include "gtest/gtest.h"
#include "Common/CommonEx.h"
#include "Common/TypesPub.h"

class SipGatewayHigherConfigTest : public ::testing::Test
{
protected:
    void SetUp() override
    {
        Common::StrStrMap config = {
            {"global.Log.Level", "2"},
            {"global.Log.Print", "1"},
        };
        _app = Common::ApplicationEx::create("jrtcMpCallSipGateway.DC0.Main01", "", 0, config);
        _app->activate();
    }

    void TearDown() override
    {
        _app->indShutdown();
        while (!_app->isShutdown())
            Common::sleep(10);
    }

    Common::ApplicationExPtr _app;
};

TEST_F(SipGatewayHigherConfigTest, ApplyHigherConfigWithB03Cisco)
{
    // Set the CoreNetId to B03Cisco
    _app->setConfig("global.SipCall.CoreNetId", "B03Cisco");

    // Apply higher config
    SipMpCall::applyHigherConfig(_app);

    // Verify that B03Cisco specific configurations are applied
    Common::String value;

    // Check SipCall.LocalAddress
    EXPECT_TRUE(_app->getAppConfig("SipCall.LocalAddress", value));
    EXPECT_STREQ(value.c_str(), "0.0.0.0:5060");

    // Check SipCall.RemoteAddress
    EXPECT_TRUE(_app->getAppConfig("SipCall.RemoteAddress", value));
    EXPECT_STREQ(value.c_str(), "************:5060;***********:5060;");

    // Check SipCall.IncomingDomainId
    EXPECT_TRUE(_app->getAppConfig("SipCall.IncomingDomainId", value));
    EXPECT_STREQ(value.c_str(), "103299");

    // Check MpCallRoomConfig.ConfigKeyRecordLayout
    EXPECT_TRUE(_app->getAppConfig("MpCallRoomConfig.ConfigKeyRecordLayout", value));
    EXPECT_STREQ(value.c_str(), "b03_voip_mix_aac");

    // Check MpCallRoomConfig.ConfigKeyOrdinaryRecord
    EXPECT_TRUE(_app->getAppConfig("MpCallRoomConfig.ConfigKeyOrdinaryRecord", value));
    EXPECT_STREQ(value.c_str(), "0");
}

TEST_F(SipGatewayHigherConfigTest, ApplyHigherConfigWithB03CCECisco)
{
    // Set the CoreNetId to B03CCECisco
    _app->setConfig("global.SipCall.CoreNetId", "B03CCECisco");

    // Apply higher config
    SipMpCall::applyHigherConfig(_app);

    // Verify that B03CCECisco specific configurations are applied
    Common::String value;

    // Check SipCall.LocalAddress
    EXPECT_TRUE(_app->getAppConfig("SipCall.LocalAddress", value));
    EXPECT_STREQ(value.c_str(), "0.0.0.0:5061");

    // Check SipCall.RemoteAddress
    EXPECT_TRUE(_app->getAppConfig("SipCall.RemoteAddress", value));
    EXPECT_STREQ(value.c_str(), "************:5060;***********:5060;");
}

TEST_F(SipGatewayHigherConfigTest, ApplyHigherConfigWithB03TBPHuawei)
{
    // Set the CoreNetId to B03TBPHuawei
    _app->setConfig("global.SipCall.CoreNetId", "B03TBPHuawei");

    // Apply higher config
    SipMpCall::applyHigherConfig(_app);

    // Verify that B03TBPHuawei specific configurations are applied
    Common::String value;

    // Check SipCall.LocalAddress
    EXPECT_TRUE(_app->getAppConfig("SipCall.LocalAddress", value));
    EXPECT_STREQ(value.c_str(), "0.0.0.0:5065");

    // Check SipCall.RemoteAddress
    EXPECT_TRUE(_app->getAppConfig("SipCall.RemoteAddress", value));
    EXPECT_STREQ(value.c_str(), "***********:5060;***********:5061;");
}

TEST_F(SipGatewayHigherConfigTest, ApplyHigherConfigWithB03HuaweiOp1)
{
    // Set the CoreNetId to B03HuaweiOp1
    _app->setConfig("global.SipCall.CoreNetId", "B03HuaweiOp1");

    // Apply higher config
    SipMpCall::applyHigherConfig(_app);

    // Verify that B03HuaweiOp1 specific configurations are applied
    Common::String value;

    // Check SipCall.LocalAddress
    EXPECT_TRUE(_app->getAppConfig("SipCall.LocalAddress", value));
    EXPECT_STREQ(value.c_str(), "0.0.0.0:5067");

    // Check SipCall.RemoteAddress
    EXPECT_TRUE(_app->getAppConfig("SipCall.RemoteAddress", value));
    EXPECT_STREQ(value.c_str(), "***********:5060;***********:5060;");

    // Check SipCall.IncomingDomainId
    EXPECT_TRUE(_app->getAppConfig("SipCall.IncomingDomainId", value));
    EXPECT_STREQ(value.c_str(), "200014");

    // Check MpCallRoomConfig.ConfigKeyRecordLayout
    EXPECT_TRUE(_app->getAppConfig("MpCallRoomConfig.ConfigKeyRecordLayout", value));
    EXPECT_STREQ(value.c_str(), "b03_voip_mix_aac");

    // Check MpCallRoomConfig.ConfigKeyOrdinaryRecord
    EXPECT_TRUE(_app->getAppConfig("MpCallRoomConfig.ConfigKeyOrdinaryRecord", value));
    EXPECT_STREQ(value.c_str(), "0");
}

TEST_F(SipGatewayHigherConfigTest, ApplyHigherConfigWithB03HuaweiOp2)
{
    // Set the CoreNetId to B03HuaweiOp2
    _app->setConfig("global.SipCall.CoreNetId", "B03HuaweiOp2");

    // Apply higher config
    SipMpCall::applyHigherConfig(_app);

    // Verify that B03HuaweiOp2 specific configurations are applied
    Common::String value;

    // Check SipCall.LocalAddress
    EXPECT_TRUE(_app->getAppConfig("SipCall.LocalAddress", value));
    EXPECT_STREQ(value.c_str(), "0.0.0.0:5066");

    // Check SipCall.RemoteAddress
    EXPECT_TRUE(_app->getAppConfig("SipCall.RemoteAddress", value));
    EXPECT_STREQ(value.c_str(), "***********:5060;***********:5060;");

    // Check SipCall.IncomingDomainId
    EXPECT_TRUE(_app->getAppConfig("SipCall.IncomingDomainId", value));
    EXPECT_STREQ(value.c_str(), "CalleePrefix");

    // Check MpCallRoomConfig.ConfigKeyRecordLayout
    EXPECT_TRUE(_app->getAppConfig("MpCallRoomConfig.ConfigKeyRecordLayout", value));
    EXPECT_STREQ(value.c_str(), "b03_voip_mix_aac");

    // Check MpCallRoomConfig.ConfigKeyOrdinaryRecord
    EXPECT_TRUE(_app->getAppConfig("MpCallRoomConfig.ConfigKeyOrdinaryRecord", value));
    EXPECT_STREQ(value.c_str(), "0");
}

TEST_F(SipGatewayHigherConfigTest, ApplyHigherConfigWithB03Yugao)
{
    // Set the CoreNetId to B03Yugao
    _app->setConfig("global.SipCall.CoreNetId", "B03Yugao");

    // Apply higher config
    SipMpCall::applyHigherConfig(_app);

    // Verify that B03Yugao specific configurations are applied
    Common::String value;

    // Check SipCall.LocalAddress
    EXPECT_TRUE(_app->getAppConfig("SipCall.LocalAddress", value));
    EXPECT_STREQ(value.c_str(), "0.0.0.0:5073");

    // Check SipCall.RemoteAddress
    EXPECT_TRUE(_app->getAppConfig("SipCall.RemoteAddress", value));
    EXPECT_STREQ(value.c_str(), "***********:5060;");
}

TEST_F(SipGatewayHigherConfigTest, ApplyHigherConfigWithB03NGCC)
{
    // Set the CoreNetId to B03NGCC
    _app->setConfig("global.SipCall.CoreNetId", "B03NGCC");

    // Apply higher config
    SipMpCall::applyHigherConfig(_app);

    // Verify that B03NGCC specific configurations are applied
    Common::String value;

    // Check SipCall.LocalAddress
    EXPECT_TRUE(_app->getAppConfig("SipCall.LocalAddress", value));
    EXPECT_STREQ(value.c_str(), "0.0.0.0:5074");

    // Check SipCall.RemoteAddress
    EXPECT_TRUE(_app->getAppConfig("SipCall.RemoteAddress", value));
    EXPECT_STREQ(value.c_str(), "************:5060;************:5060;");

    // Check SipCall.IncomingDomainId
    EXPECT_TRUE(_app->getAppConfig("SipCall.IncomingDomainId", value));
    EXPECT_STREQ(value.c_str(), "200023");

    // Check MpCallRoomConfig.ConfigKeyRecordLayout
    EXPECT_TRUE(_app->getAppConfig("MpCallRoomConfig.ConfigKeyRecordLayout", value));
    EXPECT_STREQ(value.c_str(), "b03_voip_mix_aac");

    // Check MpCallRoomConfig.ConfigKeyOrdinaryRecord
    EXPECT_TRUE(_app->getAppConfig("MpCallRoomConfig.ConfigKeyOrdinaryRecord", value));
    EXPECT_STREQ(value.c_str(), "0");
}

TEST_F(SipGatewayHigherConfigTest, ApplyHigherConfigWithUnknownCoreNetId)
{
    // Set the CoreNetId to an unknown value
    _app->setConfig("global.SipCall.CoreNetId", "UnknownNetId");

    // Apply higher config
    SipMpCall::applyHigherConfig(_app);

    // Verify that no B03 configurations are applied for unknown CoreNetId
    Common::String value;

    // These should not be set for unknown CoreNetId
    EXPECT_FALSE(_app->getAppConfig("SipCall.LocalAddress", value));
    EXPECT_FALSE(_app->getAppConfig("SipCall.RemoteAddress", value));
    EXPECT_FALSE(_app->getAppConfig("SipCall.IncomingDomainId", value));
    EXPECT_FALSE(_app->getAppConfig("MpCallRoomConfig.ConfigKeyRecordLayout", value));
    EXPECT_FALSE(_app->getAppConfig("MpCallRoomConfig.ConfigKeyOrdinaryRecord", value));
}

TEST_F(SipGatewayHigherConfigTest, ApplyHigherConfigWithEmptyCoreNetId)
{
    // Don't set CoreNetId (empty)

    // Apply higher config
    SipMpCall::applyHigherConfig(_app);

    // Verify that no B03 configurations are applied for empty CoreNetId
    Common::String value;

    // These should not be set for empty CoreNetId
    EXPECT_FALSE(_app->getAppConfig("SipCall.LocalAddress", value));
    EXPECT_FALSE(_app->getAppConfig("SipCall.RemoteAddress", value));
    EXPECT_FALSE(_app->getAppConfig("SipCall.IncomingDomainId", value));
    EXPECT_FALSE(_app->getAppConfig("MpCallRoomConfig.ConfigKeyRecordLayout", value));
    EXPECT_FALSE(_app->getAppConfig("MpCallRoomConfig.ConfigKeyOrdinaryRecord", value));
}

TEST_F(SipGatewayHigherConfigTest, ApplyHigherConfigMultipleTimes)
{
    // Set the CoreNetId to B03Cisco
    _app->setConfig("global.SipCall.CoreNetId", "B03Cisco");

    // Apply higher config multiple times
    SipMpCall::applyHigherConfig(_app);
    SipMpCall::applyHigherConfig(_app);
    SipMpCall::applyHigherConfig(_app);

    // Verify that configurations are still correct after multiple applications
    Common::String value;

    // Check SipCall.LocalAddress
    EXPECT_TRUE(_app->getAppConfig("SipCall.LocalAddress", value));
    EXPECT_STREQ(value.c_str(), "0.0.0.0:5060");

    // Check SipCall.RemoteAddress
    EXPECT_TRUE(_app->getAppConfig("SipCall.RemoteAddress", value));
    EXPECT_STREQ(value.c_str(), "************:5060;***********:5060;");

    // Check SipCall.IncomingDomainId
    EXPECT_TRUE(_app->getAppConfig("SipCall.IncomingDomainId", value));
    EXPECT_STREQ(value.c_str(), "103299");
}

TEST_F(SipGatewayHigherConfigTest, ApplyHigherConfigWithPreExistingConfig)
{
    // Set some pre-existing configurations
    _app->setConfig("global.SipCall.LocalAddress", "*******:8080");
    _app->setConfig("global.SipCall.RemoteAddress", "*******:9090");

    // Set the CoreNetId to B03Cisco
    _app->setConfig("global.SipCall.CoreNetId", "B03Cisco");

    // Apply higher config
    SipMpCall::applyHigherConfig(_app);

    // Verify that pre-existing configurations are not overridden
    Common::String value;

    // These should retain their original values
    EXPECT_TRUE(_app->getAppConfig("SipCall.LocalAddress", value));
    EXPECT_STREQ(value.c_str(), "*******:8080");

    EXPECT_TRUE(_app->getAppConfig("SipCall.RemoteAddress", value));
    EXPECT_STREQ(value.c_str(), "*******:9090");

    // But new configurations should be applied
    EXPECT_TRUE(_app->getAppConfig("SipCall.IncomingDomainId", value));
    EXPECT_STREQ(value.c_str(), "103299");

    EXPECT_TRUE(_app->getAppConfig("MpCallRoomConfig.ConfigKeyRecordLayout", value));
    EXPECT_STREQ(value.c_str(), "b03_voip_mix_aac");

    EXPECT_TRUE(_app->getAppConfig("MpCallRoomConfig.ConfigKeyOrdinaryRecord", value));
    EXPECT_STREQ(value.c_str(), "0");
}

TEST_F(SipGatewayHigherConfigTest, ApplyHigherConfigWithC09Configurations)
{
    // Set the FeatureTag to C09 (C09 configurations use SipCall.FeatureTag as condition)
    _app->setConfig("global.SipCall.FeatureTag", "C09");

    // Apply higher config
    SipMpCall::applyHigherConfig(_app);

    // Verify that C09 specific configurations are applied
    Common::String value;

    // Check SipCall.IncomingCalleeType
    EXPECT_TRUE(_app->getAppConfig("SipCall.IncomingCalleeType", value));
    EXPECT_STREQ(value.c_str(), "Ivvr");

    // Check SipCall.PAIUriFormat
    EXPECT_TRUE(_app->getAppConfig("SipCall.PAIUriFormat", value));
    EXPECT_STREQ(value.c_str(), "TelUri");
}
