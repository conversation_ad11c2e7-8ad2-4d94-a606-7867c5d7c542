//
// *****************************************************************************
// Copyright (c) 2025 Juphoon System Software Co., LTD. All rights reserved
// *****************************************************************************
//

#include "MpCallHigherConfig.h"
#include "MpCallHigherConfig/cfg/HigherConfigB03.yaml.h"
#include "MpCallHigherConfig/cfg/HigherConfigC09.yaml.h"

namespace SipMpCall
{

void applyHigherConfig(const Common::ApplicationExPtr &app)
{
    HigherConfig::HigherConfig::getInstance()->loadConfig(std::string((const char *)HigherConfigB03_yaml, HigherConfigB03_yaml_len));
    HigherConfig::HigherConfig::getInstance()->loadConfig(std::string((const char *)HigherConfigC09_yaml, HigherConfigC09_yaml_len));
    HigherConfig::HigherConfig::getInstance()->applyConfig(app);
}

} // namespace SipMpCall
