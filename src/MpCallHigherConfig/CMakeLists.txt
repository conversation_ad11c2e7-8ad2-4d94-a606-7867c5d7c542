# manager cfg
FILE(GLOB_RECURSE EMBEDED_CONFIG_RESOURCES "cfg/*.yaml")
FOREACH(INPUT_FILE ${EMBEDED_CONFIG_RESOURCES})
    SET(OUTPUT_FILE ${INPUT_FILE}.h)
    LIST(APPEND EMBEDED_CONFIG_SRC ${OUTPUT_FILE})
ENDFOREACH()

ADD_CUSTOM_COMMAND(
    OUTPUT cfg/__header.h ${EMBEDED_CONFIG_SRC}
    COMMAND ${CMAKE_CURRENT_SOURCE_DIR}/cfg/update.sh
    COMMENT "Generate embeded configs"
)

# implement
aux_source_directory(. MPCALL_HIGHER_CONFIG_SRC)
add_library(MpCallHigherConfig
    ${MPCALL_HIGHER_CONFIG_SRC}
    ${EMBEDED_CONFIG_SRC}
)

target_include_directories(MpCallHigherConfig PRIVATE
    ${DEFAULT_INCLUDE_DIRECTORIES}
    ${PROJECT_SOURCE_DIR}/3rd
)
target_compile_definitions(MpCallHigherConfig PRIVATE
    ${DEFAULT_COMPILE_DEFINITIONS}
)

# UnitTest
aux_source_directory(test HIGHER_CONFIG_TEST_SRC)

add_executable(MpCallHigherConfigUnitTest ${HIGHER_CONFIG_TEST_SRC})
target_include_directories(MpCallHigherConfigUnitTest PRIVATE
    ${DEFAULT_INCLUDE_DIRECTORIES}
    ${PROJECT_SOURCE_DIR}/3rd
)
target_compile_definitions(MpCallHigherConfigUnitTest PRIVATE
    ${DEFAULT_COMPILE_DEFINITIONS}
)

target_link_libraries(MpCallHigherConfigUnitTest MpCallHigherConfig HigherConfig
    ${DEFAULT_SERVER_LIBRARIES}
    $ENV{JssSharedPath}/lib/${DEFAULT_LIB_PATH}/libprotobuf.a
    $ENV{JssSharedPath}/lib/${DEFAULT_LIB_PATH}/libyaml-cpp.a
    $ENV{JssSharedPath}/lib/${DEFAULT_LIB_PATH}/libgtest.a
    $ENV{JssSharedPath}/lib/${DEFAULT_LIB_PATH}/libgmock.a
    $ENV{JssSharedPath}/lib/${DEFAULT_LIB_PATH}/libgtest_main.a
    ${DEFAULT_SYS_LIBRARIES}
)
include(GoogleTest)
gtest_discover_tests(MpCallHigherConfigUnitTest)
