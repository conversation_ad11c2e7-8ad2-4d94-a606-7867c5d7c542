metadata:
  name: B03

services:
  - name: jrtcMpCallSipGateway
    configs:
      - match:
          - {key: SipCall.CoreNetId, value: B03C<PERSON>}
        config:
          - {key: SipCall.LocalAddress, value: 0.0.0.0:5060}
          - {key: SipCall.RemoteAddress, value: ************:5060;***********:5060;}
          - {key: SipCall.IncomingDomainId, value: 103299}
          - {key: MpCallRoomConfig.ConfigKeyRecordLayout, value: b03_voip_mix_aac}
          - {key: MpCallRoomConfig.ConfigKeyOrdinaryRecord, value: 0}

      - match:
          - {key: SipCall.CoreNetId, value: B03CCECisco}
        config:
          - {key: SipCall.LocalAddress, value: 0.0.0.0:5061}
          - {key: SipCall.RemoteAddress, value: ************:5060;***********:5060;}

      - match:
          - {key: Sip<PERSON>all.CoreNetId, value: B03T<PERSON><PERSON><PERSON><PERSON><PERSON>}
        config:
          - {key: SipCall.LocalAddress, value: 0.0.0.0:5065}
          - {key: SipCall.RemoteAddress, value: ***********:5060;***********:5061;}

      - match:
          - {key: SipCall.CoreNetId, value: B03HuaweiOp1}
        config:
          - {key: SipCall.LocalAddress, value: 0.0.0.0:5067}
          - {key: SipCall.RemoteAddress, value: ***********:5060;***********:5060;}
          - {key: SipCall.IncomingDomainId, value: 200014}
          - {key: MpCallRoomConfig.ConfigKeyRecordLayout, value: b03_voip_mix_aac}
          - {key: MpCallRoomConfig.ConfigKeyOrdinaryRecord, value: 0}

      - match:
          - {key: SipCall.CoreNetId, value: B03HuaweiOp2}
        config:
          - {key: SipCall.LocalAddress, value: 0.0.0.0:5066}
          - {key: SipCall.RemoteAddress, value: ***********:5060;***********:5060;}
          - {key: SipCall.IncomingDomainId, value: CalleePrefix}
          - {key: MpCallRoomConfig.ConfigKeyRecordLayout, value: b03_voip_mix_aac}
          - {key: MpCallRoomConfig.ConfigKeyOrdinaryRecord, value: 0}

      - match:
          - {key: SipCall.CoreNetId, value: B03Yugao}
        config:
          - {key: SipCall.LocalAddress, value: 0.0.0.0:5073}
          - {key: SipCall.RemoteAddress, value: ***********:5060;}

      - match:
          - {key: SipCall.CoreNetId, value: B03NGCC}
        config:
          - {key: SipCall.LocalAddress, value: 0.0.0.0:5074}
          - {key: SipCall.RemoteAddress, value: ************:5060;************:5060;}
          - {key: SipCall.IncomingDomainId, value: 200023}
          - {key: MpCallRoomConfig.ConfigKeyRecordLayout, value: b03_voip_mix_aac}
          - {key: MpCallRoomConfig.ConfigKeyOrdinaryRecord, value: 0}

  - name: jrtcMpCallLegacyAppGateway
    configs:
      - match:
          - {key: LegacyAppGateway.FeatureTag, value: B03}
        config:
          - {key: LegacyAppGateway.SipCoreNetMap.B03Cisco, value: CiscoMobile;}
          - {key: LegacyAppGateway.SipCoreNetMap.B03CCECisco, value: CCECiscoMobile;}
          - {key: LegacyAppGateway.SipCoreNetMap.B03TBPHuawei, value: TBPHuaweiMobile;}
          - {key: LegacyAppGateway.SipCoreNetMap.B03HuaweiOp1, value: SSDMobile;EMFMobile;}
          - {key: LegacyAppGateway.SipCoreNetMap.B03HuaweiOp2, value: TBPAOKEMobile;YXWMobile;STMobile;CRMMobile;EZGMobile;YDXSMobile;QHFWMobile;YXHBMobile;}
          - {key: LegacyAppGateway.SipCoreNetMap.B03Yugao, value: YGHBMobile;YGMobile;}
          - {key: LegacyAppGateway.SipCoreNetMap.B03NGCC, value: NGMobile;}
          - {key: LegacyAppGateway.SipCoreNetDomains.B03CCECisco, value: 103300}
          - {key: LegacyAppGateway.SipCoreNetDomains.B03TBPHuawei, value: 103301}
          - {key: LegacyAppGateway.SipCoreNetDomains.B03HuaweiOp1, value: 103303;200014}
          - {key: LegacyAppGateway.SipCoreNetDomains.B03HuaweiOp2, value: 103302;200015;200017;200018;200019;200020;200021}
          - {key: MpCallRoomConfig.ConfigKeyRecordLayout, value: b03_voip_mix_aac}
          - {key: MpCallRoomConfig.ConfigKeyOrdinaryRecord, value: 0}
