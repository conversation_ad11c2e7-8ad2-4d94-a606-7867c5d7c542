#ifndef __VERSION_h__
#define __VERSION_h__

#include "Common/TypesPub.h"

#define LIB_PATCHSET_VER "3663"

#define VER "Version."
#define MOD_PREFIX "Module."

#define DECLARE_LIB_VERSIONS(libName) \
    extern void libName##_versions(Common::StrStrMap &vers);

#define DECLARE_LIB_VERSIONS_FUNC(libName)                                                                  \
    void libName##_versions(Common::StrStrMap &vers)                                                        \
    {                                                                                                       \
        vers.insert(make_pair(#libName, libName##_MAJOR_VER "." libName##_MINOR_VER "." LIB_PATCHSET_VER)); \
    }

/* Module */

/* Account: Account/AccountDb/AccountResidentDb/AccountGlobalDb/AccountResidentRdb/AccountGlobalRdb/AccountLocatorReplica */
#define Account_MAJOR_VER "4"
#define Account_MINOR_VER "2"

/* Group: Group/GroupSdb/MessageRdb/MessageMdb */
#define Group_MAJOR_VER "4"
#define Group_MINOR_VER "1"

/* Distance: Grid/HostDist */
#define Distance_MAJOR_VER "4"
#define Distance_MINOR_VER "0"

/* User: User/UserSdb/UserOdb */
#define User_MAJOR_VER "4"
#define User_MINOR_VER "2"

/* Licence: Licence/LicenceTool/libLicenceDesc.so */
#define Licence_MAJOR_VER "4"
#define Licence_MINOR_VER "0"

/* NmsDb NmsConfig NmsStatus NmsAccess NmsRouter NmsUpdate NmsDaemon NmsInterpreter */
#define Nms_MAJOR_VER "4"
#define Nms_MINOR_VER "1"

/* Proxy: RESTfulGateway/RESTfulProxy/JsonProxy */
#define Proxy_MAJOR_VER "4"
#define Proxy_MINOR_VER "0"

/* Resource: Resource/EndpointRelay */
#define Resource_MAJOR_VER "4"
#define Resource_MINOR_VER "0"

/* Status: Status/StatusManager */
#define Status_MAJOR_VER "4"
#define Status_MINOR_VER "0"

/* Storage: StorageEx/StorageExRdb/MediaContent/FileRelay */
#define Storage_MAJOR_VER "4"
#define Storage_MINOR_VER "0"

#define HOST_MAJOR_VER_SWAP_INFO "2"

/* SipGateway: SipGateway/SipProxy/SipProxyDb/RtpGateway/CloudSipGateway/MgcfSipGateway/ENUMProxy/Ats */
#define SipGateway_MAJOR_VER "4"
#define SipGateway_MINOR_VER "2"

/* Service */

/* AcdGateway */
#define AcdGateway_MAJOR_VER "1"
#define AcdGateway_MINOR_VER "0"

/* AcdGatewayRdb */
#define AcdGatewayRdb_MAJOR_VER "1"
#define AcdGatewayRdb_MINOR_VER "0"

/* CallServerEx */
#define CallEx_MAJOR_VER "4"
#define CallEx_MINOR_VER "1"

/* EmailSender */
#define EmailSender_MAJOR_VER "1"
#define EmailSender_MINOR_VER "0"

/* File */
#define File_MAJOR_VER "1"
#define File_MINOR_VER "0"

/* FileDb */
#define FileDb_MAJOR_VER "1"
#define FileDb_MINOR_VER "0"

/* H264AvcRelay */
#define H264AvcRelay_MAJOR_VER "1"
#define H264AvcRelay_MINOR_VER "0"

/* Host */
#define Host_MAJOR_VER "2"
#define Host_MINOR_VER "0"

/* HostDist */
#define HostDist_MAJOR_VER "1"
#define HostDist_MINOR_VER "1"

/* JsonProxy */
#define JsonProxy_MAJOR_VER "1"
#define JsonProxy_MINOR_VER "0"

#define Log_MAJOR_VER "4"
#define Log_MINOR_VER "3"

/* MediaContent */
#define MediaContent_MAJOR_VER "1"
#define MediaContent_MINOR_VER "0"

/* NmsAlarm */
#define NmsAlarm_MAJOR_VER "1"
#define NmsAlarm_MINOR_VER "0"

/* NmsDaemon */
#define NmsDaemon_MAJOR_VER "1"
#define NmsDaemon_MINOR_VER "3"

/* NmsUpdate */
#define NmsUpdate_MAJOR_VER "2"
#define NmsUpdate_MINOR_VER "0"

/* NmsDb */
#define NmsDb_MAJOR_VER "2"
#define NmsDb_MINOR_VER "3"

/* NmsConfig */
#define NmsConfig_MAJOR_VER "3"
#define NmsConfig_MINOR_VER "0"

/* NmsStatus */
#define NmsStatus_MAJOR_VER "2"
#define NmsStatus_MINOR_VER "0"

/* NmsAccess */
#define NmsAccess_MAJOR_VER "3"
#define NmsAccess_MINOR_VER "1"

/* NmsRouter */
#define NmsRouter_MAJOR_VER "1"
#define NmsRouter_MINOR_VER "0"

/* NmsInterpreter */
#define NmsInterpreter_MAJOR_VER "2"
#define NmsInterpreter_MINOR_VER "0"

/* RESTfulGateway */
#define RESTfulGateway_MAJOR_VER "2"
#define RESTfulGateway_MINOR_VER "0"

/* RESTfulProxy */
#define RESTfulProxy_MAJOR_VER "2"
#define RESTfulProxy_MINOR_VER "1"

/* RtpGateway */
#define RtpGateway_MAJOR_VER "1"
#define RtpGateway_MINOR_VER "1"

/* SipGateway */
//#define SipGateway_MAJOR_VER "1"
//#define SipGateway_MINOR_VER "1"

/* CloudSipGateway */
#define CloudSipGateway_MAJOR_VER "1"
#define CloudSipGateway_MINOR_VER "2"

/* MgcfSipGateway */
#define MgcfSipGateway_MAJOR_VER "1"
#define MgcfSipGateway_MINOR_VER "2"

/* SipGatewayEntry */
#define SipGatewayEntry_MAJOR_VER "1"
#define SipGatewayEntry_MINOR_VER "1"

/* SipMessageGateway */
#define SipMessageGateway_MAJOR_VER "1"
#define SipMessageGateway_MINOR_VER "0"

/* SipProxy */
#define SipProxy_MAJOR_VER "1"
#define SipProxy_MINOR_VER "4"

/* SipProxyDb */
#define SipProxyDb_MAJOR_VER "1"
#define SipProxyDb_MINOR_VER "2"

/* SipCall */
#define SipCall_MAJOR_VER "2"
#define SipCall_MINOR_VER "2"

/* SipCallManager */
#define SipCallManager_MAJOR_VER "2"
#define SipCallManager_MINOR_VER "2"

/* SipTransfer */
#define SipTransfer_MAJOR_VER "1"
#define SipTransfer_MINOR_VER "0"

/* Sms */
#define Sms_MAJOR_VER "1"
#define Sms_MINOR_VER "0"

/* TestManager */
#define TestManager_MAJOR_VER "1"
#define TestManager_MINOR_VER "0"

/* ENUMProxy */
#define ENUMProxy_MAJOR_VER "1"
#define ENUMProxy_MINOR_VER "0"

/* Ats */
#define Ats_MAJOR_VER "1"
#define Ats_MINOR_VER "1"

/* DELEGATE */
#define DelegateRedis_MAJOR_VER "1"
#define DelegateRedis_MINOR_VER "0"

#define DelegateSrs_MAJOR_VER "1"
#define DelegateSrs_MINOR_VER "0"

/* JMDS */
#define JMDS_MAJOR_VER                                               "2302"
#define JMDS_MINOR_VER                                               "7136"
#define JMDS_PATCH                                                   "5"
#define JMDS_STATUS                                                  ""

/* SipAgentGatewayEntry */
#define SipAgentGatewayEntry_MAJOR_VER "2302"
#define SipAgentGatewayEntry_MINOR_VER "7136"
#define SipAgentGatewayEntry_PATCH "4"
#define SipAgentGatewayEntry_STATUS ""

/* SipAgentGatewayEntryTester */
#define SipAgentGatewayEntryTester_MAJOR_VER "2302"
#define SipAgentGatewayEntryTester_MINOR_VER "7136"
#define SipAgentGatewayEntryTester_PATCH "2"
#define SipAgentGatewayEntryTester_STATUS ""

/* SipDialerManager */
#define SipDialerManager_MAJOR_VER "2302"
#define SipDialerManager_MINOR_VER "7136"
#define SipDialerManager_PATCH "2"
#define SipDialerManager_STATUS ""

/* LIBRARY */

/* libClient.a */
#define libClient_MAJOR_VER "1"
#define libClient_MINOR_VER "0"
DECLARE_LIB_VERSIONS(libClient)

/* libAccountBase.a */
#define libAccountBase_MAJOR_VER "1"
#define libAccountBase_MINOR_VER "0"
DECLARE_LIB_VERSIONS(libAccountBase)

/* libDbBase.a */
#define libDbBase_MAJOR_VER "1"
#define libDbBase_MINOR_VER "1"
DECLARE_LIB_VERSIONS(libDbBase)

/* libDbConn.a */
#define libDbConn_MAJOR_VER "1"
#define libDbConn_MINOR_VER "0"
DECLARE_LIB_VERSIONS(libDbConn)

/* libDistance.a */
#define libDistance_MAJOR_VER "1"
#define libDistance_MINOR_VER "0"
DECLARE_LIB_VERSIONS(libDistance)

/* libMediaPlatform.a */
#define libMediaPlatform_MAJOR_VER "1"
#define libMediaPlatform_MINOR_VER "0"
DECLARE_LIB_VERSIONS(libMediaPlatform)

/* libMediaRelay.a */
#define libMediaRelay_MAJOR_VER "1"
#define libMediaRelay_MINOR_VER "0"
DECLARE_LIB_VERSIONS(libMediaRelay)

/* libRegExp.a */
#define libRegExp_MAJOR_VER "1"
#define libRegExp_MINOR_VER "0"
DECLARE_LIB_VERSIONS(libRegExp)

/* libRESTful.a */
#define libRESTful_MAJOR_VER "2"
#define libRESTful_MINOR_VER "1"
DECLARE_LIB_VERSIONS(libRESTful)

/* libService.a */
#define libService_MAJOR_VER "2"
#define libService_MINOR_VER "1"
DECLARE_LIB_VERSIONS(libService)

/* libSimpleHttp.a */
#define libSimpleHttp_MAJOR_VER "1"
#define libSimpleHttp_MINOR_VER "0"
DECLARE_LIB_VERSIONS(libSimpleHttp)

/* libStorageBase.a */
#define libStorageBase_MAJOR_VER "1"
#define libStorageBase_MINOR_VER "0"
DECLARE_LIB_VERSIONS(libStorageBase)

/* Specific Version */
#define HOST_MAJOR_VER_SWAP_INFO "2"

#define LogTail_MAJOR_VER "4"
#define LogTail_MINOR_VER "0"

#endif
