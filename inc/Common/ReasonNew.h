#ifndef __ReasonNew_h__
#define __ReasonNew_h__

//add
#define DB_ERROR                        "db-error"              /* access database error */
#define PERMISSION_DENIED               "permission-denied"     /* permission denied */
#define LICENCE_ERROR                   "licence-error"         /* licence error */
#define REASONABLE_ERROR                "reasonable-error"      /* reasonable error */
#define APPKEY_ERROR                    "appkey-error"          /* appkey error */
#define DOMAIN_ERROR                    "domain-error"          /* domain error */
#define RANDOM_ERROR                    "random-error"          /* random error, maybe random expired, or connection closed */
#define ACCOUNT_ERROR                   "account-error"         /* account error */
#define SESSION_ERROR                   "session-error"         /* session error */
#define OBJECT_ERROR                    "object-error"          /* object error, maybe objectId not found */
#define TOKEN_ERROR                     "token-error"           /* token error, maybe prev valid connection released */
#define CONDS_ERROR                     "conds-error"           /* conditions error, inner error */
#define PWD_ERROR                       "pwd-error"             /* password error */
#define SERVER_BUSY                     "server-busy"           /* server-busy:xxx-error, client retry after 10min when server busy */
#define ANOTHER_DEVICE_LOGINED          "another-device-logined" /* kick off by server when another device logined */
#define CONNECTION_ERROR                "connection-error"      /* connection error, inner error */
#define TIMEOUT_ERROR                   "timeout-error"         /* wait timeout */
#define TERMINATED_ERROR                "terminated-error"      /* call terminated when application shutdown */
#define THIRD_AUTH_ERROR                "third-auth-error"      /* 3rd auth server return error */
#define KX_VERSION_ERROR                "kx-version-error"      /* key exchange version error */
#define KX_CERT_ERROR                   "kx-cert-error"         /* key exchange certificate error */
#define DOMAIN_BLOCKED_ERROR            "domain-blocked-error"  /* domain blocked error */


#endif
