function updateSipFlow(events) {
    const flowContainer = document.getElementById('sip-flow');
    const flowContainerParent = document.getElementById('sip-flow-container');
    flowContainer.innerHTML = ''; // 清空现有内容
    flowContainerParent.style.position = 'relative'; // 设置父容器为相对定位
    flowContainerParent.style.paddingTop = '40px'; // 增加顶部内边距以容纳端点名称
    // 移除 max-width，允许容器根据内容调整或浏览器宽度调整
    // flowContainerParent.style.maxWidth = '800px'; 
    // flowContainerParent.style.margin = '0 auto'; 
    // 可以设置一个最小宽度确保元素不重叠
    flowContainerParent.style.minWidth = '750px'; 
    
    // 先清除旧的端点和线，避免重复添加
    const existingNames = flowContainerParent.querySelectorAll('.endpoint-name');
    const existingLines = flowContainerParent.querySelectorAll('.endpoint-line');
    existingNames.forEach(el => el.remove());
    existingLines.forEach(el => el.remove());

    // 创建本地和远程端点名称 (使用固定的左侧偏移)
    const localEndpointName = document.createElement('div');
    localEndpointName.className = 'endpoint-name'; 
    localEndpointName.innerHTML = '<strong>Local</strong>';
    localEndpointName.style.position = 'absolute';
    localEndpointName.style.top = '10px';
    localEndpointName.style.left = '100px'; // Local 名称位置
    localEndpointName.style.width = '100px';
    localEndpointName.style.textAlign = 'center';

    const remoteEndpointName = document.createElement('div');
    remoteEndpointName.className = 'endpoint-name'; 
    remoteEndpointName.innerHTML = '<strong>Remote</strong>';
    remoteEndpointName.style.position = 'absolute';
    remoteEndpointName.style.top = '10px';
    // remoteEndpointName.style.right = '100px'; // 移除 right 定位
    remoteEndpointName.style.left = '600px'; // Remote 名称位置 (相对于左侧固定)
    remoteEndpointName.style.width = '100px';
    remoteEndpointName.style.textAlign = 'center';

    // 创建本地竖线 (使用固定的左侧偏移)
    const localLine = document.createElement('div');
    localLine.className = 'endpoint-line'; 
    localLine.style.position = 'absolute';
    localLine.style.top = '40px'; 
    localLine.style.bottom = '0';
    localLine.style.left = '150px'; // Local 竖线位置
    localLine.style.width = '2px';
    localLine.style.backgroundColor = '#666';

    // 创建远程竖线 (使用固定的左侧偏移)
    const remoteLine = document.createElement('div');
    remoteLine.className = 'endpoint-line'; 
    remoteLine.style.position = 'absolute';
    remoteLine.style.top = '40px'; 
    remoteLine.style.bottom = '0';
    // remoteLine.style.right = '150px'; // 移除 right 定位
    remoteLine.style.left = '650px'; // Remote 竖线位置 (相对于左侧固定)
    remoteLine.style.width = '2px';
    remoteLine.style.backgroundColor = '#666';

    // 添加端点名称和竖线到父容器
    flowContainerParent.appendChild(localEndpointName);
    flowContainerParent.appendChild(remoteEndpointName);
    flowContainerParent.appendChild(localLine);
    flowContainerParent.appendChild(remoteLine);
    
    // 添加消息 (布局基于固定的竖线位置)
    const messageAreaWidth = 500; // 计算出的固定消息区域宽度 (650 - 150)
    events.forEach(event => {
        const messageRow = document.createElement('div');
        messageRow.style.marginBottom = '10px';
        messageRow.style.position = 'relative'; 
        messageRow.style.paddingLeft = '150px'; // 左侧留出时间戳和Local竖线空间 (150px)
        messageRow.style.width = '650px'; // 行的总宽度等于右侧竖线的位置
        messageRow.style.clear = 'both'; 
        
        // 时间戳
        const timestamp = document.createElement('div');
        timestamp.textContent = event.time;
        timestamp.style.position = 'absolute';
        timestamp.style.left = '0px'; 
        timestamp.style.width = '140px'; // 调整宽度以避免接触竖线
        timestamp.style.textAlign = 'right';
        timestamp.style.paddingRight = '10px';
        timestamp.style.color = '#666';
        timestamp.style.top = '0';
        
        // 消息内容 (占据 Local 和 Remote 线之间的区域)
        const messageContent = document.createElement('div');
        messageContent.style.width = `${messageAreaWidth}px`; // 固定宽度
        messageContent.style.marginLeft = '0px'; // 从Local竖线右侧开始
        messageContent.style.position = 'relative'; // 允许内部元素定位

        // 创建箭头和消息
        if (event.type === 'SEND') {
            // 发送消息：Local -> Remote
            messageContent.innerHTML = `
                <div style="text-align: left; padding-left: 10px;">
                    <strong>${event.title}</strong>
                </div>
                <div style="text-align: left; padding-left: 10px;">
                    <span style="color: #666;">---------------→</span>
                    <button onclick="alert('${event.detail.replace(/'/g, "\\'")}')" style="margin-left: 10px;">查看详情</button>
                </div>
            `;
        } else {
            // 接收消息：Remote -> Local (需要调整对齐和箭头位置)
            messageContent.innerHTML = `
                 <div style="text-align: right; padding-right: 10px;">
                    <strong>${event.title}</strong>
                </div>
                 <div style="text-align: right; padding-right: 10px;">
                    <button onclick="alert('${event.detail.replace(/'/g, "\\'")}')" style="margin-right: 10px;">查看详情</button>
                    <span style="color: #666;">←---------------</span>
                </div>
            `;
        }
        
        // 组装行
        messageRow.appendChild(timestamp);
        messageRow.appendChild(messageContent);
        flowContainer.appendChild(messageRow); 
    });
} 