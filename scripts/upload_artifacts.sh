#!/bin/bash

set -e
set -x

source build.vars

function upload()
{
    jf rt upload $1 $ARTIFACTORY_PATH/
}

function upload_bundles()
{
    for file in ./jrtc*.tgz
    do
        upload $file
    done
}

function post_comment()
{
    base_url=$(echo $DRONE_REMOTE_URL | awk -F'/' '{print $1"//"$3}')
if [ $ARCH == x86_64 ]; then
    if [ -n "$DRONE_PULL_REQUEST" ]; then
        curl -XPOST "$base_url/api/v1/repos/$CI_REPO_NAME/pulls/$DRONE_PULL_REQUEST/reviews" \
        -H "Authorization: Bearer $GITEA_ACCESS_TOKEN" \
        -H "Accept: application/json" \
        -H "Content-Type: application/json" \
        -d "{\"body\":\"Artifactory build URI: $artifactory_build_uri\\nDoc URI:$doc_uri\"}"
    fi
else
    if [ -n "$DRONE_PULL_REQUEST" ]; then
        curl -XPOST "$base_url/api/v1/repos/$CI_REPO_NAME/pulls/$DRONE_PULL_REQUEST/reviews" \
        -H "Authorization: Bearer $GITEA_ACCESS_TOKEN" \
        -H "Accept: application/json" \
        -H "Content-Type: application/json" \
        -d "{\"body\":\"Artifactory build URI: $artifactory_build_uri\"}"
    fi
fi
}

function publish_doc()
{
    mkdir -p ~/.ssh
    cp scripts/id_rsa scripts/id_rsa.pub scripts/known_hosts ~/.ssh
    chmod 600 ~/.ssh/*
    if [ "$RELEASE_BUILD" == true ]; then
        ssh droneci@************* mkdir -p /var/www/html/$PROJECT_PATH/$PROJECT_BRANCH
        rsync -e ssh -avz doc/_build/html/* droneci@*************:/var/www/html/$PROJECT_PATH/$PROJECT_BRANCH
        doc_uri=http://*************/$PROJECT_PATH/$PROJECT_BRANCH
    else
        ssh droneci@************* mkdir -p /var/www/html/snapshot/$PROJECT_PATH/$BUILD_NO
        rsync -e ssh -avz doc/_build/html/* droneci@*************:/var/www/html/snapshot/$PROJECT_PATH/$BUILD_NO
        doc_uri=http://*************/snapshot/$PROJECT_PATH/$BUILD_NO
    fi
}

function publish()
{
    artifactory_build_uri=$(jf rt build-publish | jq -r '.buildInfoUiUrl' -)
    post_comment
}

function collect_build_info()
{
    jf rt build-add-git $ROOT_DIR
    jf rt build-collect-env
}

function discard_old_build()
{
    if [ "$RELEASE_BUILD" == false ]; then
        jf rt build-discard $BUILD_NAME --max-builds=15 --max-days=15 --delete-artifacts=true
    fi
}

pushd $ROOT_DIR
collect_build_info
upload_bundles
upload $PACKAGE_NAME.tgz
upload $PACKAGE_NAME-utils.tgz
if [ -f $PACKAGE_NAME-doc.tgz ]; then
    upload $PACKAGE_NAME-doc.tgz
    publish_doc
fi
publish
discard_old_build
popd
