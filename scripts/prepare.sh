#!/bin/bash

set -e
set -x

RELEASE_BUILD=false

PKG_DIR=$(dirname "$0")
ROOT_DIR=$PKG_DIR/..
BUILD_NAME=${CI_REPO_NAME//\//_}
BUILD_NO=$DRONE_BUILD_NUMBER
PROJECT_PATH=$CI_REPO_NAME
PROJECT_BRANCH=

ARTIFACTORY_RELEASE_REPO=cloud-generic-release-local
ARTIFACTORY_PRERELEASE_REPO=cloud-generic-prerelease-local
ARTIFACTORY_SNAPSHOT_REPO=cloud-generic-snapshot-local
ARTIFACTORY_REPO=$ARTIFACTORY_SNAPSHOT_REPO
ARTIFACTORY_PATH_PREFIX=com/juphoon
ARTIFACTORY_PATH=

VERSION=
GITHASH=`git log -n1 --pretty=format:%h`
ARCH=x86_64
PLATFORM=$DRONE_STAGE_OS

BUNDLE_POSTFIX=

PACKAGE_NAME=

function prepare()
{
    source project.vars

    if [ "$DRONE_STAGE_ARCH" == amd64 -o "$(uname -m)" == "x86_64" ]; then
        ARCH=x86_64
    elif [ "$DRONE_STAGE_ARCH" == arm64 -o "$(uname -m)" == "aarch64" ]; then
        ARCH=arm64
        PLATFORM=linux_deb10
    else
        echo "not support arch $DRONE_STAGE_ARCH"
        exit 1
    fi

    BUILD_NAME=$BUILD_NAME-$ARCH
    if [ -z "$DRONE_TAG" ]; then
        VERSION=$version_branch
    else
        RELEASE_BUILD=true
        BUILD_NAME=$BUILD_NAME-release
        VERSION=$(basename "$DRONE_TAG")
    fi

    PROJECT_BRANCH=$version_branch
    PACKAGE_NAME=$project_name-$VERSION-$GITHASH-$PLATFORM-$ARCH
    BUNDLE_POSTFIX=$VERSION-$GITHASH-$PLATFORM-$ARCH

    if [ "$RELEASE_BUILD" == true ]; then
        ARTIFACTORY_REPO=$ARTIFACTORY_RELEASE_REPO
    fi
    ARTIFACTORY_PATH=$ARTIFACTORY_REPO/$ARTIFACTORY_PATH_PREFIX/$PROJECT_PATH/$PROJECT_BRANCH

    JFROG_CLI_BUILD_NAME=$BUILD_NAME
    JFROG_CLI_BUILD_NUMBER=$BUILD_NO

    echo "
source project.vars
RELEASE_BUILD=$RELEASE_BUILD
PKG_DIR=$PKG_DIR
ROOT_DIR=$ROOT_DIR
BUILD_NAME=$BUILD_NAME
BUILD_NO=$BUILD_NO
PROJECT_PATH=$PROJECT_PATH
PROJECT_BRANCH=$PROJECT_BRANCH
ARTIFACTORY_RELEASE_REPO=$ARTIFACTORY_RELEASE_REPO
ARTIFACTORY_PRERELEASE_REPO=$ARTIFACTORY_PRERELEASE_REPO
ARTIFACTORY_SNAPSHOT_REPO=$ARTIFACTORY_SNAPSHOT_REPO
ARTIFACTORY_REPO=$ARTIFACTORY_REPO
ARTIFACTORY_PATH_PREFIX=$ARTIFACTORY_PATH_PREFIX
ARTIFACTORY_PATH=$ARTIFACTORY_PATH
VERSION=$VERSION
GITHASH=$GITHASH
ARCH=$ARCH
PLATFORM=$PLATFORM
BUNDLE_POSTFIX=$BUNDLE_POSTFIX
PACKAGE_NAME=$PACKAGE_NAME
export JFROG_CLI_BUILD_NAME=$JFROG_CLI_BUILD_NAME
export JFROG_CLI_BUILD_NUMBER=$JFROG_CLI_BUILD_NUMBER
" > build.vars
}

pushd $ROOT_DIR
prepare
popd
