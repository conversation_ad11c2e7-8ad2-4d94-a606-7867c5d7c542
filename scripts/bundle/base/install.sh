#!/bin/sh

# installation script for c++ application
#
# usage
# ./_pullDir/install.sh /home/<USER>/jrtcSignal.Main0.0

# set -x
set -e

CUR_APP_DIR=$1
NEW_APP_DIR=$(dirname "$0")

# load variables
source $NEW_APP_DIR/info.vars

if [ -d $CUR_APP_DIR ]; then
    # move exist content to app directory
    dirs=(data log)
    for d in ${dirs[@]}; do
        rm -rf $NEW_APP_DIR/$d || true
        if [ -d $CUR_APP_DIR/$d ]; then
            cp -r $CUR_APP_DIR/$d $NEW_APP_DIR/
        fi
    done

    # move all xml files
    rm -f $NEW_APP_DIR/*.xml || true
    cp -f $CUR_APP_DIR/*.xml $NEW_APP_DIR/ || true
fi

# generate application execuable link
APP_NAME=$(basename "$CUR_APP_DIR")
IFS_OLD=$IFS
IFS=. app_name_components=($APP_NAME)
IFS=$IFS_OLD
APP_PREFIX_NAME=${app_name_components[0]}
(cd $NEW_APP_DIR; ln -sf $BUNDLE_EXECUTABLE $APP_PREFIX_NAME)
