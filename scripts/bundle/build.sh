#!/bin/bash

# build bundle for c++ application
#
# usage:
# ./bundle/build.sh Account-4.3-341337-linux_el6-x86_64.tgz bin/update/Account

set -x
set -e

PKG_DIR=$(dirname "$0")
BUNDLE_NAME=$1
TGZ_NAME="$(pwd)/$2"
PROG_FILE=$3
PROG_NAME=$(basename "$PROG_FILE")
shift; shift; shift;

TMP_DIR=_bundle_build_temp

if [ -d $TMP_DIR ]; then
    rm -rf $TMP_DIR
fi

if [ -f $PKG_DIR/$BUNDLE_NAME.vars ]; then
    source $PKG_DIR/$BUNDLE_NAME.vars
fi

# copy programe file
mkdir -p $TMP_DIR/cfg

# copy config files
if [ -f cfg/$PROG_NAME.cfg ]; then
    cp cfg/$PROG_NAME.cfg cfg/$BUNDLE_NAME.cfg
    cp cfg/$PROG_NAME.cfg $TMP_DIR/cfg/$BUNDLE_NAME.cfg
fi

# copy release note
if [ -f doc/$PROG_NAME/versions.md ]; then
    cp doc/$PROG_NAME/versions.md $TMP_DIR/release_note.md
fi

# copy error code
if [ -f doc/$PROG_NAME/error_code.csv ]; then
    cp doc/$PROG_NAME/error_code.csv $TMP_DIR/errorcode.csv
fi

# copy main program
cp -f $PROG_FILE $TMP_DIR/
(cd $TMP_DIR; ln -sf $PROG_NAME $BUNDLE_NAME)

# copy additional files
for item in "${additional_files[@]}"; do
    TARGET_FILE="${item%%:*}"
    SOURCE_FILE="${item##*:}"
    mkdir -p $TMP_DIR/$(dirname "$TARGET_FILE")
    cp -rf $SOURCE_FILE $TMP_DIR/$TARGET_FILE
done

# create pakcage
(cd $TMP_DIR; tar -zcf $TGZ_NAME *)
