#!/bin/bash

set -e
set -x

source build.vars

CMAKE_CMD=cmake3
if ! command -v $CMAKE_CMD &> /dev/null; then
    CMAKE_CMD=cmake
fi

if [ "$JF_PLATFORM" != "" ]; then
    PLATFORM=$JF_PLATFORM
fi
export JssSharedPath=`pwd`/_dep/$PLATFORM/$ARCH

function build_artifacts()
{
    mkdir -p build
    pushd build
    # cmake -DCMAKE_VERBOSE_MAKEFILE:BOOL=ON ..
    $CMAKE_CMD -DCMAKE_BUILD_TYPE=RelWithDebInfo -DCMAKE_INSTALL_PREFIX=.. ..
    $CMAKE_CMD --build . --target apps -j $(nproc)
    $CMAKE_CMD --build . --target tools -j $(nproc)
    make install
    popd
}

function build_package()
{
    cp doc/release_note.md bin/update
    ls -l bin/update
    tar -zcvf $PACKAGE_NAME.tgz -C bin/update \
        release_note.md MpCallSipGateway MpCallRtpRunner MpCallRtpRunnerDaemon libjssisipcall.so libjssirtp.so libzmf_zero.so MpCallLegacyAppGateway MpCallEbService
    tar -zcvf $PACKAGE_NAME-utils.tgz -C bin \
        MpCallSipCli
}

function build_bundle()
{
    ./scripts/bundle/build.sh jrtcMpCallSipGateway jrtcMpCallSipGateway-$BUNDLE_POSTFIX.tgz bin/update/MpCallSipGateway
    ./scripts/bundle/build.sh jrtcMpCallRtpRunnerDaemon jrtcMpCallRtpRunnerDaemon-$BUNDLE_POSTFIX.tgz bin/update/MpCallRtpRunnerDaemon
    ./scripts/bundle/build.sh jrtcMpCallLegacyAppGateway jrtcMpCallLegacyAppGateway-$BUNDLE_POSTFIX.tgz bin/update/MpCallLegacyAppGateway
    ./scripts/bundle/build.sh jrtcMpCallSipDialer jrtcMpCallSipDialer-$BUNDLE_POSTFIX.tgz bin/update/MpCallSipDialer

    ./scripts/bundle/build.sh jrtcMpCallEbSipGateway jrtcMpCallEbSipGateway-$BUNDLE_POSTFIX.tgz bin/update/MpCallSipGateway
    ./scripts/bundle/build.sh jrtcMpCallEbService jrtcMpCallEbService-$BUNDLE_POSTFIX.tgz bin/update/MpCallEbService
    ./scripts/bundle/build.sh jrtcMpCallEbDialer jrtcMpCallEbDialer-$BUNDLE_POSTFIX.tgz bin/update/MpCallEbDialer

    ./scripts/bundle/build.sh jrtcAcdSipGateway jrtcAcdSipGateway-$BUNDLE_POSTFIX.tgz bin/update/MpCallSipGateway
    ./scripts/bundle/build.sh jrtcAcdRtpRunnerDaemon jrtcAcdRtpRunnerDaemon-$BUNDLE_POSTFIX.tgz bin/update/MpCallRtpRunnerDaemon
}

pushd $ROOT_DIR
build_artifacts
build_package
build_bundle
popd
