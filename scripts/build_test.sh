#!/bin/bash

set -e
set -x

source build.vars

CMAKE_CMD=cmake3
if ! command -v $CMAKE_CMD &> /dev/null; then
    CMAKE_CMD=cmake
fi

if [ "$JF_PLATFORM" != "" ]; then
    PLATFORM=$JF_PLATFORM
fi
export JssSharedPath=`pwd`/_dep/$PLATFORM/$ARCH

GCOVR_CMD=/usr/local/bin/gcovr
if command -v gcovr &> /dev/null; then
    GCOVR_CMD=gcovr
fi

function build_tests()
{
    mkdir -p tests
    pushd tests
    # cmake -DCMAKE_VERBOSE_MAKEFILE:BOOL=ON ..
    $CMAKE_CMD -DCMAKE_BUILD_TYPE=Debug -DBUILD_FOR_COVERAGE=ON ..
    make -j $(nproc) tests
    unit_tests=$(find . -name "*UnitTest")
    for test in $unit_tests; do
        LD_PRELOAD=$JssSharedPath/lib/linux/$ARCH/libjssisipcall.so "$test" --gtest_output=xml:"$(basename "$test").xml" || exit 1
    done
    popd
    mkdir -p doc/_static/test_coverage

    $GCOVR_CMD --html -o doc/_static/test_coverage/index.html --html-details -r `pwd` -f .*src.*  -e ".*/test/.*" -e "tests/.*" -e ".*pb.*"  -e ".*Agent\..*" -e ".*Pub\..*" -e ".*Server\..*" -e ".*Mock.h" --object-directory=`pwd`/tests
    xsltproc scripts/gtest2html.xsl tests/*.xml > doc/_static/test_report.html
}

function build_test()
{
    target_dir=$1
    shift
    target_names=$*
    mkdir -p $target_dir
    pushd $target_dir
    $CMAKE_CMD -DCMAKE_BUILD_TYPE=Debug -DBUILD_FOR_COVERAGE=ON ..
    make -j $(nproc) $target_names
    for target_name in $target_names; do
        unit_test=$(find . -name $target_name)
        LD_PRELOAD=$JssSharedPath/lib/linux/$ARCH/libjssisipcall.so "$unit_test" --gtest_output=xml:"$target_name.xml" || exit 1
    done
    popd
}

function build_test_report()
{
    mkdir -p doc/_static/test_coverage

    $GCOVR_CMD --html -o doc/_static/test_coverage/index.html --html-details -r `pwd` -f .*src.*  -e ".*/test/.*" -e "test1/.*" -e "test2/.*" -e "test3/.*" -e ".*pb.*"  -e ".*Agent\..*" -e ".*Pub\..*" -e ".*Server\..*" -e ".*Mock.h" --object-directory=`pwd`
    xsltproc scripts/gtest2html.xsl test1/*.xml test2/*.xml test3/*.xml > doc/_static/test_report.html
}

pushd $ROOT_DIR
if [ $# -eq 0 ]; then
    build_test_report
else
    build_test $*
fi
popd
