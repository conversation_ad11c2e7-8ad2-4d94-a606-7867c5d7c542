#!/bin/bash

set -e
set -x

source build.vars
source project.vars

function download()
{
    jf rt download "$ARTIFACTORY_RELEASE_REPO/$ARTIFACTORY_PATH_PREFIX/$1/*/*$2-$3-$ARCH.tgz" _dep/$PLATFORM/$ARCH/ --sort-by=created --sort-order=desc --limit=1 --flat=true --detailed-summary=true
    file=`ls _dep/$PLATFORM/$ARCH/*.tgz || echo "__"`
    if [ -f $file ]; then
        tar -zxf $file -C _dep/$PLATFORM/$ARCH && rm -rf $file && return 0
    fi

    jf rt download "$ARTIFACTORY_PRERELEASE_REPO/$ARTIFACTORY_PATH_PREFIX/$1/*/*$2-$3-$ARCH.tgz" _dep/$PLATFORM/$ARCH/ --sort-by=created --sort-order=desc --limit=1 --flat=true --detailed-summary=true
    file=`ls _dep/$PLATFORM/$ARCH/*.tgz || echo "__"`
    if [ -f $file ]; then
        tar -zxf $file -C _dep/$PLATFORM/$ARCH && rm -rf $file && return 0
    fi

    jf rt download "$ARTIFACTORY_SNAPSHOT_REPO/$ARTIFACTORY_PATH_PREFIX/$1/*/*$2-$3-$ARCH.tgz" _dep/$PLATFORM/$ARCH/ --sort-by=created --sort-order=desc --limit=1 --flat=true --detailed-summary=true
    file=`ls _dep/$PLATFORM/$ARCH/*.tgz || echo "__"`
    if [ -f $file ]; then
        tar -zxf $file -C _dep/$PLATFORM/$ARCH && rm -rf $file && return 0
    fi

    jf rt download "$ARTIFACTORY_RELEASE_REPO/$ARTIFACTORY_PATH_PREFIX/$1/*/*$2-$3-$ARCH*.tgz" _dep/$PLATFORM/$ARCH/ --sort-by=created --sort-order=desc --limit=1 --flat=true --detailed-summary=true
    file=`ls _dep/$PLATFORM/$ARCH/*.tgz || echo "__"`
    if [ -f $file ]; then
        tar -zxf $file -C _dep/$PLATFORM/$ARCH && rm -rf $file && return 0
    fi

    jf rt download "$ARTIFACTORY_PRERELEASE_REPO/$ARTIFACTORY_PATH_PREFIX/$1/*/*$2-$3-$ARCH*.tgz" _dep/$PLATFORM/$ARCH/ --sort-by=created --sort-order=desc --limit=1 --flat=true --detailed-summary=true
    file=`ls _dep/$PLATFORM/$ARCH/*.tgz || echo "__"`
    if [ -f $file ]; then
        tar -zxf $file -C _dep/$PLATFORM/$ARCH && rm -rf $file && return 0
    fi

    jf rt download "$ARTIFACTORY_SNAPSHOT_REPO/$ARTIFACTORY_PATH_PREFIX/$1/*/*$2-$3-$ARCH*.tgz" _dep/$PLATFORM/$ARCH/ --sort-by=created --sort-order=desc --limit=1 --flat=true --detailed-summary=true
    file=`ls _dep/$PLATFORM/$ARCH/*.tgz`
    tar -zxf $file -C _dep/$PLATFORM/$ARCH && rm -rf $file
}

function download_deps()
{
    if [ "$JF_PLATFORM" != "" ]; then
        PLATFORM=$JF_PLATFORM
    fi

    if [ -f deps.$PLATFORM.$ARCH ]; then
        source deps.$PLATFORM.$ARCH

        mkdir -p _dep/$PLATFORM/$ARCH
        for dep in "${depends[@]}"; do
            KEY="${dep%%:*}"
            VALUE="${dep##*:}"
            VER="${VALUE%%,*}"
            PLT="${VALUE##*,}"

            if [ "$VER" == "$PLT" ]; then
                PLT=$PLATFORM
            fi
            download $KEY $VER $PLT
        done
    fi
}

pushd $ROOT_DIR
download_deps
popd
