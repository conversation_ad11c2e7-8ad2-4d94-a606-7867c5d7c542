# @caption          SipDialer.Http.ListenHost
# @description      HTTP 监听地址
# @mandatory        否
# @default
# @range
# @takeeffect       重启
# @example
# @tag
# @note

SipDialer.Http.ListenHost=0.0.0.0

# @caption          SipDialer.Http.ListenPort
# @description      HTTP 监听端口
# @mandatory        否
# @default          (随机端口)
# @range
# @takeeffect       重启
# @example
# @tag
# @note

SipDialer.Http.ListenPort=8080

# @caption          SipCall.LocalAddress
# @description      本地服务用于SIP交互的UDP地址
# @mandatory        是
# @default
# @range
# @takeeffect       实时
# @example          "127.0.0.1:5060"
# @tag
# @note             一定要配置，与核心网处于不同局域网需要设置成公网IP
# @groupName
# @mode
# @groupDesc
# @regExp 	        ^(?:[0-9]{1,3}\.){3}[0-9]{1,3}:[0-9]{1,5}$
# @regExpDesc       IP:Port

SipCall.LocalAddress=127.0.0.1:5060

# @caption          SipCall.RemoteAddress
# @description      核心网SBC用于SIP交互的UDP地址
# @mandatory        是
# @default
# @range
# @takeeffect       实时
# @example          "***********:5060"
# @tag
# @note             一定要配置，根据实际情况设置
# @groupName
# @mode
# @groupDesc
# @regExp 	        ^((?:[0-9]{1,3}\.){3}[0-9]{1,3}:[0-9]{1,5}(,([1-9][0-9]{0,3}))?[;]?)+$
# @regExpDesc       IP:Port

SipCall.RemoteAddress=***********:5060

# @caption          SipDialer.RtpPorts.UpperBound
# @description      RTP 端口范围上限
# @mandatory        否
# @default          22000
# @range
# @takeeffect       重启
# @example          22000
# @tag
# @note
# @groupName
# @mode
# @groupDesc
# @regExp 	        ^[0-9]{1,5}$
# @regExpDesc       Port

SipDialer.RtpPorts.UpperBound=22000

# @caption          SipDialer.RtpPorts.LowerBound
# @description      RTP 端口范围下限
# @mandatory        否
# @default          22999
# @range
# @takeeffect       重启
# @example          22999
# @tag
# @note
# @groupName
# @mode
# @groupDesc
# @regExp 	        ^[0-9]{1,5}$
# @regExpDesc       Port

SipDialer.RtpPorts.LowerBound=22999

# @caption          SipDialer.EchoMode
# @description      是否为回声服务
# @mandatory        否
# @default          0
# @range
# @takeeffect       重启
# @example          1
# @tag
# @note

SipDialer.EchoMode=1

# @caption          SipDialer.EchoTimeoutSeconds
# @description      回声模式下会话超时时间
# @mandatory        否
# @default          0
# @range
# @takeeffect       实时
# @example          60
# @tag
# @note

SipDialer.EchoTimeoutSeconds=60
