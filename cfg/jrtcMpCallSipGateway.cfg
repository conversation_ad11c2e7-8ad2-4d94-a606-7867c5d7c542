# @caption          SipCall.CoreNetId
# @description      对接的核心网ID
# @mandatory        是
# @default
# @range
# @takeeffect       实时
# @example          "Test", "Cisco", "Huawei"
# @tag
# @note             用于区分不同的SIP核心网
# @groupName
# @mode
# @groupDesc
# @regExp 	        ^[a-zA-Z0-9]{1,32}$
# @regExpDesc

SipCall.CoreNetId=Test

# @caption          SipCall.ASBC
# @description      SIP 线路为 A-SBC 模式
# @mandatory        否
# @default          0
# @range            0,1
# @takeeffect       重启
# @example          SipCall.ASBC=1
# @tag
# @note
# @groupName
# @mode
# @groupDesc
# @regExp 	        ^(0|1)$
# @regExpDesc       0,1

SipCall.ASBC=0

# @caption          SipCall.ExclusiveRtpRunner
# @description      Whether sip gateway monopolizes rtp runner.
# @mandatory        否
# @default
# @range
# @takeeffect       实时
# @example          SipCall.ExclusiveRtpRunner=1
# @tag
# @note             If 1, sip gateway service monopolizes rtp runner with same core network id.
# @groupName
# @mode
# @groupDesc
# @regExp 	        ^(0|1)$
# @regExpDesc       0,1

SipCall.ExclusiveRtpRunner=1

# @caption          SipCall.LocalAddress
# @description      本地服务用于SIP交互的UDP地址
# @mandatory        是
# @default
# @range
# @takeeffect       实时
# @example          "127.0.0.1:5060"
# @tag
# @note             一定要配置，与核心网处于不同局域网需要设置成公网IP
# @groupName
# @mode
# @groupDesc
# @regExp 	        ^(?:[0-9]{1,3}\.){3}[0-9]{1,3}:[0-9]{1,5}$
# @regExpDesc       IP:Port

SipCall.LocalAddress=127.0.0.1:5060

# @caption          SipCall.RemoteAddress
# @description      核心网SBC用于SIP交互的UDP地址
# @mandatory        是
# @default
# @range
# @takeeffect       实时
# @example          "***********:5060"
# @tag
# @note             一定要配置，根据实际情况设置
# @groupName
# @mode
# @groupDesc
# @regExp 	        ^((?:[0-9]{1,3}\.){3}[0-9]{1,3}:[0-9]{1,5}(,([1-9][0-9]{0,3}))?[;]?)+$
# @regExpDesc       IP:Port

SipCall.RemoteAddress=***********:5060

# @caption          SipCall.ContactEx
# @description      SIP信息中Contact头的自定义扩展
# @mandatory        否
# @default
# @range
# @takeeffect       实时
# @example          "useradd=***********;userport=5060"
# @tag
# @note             浙江移动项目需要配置, 用于SIP对接
# @groupName
# @mode
# @groupDesc
# @regExp
# @regExpDesc

SipCall.ContactEx=

# @caption          SipCall.CalleePrefix
# @description      线路侧的号码前缀
# @mandatory        否
# @default
# @range
# @takeeffect       实时
# @example          "+86", "86"
#   [浙江移动], 配置"+86"
# @tag
# @note             外呼到线路, 被叫号加前缀; 收到线路呼叫, 主叫号去前缀
# @groupName
# @mode
# @groupDesc
# @regExp           ^\+?[0-9]{1,10}$
# @regExpDesc

SipCall.CalleePrefix=

# @caption          SipCall.KeepAlive
# @description      心跳模式配置
# @mandatory        否
# @default          OPTIONS:30
# @range            OFF, CRLF:30, OPTIONS:30, CRLF:30 OPTIONS:240
# @takeeffect       重启
# @example          SipCall.KeepAlive=CRLF:30 OPTIONS:3600
# @tag
# @note             冒号之后表示间隔时长（单位秒）。可以同时配置 CRLF 和 OPTIONS
# @groupName
# @mode
# @groupDesc
# @regExp           ^(OFF|((CRLF|OPTION|OPTIONS):[1-9][0-9]{1,10} ?)+)$
# @regExpDesc

SipCall.KeepAlive=OPTIONS:30

# @caption          SipCall.NoListening
# @description      是否开启不监听模式
# @mandatory        否
# @default          0
# @range            0,1
# @takeeffect       重启
# @example          SipCall.NoListening=1
# @tag
# @note
# @groupName
# @mode
# @groupDesc
# @regExp           ^(0|1)$
# @regExpDesc       0,1

SipCall.NoListening=

# @caption          RunnerManager.AllocTimeout
# @description      分配Runner的超时时间
# @mandatory        否
# @default          0
# @range            0-30
# @takeeffect       重启
# @example          RunnerManager.AllocTimeout=10
# @tag
# @note
# @groupName
# @mode
# @groupDesc
# @regExp           ^(\d|[1-2]\d|30)$
# @regExpDesc       0-30

RunnerManager.AllocTimeout=

# @caption          SipCall.AudioExcludeRole
# @description      过滤成员音频的角色
# @mandatory        否
# @default
# @range
# @takeeffect
# @example          SipCall.AudioExcludeRole=
# @tag
# @note
# @groupName
# @mode
# @groupDesc
# @regExp
# @regExpDesc

SipCall.AudioExcludeRole=

# @caption          SipCall.Registration.Accounts
# @description      过滤成员音频的角色
# @mandatory        否
# @default
# @range
# @takeeffect
# @example          SipCall.Registration.Accounts=
# @tag
# @note
# @groupName
# @mode
# @groupDesc
# @regExp
# @regExpDesc

SipCall.Registration.Accounts=

# @caption          SipCall.Registration.Realm
# @description      过滤成员音频的角色
# @mandatory        否
# @default
# @range
# @takeeffect
# @example          SipCall.Registration.Realm=
# @tag
# @note
# @groupName
# @mode
# @groupDesc
# @regExp
# @regExpDesc

SipCall.Registration.Realm=

# @caption          SipCall.Registration.LocalPorts
# @description      过滤成员音频的角色
# @mandatory        否
# @default
# @range
# @takeeffect
# @example          SipCall.Registration.LocalPorts=
# @tag
# @note
# @groupName
# @mode
# @groupDesc
# @regExp
# @regExpDesc

SipCall.Registration.LocalPorts=

# @caption          SipCall.FeatureTag
# @description      SipGateway流程控制特性标签
# @mandatory
# @default
# @range
# @takeeffect
# @example          SipCall.Featuretag=C09
# @tag
# @note

SipCall.FeatureTag=

# @caption          SipGateway.DummyRtpRunner
# @description      SipGateway使用假 RtpRunner
# @mandatory
# @default
# @range
# @takeeffect       重启
# @example
# @tag
# @note

SipGateway.DummyRtpRunner=1

# @caption          SipCall.EarlyMedia
# @description      是否开启 EarlyMedia
# @mandatory        否
# @default          0
# @range            0,1
# @takeeffect       重启
# @example          SipCall.EarlyMedia=1
# @tag
# @note
# @groupName
# @mode
# @groupDesc
# @regExp           ^(0|1)$
# @regExpDesc       0,1

SipCall.EarlyMedia=

# @caption          SipCall.Precondition
# @description      是否开启 Precondition
# @mandatory        否
# @default          0
# @range            0,1
# @takeeffect       重启
# @example          SipCall.Precondition=1
# @tag
# @note
# @groupName
# @mode
# @groupDesc
# @regExp           ^(0|1)$
# @regExpDesc       0,1

SipCall.Precondition=

# @caption          SipCall.ScreenSharingMode
# @description      是否开启屏幕共享
# @mandatory        否
# @default          0
# @range            0,1
# @takeeffect       重启
# @example          SipCall.ScreenSharingMode=1
# @tag
# @note
# @groupName
# @mode
# @groupDesc
# @regExp           ^(0|1)$
# @regExpDesc       0,1

SipCall.ScreenSharingMode=

# @caption          SipCall.IncomingDomainId
# @description      呼入号码的域ID
# @mandatory        否
# @default
# @range            domainId/appId 或者 CalleePrefix
# @takeeffect       实时
# @example          SipCall.IncomingDomainId=100645/4
# @tag
# @note
# @groupName
# @mode
# @groupDesc
# @regExp
# @regExpDesc

SipCall.IncomingDomainId=100645/4

# @caption          SipCall.IncomingCalleeType
# @description      呼入号码的被叫类型
# @mandatory        否
# @default
# @range            App, LegacyApp, Sip, Vman, AcdApp, H5, Wechat, Yuv, Ivvr
# @takeeffect       实时
# @example          SipCall.IncomingCalleeType=LegacyApp
# @tag
# @note
# @groupName
# @mode
# @groupDesc
# @regExp
# @regExpDesc

SipCall.IncomingCalleeType=App

# @caption          SipCall.ResponseTimeoutSeconds
# @description      线路响应超时时间，单位秒，0 表示不启用重试
# @mandatory        否
# @default          4
# @range            0-30
# @takeeffect       重启
# @example          SipCall.ResponseTimeoutSeconds=4
# @tag
# @note
# @groupName
# @mode
# @groupDesc
# @regExp           ^([0-9]|[1-2][0-9]|30)$
# @regExpDesc       0-30

SipCall.ResponseTimeoutSeconds=4

# @caption          SipCall.LineBreak.DetectTimeSeconds
# @description      线路熔断的检测时间长度，单位秒
# @mandatory        否
# @default          60
# @range            10-3600
# @takeeffect       重启
# @example          SipCall.LineBreak.DetectTimeSeconds=60
# @tag
# @note
# @groupName
# @mode
# @groupDesc
# @regExp           ^([1-9][0-9]{1,2}|[1-2][0-9]{3}|3[0-5][0-9]{2}|3600)$
# @regExpDesc       10-3600

SipCall.LineBreak.DetectTimeSeconds=60

# @caption          SipCall.LineBreak.FailRatio
# @description      线路熔断的失败比例, 单位百分比，表示在指定时间范围之内达到熔断条件的呼叫失败比例，0 表示熔断不考虑失败比例
# @mandatory        否
# @default          50
# @range            0-100
# @takeeffect       重启
# @example          SipCall.LineBreak.FailRatio=50
# @tag
# @note
# @groupName
# @mode
# @groupDesc
# @regExp           ^([0-9]|[1-9][0-9]|100)$
# @regExpDesc       0-100

SipCall.LineBreak.FailRatio=50

# @caption          SipCall.LineBreak.ContinuousFailCount
# @description      线路熔断的连续失败次数，表示在指定时间范围之内达到熔断条件的呼叫连续失败次数，0 表示熔断不考虑连续失败次数
# @mandatory        否
# @default          3
# @range            0-100
# @takeeffect       重启
# @example          SipCall.LineBreak.ContinuousFailCount=3
# @tag
# @note
# @groupName
# @mode
# @groupDesc
# @regExp           ^([0-9]|[1-9][0-9]|100)$
# @regExpDesc       0-100

SipCall.LineBreak.ContinuousFailCount=0

# @caption          SipCall.LineBreak.RecoverySeconds
# @description      线路熔断恢复时间，单位秒
# @mandatory        否
# @default          60
# @range            3-3600
# @takeeffect       重启
# @example          SipCall.LineBreak.RecoverySeconds=60
# @tag
# @note
# @groupName
# @mode
# @groupDesc
# @regExp           ^(3|[4-9]|[1-9][0-9]|[1-9][0-9]{2}|[1-2][0-9]{3}|3[0-5][0-9]{2}|3600)$
# @regExpDesc       3-3600

SipCall.LineBreak.RecoverySeconds=60

# @caption          SipGateway.Http.ListenHost
# @description      SipGateway 统计页面监听地址
# @mandatory        否
# @default
# @range
# @takeeffect       重启
# @example          SipGateway.Http.ListenHost=0.0.0.0
# @tag
# @note
# @groupName
# @mode
# @groupDesc
# @regExp           ^(?:[0-9]{1,3}\.){3}[0-9]{1,3}$
# @regExpDesc       IP

SipGateway.Http.ListenHost=0.0.0.0

# @caption          SipGateway.Http.ListenPort
# @description      SipGateway 统计页面监听端口
# @mandatory        否
# @default          (随机端口)
# @range
# @takeeffect       重启
# @example          SipGateway.Http.ListenPort=8080
# @tag
# @note
# @groupName
# @mode
# @groupDesc
# @regExp           ^[0-9]{1,5}$
# @regExpDesc       0-65535

SipGateway.Http.ListenPort=8080

# @caption          MpCallRoomConfig.ConfigKeyRecordLayout
# @description      设置通用录制的配置
# @mandatory        否
# @default
# @range
# @takeeffect       重启
# @example          MpCallRoomConfig.ConfigKeyRecordLayout=b03_voip_mix_aac
# @tag
# @note
# @groupName
# @mode
# @groupDesc
# @regExp
# @regExpDesc

MpCallRoomConfig.ConfigKeyRecordLayout=

# @caption          MpCallRoomConfig.ConfigKeyOrdinaryRecord
# @description      设置是否开启普通录制
# @mandatory        否
# @default
# @range
# @takeeffect       重启
# @example          MpCallRoomConfig.ConfigKeyOrdinaryRecord=0
# @tag
# @note
# @groupName
# @mode
# @groupDesc
# @regExp           0|1
# @regExpDesc

MpCallRoomConfig.ConfigKeyOrdinaryRecord=0
