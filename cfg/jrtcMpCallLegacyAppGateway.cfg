# @caption          LegacyAppGateway.SipCoreNetDomains
# @description      线路ID和域映射关系，针对App所属域选择线路
# @mandatory        否
# @default
# @range
# @takeeffect       实时
# @example          LegacyAppGateway.SipCoreNetDomains.TestLine=100645,200010
# @tag
# @note
# @groupName
# @mode
# @groupDesc
# @regExp
# @regExpDesc

LegacyAppGateway.SipCoreNetDomains.TestLine=100645,200010

# @caption          LegacyAppGateway.SipCoreNetMap
# @description      新旧线路ID映射关系
# @mandatory        否
# @default
# @range
# @takeeffect       实时
# @example          LegacyAppGateway.SipCoreNetMap.TestLine=LegacyLineName
# @tag
# @note
# @groupName
# @mode
# @groupDesc
# @regExp
# @regExpDesc

LegacyAppGateway.SipCoreNetMap.TestLine=LegacyLineName1,LegacyLineName2

# @caption          MpCallRoomConfig.ConfigKeyRecordLayout
# @description      设置通用录制的配置
# @mandatory        否
# @default
# @range
# @takeeffect       重启
# @example          MpCallRoomConfig.ConfigKeyRecordLayout=b03_voip_mix_aac
# @tag
# @note
# @groupName
# @mode
# @groupDesc
# @regExp
# @regExpDesc

MpCallRoomConfig.ConfigKeyRecordLayout=

# @caption          MpCallRoomConfig.ConfigKeyOrdinaryRecord
# @description      设置是否开启普通录制
# @mandatory        否
# @default
# @range
# @takeeffect       重启
# @example          MpCallRoomConfig.ConfigKeyOrdinaryRecord=0
# @tag
# @note
# @groupName
# @mode
# @groupDesc
# @regExp           0|1
# @regExpDesc

MpCallRoomConfig.ConfigKeyOrdinaryRecord=0
