# @caption          Daemon.Endpoints
# @description      Daemon 服务监听地址
# @mandatory
# @default          sudp -h 127.0.0.1;
# @range            Endpoints 配置
# @takeeffect
# @example
# @tag
# @note
# @groupName
# @mode
# @groupDesc
# @regExp 	        ^( *(sudp|udp|stcp|tcp|tls|arc|varc|sarc|svarc)( -h ([a-zA-Z0-9][-a-zA-Z0-9]{0,62}\.?)+| -p [1-9]\d{0,4}|( -[a-zA-Z0-9 -]+)*);)+$
# @regExpDesc 	    Endpoints 信息

Daemon.Endpoints=sudp -h 127.0.0.1;

# @caption          MpCallRtpRunner.Count.Max
# @description      最大 Runner 数量
# @mandatory
# @default          50
# @range            1-1024
# @takeeffect
# @example
# @tag
# @note
# @groupName
# @mode
# @groupDesc
# @regExp 	        ^([1-9]\d{0,2}|1000)$
# @regExpDesc 	    1-1000

MpCallRtpRunner.Count.Max=10

# @caption          MpCallRtpRunner.Count.PreStart
# @description      预启动 Runner 数量
# @mandatory
# @default          1
# @range            1-1024
# @takeeffect
# @example
# @tag
# @note
# @groupName
# @mode
# @groupDesc
# @regExp 	        ^([1-9]\d{0,2}|1000)$
# @regExpDesc 	    1-1000

MpCallRtpRunner.Count.PreStart=2

# @caption          MpCallRtpRunner.WorkPath
# @description      Runner 工作目录
# @mandatory
# @default          ./runners
# @range            本地路径
# @takeeffect
# @example
# @tag
# @note
# @groupName
# @mode
# @groupDesc
# @regExp 	        ^(?:\/[a-zA-Z0-9_\-\.]+|[a-zA-Z0-9_\-\.]+)+$
# @regExpDesc 	    文件路径

MpCallRtpRunner.WorkPath=/home/<USER>/log/runners

# @caption          MpCallRtpRunner.WorkPath.ExpiredDays
# @description      Runner 工作目录超时清理时间，单位天
# @mandatory
# @default          30
# @range            1-3650
# @takeeffect
# @example
# @tag
# @note
# @groupName
# @mode
# @groupDesc
# @regExp 	        ^([1-9]\d{0,3}|10000)$
# @regExpDesc 	    1-10000

MpCallRtpRunner.WorkPath.ExpiredDays=15

# @caption          MpCallRtpRunner.WorkPath.ScanIntervalSeconds
# @description      Runner 工作目录超时清理时间，单位天
# @mandatory
# @default          3600
# @range            60-24*3600
# @takeeffect
# @example
# @tag
# @note
# @groupName
# @mode
# @groupDesc
# @regExp 	        ^([1-9]\d{0,4}|100000)$
# @regExpDesc 	    1-100000

MpCallRtpRunner.WorkPath.ScanIntervalSeconds=600

# @caption          SipCall.CoreNetId
# @description      对接的核心网ID
# @mandatory        是
# @default
# @range
# @takeeffect       实时
# @example          "Test", "Cisco", "Huawei"
# @tag
# @note             用于区分不同的SIP核心网
# @groupName
# @mode
# @groupDesc
# @regExp 	        ^[a-zA-Z0-9]{1,32}$
# @regExpDesc

SipCall.CoreNetId=Test

# @caption          RtpRunner.VideoPipeMode
# @description      视频管道模式，设置 Merged 为合流模式
# @mandatory        否
# @default
# @range
# @takeeffect       实时
# @example          "Merged"
# @tag
# @note
# @groupName
# @mode
# @groupDesc
# @regExp 	        ^Merged$
# @regExpDesc

RtpRunner.VideoPipeMode=Merged


# @caption          RtpRunner.VideoPipePrioritys
# @description      视频桥接模式下优先级配置
# @mandatory
# @default
# @range
# @takeeffect       实时
# @example          "0x40", "0x80/0x80080"
# @tag
# @note
# @groupName
# @mode
# @groupDesc
# @regExp
# @regExpDesc

RtpRunner.VideoPipePrioritys=0x40,0x80/0x80080

# @caption          RtpRunner.ScreenSharingMode
# @description      是否使用屏幕共享的编码模式
# @mandatory
# @default          0
# @range            0-1
# @takeeffect       实时
# @example          "0", "1"
# @tag
# @note             0: 不使用屏幕共享的编码模式，1: 使用屏幕共享的编码模式
# @groupName
# @mode
# @groupDesc
# @regExp           ^[0-1]$
# @regExpDesc

RtpRunner.ScreenSharingMode=1

# @caption          RtpRunner.AudioBandwidth
# @description      音频SDP带宽配置
# @mandatory
# @default          0,0,0
# @range
# @takeeffect       实时
# @example          "64,800,2400"
# @tag
# @note
# @groupName
# @mode
# @groupDesc
# @regExp
# @regExpDesc

RtpRunner.AudioBandwidth=64,800,2400

# @caption          RtpRunner.VideoBandwidth
# @description      视频SDP带宽配置
# @mandatory
# @default          0,0,0
# @range
# @takeeffect       实时
# @example          "256,800,2400"
# @tag
# @note
# @groupName
# @mode
# @groupDesc
# @regExp
# @regExpDesc

RtpRunner.VideoBandwidth=256,800,2400
