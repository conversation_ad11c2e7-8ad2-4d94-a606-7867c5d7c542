# @caption          Eb.LineCallNumber
# @description      排队机呼叫号码，分配的用于区分渠道的 SIP 呼叫号码
# @mandatory
# @default
# @range
# @takeeffect
# @example
# @tag
# @note

Eb.LineCallNumber=5000

# @caption          Eb.DomainId
# @description      App终端登录使用的 DomainId
# @mandatory
# @default
# @range
# @takeeffect
# @example
# @tag
# @note

Eb.DomainId=102000

# @caption          Eb.Http.ListenHost
# @description      HTTP 服务监听的 IP 地址
# @mandatory
# @default
# @range
# @takeeffect       重启
# @example
# @tag
# @note

Eb.Http.ListenHost=*************

# @caption          Eb.Http.ListenPort
# @description      HTTP 服务监听的端口
# @mandatory
# @default
# @range
# @takeeffect       重启
# @example
# @tag
# @note

Eb.Http.ListenPort=18080

# @caption          Eb.Http.QueryAddr
# @description      查询排队信息的 HTTP 服务地址
# @mandatory
# @default
# @range
# @takeeffect
# @example
# @tag
# @note

Eb.Http.QueryAddr=http://*************:8000

# @caption          Eb.Agent.CallNumberRange
# @description      坐席呼叫号码范围
# @mandatory
# @default
# @range
# @takeeffect       重启
# @example
# @tag
# @note

Eb.Agent.CallNumberRange=1000-2000