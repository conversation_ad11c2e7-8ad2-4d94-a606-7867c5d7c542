{"cmake.configureOnOpen": true, "files.associations": {"map": "cpp", "__hash_table": "cpp", "__tree": "cpp", "deque": "cpp", "list": "cpp", "set": "cpp", "string": "cpp", "unordered_map": "cpp", "unordered_set": "cpp", "vector": "cpp", "__bit_reference": "cpp", "__config": "cpp", "__locale": "cpp", "__node_handle": "cpp", "__split_buffer": "cpp", "__threading_support": "cpp", "__verbose_abort": "cpp", "array": "cpp", "bitset": "cpp", "cctype": "cpp", "clocale": "cpp", "cmath": "cpp", "cstdarg": "cpp", "cstddef": "cpp", "cstdint": "cpp", "cstdio": "cpp", "cstdlib": "cpp", "cstring": "cpp", "ctime": "cpp", "cwchar": "cpp", "cwctype": "cpp", "execution": "cpp", "initializer_list": "cpp", "ios": "cpp", "iosfwd": "cpp", "iostream": "cpp", "istream": "cpp", "limits": "cpp", "locale": "cpp", "mutex": "cpp", "new": "cpp", "optional": "cpp", "ostream": "cpp", "ratio": "cpp", "stdexcept": "cpp", "streambuf": "cpp", "string_view": "cpp", "tuple": "cpp", "typeinfo": "cpp", "variant": "cpp", "sstream": "cpp", "algorithm": "cpp", "queue": "cpp", "regex": "cpp", "stack": "cpp", "any": "cpp", "fstream": "cpp", "chrono": "cpp", "condition_variable": "cpp", "thread": "cpp", "iomanip": "cpp", "system_error": "cpp", "version": "cpp", "memory_resource": "cpp", "*.inc": "cpp"}}